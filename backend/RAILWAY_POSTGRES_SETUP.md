# Railway PostgreSQL Setup Guide

## Current Issue
Your backend is trying to use SQLite but Railway's filesystem is read-only. You need to configure it to use the PostgreSQL database you created.

## Step-by-Step Setup

### 1. Get PostgreSQL Connection String

1. **Go to Railway Dashboard** → Your Project
2. **Click on your PostgreSQL service** (not the backend service)
3. **Go to "Variables" or "Connect" tab**
4. **Copy the DATABASE_URL** - it looks like:
   ```
   postgresql://postgres:password@hostname:port/railway
   ```

### 2. Configure Backend Service

1. **Go to your Backend service** in Railway
2. **Click "Variables" tab**
3. **Add new environment variable:**
   - **Name:** `DATABASE_URL`
   - **Value:** Paste the PostgreSQL connection string from step 1
4. **Click "Add"**

### 3. Verify Environment Variables

Your backend should have these environment variables:
- `DATABASE_URL` - PostgreSQL connection string
- `GEMINI_API_KEY` - Your Google AI API key
- `NODE_ENV` - Should be "production" (auto-set by Railway)
- `PORT` - Auto-set by Railway

### 4. Redeploy

After adding the DATABASE_URL:
1. **Trigger a new deployment** (Railway should auto-deploy)
2. **Check the logs** for these messages:
   ```
   DATABASE_URL exists: true
   DATABASE_URL type: PostgreSQL
   Connected to PostgreSQL database
   Database tables initialized successfully
   ```

### 5. Test Connection (Optional)

If you want to test the connection locally first:

```bash
# In the backend directory
DATABASE_URL="your_postgres_url_here" npm run test-postgres
```

## Expected Log Output

After successful setup, you should see:
```
=== SERVER STARTUP DEBUG ===
DATABASE_URL exists: true
DATABASE_URL type: PostgreSQL
============================
Connected to PostgreSQL database
Database tables initialized successfully
Prompt templates initialized successfully
Server is running on port 8080
```

## Troubleshooting

### If you see "DATABASE_URL exists: false"
- The environment variable is not set in Railway
- Go back to step 2 and add the DATABASE_URL variable

### If you see "DATABASE_URL type: Other"
- The connection string format is incorrect
- Make sure it starts with `postgresql://`

### If connection fails
- Check that the PostgreSQL service is running in Railway
- Verify the connection string is correct
- Make sure both services are in the same Railway project

## Database Schema

The backend will automatically create these tables:
- `users` - User accounts and authentication
- `tasks` - Article generation tasks
- `user_presets` - User-saved presets
- `blog_posts` - Generated articles
- `user_sessions` - Session management
- `prompt_templates` - AI prompt templates

## Next Steps

Once PostgreSQL is working:
1. Test user registration/login
2. Test article generation
3. Verify data persistence between deployments
