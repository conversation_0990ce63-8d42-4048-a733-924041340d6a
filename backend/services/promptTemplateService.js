const promptTemplates = require('../config/promptTemplates');

class PromptTemplateService {
  constructor() {
    // 使用代码中的配置而不是数据库
    this.templates = promptTemplates.PROMPT_TEMPLATES;
  }

  /**
   * 获取指定内容类型的模板
   * @param {string} contentType 内容类型
   * @returns {Object|null} 模板对象
   */
  async getTemplateByContentType(contentType) {
    try {
      const template = promptTemplates.getTemplateByContentType(contentType);
      
      if (template) {
        // 添加content_type字段用于兼容性
        return {
          content_type: contentType,
          ...template
        };
      }
      
      return null;
    } catch (error) {
      console.error('Error getting template by content type:', error);
      throw error;
    }
  }

  /**
   * 获取所有活跃模板
   * @returns {Array} 活跃模板数组
   */
  async getAllActiveTemplates() {
    try {
      return promptTemplates.getAllActiveTemplates();
    } catch (error) {
      console.error('Error getting all active templates:', error);
      throw error;
    }
  }

  /**
   * 获取所有可用的内容类型
   * @returns {Array} 内容类型数组
   */
  getAvailableContentTypes() {
    return promptTemplates.getAvailableContentTypes();
  }

  /**
   * 构建动态prompt - 主要的prompt构建方法
   * @param {string} contentType 内容类型
   * @param {Object} articleData 文章数据
   * @returns {string} 构建好的prompt
   */
  async buildDynamicPrompt(contentType, articleData) {
    try {
      console.log(`Building prompt for content type: ${contentType || 'generic'}`);
      
      // 使用新的基于代码的prompt构建
      const template = promptTemplates.getTemplateByContentType(contentType);
      
      if (!template) {
        console.warn(`No template found for content type: ${contentType}, using generic template`);
        throw new Error(`No template available for content type: ${contentType}`);
      }

      console.log(`Using template: ${template.template_name} (${contentType})`);

      // 提取文章数据
      const {
        topics,
        sources,
        productInfo,
        tonality,
        length,
        format,
        authorName,
        authorBio,
        targetAudience,
        articleGoal,
        primaryKeywords,
        secondaryKeywords,
        jurisdiction,
        complianceTemplate,
        customDisclaimers,
        additionalRequirements,
        targetLanguage,
        targetCountry
      } = articleData;

      // 构建article_inputs参数
      const articleInputs = this.buildArticleInputs({
        topics,
        primaryKeywords,
        secondaryKeywords,
        sources,
        productInfo,
        authorName,
        authorBio,
        targetAudience,
        articleGoal,
        tonality,
        length,
        format,
        jurisdiction,
        complianceTemplate,
        customDisclaimers,
        additionalRequirements,
        contentType,
        targetLanguage,
        targetCountry
      });

      // 计算字数
      const wordCount = this.getWordCountFromLength(length);

      // 使用新的prompt构建方法
      let dynamicPrompt = promptTemplates.buildDynamicPrompt(contentType, {
        ...articleData,
        jurisdiction: jurisdiction || 'international'
      });

      // 替换article_inputs和wordCount占位符
      dynamicPrompt = dynamicPrompt
        .replace('{{article_inputs}}', articleInputs)
        .replace('{{wordCount}}', wordCount)
        .replace('{{CONTENT_TYPE}}', contentType || 'general content');

      console.log('✅ Dynamic prompt built successfully');
      return dynamicPrompt;
    } catch (error) {
      console.error('Error building dynamic prompt:', error);
      throw error;
    }
  }

  /**
   * 构建文章输入参数
   * @param {Object} data 文章数据
   * @returns {string} 格式化的文章输入
   */
  buildArticleInputs(data) {
    const {
      topics,
      primaryKeywords,
      secondaryKeywords,
      sources,
      productInfo,
      authorName,
      authorBio,
      targetAudience,
      articleGoal,
      tonality,
      length,
      format,
      jurisdiction,
      complianceTemplate,
      customDisclaimers,
      additionalRequirements,
      contentType,
      targetLanguage,
      targetCountry
    } = data;

    let articleInputs = '';

    // 语言和市场信息 (添加在最开始)
    if (targetLanguage || targetCountry) {
      const languageNames = {
        'en': 'English',
        'zh': 'Chinese (Simplified)',
        'pt': 'Portuguese',
        'es': 'Spanish', 
        'de': 'German',
        'fr': 'French',
        'it': 'Italian',
        'ja': 'Japanese'
      };
      
      const countryNames = {
        'us': 'United States',
        'br': 'Brazil',
        'gb': 'United Kingdom',
        'ca': 'Canada',
        'au': 'Australia',
        'de': 'Germany',
        'fr': 'France',
        'es': 'Spain',
        'it': 'Italy',
        'jp': 'Japan',
        'cn': 'China'
      };
      
      const targetLanguageName = languageNames[targetLanguage] || targetLanguage;
      const targetCountryName = countryNames[targetCountry] || targetCountry;
      
      articleInputs += `**CRITICAL LANGUAGE & MARKET REQUIREMENTS:**\n`;
      if (targetLanguage && targetLanguage !== 'en') {
        articleInputs += `- WRITE THE ENTIRE ARTICLE IN ${targetLanguageName.toUpperCase()}. Do NOT use English unless specifically required.\n`;
      }
      if (targetCountry) {
        articleInputs += `- Target Market: ${targetCountryName} - Consider local culture, regulations, and market preferences\n`;
      }
      articleInputs += '\n';
    }

    // 1. 主题部分
    if (topics && topics.length > 0) {
      articleInputs += `**KEY CONCEPTS & ANGLES TO COVER:**\n`;
      topics.forEach((topic, index) => {
        articleInputs += `${index + 1}. ${topic}\n`;
      });
      articleInputs += '\n';
    }

    // 2. 关键词部分
    if (primaryKeywords && primaryKeywords.length > 0) {
      articleInputs += `**PRIMARY KEYWORDS (must include naturally):**\n${primaryKeywords.join(', ')}\n\n`;
    }
    
    if (secondaryKeywords && secondaryKeywords.length > 0) {
      articleInputs += `**SECONDARY KEYWORDS (include where relevant):**\n${secondaryKeywords.join(', ')}\n\n`;
    }

    // 3. 来源部分
    if (sources && sources.length > 0) {
      articleInputs += `**REFERENCE SOURCES:**\n`;
      sources.forEach((source, index) => {
        if (source.type === 'reddit') {
          articleInputs += `${index + 1}. [Reddit Discussion] ${source.title}\n`;
          articleInputs += `   - Community: r/${source.subreddit}\n`;
          articleInputs += `   - Engagement: ${source.upvotes || 0} upvotes, ${source.comments || 0} comments\n`;
          if (source.selftext || source.body) {
            const content = source.selftext || source.body || '';
            articleInputs += `   - Content: ${content.substring(0, 500)}${content.length > 500 ? '...' : ''}\n`;
          }
          articleInputs += `   - URL: ${source.url}\n`;
          
          if (source.topComments && source.topComments.length > 0) {
            articleInputs += `   - Key Comments:\n`;
            source.topComments.slice(0, 3).forEach((comment, idx) => {
              articleInputs += `     ${idx + 1}. "${comment.body.substring(0, 200)}${comment.body.length > 200 ? '...' : ''}" (${comment.score} points)\n`;
            });
          }
        } else if (source.type === 'url') {
          articleInputs += `${index + 1}. [Web Article] ${source.title || 'Untitled'}\n`;
          articleInputs += `   - URL: ${source.url}\n`;
          if (source.description) {
            articleInputs += `   - Description: ${source.description}\n`;
          }
          if (source.content) {
            articleInputs += `   - Content: ${source.content.substring(0, 500)}${source.content.length > 500 ? '...' : ''}\n`;
          }
        } else if (source.type === 'text') {
          articleInputs += `${index + 1}. [Text Block] ${source.title || 'Custom Text'}\n`;
          articleInputs += `   - Content: ${source.content || ''}\n`;
        }
      });
      articleInputs += '\n';
    }

    // 4. 产品集成部分
    if (productInfo && productInfo.name) {
      articleInputs += `**PRODUCT INTEGRATION:**\n`;
      articleInputs += `Product Name: ${productInfo.name}\n`;
      if (productInfo.description) {
        articleInputs += `Description: ${productInfo.description}\n`;
      }
      if (productInfo.features && productInfo.features.length > 0) {
        articleInputs += `Key Features: ${productInfo.features.join(', ')}\n`;
      }
      if (productInfo.link) {
        articleInputs += `Product Link: ${productInfo.link}\n`;
      }
      articleInputs += '\n';
    }

    // 5. 作者和E-E-A-T部分
    if (authorName || authorBio) {
      articleInputs += `**AUTHOR EXPERTISE (E-E-A-T):**\n`;
      articleInputs += `Author: ${authorName || 'Anonymous'}\n`;
      if (authorBio) {
        articleInputs += `Bio: ${authorBio}\n`;
      }
      articleInputs += '\n';
    }

    // 6. 文章参数
    articleInputs += `**ARTICLE PARAMETERS:**\n`;
    articleInputs += `Content Type: ${contentType || 'general'}\n`;
    articleInputs += `Tonality: ${tonality || 'informative'}\n`;
    articleInputs += `Target Length: ${this.getWordCountFromLength(length)} words (${length || 'medium_article'})\n`;
    articleInputs += `Format: ${format || 'markdown'}\n`;
    if (targetAudience) {
      articleInputs += `Target Audience: ${targetAudience}\n`;
    }
    if (articleGoal) {
      articleInputs += `Article Goal: ${articleGoal}\n`;
    }
    articleInputs += '\n';

    // 7. 合规性和特殊要求部分
    if (jurisdiction || complianceTemplate || customDisclaimers || additionalRequirements) {
      articleInputs += `**COMPLIANCE & SPECIAL REQUIREMENTS:**\n`;
      articleInputs += `Jurisdiction: ${jurisdiction || 'international'}\n`;
      
      if (complianceTemplate) {
        articleInputs += `Compliance Framework: ${complianceTemplate.name || 'Standard'}\n`;
        if (complianceTemplate.requirements && complianceTemplate.requirements.length > 0) {
          articleInputs += `Required Elements: ${complianceTemplate.requirements.join(', ')}\n`;
        }
        if (complianceTemplate.disclaimers && complianceTemplate.disclaimers.length > 0) {
          articleInputs += `Mandatory Disclaimers: ${complianceTemplate.disclaimers.join(' | ')}\n`;
        }
      }
      
      if (customDisclaimers && customDisclaimers.length > 0) {
        articleInputs += `Custom Disclaimers: ${customDisclaimers.join(' | ')}\n`;
      }
      
      if (additionalRequirements) {
        articleInputs += `Additional Requirements: ${additionalRequirements}\n`;
      }
    }

    return articleInputs.trim();
  }

  /**
   * 根据长度类型获取字数
   * @param {string} length 长度类型
   * @returns {number} 字数
   */
  getWordCountFromLength(length) {
    const lengthMap = {
      'snippet': 500,
      'short_post': 800,
      'medium_article': 1500,
      'long_guide': 2500,
      'pillar_module': 700
    };

    const selectedKey = typeof length === 'string' ? length.toLowerCase() : 'medium_article';
    return lengthMap[selectedKey] || 1500;
  }

  // ===== The following methods are kept for backward compatibility, but now use code-based configuration =====

  /**
   * Initialize default templates - now a no-op because templates are in code
   * Keeping this method for API compatibility
   */
  async initializeDefaultTemplates() {
    console.log('⚠️  initializeDefaultTemplates is deprecated - prompt templates are now managed in code');
    console.log('✅ Code-based prompt template system is enabled');
    return Promise.resolve();
  }

  /**
   * Create new template - not supported now, need to modify code
   * @param {Object} templateData Template data
   */
  async createTemplate(templateData) {
    throw new Error('Creating templates via API is not supported. Please add new templates in backend/config/promptTemplates.js.');
  }

  /**
   * Update template - not supported now, need to modify code
   * @param {string} id Template ID
   * @param {Object} templateData Template data
   */
  async updateTemplate(id, templateData) {
    throw new Error('Updating templates via API is not supported. Please modify templates in backend/config/promptTemplates.js.');
  }

  /**
   * Build default prompt - now uses code-based configuration
   * @param {Object} articleData Article data
   * @returns {string} Built prompt
   */
  buildDefaultPrompt(articleData) {
    console.log('Using generic template as fallback');
    return this.buildDynamicPrompt('generic', articleData);
  }
}

module.exports = new PromptTemplateService();