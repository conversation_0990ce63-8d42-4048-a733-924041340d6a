const Database = require('../config/database');

/**
 * 联盟营销追踪服务
 * 处理点击统计、转化追踪、智能链接推荐等功能
 */

class AffiliateTrackingService {
  
  /**
   * 智能推荐联盟链接
   * 根据文章内容类型、语言、国家等推荐最合适的联盟链接
   */
  async getRecommendedLinks(criteria) {
    try {
      const {
        content_type,
        language,
        country,
        category,
        position = 'primary_cta',
        limit = 3
      } = criteria;

      let whereConditions = ['is_active = true'];
      let params = [];
      let paramIndex = 1;

      // 内容类型匹配
      if (content_type) {
        whereConditions.push(`content_types LIKE ?`);
        params.push(`%"${content_type}"%`);
      }

      // 语言匹配
      if (language) {
        whereConditions.push(`target_languages LIKE ?`);
        params.push(`%"${language}"%`);
      }

      // 国家匹配
      if (country) {
        whereConditions.push(`target_countries LIKE ?`);
        params.push(`%"${country}"%`);
      }

      // 分类匹配
      if (category) {
        whereConditions.push(`categories LIKE ?`);
        params.push(`%"${category}"%`);
      }

      // 检查时间有效性
      whereConditions.push(`(start_date IS NULL OR start_date <= CURRENT_TIMESTAMP)`);
      whereConditions.push(`(end_date IS NULL OR end_date >= CURRENT_TIMESTAMP)`);

      const whereClause = `WHERE ${whereConditions.join(' AND ')}`;

      const query = `
        SELECT 
          id, name, description, affiliate_url, display_text, button_style,
          content_types, categories, target_countries, target_languages,
          commission_rate, priority, click_count, conversion_rate,
          tracking_code, tracking_params
        FROM affiliate_links 
        ${whereClause}
        ORDER BY 
          priority DESC, 
          conversion_rate DESC, 
          click_count DESC
        LIMIT ?
      `;
      params.push(parseInt(limit));

      const links = await Database.all(query, params);

      // 解析JSON字段
      return links.map(link => ({
        ...link,
        content_types: link.content_types ? JSON.parse(link.content_types) : [],
        categories: link.categories ? JSON.parse(link.categories) : [],
        target_countries: link.target_countries ? JSON.parse(link.target_countries) : [],
        target_languages: link.target_languages ? JSON.parse(link.target_languages) : [],
        tracking_params: link.tracking_params ? JSON.parse(link.tracking_params) : {}
      }));

    } catch (error) {
      console.error('获取推荐联盟链接失败:', error);
      throw error;
    }
  }

  /**
   * 记录详细的点击事件
   * 包括用户设备信息、地理位置、推荐来源等
   */
  async trackClick(clickData) {
    try {
      const {
        affiliate_link_id,
        article_id,
        article_url,
        article_title,
        click_position,
        user_ip,
        user_agent,
        referer,
        session_id
      } = clickData;

      // 从 User-Agent 解析设备和浏览器信息
      const deviceInfo = this.parseUserAgent(user_agent);
      
      // 从 IP 推测地理位置（简化版，实际可集成 GeoIP 服务）
      const geoInfo = await this.getGeoInfo(user_ip);

      // 从 referer 提取 UTM 参数
      const utmParams = this.extractUTMParams(referer);

      // 记录点击
      const result = await Database.run(`
        INSERT INTO affiliate_clicks (
          affiliate_link_id, article_id, article_url, article_title, click_position,
          user_ip, user_agent, referer, country_code, language_code, 
          device_type, browser, utm_source, utm_medium, utm_campaign, 
          utm_content, utm_term, session_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        affiliate_link_id, article_id, article_url, article_title, click_position,
        user_ip, user_agent, referer, geoInfo.country_code, geoInfo.language_code,
        deviceInfo.device_type, deviceInfo.browser, utmParams.utm_source,
        utmParams.utm_medium, utmParams.utm_campaign, utmParams.utm_content,
        utmParams.utm_term, session_id
      ]);

      // 更新联盟链接的点击计数
      await Database.run(
        'UPDATE affiliate_links SET click_count = click_count + 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [affiliate_link_id]
      );

      return { clickId: result.id, success: true };

    } catch (error) {
      console.error('记录点击失败:', error);
      throw error;
    }
  }

  /**
   * 记录转化事件
   */
  async trackConversion(conversionData) {
    try {
      const {
        click_id,
        affiliate_link_id,
        conversion_value,
        session_id
      } = conversionData;

      // 更新点击记录为已转化
      await Database.run(`
        UPDATE affiliate_clicks 
        SET conversion_tracked = true, conversion_value = ?, conversion_date = CURRENT_TIMESTAMP
        WHERE id = ? OR (affiliate_link_id = ? AND session_id = ? AND conversion_tracked = false)
      `, [conversion_value, click_id, affiliate_link_id, session_id]);

      // 更新联盟链接的转化数据
      await Database.run(`
        UPDATE affiliate_links 
        SET 
          conversion_count = conversion_count + 1,
          total_earnings = total_earnings + ?,
          conversion_rate = CASE 
            WHEN click_count > 0 THEN (conversion_count + 1.0) / click_count * 100 
            ELSE 0 
          END,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [conversion_value || 0, affiliate_link_id]);

      return { success: true };

    } catch (error) {
      console.error('记录转化失败:', error);
      throw error;
    }
  }

  /**
   * 获取联盟链接性能分析
   */
  async getPerformanceAnalytics(linkId, timeRange = '30d') {
    try {
      // 计算时间范围
      const timeCondition = this.getTimeCondition(timeRange);
      
      // 基础性能数据
      const basicStats = await Database.get(`
        SELECT 
          COUNT(*) as total_clicks,
          COUNT(DISTINCT session_id) as unique_sessions,
          COUNT(DISTINCT user_ip) as unique_visitors,
          COUNT(CASE WHEN conversion_tracked = true THEN 1 END) as conversions,
          AVG(CASE WHEN conversion_tracked = true THEN conversion_value ELSE 0 END) as avg_conversion_value,
          SUM(CASE WHEN conversion_tracked = true THEN conversion_value ELSE 0 END) as total_revenue
        FROM affiliate_clicks 
        WHERE affiliate_link_id = ? AND click_timestamp >= ${timeCondition}
      `, [linkId]);

      // 转化率计算
      const conversionRate = basicStats.total_clicks > 0 
        ? (basicStats.conversions / basicStats.total_clicks * 100).toFixed(2)
        : 0;

      // 按时间分组的点击趋势
      const clickTrend = await Database.all(`
        SELECT 
          DATE_TRUNC('day', click_timestamp) as date,
          COUNT(*) as clicks,
          COUNT(CASE WHEN conversion_tracked = true THEN 1 END) as conversions
        FROM affiliate_clicks 
        WHERE affiliate_link_id = ? AND click_timestamp >= ${timeCondition}
        GROUP BY DATE_TRUNC('day', click_timestamp)
        ORDER BY date DESC
      `, [linkId]);

      // 地理分布
      const geoDistribution = await Database.all(`
        SELECT 
          country_code,
          COUNT(*) as clicks,
          COUNT(CASE WHEN conversion_tracked = true THEN 1 END) as conversions
        FROM affiliate_clicks 
        WHERE affiliate_link_id = ? AND click_timestamp >= ${timeCondition}
        GROUP BY country_code
        ORDER BY clicks DESC
        LIMIT 10
      `, [linkId]);

      // 设备分布
      const deviceDistribution = await Database.all(`
        SELECT 
          device_type,
          COUNT(*) as clicks,
          COUNT(CASE WHEN conversion_tracked = true THEN 1 END) as conversions
        FROM affiliate_clicks 
        WHERE affiliate_link_id = ? AND click_timestamp >= ${timeCondition}
        GROUP BY device_type
        ORDER BY clicks DESC
      `, [linkId]);

      return {
        basicStats: {
          ...basicStats,
          conversion_rate: conversionRate
        },
        clickTrend,
        geoDistribution,
        deviceDistribution
      };

    } catch (error) {
      console.error('获取性能分析失败:', error);
      throw error;
    }
  }

  /**
   * 批量管理联盟链接关联
   */
  async bulkManageArticleLinks(operation) {
    try {
      const {
        action, // 'add', 'remove', 'update'
        article_ids,
        affiliate_link_id,
        position,
        filters
      } = operation;

      let targetArticleIds = article_ids || [];

      // 如果没有指定文章ID，根据筛选条件查找
      if (targetArticleIds.length === 0 && filters) {
        const articleQuery = this.buildArticleFilterQuery(filters);
        const articles = await Database.all(articleQuery.sql, articleQuery.params);
        targetArticleIds = articles.map(article => article.id);
      }

      if (targetArticleIds.length === 0) {
        throw new Error('没有找到符合条件的文章');
      }

      let results = { success: 0, failed: 0, errors: [] };

      for (const articleId of targetArticleIds) {
        try {
          switch (action) {
            case 'add':
              await Database.run(`
                INSERT INTO article_affiliate_links (article_id, affiliate_link_id, position)
                VALUES (?, ?, ?)
                ON CONFLICT (article_id, affiliate_link_id, position) DO NOTHING
              `, [articleId, affiliate_link_id, position]);
              break;

            case 'remove':
              await Database.run(`
                DELETE FROM article_affiliate_links 
                WHERE article_id = ? AND affiliate_link_id = ?
              `, [articleId, affiliate_link_id]);
              break;

            case 'update':
              await Database.run(`
                UPDATE article_affiliate_links 
                SET position = ?, updated_at = CURRENT_TIMESTAMP
                WHERE article_id = ? AND affiliate_link_id = ?
              `, [position, articleId, affiliate_link_id]);
              break;
          }
          results.success++;
        } catch (error) {
          results.failed++;
          results.errors.push({ articleId, error: error.message });
        }
      }

      return results;

    } catch (error) {
      console.error('批量管理失败:', error);
      throw error;
    }
  }

  // ================== 辅助方法 ==================

  /**
   * 解析 User-Agent 获取设备和浏览器信息
   */
  parseUserAgent(userAgent) {
    if (!userAgent) {
      return { device_type: 'unknown', browser: 'unknown' };
    }

    const ua = userAgent.toLowerCase();
    
    let device_type = 'desktop';
    if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
      device_type = 'mobile';
    } else if (ua.includes('tablet') || ua.includes('ipad')) {
      device_type = 'tablet';
    }

    let browser = 'unknown';
    if (ua.includes('chrome')) browser = 'chrome';
    else if (ua.includes('firefox')) browser = 'firefox';
    else if (ua.includes('safari')) browser = 'safari';
    else if (ua.includes('edge')) browser = 'edge';
    else if (ua.includes('opera')) browser = 'opera';

    return { device_type, browser };
  }

  /**
   * 简化的地理位置信息获取
   * 实际应用中可以集成 MaxMind GeoIP 或其他服务
   */
  async getGeoInfo(ip) {
    // 简化实现，实际可调用 GeoIP API
    return {
      country_code: 'US',
      language_code: 'en'
    };
  }

  /**
   * 从 URL 中提取 UTM 参数
   */
  extractUTMParams(url) {
    const params = {
      utm_source: null,
      utm_medium: null,
      utm_campaign: null,
      utm_content: null,
      utm_term: null
    };

    if (!url) return params;

    try {
      const urlObj = new URL(url);
      Object.keys(params).forEach(key => {
        params[key] = urlObj.searchParams.get(key);
      });
    } catch (error) {
      // URL 解析失败，返回空参数
    }

    return params;
  }

  /**
   * 根据时间范围生成 SQL 时间条件
   */
  getTimeCondition(timeRange) {
    const ranges = {
      '24h': "NOW() - INTERVAL '24 hours'",
      '7d': "NOW() - INTERVAL '7 days'",
      '30d': "NOW() - INTERVAL '30 days'",
      '90d': "NOW() - INTERVAL '90 days'",
      '365d': "NOW() - INTERVAL '365 days'"
    };

    return ranges[timeRange] || ranges['30d'];
  }

  /**
   * 构建文章筛选查询
   */
  buildArticleFilterQuery(filters) {
    let whereConditions = [];
    let params = [];
    let paramIndex = 1;

    if (filters.content_type) {
      whereConditions.push(`content_type = $${paramIndex}`);
      params.push(filters.content_type);
      paramIndex++;
    }

    if (filters.language) {
      whereConditions.push(`target_language = $${paramIndex}`);
      params.push(filters.language);
      paramIndex++;
    }

    if (filters.status) {
      whereConditions.push(`status = $${paramIndex}`);
      params.push(filters.status);
      paramIndex++;
    }

    if (filters.date_from) {
      whereConditions.push(`created_at >= $${paramIndex}`);
      params.push(filters.date_from);
      paramIndex++;
    }

    if (filters.date_to) {
      whereConditions.push(`created_at <= $${paramIndex}`);
      params.push(filters.date_to);
      paramIndex++;
    }

    const whereClause = whereConditions.length > 0 
      ? `WHERE ${whereConditions.join(' AND ')}` 
      : '';

    return {
      sql: `SELECT id FROM tasks ${whereClause}`,
      params
    };
  }
}

module.exports = new AffiliateTrackingService();