const geminiService = require('./geminiService');

class RiskKeywordService {
  constructor() {
    this.riskKeywords = this.initializeRiskKeywords();
  }

  // Initialize multilingual risk keywords database
  initializeRiskKeywords() {
    return {
      'en': { // English
        predatory: {
          level: 'high',
          keywords: [
            'guaranteed win',
            'guaranteed profit',
            'sure win',
            'easy money',
            'get rich quick',
            'foolproof system',
            'never lose',
            'always win',
            'risk-free',
            'guaranteed returns',
            'instant wealth',
            'secret system',
            'insider tips',
            'can\'t lose',
            'surefire',
            'bulletproof strategy',
            'unlimited winnings',
            'guaranteed success'
          ],
          message: 'These terms make unrealistic promises and should be avoided in gambling content.'
        },

        // Medium-risk promotional terms
        promotional: {
          level: 'medium',
          keywords: [
            'hot streak',
            'winning streak',
            'lucky numbers',
            'due to win',
            'overdue',
            'chase losses',
            'double down',
            'all-in',
            'bet big',
            'max bet',
            'go for broke',
            'last chance',
            'limited time',
            'act now',
            'don\'t miss out',
            'exclusive offer',
            'VIP treatment',
            'high roller'
          ],
          message: 'These terms may encourage excessive gambling and should be used carefully with appropriate disclaimers.'
        },

        // Addiction-triggering terms
        addiction_triggers: {
          level: 'high',
          keywords: [
            'addictive',
            'can\'t stop',
            'one more bet',
            'just one more',
            'keep playing',
            'play until you win',
            'don\'t give up',
            'persistence pays',
            'keep trying',
            'never quit',
            'play through',
            'chase the win',
            'recover losses',
            'win back',
            'make up for',
            'compensate'
          ],
          message: 'These terms may trigger addictive behaviors and should be avoided entirely.'
        },

        // Misleading probability terms
        probability_misleading: {
          level: 'medium',
          keywords: [
            'due',
            'overdue',
            'hot machine',
            'cold machine',
            'lucky machine',
            'pattern',
            'system',
            'trend',
            'streak continues',
            'law of averages',
            'gambler\'s fallacy',
            'past results predict',
            'based on history',
            'statistical certainty',
            'mathematical guarantee'
          ],
          message: 'These terms misrepresent how probability works in gambling and should be corrected or avoided.'
        },

        // Financial pressure terms
        financial_pressure: {
          level: 'high',
          keywords: [
            'bet your savings',
            'mortgage the house',
            'borrow to bet',
            'credit card gambling',
            'loan for gambling',
            'bet everything',
            'all your money',
            'life savings',
            'retirement fund',
            'emergency fund',
            'kids\' college fund',
            'sell belongings',
            'pawn items',
            'desperate measures'
          ],
          message: 'These terms encourage dangerous financial behavior and must be avoided.'
        },

        // Age-inappropriate terms
        age_inappropriate: {
          level: 'high',
          keywords: [
            'kids love',
            'family fun',
            'children enjoy',
            'teen favorite',
            'young players',
            'student special',
            'pocket money',
            'allowance betting',
            'school break',
            'after school',
            'weekend fun for all',
            'family activity'
          ],
          message: 'These terms inappropriately target minors and violate age restrictions.'
        }
      },

      'zh': { // Chinese (中文)
        predatory: {
          level: 'high',
          keywords: [
            '保证获胜',
            '保证盈利',
            '必胜',
            '轻松赚钱',
            '快速致富',
            '万无一失的系统',
            '绝不会输',
            '总是赢',
            '无风险',
            '保证回报',
            '瞬间致富',
            '秘密系统',
            '内幕消息',
            '不可能输',
            '万无一失',
            '完美策略',
            '无限获胜',
            '保证成功'
          ],
          message: '这些词汇做出了不现实的承诺，在博彩内容中应该避免使用。'
        },

        // 中等风险的促销术语
        promotional: {
          level: 'medium',
          keywords: [
            '连胜期',
            '幸运数字',
            '该中奖了',
            '过期未开',
            '追损',
            '加倍下注',
            '全押',
            '大额投注',
            '最大投注',
            '大赌一把',
            '孤注一掷',
            '最后一搏',
            '做梦都想赢',
            '今晚就是你的',
            '千载难逢',
            '机不可失',
            '热门号码',
            '冷门反弹'
          ],
          message: '这些术语可能鼓励过度赌博，应谨慎使用并配以适当的免责声明。'
        },

        // 成瘾触发词
        addiction_triggers: {
          level: 'high',
          keywords: [
            '上瘾',
            '停不下来',
            '再来一把',
            '就再一次',
            '继续玩',
            '玩到赢为止',
            '不要放弃',
            '坚持就是胜利',
            '继续尝试',
            '永不放弃',
            '玩到最后',
            '追求胜利',
            '回本',
            '赢回来',
            '补偿',
            '翻本'
          ],
          message: '这些术语可能触发成瘾行为，必须完全避免。'
        },

        // 误导性概率声明
        probability_misleading: {
          level: 'medium',
          keywords: [
            '应该',
            '过期',
            '热门机器',
            '冷门机器',
            '幸运机器',
            '规律',
            '系统',
            '趋势',
            '连胜继续',
            '平均法则',
            '赌徒谬误',
            '过往结果预测',
            '基于历史',
            '统计确定性',
            '数学保证'
          ],
          message: '这些术语误解了博彩中概率的工作原理，应该更正或避免。'
        },

        // 财务压力术语
        financial_pressure: {
          level: 'high',
          keywords: [
            '赌上积蓄',
            '抵押房屋',
            '借钱赌博',
            '信用卡赌博',
            '贷款赌博',
            '押上一切',
            '全部资金',
            '毕生积蓄',
            '退休基金',
            '应急基金',
            '孩子教育基金',
            '卖掉家产',
            '典当物品',
            '铤而走险'
          ],
          message: '这些术语鼓励危险的财务行为，必须避免。'
        },

        // 年龄不当术语
        age_inappropriate: {
          level: 'high',
          keywords: [
            '孩子喜欢',
            '家庭娱乐',
            '儿童享受',
            '青少年最爱',
            '年轻玩家',
            '学生特价',
            '零花钱',
            '压岁钱投注',
            '学校假期',
            '放学后',
            '全家周末娱乐',
            '家庭活动'
          ],
          message: '这些术语不当地针对未成年人，违反年龄限制规定。'
        }
      },

      'es': { // Spanish (Español)
        predatory: {
          level: 'high',
          keywords: [
            'ganancia garantizada',
            'beneficio garantizado',
            'victoria segura',
            'dinero fácil',
            'hacerse rico rápido',
            'sistema infalible',
            'nunca perder',
            'siempre ganar',
            'sin riesgo',
            'retornos garantizados',
            'riqueza instantánea',
            'sistema secreto',
            'consejos internos',
            'no se puede perder',
            'seguro',
            'estrategia a prueba de balas',
            'ganancias ilimitadas',
            'éxito garantizado'
          ],
          message: 'Estos términos hacen promesas poco realistas y deben evitarse en el contenido de juegos.'
        },

        promotional: {
          level: 'medium',
          keywords: [
            'racha caliente',
            'racha ganadora',
            'números de la suerte',
            'debido a ganar',
            'atrasado',
            'perseguir pérdidas',
            'doblar apuesta',
            'todo incluido',
            'apostar grande',
            'apuesta máxima',
            'ir a por todas',
            'última oportunidad',
            'tiempo limitado',
            'actúa ahora',
            'no te lo pierdas',
            'oferta exclusiva',
            'trato VIP',
            'gran apostador'
          ],
          message: 'Estos términos pueden fomentar el juego excesivo y deben usarse con cuidado con las exenciones apropiadas.'
        },

        addiction_triggers: {
          level: 'high',
          keywords: [
            'adictivo',
            'no puedo parar',
            'una apuesta más',
            'solo una más',
            'sigue jugando',
            'juega hasta ganar',
            'no te rindas',
            'la persistencia paga',
            'sigue intentando',
            'nunca te rindas',
            'juega hasta el final',
            'persigue la victoria',
            'recuperar pérdidas',
            'ganar de vuelta',
            'compensar',
            'recuperar'
          ],
          message: 'Estos términos pueden desencadenar comportamientos adictivos y deben evitarse por completo.'
        },

        probability_misleading: {
          level: 'medium',
          keywords: [
            'debido',
            'atrasado',
            'máquina caliente',
            'máquina fría',
            'máquina de la suerte',
            'patrón',
            'sistema',
            'tendencia',
            'la racha continúa',
            'ley de promedios',
            'falacia del jugador',
            'resultados pasados predicen',
            'basado en historial',
            'certeza estadística',
            'garantía matemática'
          ],
          message: 'Estos términos tergiversan cómo funciona la probabilidad en los juegos y deben corregirse o evitarse.'
        },

        financial_pressure: {
          level: 'high',
          keywords: [
            'apuesta tus ahorros',
            'hipoteca la casa',
            'pide prestado para apostar',
            'juego con tarjeta de crédito',
            'préstamo para jugar',
            'apuesta todo',
            'todo tu dinero',
            'ahorros de toda la vida',
            'fondo de jubilación',
            'fondo de emergencia',
            'fondo universitario de los niños',
            'vender pertenencias',
            'empeñar artículos',
            'medidas desesperadas'
          ],
          message: 'Estos términos fomentan comportamientos financieros peligrosos y deben evitarse.'
        },

        age_inappropriate: {
          level: 'high',
          keywords: [
            'a los niños les encanta',
            'diversión familiar',
            'los niños disfrutan',
            'favorito de adolescentes',
            'jugadores jóvenes',
            'especial para estudiantes',
            'dinero de bolsillo',
            'apuestas con mesada',
            'vacaciones escolares',
            'después de la escuela',
            'diversión de fin de semana para todos',
            'actividad familiar'
          ],
          message: 'Estos términos se dirigen inapropiadamente a menores y violan las restricciones de edad.'
        }
      },

      'de': { // German (Deutsch)
        predatory: {
          level: 'high',
          keywords: [
            'garantierter gewinn',
            'garantierter profit',
            'sicherer gewinn',
            'leichtes geld',
            'schnell reich werden',
            'narrensicheres system',
            'nie verlieren',
            'immer gewinnen',
            'risikofrei',
            'garantierte renditen',
            'sofortiger reichtum',
            'geheimes system',
            'insider-tipps',
            'kann nicht verlieren',
            'todsicher',
            'kugelsichere strategie',
            'unbegrenzte gewinne',
            'garantierter erfolg'
          ],
          message: 'Diese Begriffe machen unrealistische Versprechungen und sollten in Glücksspielinhalten vermieden werden.'
        },

        promotional: {
          level: 'medium',
          keywords: [
            'glückssträhne',
            'gewinnsträhne',
            'glückszahlen',
            'fällig zu gewinnen',
            'überfällig',
            'verluste jagen',
            'verdoppeln',
            'all-in',
            'groß setzen',
            'maximaler einsatz',
            'alles oder nichts',
            'letzte chance',
            'begrenzte zeit',
            'jetzt handeln',
            'nicht verpassen',
            'exklusives angebot',
            'vip-behandlung',
            'high roller'
          ],
          message: 'Diese Begriffe können exzessives Glücksspiel fördern und sollten vorsichtig mit entsprechenden Haftungsausschlüssen verwendet werden.'
        },

        addiction_triggers: {
          level: 'high',
          keywords: [
            'süchtig machend',
            'kann nicht aufhören',
            'noch eine wette',
            'nur noch eine',
            'weiterspielen',
            'spielen bis du gewinnst',
            'gib nicht auf',
            'beharrlichkeit zahlt sich aus',
            'weiter versuchen',
            'niemals aufgeben',
            'durchspielen',
            'den gewinn jagen',
            'verluste zurückgewinnen',
            'zurückgewinnen',
            'ausgleichen',
            'kompensieren'
          ],
          message: 'Diese Begriffe können süchtiges Verhalten auslösen und sollten vollständig vermieden werden.'
        },

        probability_misleading: {
          level: 'medium',
          keywords: [
            'fällig',
            'überfällig',
            'heiße maschine',
            'kalte maschine',
            'glücksmaschine',
            'muster',
            'system',
            'trend',
            'strähne setzt sich fort',
            'gesetz der durchschnitte',
            'spielerfehlschluss',
            'vergangene ergebnisse sagen voraus',
            'basierend auf geschichte',
            'statistische gewissheit',
            'mathematische garantie'
          ],
          message: 'Diese Begriffe stellen falsch dar, wie Wahrscheinlichkeit beim Glücksspiel funktioniert und sollten korrigiert oder vermieden werden.'
        },

        financial_pressure: {
          level: 'high',
          keywords: [
            'setze deine ersparnisse',
            'verpfände das haus',
            'leihe zum wetten',
            'kreditkarten-glücksspiel',
            'kredit zum spielen',
            'setze alles',
            'all dein geld',
            'lebensersparnisse',
            'rentenfonds',
            'notfallfonds',
            'college-fonds der kinder',
            'besitztümer verkaufen',
            'gegenstände verpfänden',
            'verzweifelte maßnahmen'
          ],
          message: 'Diese Begriffe fördern gefährliches finanzielles Verhalten und müssen vermieden werden.'
        },

        age_inappropriate: {
          level: 'high',
          keywords: [
            'kinder lieben',
            'familienspaß',
            'kinder genießen',
            'teenager-favorit',
            'junge spieler',
            'studenten-spezial',
            'taschengeld',
            'taschengeld-wetten',
            'schulferien',
            'nach der schule',
            'wochenend-spaß für alle',
            'familienaktivität'
          ],
          message: 'Diese Begriffe zielen unangemessen auf Minderjährige ab und verletzen Altersbeschränkungen.'
        }
      },

      'fr': { // French (Français)
        predatory: {
          level: 'high',
          keywords: [
            'gain garanti',
            'profit garanti',
            'victoire sûre',
            'argent facile',
            'devenir riche rapidement',
            'système infaillible',
            'ne jamais perdre',
            'toujours gagner',
            'sans risque',
            'retours garantis',
            'richesse instantanée',
            'système secret',
            'conseils d\'initiés',
            'impossible de perdre',
            'sûr à 100%',
            'stratégie à toute épreuve',
            'gains illimités',
            'succès garanti'
          ],
          message: 'Ces termes font des promesses irréalistes et doivent être évités dans le contenu de jeu.'
        },

        promotional: {
          level: 'medium',
          keywords: [
            'série de chance',
            'série gagnante',
            'numéros chanceux',
            'dû pour gagner',
            'en retard',
            'poursuivre les pertes',
            'doubler la mise',
            'tout miser',
            'miser gros',
            'mise maximale',
            'tout ou rien',
            'dernière chance',
            'temps limité',
            'agir maintenant',
            'ne pas manquer',
            'offre exclusive',
            'traitement vip',
            'gros joueur'
          ],
          message: 'Ces termes peuvent encourager le jeu excessif et doivent être utilisés avec prudence avec des avertissements appropriés.'
        },

        addiction_triggers: {
          level: 'high',
          keywords: [
            'addictif',
            'ne peut pas s\'arrêter',
            'encore un pari',
            'juste un de plus',
            'continuer à jouer',
            'jouer jusqu\'à gagner',
            'n\'abandonne pas',
            'la persévérance paie',
            'continuer d\'essayer',
            'ne jamais abandonner',
            'jouer jusqu\'au bout',
            'poursuivre la victoire',
            'récupérer les pertes',
            'regagner',
            'compenser',
            'rattraper'
          ],
          message: 'Ces termes peuvent déclencher des comportements addictifs et doivent être complètement évités.'
        },

        probability_misleading: {
          level: 'medium',
          keywords: [
            'dû',
            'en retard',
            'machine chaude',
            'machine froide',
            'machine chanceuse',
            'modèle',
            'système',
            'tendance',
            'la série continue',
            'loi des moyennes',
            'sophisme du joueur',
            'les résultats passés prédisent',
            'basé sur l\'historique',
            'certitude statistique',
            'garantie mathématique'
          ],
          message: 'Ces termes déforment le fonctionnement des probabilités dans les jeux et doivent être corrigés ou évités.'
        },

        financial_pressure: {
          level: 'high',
          keywords: [
            'parie tes économies',
            'hypothèque la maison',
            'emprunte pour parier',
            'jeu avec carte de crédit',
            'prêt pour jouer',
            'parie tout',
            'tout ton argent',
            'économies de toute une vie',
            'fonds de retraite',
            'fonds d\'urgence',
            'fonds universitaire des enfants',
            'vendre ses biens',
            'mettre en gage',
            'mesures désespérées'
          ],
          message: 'Ces termes encouragent des comportements financiers dangereux et doivent être évités.'
        },

        age_inappropriate: {
          level: 'high',
          keywords: [
            'les enfants adorent',
            'amusement familial',
            'les enfants apprécient',
            'favori des ados',
            'jeunes joueurs',
            'spécial étudiant',
            'argent de poche',
            'paris avec l\'argent de poche',
            'vacances scolaires',
            'après l\'école',
            'amusement de week-end pour tous',
            'activité familiale'
          ],
          message: 'Ces termes ciblent de manière inappropriée les mineurs et violent les restrictions d\'âge.'
        }
      },

      'pt': { // Portuguese (Português)
        predatory: {
          level: 'high',
          keywords: [
            'ganho garantido',
            'lucro garantido',
            'vitória certa',
            'dinheiro fácil',
            'ficar rico rapidamente',
            'sistema infalível',
            'nunca perder',
            'sempre ganhar',
            'sem risco',
            'retornos garantidos',
            'riqueza instantânea',
            'sistema secreto',
            'dicas privilegiadas',
            'impossível perder',
            'certeza absoluta',
            'estratégia à prova de falhas',
            'ganhos ilimitados',
            'sucesso garantido'
          ],
          message: 'Estes termos fazem promessas irreais e devem ser evitados no conteúdo de jogos.'
        },

        promotional: {
          level: 'medium',
          keywords: [
            'sequência de sorte',
            'sequência vencedora',
            'números da sorte',
            'devido para ganhar',
            'atrasado',
            'perseguir perdas',
            'dobrar a aposta',
            'apostar tudo',
            'apostar alto',
            'aposta máxima',
            'tudo ou nada',
            'última chance',
            'tempo limitado',
            'agir agora',
            'não perca',
            'oferta exclusiva',
            'tratamento vip',
            'apostador alto'
          ],
          message: 'Estes termos podem encorajar jogos excessivos e devem ser usados com cuidado com avisos apropriados.'
        },

        addiction_triggers: {
          level: 'high',
          keywords: [
            'viciante',
            'não consegue parar',
            'mais uma aposta',
            'só mais uma',
            'continuar jogando',
            'jogar até ganhar',
            'não desista',
            'persistência compensa',
            'continuar tentando',
            'nunca desistir',
            'jogar até o fim',
            'perseguir a vitória',
            'recuperar perdas',
            'ganhar de volta',
            'compensar',
            'recuperar'
          ],
          message: 'Estes termos podem desencadear comportamentos viciantes e devem ser completamente evitados.'
        },

        probability_misleading: {
          level: 'medium',
          keywords: [
            'devido',
            'atrasado',
            'máquina quente',
            'máquina fria',
            'máquina da sorte',
            'padrão',
            'sistema',
            'tendência',
            'sequência continua',
            'lei das médias',
            'falácia do jogador',
            'resultados passados preveem',
            'baseado no histórico',
            'certeza estatística',
            'garantia matemática'
          ],
          message: 'Estes termos distorcem como a probabilidade funciona nos jogos e devem ser corrigidos ou evitados.'
        },

        financial_pressure: {
          level: 'high',
          keywords: [
            'aposte suas economias',
            'hipoteque a casa',
            'empreste para apostar',
            'jogo com cartão de crédito',
            'empréstimo para jogar',
            'aposte tudo',
            'todo seu dinheiro',
            'economias de toda vida',
            'fundo de aposentadoria',
            'fundo de emergência',
            'fundo universitário dos filhos',
            'vender pertences',
            'penhorar itens',
            'medidas desesperadas'
          ],
          message: 'Estes termos encorajam comportamento financeiro perigoso e devem ser evitados.'
        },

        age_inappropriate: {
          level: 'high',
          keywords: [
            'crianças adoram',
            'diversão familiar',
            'crianças gostam',
            'favorito dos adolescentes',
            'jogadores jovens',
            'especial para estudantes',
            'dinheiro do bolso',
            'apostas com mesada',
            'férias escolares',
            'depois da escola',
            'diversão de fim de semana para todos',
            'atividade familiar'
          ],
          message: 'Estes termos visam inadequadamente menores e violam restrições de idade.'
        }
      },

      'it': { // Italian (Italiano)
        predatory: {
          level: 'high',
          keywords: [
            'vincita garantita',
            'profitto garantito',
            'vittoria sicura',
            'soldi facili',
            'diventare ricchi velocemente',
            'sistema infallibile',
            'mai perdere',
            'sempre vincere',
            'senza rischi',
            'ritorni garantiti',
            'ricchezza istantanea',
            'sistema segreto',
            'consigli privilegiati',
            'impossibile perdere',
            'sicuro al 100%',
            'strategia a prova di bomba',
            'vincite illimitate',
            'successo garantito'
          ],
          message: 'Questi termini fanno promesse irrealistiche e dovrebbero essere evitati nei contenuti di gioco.'
        },

        promotional: {
          level: 'medium',
          keywords: [
            'striscia fortunata',
            'striscia vincente',
            'numeri fortunati',
            'dovuto per vincere',
            'in ritardo',
            'inseguire le perdite',
            'raddoppiare la puntata',
            'puntare tutto',
            'puntare grosso',
            'puntata massima',
            'tutto o niente',
            'ultima possibilità',
            'tempo limitato',
            'agire ora',
            'non perdere',
            'offerta esclusiva',
            'trattamento vip',
            'giocatore d\'alto livello'
          ],
          message: 'Questi termini possono incoraggiare il gioco eccessivo e dovrebbero essere usati con cautela con avvertimenti appropriati.'
        },

        addiction_triggers: {
          level: 'high',
          keywords: [
            'che crea dipendenza',
            'non riesco a smettere',
            'ancora una scommessa',
            'solo un\'altra',
            'continuare a giocare',
            'giocare fino a vincere',
            'non arrenderti',
            'la perseveranza paga',
            'continuare a provare',
            'mai arrendersi',
            'giocare fino alla fine',
            'inseguire la vittoria',
            'recuperare le perdite',
            'riconquistare',
            'compensare',
            'recuperare'
          ],
          message: 'Questi termini possono scatenare comportamenti di dipendenza e dovrebbero essere completamente evitati.'
        },

        probability_misleading: {
          level: 'medium',
          keywords: [
            'dovuto',
            'in ritardo',
            'macchina calda',
            'macchina fredda',
            'macchina fortunata',
            'schema',
            'sistema',
            'tendenza',
            'la striscia continua',
            'legge delle medie',
            'fallacia del giocatore',
            'i risultati passati predicono',
            'basato sulla cronologia',
            'certezza statistica',
            'garanzia matematica'
          ],
          message: 'Questi termini distorcono come funziona la probabilità nei giochi e dovrebbero essere corretti o evitati.'
        },

        financial_pressure: {
          level: 'high',
          keywords: [
            'scommetti i tuoi risparmi',
            'ipoteca la casa',
            'prendi in prestito per scommettere',
            'gioco con carta di credito',
            'prestito per giocare',
            'scommetti tutto',
            'tutti i tuoi soldi',
            'risparmi di una vita',
            'fondo pensione',
            'fondo di emergenza',
            'fondo universitario dei bambini',
            'vendere i beni',
            'impegnare oggetti',
            'misure disperate'
          ],
          message: 'Questi termini incoraggiano comportamenti finanziari pericolosi e devono essere evitati.'
        },

        age_inappropriate: {
          level: 'high',
          keywords: [
            'i bambini adorano',
            'divertimento familiare',
            'i bambini si divertono',
            'preferito degli adolescenti',
            'giovani giocatori',
            'speciale studenti',
            'paghetta',
            'scommesse con la paghetta',
            'vacanze scolastiche',
            'dopo la scuola',
            'divertimento del weekend per tutti',
            'attività familiare'
          ],
          message: 'Questi termini prendono di mira inappropriatamente i minori e violano le restrizioni di età.'
        }
      },

      'ja': { // Japanese (日本語)
        predatory: {
          level: 'high',
          keywords: [
            '絶対勝てる',
            '必ず儲かる',
            '確実勝利',
            '簡単にお金',
            '一攫千金',
            '完璧システム',
            '絶対負けない',
            '必ず勝つ',
            'リスクゼロ',
            '確実利益',
            '瞬間富豪',
            '秘密システム',
            '内部情報',
            '負けるはずがない',
            '絶対確実',
            '無敵戦略',
            '無限勝利',
            '成功保証'
          ],
          message: 'これらの用語は非現実的な約束をしており、ギャンブルコンテンツでは避けるべきです。'
        },

        promotional: {
          level: 'medium',
          keywords: [
            '連勝中',
            '勝利の流れ',
            'ラッキーナンバー',
            'そろそろ当たる',
            '当たり時',
            '損失追求',
            'ダブルダウン',
            'オールイン',
            '大勝負',
            '最大ベット',
            '全てか無か',
            '最後のチャンス',
            '期間限定',
            '今すぐ行動',
            '見逃すな',
            '特別オファー',
            'VIP待遇',
            'ハイローラー'
          ],
          message: 'これらの用語は過度なギャンブルを促す可能性があり、適切な警告とともに慎重に使用する必要があります。'
        },

        addiction_triggers: {
          level: 'high',
          keywords: [
            '中毒性',
            'やめられない',
            'もう一度賭ける',
            'もう一回だけ',
            'プレイを続ける',
            '勝つまでプレイ',
            '諦めるな',
            '継続は力なり',
            '挑戦し続ける',
            '絶対諦めない',
            'プレイし続ける',
            '勝利を追求',
            '損失回復',
            '取り戻す',
            '補償する',
            '埋め合わせ'
          ],
          message: 'これらの用語は中毒的行動を引き起こす可能性があり、完全に避けるべきです。'
        },

        probability_misleading: {
          level: 'medium',
          keywords: [
            '当たるべき',
            '遅れている',
            '熱い台',
            '冷たい台',
            'ラッキー台',
            'パターン',
            'システム',
            'トレンド',
            '連続は続く',
            '平均の法則',
            'ギャンブラーの誤謬',
            '過去が予測',
            '履歴に基づく',
            '統計的確実性',
            '数学的保証'
          ],
          message: 'これらの用語はギャンブルにおける確率の仕組みを誤って表現しており、修正または回避する必要があります。'
        },

        financial_pressure: {
          level: 'high',
          keywords: [
            '貯金を投入',
            '家を担保に',
            '借金してベット',
            'クレジットカードでギャンブル',
            'ギャンブル用ローン',
            '全て投入',
            '全財産',
            '生涯貯蓄',
            '年金基金',
            '緊急資金',
            '子供の学費',
            '持ち物を売る',
            '物を質に入れる',
            '最後の手段'
          ],
          message: 'これらの用語は危険な金銭行動を促し、避けるべきです。'
        },

        age_inappropriate: {
          level: 'high',
          keywords: [
            '子供が大好き',
            '家族の楽しみ',
            '子供が楽しむ',
            '学生の人気',
            '若いプレイヤー',
            '学生特別',
            'お小遣い',
            'お小遣いで賭ける',
            '夏休み',
            '放課後',
            '週末みんなで楽しむ',
            '家族活動'
          ],
          message: 'これらの用語は未成年者を不適切に対象とし、年齢制限に違反します。'
        }
      }
    };
  }

  // Analyze text for risk keywords with multilingual support
  async analyzeText(text, contentType = '', jurisdiction = 'international', languageCode = 'en') {
    if (!text || typeof text !== 'string') {
      return {
        riskLevel: 'low',
        detectedRisks: [],
        recommendations: [],
        isCompliant: true,
        keywordAnalysis: { detectedRisks: [], isCompliant: true },
        aiAnalysis: { detectedViolations: [], isCompliant: true }
      };
    }

    // Step 1: Keyword-based analysis (fast, deterministic)
    const keywordAnalysis = this.analyzeKeywords(text, languageCode, contentType, jurisdiction);

    // Step 2: AI-powered analysis (context-aware, language-agnostic)
    const aiAnalysis = await this.analyzeTextWithAI(text, languageCode);

    // Step 3: Combine results
    const combinedAnalysis = this.combineAnalyses(keywordAnalysis, aiAnalysis, contentType, jurisdiction);

    return combinedAnalysis;
  }

  // Traditional keyword-based analysis
  analyzeKeywords(text, languageCode = 'en', contentType = '', jurisdiction = 'international') {
    const riskKeywordsForLanguage = this.riskKeywords[languageCode] || this.riskKeywords['en'];
    const textLower = text.toLowerCase();
    const detectedRisks = [];
    let highestRiskLevel = 'low';

    // Check each category of risk keywords
    Object.entries(riskKeywordsForLanguage).forEach(([category, categoryData]) => {
      const { level, keywords, message } = categoryData;
      const foundKeywords = [];

      keywords.forEach(keyword => {
        if (textLower.includes(keyword.toLowerCase())) {
          foundKeywords.push(keyword);
        }
      });

      if (foundKeywords.length > 0) {
        detectedRisks.push({
          category,
          level,
          keywords: foundKeywords,
          message,
          count: foundKeywords.length,
          source: 'keyword_analysis'
        });

        // Update highest risk level
        if (level === 'high' || (level === 'medium' && highestRiskLevel === 'low')) {
          highestRiskLevel = level;
        }
      }
    });

    // Generate recommendations
    const recommendations = this.generateRecommendations(detectedRisks, contentType, jurisdiction);

    // Determine compliance status
    const isCompliant = highestRiskLevel !== 'high' &&
                       !detectedRisks.some(risk =>
                         risk.category === 'addiction_triggers' ||
                         risk.category === 'financial_pressure' ||
                         risk.category === 'age_inappropriate'
                       );

    return {
      riskLevel: highestRiskLevel,
      detectedRisks,
      recommendations,
      isCompliant,
      summary: this.generateSummary(detectedRisks, highestRiskLevel)
    };
  }

  // AI-powered risk analysis (language-agnostic)
  async analyzeTextWithAI(text, languageCode = 'en') {
    try {
      const languageNames = {
        'en': 'English',
        'zh': 'Chinese',
        'pt': 'Portuguese',
        'es': 'Spanish',
        'de': 'German',
        'fr': 'French',
        'it': 'Italian',
        'ja': 'Japanese'
      };

      const targetLanguage = languageNames[languageCode] || 'English';

      const prompt = `You are an AI Compliance Officer specializing in responsible iGaming content. Analyze the following text provided by the user. Your task is to identify if the text violates any of the following responsible gaming principles. The text is in ${targetLanguage}. Respond ONLY with a JSON object.

**CRITICAL SAFETY & RESPONSIBILITY DIRECTIVES:**

Principles to check for:
1. **Predatory Promises:** Does the text make unrealistic promises of guaranteed wins, easy money, or risk-free profit?
2. **Addiction Triggers:** Does the text use language that encourages chasing losses, playing beyond one's means, or suggests gambling is a way to solve problems?
3. **Financial Pressure:** Does the text suggest using essential funds (rent, savings) for gambling?
4. **Probability Misrepresentation:** Does the text inaccurately describe how odds or randomness work (e.g., Gambler's Fallacy)?
5. **Age-Inappropriate Content:** Does the text use language that could appeal to minors?

**Text for Analysis:**
"""
${text}
"""

**Your JSON Output format:**
{
  "isCompliant": boolean,
  "riskLevel": "none" | "low" | "medium" | "high",
  "detectedViolations": [
    {
      "principleViolated": "Predatory Promises" | "Addiction Triggers" | "Financial Pressure" | "Probability Misrepresentation" | "Age-Inappropriate Content",
      "violatingPhrase": "[The exact phrase from the text that violates the principle]",
      "explanation": "[A brief explanation of why this phrase is problematic]",
      "severity": "low" | "medium" | "high"
    }
  ]
}`;

      const response = await geminiService.generateContent(prompt);

      // Parse the JSON response
      let aiResult;
      try {
        // Extract JSON from response if it's wrapped in markdown
        const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/) || response.match(/\{[\s\S]*\}/);
        const jsonString = jsonMatch ? (jsonMatch[1] || jsonMatch[0]) : response;
        aiResult = JSON.parse(jsonString);
      } catch (parseError) {
        console.warn('Failed to parse AI analysis response:', parseError);
        return {
          isCompliant: true,
          riskLevel: 'none',
          detectedViolations: [],
          error: 'Failed to parse AI response'
        };
      }

      return aiResult;
    } catch (error) {
      console.error('AI analysis failed:', error);
      return {
        isCompliant: true,
        riskLevel: 'none',
        detectedViolations: [],
        error: 'AI analysis unavailable'
      };
    }
  }

  // Combine keyword and AI analyses
  combineAnalyses(keywordAnalysis, aiAnalysis, contentType, jurisdiction) {
    const combinedRisks = [...keywordAnalysis.detectedRisks];

    // Convert AI violations to risk format
    if (aiAnalysis.detectedViolations && aiAnalysis.detectedViolations.length > 0) {
      aiAnalysis.detectedViolations.forEach(violation => {
        const categoryMap = {
          'Predatory Promises': 'predatory',
          'Addiction Triggers': 'addiction_triggers',
          'Financial Pressure': 'financial_pressure',
          'Probability Misrepresentation': 'probability_misleading',
          'Age-Inappropriate Content': 'age_inappropriate'
        };

        const category = categoryMap[violation.principleViolated] || 'other';

        combinedRisks.push({
          category,
          level: violation.severity || 'medium',
          keywords: [violation.violatingPhrase],
          message: violation.explanation,
          count: 1,
          source: 'ai_analysis'
        });
      });
    }

    // Determine overall risk level
    const keywordRiskLevel = keywordAnalysis.riskLevel;
    const aiRiskLevel = aiAnalysis.riskLevel || 'none';

    const riskLevels = ['none', 'low', 'medium', 'high'];
    const keywordIndex = riskLevels.indexOf(keywordRiskLevel);
    const aiIndex = riskLevels.indexOf(aiRiskLevel);
    const highestRiskLevel = riskLevels[Math.max(keywordIndex, aiIndex)];

    // Determine compliance status
    const keywordCompliant = keywordAnalysis.isCompliant;
    const aiCompliant = aiAnalysis.isCompliant;
    const isCompliant = keywordCompliant && aiCompliant;

    // Generate combined recommendations
    const recommendations = this.generateRecommendations(combinedRisks, contentType, jurisdiction);

    return {
      riskLevel: highestRiskLevel,
      detectedRisks: combinedRisks,
      recommendations,
      isCompliant,
      summary: this.generateSummary(combinedRisks, highestRiskLevel),
      keywordAnalysis: {
        detectedRisks: keywordAnalysis.detectedRisks,
        isCompliant: keywordAnalysis.isCompliant,
        riskLevel: keywordAnalysis.riskLevel
      },
      aiAnalysis: {
        detectedViolations: aiAnalysis.detectedViolations || [],
        isCompliant: aiAnalysis.isCompliant,
        riskLevel: aiAnalysis.riskLevel || 'none',
        error: aiAnalysis.error
      }
    };
  }

  // Generate recommendations based on detected risks
  generateRecommendations(detectedRisks, contentType, jurisdiction) {
    const recommendations = [];

    detectedRisks.forEach(risk => {
      switch (risk.category) {
        case 'predatory':
          recommendations.push({
            type: 'replace',
            priority: 'high',
            message: 'Replace predatory terms with educational language that explains realistic outcomes.',
            examples: [
              'Instead of "guaranteed win" → "learn strategies that may improve your understanding"',
              'Instead of "easy money" → "entertainment value with potential rewards"',
              'Instead of "can\'t lose" → "understand the risks and play responsibly"'
            ]
          });
          break;

        case 'addiction_triggers':
          recommendations.push({
            type: 'remove',
            priority: 'critical',
            message: 'Remove all addiction-triggering language immediately. These terms violate responsible gaming principles.',
            examples: [
              'Remove phrases encouraging continued play after losses',
              'Avoid language that suggests persistence will lead to wins',
              'Replace with responsible gaming reminders'
            ]
          });
          break;

        case 'financial_pressure':
          recommendations.push({
            type: 'remove',
            priority: 'critical',
            message: 'Remove all references to using essential funds for gambling. Add bankroll management guidance.',
            examples: [
              'Replace with "only bet what you can afford to lose"',
              'Add information about setting gambling budgets',
              'Include responsible gambling resources'
            ]
          });
          break;

        case 'age_inappropriate':
          recommendations.push({
            type: 'remove',
            priority: 'critical',
            message: 'Remove all content that could appeal to minors. Ensure 18+ messaging is prominent.',
            examples: [
              'Replace family-oriented language with adult-focused content',
              'Add clear age verification requirements',
              'Include "Must be 18 or older" disclaimers'
            ]
          });
          break;

        case 'promotional':
          recommendations.push({
            type: 'modify',
            priority: 'medium',
            message: 'Balance promotional language with responsible gaming disclaimers.',
            examples: [
              'Add "Terms and conditions apply" to promotional content',
              'Include responsible gaming reminders',
              'Clarify that gambling involves risk of loss'
            ]
          });
          break;

        case 'probability_misleading':
          recommendations.push({
            type: 'correct',
            priority: 'high',
            message: 'Correct misleading probability information with accurate explanations.',
            examples: [
              'Explain that each game round is independent',
              'Clarify that past results don\'t affect future outcomes',
              'Include information about house edge and randomness'
            ]
          });
          break;
      }
    });

    // Add jurisdiction-specific recommendations
    if (jurisdiction !== 'international') {
      recommendations.push({
        type: 'compliance',
        priority: 'high',
        message: `Ensure content meets specific regulatory requirements for ${jurisdiction}.`,
        examples: [
          'Include jurisdiction-specific disclaimers',
          'Verify compliance with local advertising standards',
          'Add required responsible gaming resources for the region'
        ]
      });
    }

    // Add content-type specific recommendations
    if (contentType === 'game_guide') {
      recommendations.push({
        type: 'educational',
        priority: 'medium',
        message: 'Emphasize educational value over winning potential.',
        examples: [
          'Focus on understanding game mechanics',
          'Explain probability and house edge',
          'Include responsible gaming strategies'
        ]
      });
    }

    return recommendations;
  }

  // Generate summary of risk analysis
  generateSummary(detectedRisks, riskLevel) {
    if (detectedRisks.length === 0) {
      return {
        status: 'compliant',
        message: 'No high-risk keywords detected. Content appears to follow responsible gaming guidelines.',
        action: 'proceed'
      };
    }

    const totalKeywords = detectedRisks.reduce((sum, risk) => sum + risk.count, 0);
    const highRiskCategories = detectedRisks.filter(risk => risk.level === 'high').length;

    if (riskLevel === 'high' || highRiskCategories > 0) {
      return {
        status: 'non_compliant',
        message: `Content contains ${totalKeywords} high-risk keywords across ${highRiskCategories} critical categories. Immediate revision required.`,
        action: 'revise_immediately'
      };
    } else {
      return {
        status: 'needs_review',
        message: `Content contains ${totalKeywords} medium-risk keywords. Review and add appropriate disclaimers.`,
        action: 'review_and_modify'
      };
    }
  }

  // Check specific keywords (synchronous keyword-only analysis)
  checkKeywords(keywords, languageCode = 'en') {
    if (!Array.isArray(keywords)) {
      return { safe: true, risks: [] };
    }

    const risks = [];

    keywords.forEach(keyword => {
      const analysis = this.analyzeKeywords(keyword, languageCode);
      if (analysis.detectedRisks.length > 0) {
        risks.push({
          keyword,
          risks: analysis.detectedRisks
        });
      }
    });

    return {
      safe: risks.length === 0,
      risks,
      recommendation: risks.length > 0 ?
        'Consider replacing flagged keywords with safer alternatives' :
        'Keywords appear safe for gambling content'
    };
  }

  // Async version for full analysis including AI
  async checkKeywordsWithAI(keywords, languageCode = 'en', contentType = '') {
    if (!Array.isArray(keywords)) {
      return { safe: true, risks: [] };
    }

    const risks = [];

    for (const keyword of keywords) {
      const analysis = await this.analyzeText(keyword, contentType, 'international', languageCode);
      if (analysis.detectedRisks.length > 0) {
        risks.push({
          keyword,
          risks: analysis.detectedRisks,
          keywordAnalysis: analysis.keywordAnalysis,
          aiAnalysis: analysis.aiAnalysis
        });
      }
    }

    return {
      safe: risks.length === 0,
      risks,
      recommendation: risks.length > 0 ?
        'Consider replacing flagged keywords with safer alternatives' :
        'Keywords appear safe for gambling content'
    };
  }

  // Get safe alternatives for risky terms (multilingual)
  getSafeAlternatives(riskyTerm, languageCode = 'en') {
    const alternatives = {
      'en': {
        'guaranteed win': ['potential success', 'possible outcome', 'learning opportunity'],
        'easy money': ['entertainment value', 'gaming experience', 'potential rewards'],
        'sure win': ['strategic play', 'informed decisions', 'skillful approach'],
        'can\'t lose': ['understand the game', 'play responsibly', 'manage your bankroll'],
        'get rich quick': ['entertainment experience', 'gaming enjoyment', 'responsible play'],
        'never lose': ['learn strategies', 'improve skills', 'play smart'],
        'always win': ['enhance understanding', 'develop skills', 'enjoy responsibly'],
        'risk-free': ['understand risks', 'informed play', 'responsible gaming'],
        'hot streak': ['recent results', 'current session', 'temporary variance'],
        'due to win': ['random outcomes', 'independent results', 'chance-based'],
        'chase losses': ['manage bankroll', 'set limits', 'play responsibly'],
        'bet big': ['consider your budget', 'play within limits', 'responsible wagering']
      },
      'zh': {
        '保证获胜': ['潜在成功', '可能结果', '学习机会'],
        '轻松赚钱': ['娱乐价值', '游戏体验', '潜在奖励'],
        '必胜': ['策略游戏', '明智决策', '技巧方法'],
        '不可能输': ['了解游戏', '负责任游戏', '管理资金'],
        '快速致富': ['娱乐体验', '游戏乐趣', '负责任游戏'],
        '绝不会输': ['学习策略', '提高技能', '聪明游戏'],
        '总是赢': ['增强理解', '发展技能', '负责任享受'],
        '无风险': ['了解风险', '明智游戏', '负责任游戏'],
        '连胜期': ['近期结果', '当前游戏', '临时变化'],
        '该中奖了': ['随机结果', '独立结果', '基于概率'],
        '追损': ['管理资金', '设置限额', '负责任游戏'],
        '大额投注': ['考虑预算', '限额内游戏', '负责任投注']
      },
      'es': {
        'ganancia garantizada': ['éxito potencial', 'resultado posible', 'oportunidad de aprendizaje'],
        'dinero fácil': ['valor de entretenimiento', 'experiencia de juego', 'recompensas potenciales'],
        'victoria segura': ['juego estratégico', 'decisiones informadas', 'enfoque hábil'],
        'no se puede perder': ['entender el juego', 'jugar responsablemente', 'gestionar tu bankroll'],
        'hacerse rico rápido': ['experiencia de entretenimiento', 'disfrute del juego', 'juego responsable'],
        'nunca perder': ['aprender estrategias', 'mejorar habilidades', 'jugar inteligentemente'],
        'siempre ganar': ['mejorar comprensión', 'desarrollar habilidades', 'disfrutar responsablemente'],
        'sin riesgo': ['entender riesgos', 'juego informado', 'juego responsable'],
        'racha caliente': ['resultados recientes', 'sesión actual', 'varianza temporal'],
        'debido a ganar': ['resultados aleatorios', 'resultados independientes', 'basado en azar'],
        'perseguir pérdidas': ['gestionar bankroll', 'establecer límites', 'jugar responsablemente'],
        'apostar grande': ['considera tu presupuesto', 'juega dentro de límites', 'apuestas responsables']
      },
      'de': {
        'garantierter gewinn': ['potenzieller erfolg', 'mögliches ergebnis', 'lernmöglichkeit'],
        'leichtes geld': ['unterhaltungswert', 'spielerfahrung', 'potenzielle belohnungen'],
        'sicherer gewinn': ['strategisches spiel', 'informierte entscheidungen', 'geschickter ansatz'],
        'kann nicht verlieren': ['spiel verstehen', 'verantwortlich spielen', 'bankroll verwalten'],
        'schnell reich werden': ['unterhaltungserfahrung', 'spielvergnügen', 'verantwortliches spiel'],
        'nie verlieren': ['strategien lernen', 'fähigkeiten verbessern', 'klug spielen'],
        'immer gewinnen': ['verständnis verbessern', 'fähigkeiten entwickeln', 'verantwortlich genießen'],
        'risikofrei': ['risiken verstehen', 'informiertes spiel', 'verantwortliches spielen'],
        'glückssträhne': ['aktuelle ergebnisse', 'aktuelle sitzung', 'temporäre varianz'],
        'fällig zu gewinnen': ['zufällige ergebnisse', 'unabhängige ergebnisse', 'zufallsbasiert'],
        'verluste jagen': ['bankroll verwalten', 'grenzen setzen', 'verantwortlich spielen'],
        'groß setzen': ['budget berücksichtigen', 'innerhalb grenzen spielen', 'verantwortliche wetten']
      },
      'fr': {
        'gain garanti': ['succès potentiel', 'résultat possible', 'opportunité d\'apprentissage'],
        'argent facile': ['valeur de divertissement', 'expérience de jeu', 'récompenses potentielles'],
        'victoire sûre': ['jeu stratégique', 'décisions éclairées', 'approche habile'],
        'impossible de perdre': ['comprendre le jeu', 'jouer de manière responsable', 'gérer sa bankroll'],
        'devenir riche rapidement': ['expérience de divertissement', 'plaisir de jeu', 'jeu responsable'],
        'ne jamais perdre': ['apprendre les stratégies', 'améliorer les compétences', 'jouer intelligemment'],
        'toujours gagner': ['améliorer la compréhension', 'développer les compétences', 'profiter de manière responsable'],
        'sans risque': ['comprendre les risques', 'jeu éclairé', 'jeu responsable'],
        'série de chance': ['résultats récents', 'session actuelle', 'variance temporaire'],
        'dû pour gagner': ['résultats aléatoires', 'résultats indépendants', 'basé sur le hasard'],
        'poursuivre les pertes': ['gérer la bankroll', 'fixer des limites', 'jouer de manière responsable'],
        'miser gros': ['considérer votre budget', 'jouer dans les limites', 'paris responsables']
      },
      'pt': {
        'ganho garantido': ['sucesso potencial', 'resultado possível', 'oportunidade de aprendizado'],
        'dinheiro fácil': ['valor de entretenimento', 'experiência de jogo', 'recompensas potenciais'],
        'vitória certa': ['jogo estratégico', 'decisões informadas', 'abordagem habilidosa'],
        'impossível perder': ['entender o jogo', 'jogar responsavelmente', 'gerenciar sua banca'],
        'ficar rico rapidamente': ['experiência de entretenimento', 'prazer do jogo', 'jogo responsável'],
        'nunca perder': ['aprender estratégias', 'melhorar habilidades', 'jogar inteligentemente'],
        'sempre ganhar': ['melhorar compreensão', 'desenvolver habilidades', 'aproveitar responsavelmente'],
        'sem risco': ['entender riscos', 'jogo informado', 'jogo responsável'],
        'sequência de sorte': ['resultados recentes', 'sessão atual', 'variância temporária'],
        'devido para ganhar': ['resultados aleatórios', 'resultados independentes', 'baseado no acaso'],
        'perseguir perdas': ['gerenciar banca', 'definir limites', 'jogar responsavelmente'],
        'apostar alto': ['considerar seu orçamento', 'jogar dentro dos limites', 'apostas responsáveis']
      },
      'it': {
        'vincita garantita': ['successo potenziale', 'risultato possibile', 'opportunità di apprendimento'],
        'soldi facili': ['valore di intrattenimento', 'esperienza di gioco', 'ricompense potenziali'],
        'vittoria sicura': ['gioco strategico', 'decisioni informate', 'approccio abile'],
        'impossibile perdere': ['capire il gioco', 'giocare responsabilmente', 'gestire il bankroll'],
        'diventare ricchi velocemente': ['esperienza di intrattenimento', 'piacere del gioco', 'gioco responsabile'],
        'mai perdere': ['imparare strategie', 'migliorare le abilità', 'giocare intelligentemente'],
        'sempre vincere': ['migliorare la comprensione', 'sviluppare abilità', 'godere responsabilmente'],
        'senza rischi': ['capire i rischi', 'gioco informato', 'gioco responsabile'],
        'striscia fortunata': ['risultati recenti', 'sessione attuale', 'varianza temporanea'],
        'dovuto per vincere': ['risultati casuali', 'risultati indipendenti', 'basato sul caso'],
        'inseguire le perdite': ['gestire bankroll', 'impostare limiti', 'giocare responsabilmente'],
        'puntare grosso': ['considerare il budget', 'giocare entro i limiti', 'scommesse responsabili']
      },
      'ja': {
        '絶対勝てる': ['潜在的成功', '可能な結果', '学習機会'],
        '簡単にお金': ['エンターテイメント価値', 'ゲーム体験', '潜在的報酬'],
        '確実勝利': ['戦略的ゲーム', '情報に基づく決定', '熟練したアプローチ'],
        '負けるはずがない': ['ゲームを理解', '責任あるプレイ', '資金管理'],
        '一攫千金': ['エンターテイメント体験', 'ゲームの楽しさ', '責任あるプレイ'],
        '絶対負けない': ['戦略を学ぶ', 'スキル向上', '賢いプレイ'],
        '必ず勝つ': ['理解を深める', 'スキル開発', '責任ある楽しみ'],
        'リスクゼロ': ['リスクを理解', '情報に基づくゲーム', '責任あるギャンブル'],
        '連勝中': ['最近の結果', '現在のセッション', '一時的な変動'],
        'そろそろ当たる': ['ランダムな結果', '独立した結果', '偶然に基づく'],
        '損失追求': ['資金管理', '制限設定', '責任あるプレイ'],
        '大勝負': ['予算を考慮', '制限内でプレイ', '責任ある賭け']
      }
    };

    const languageAlternatives = alternatives[languageCode] || alternatives['en'];
    const defaultAlternatives = {
      'en': ['play responsibly', 'understand the risks', 'gamble within your means'],
      'zh': ['负责任游戏', '了解风险', '量力而行'],
      'pt': ['jogar responsavelmente', 'entender os riscos', 'apostar dentro de suas possibilidades'],
      'es': ['jugar responsablemente', 'entender los riesgos', 'apostar dentro de tus posibilidades'],
      'de': ['verantwortlich spielen', 'risiken verstehen', 'innerhalb ihrer möglichkeiten spielen'],
      'fr': ['jouer de manière responsible', 'comprendre les risques', 'parier dans vos moyens'],
      'it': ['giocare responsabilmente', 'capire i rischi', 'scommettere entro i propri mezzi'],
      'ja': ['責任あるプレイ', 'リスクを理解', '身の丈に合ったギャンブル']
    };

    return languageAlternatives[riskyTerm.toLowerCase()] ||
           defaultAlternatives[languageCode] ||
           defaultAlternatives['en'];
  }

  // Get supported languages
  getSupportedLanguages() {
    return Object.keys(this.riskKeywords);
  }

  // Check if language is supported
  isLanguageSupported(languageCode) {
    return this.riskKeywords.hasOwnProperty(languageCode);
  }
}

module.exports = new RiskKeywordService();
