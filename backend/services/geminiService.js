const { GoogleGenAI } = require('@google/genai');
const promptTemplateService = require('./promptTemplateService');
const settingsService = require('./settingsService');

class GeminiService {
  constructor() {
    this.ai = null;
    this.initialized = false;
    this.currentModel = 'gemini-2.0-flash-001'; // Default, will be loaded from database
  }

  async ensureInitialized() {
    if (this.initialized && this.ai) {
      return;
    }

    // Try to get API key from database first, then fallback to environment variable
    let apiKey = await settingsService.getApiKey();
    if (!apiKey) {
      apiKey = process.env.GOOGLE_GEMINI_API_KEY;
    }

    if (!apiKey) {
      throw new Error('Google Gemini API key not found. Please set it in the admin panel or GOOGLE_GEMINI_API_KEY environment variable.');
    }

    try {
      this.ai = new GoogleGenAI({ apiKey });

      // Load current model from database
      this.currentModel = await settingsService.getAiModel();

      this.initialized = true;
      console.log(`Google Gemini AI initialized successfully with model: ${this.currentModel}`);
    } catch (error) {
      console.error('Failed to initialize Google Gemini AI:', error);
      throw new Error(`Failed to initialize Google Gemini AI: ${error.message}`);
    }
  }

  async generateTopicSuggestions(keywords) {
    await this.ensureInitialized();

    if (!this.ai) {
      throw new Error('Google GenAI not initialized. Please check your API key and network connection.');
    }

    if (!Array.isArray(keywords) || keywords.length === 0) {
      throw new Error('Keywords must be a non-empty array');
    }

    try {
      const prompt = `You are an expert AI Content Strategist and Ideation Assistant. Your mission is to transform a list of user-selected keywords into a rich array of compelling, relevant, and actionable article topics, exploring all sensible possibilities and organizing them into strategic clusters.

A user has completed a detailed keyword research phase, analyzing Google Autocomplete, "People Also Ask" (PAA), Related Keywords, and Key Terms extracted from top search results. They have carefully selected the following keywords as highly relevant and representative of their content goals and audience's interests.

User-Selected Keywords: ${keywords.join(', ')}

Your task is to generate a comprehensive and diverse list of distinct article topics based *only* on these keywords. Please follow these guidelines meticulously for maximum impact:

1. **Goal & Quality:** Generate a *wide variety* of unique and high-quality article topics. Aim for a substantial list, but *never* sacrifice relevance, clarity, or value for sheer quantity.

2. **Strict Relevance:** Every topic *must* directly stem from one or more of the provided keywords. Do *not* introduce external concepts.

3. **Strategic Combination (High Priority):** **Actively seek and prioritize opportunities to intelligently combine multiple keywords** from the list. This is crucial for creating richer, more specific, in-depth, and targeted ideas.

4. **Strategic Grouping:** Your primary goal is to group the generated topics into logical, strategic clusters based on user intent and content format. This helps the user see the relationships between ideas and build a coherent content plan.

5. **User Intent Focus (Inspiration):** Consider the *likely user intent* behind these keywords to spark ideas for your clusters:
    * **Answering Questions:** Addressing PAA or implied queries.
    * **Providing Instructions:** How-To guides, tutorials.
    * **Solving Problems:** Addressing pain points.
    * **Offering Comparisons:** "X vs Y" or "Best X".
    * **Informing & Explaining:** Deep dives, "What is," "Why X matters."
    * **Curating Lists:** Listicles, resource roundups.

6. **Diverse Angles & Creativity (Explore Freely):** **Think expansively!** Explore different formats and perspectives. Ensure some topics allow an author to showcase **Experience, Expertise, and build Trust/Authority (E-E-A-T).** Be creative and suggest unique angles that will capture attention. Examples include:
    * **Trend Analyses & Predictions**
    * **Opinion Pieces & Thought Leadership**
    * **Myth-Busting & Debunking Articles**
    * **Historical Deep Dives & Evolution Pieces**
    * **Case Studies & Success Stories**

7. **Clarity & Actionability:** Topics should be phrased as compelling, clear titles that immediately suggest the article's value.

8. **Output Format:** **CRITICAL: You MUST use this exact Markdown format.** Use H3 headings (###) followed by numbered lists. Do not add any introductory text, explanations, or concluding remarks. Start directly with the first cluster.

### Pillar Content & Broad Guides
1. [Your comprehensive topic idea here]
2. [Your comprehensive topic idea here]
3. [Your comprehensive topic idea here]

### Question-Based Topics
1. [Your question-answering topic here]
2. [Your question-answering topic here]
3. [Your question-answering topic here]

### Niche Angles & Thought Leadership
1. [Your unique/opinionated topic here]
2. [Your unique/opinionated topic here]
3. [Your unique/opinionated topic here]

### How-To's & Actionable Guides
1. [Your tutorial/guide topic here]
2. [Your tutorial/guide topic here]
3. [Your tutorial/guide topic here]

### Comparison & List-Based Topics
1. [Your comparison/list topic here]
2. [Your comparison/list topic here]
3. [Your comparison/list topic here]

**IMPORTANT:** Follow this format exactly. Each cluster must start with "### " followed by the cluster name, then numbered list items starting with "1. ". Generate 3-5 topics per cluster.

Think like an innovative and seasoned content manager aiming to build a robust, diverse, and audience-focused content plan. Generate the clustered list now.`;

      const response = await this.ai.models.generateContent({
        model: this.currentModel,
        contents: prompt,
        config: {
          responseMimeType: 'text/plain',
        }
      });

      const text = response.text;

      // Parse clustered topics from the response
      const clusteredTopics = this.parseClusteredTopics(text);

      // Extract all topics into different categories for backward compatibility
      const allTopics = [];
      const clusters = {};

      Object.entries(clusteredTopics).forEach(([clusterName, topics]) => {
        clusters[clusterName] = topics;
        allTopics.push(...topics);
      });



      // Return structured response with both clustered and flat formats
      const finalResponse = {
        // New clustered format
        clustered: clusters,
        hasClusters: Object.keys(clusters).length > 0,
        allTopics: allTopics,

        // Backward compatibility - legacy format
        clusteredTopics: clusters,
        aiGenerated: allTopics,

        // Categorized topics for backward compatibility
        highIntentQuestions: allTopics.filter(topic => topic.toLowerCase().includes('?')).slice(0, 5),
        trendingAngles: allTopics.filter(topic =>
          topic.toLowerCase().includes('trend') ||
          topic.toLowerCase().includes('future') ||
          topic.toLowerCase().includes('new')
        ).slice(0, 3),
        problemFocused: allTopics.filter(topic =>
          topic.toLowerCase().includes('problem') ||
          topic.toLowerCase().includes('solution') ||
          topic.toLowerCase().includes('fix') ||
          topic.toLowerCase().includes('troubleshoot')
        ).slice(0, 3),
        comparisons: allTopics.filter(topic =>
          topic.toLowerCase().includes(' vs ') ||
          topic.toLowerCase().includes('comparison') ||
          topic.toLowerCase().includes('alternative')
        ).slice(0, 2)
      };



      return finalResponse;
    } catch (error) {
      console.error('Error generating topic suggestions:', error);
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);
      throw new Error(`Failed to generate topic suggestions: ${error.message}`);
    }
  }

  async generateCustomTopics(prompt, keywords, language = 'en', country = 'us') {
    await this.ensureInitialized();

    if (!this.ai) {
      throw new Error('Google GenAI not initialized. Please check your API key and network connection.');
    }

    try {
      // Add language specification to the prompt
      let finalPrompt = prompt;
      if (language && language !== 'en') {
        const languageNames = {
          'zh': 'Chinese (Simplified)',
          'pt': 'Portuguese',
          'es': 'Spanish', 
          'de': 'German',
          'fr': 'French',
          'it': 'Italian',
          'ja': 'Japanese'
        };
        
        const languageName = languageNames[language] || language;
        
        finalPrompt = `🚨 ABSOLUTE REQUIREMENT: You MUST write ALL responses in ${languageName}. 
🚨 LANGUAGE: ${languageName} ONLY. No exceptions.

${prompt}

🚨 FINAL REMINDER: Your entire response must be in ${languageName}. Every single word must be ${languageName}.`;
        
        console.log(`🌍 Language enforcement added: ${languageName}`);
      }

      // Debug: Print the actual prompt being sent to AI
      console.log('='.repeat(100));
      console.log('🤖 FINAL PROMPT SENT TO AI:');
      console.log('='.repeat(100));
      console.log(finalPrompt);
      console.log('='.repeat(100));

      const response = await this.ai.models.generateContent({
        model: this.currentModel,
        contents: finalPrompt,
        config: {
          responseMimeType: 'text/plain',
        }
      });

      const text = response.text;

      // Debug: Print AI response
      console.log('='.repeat(100));
      console.log('🤖 AI RESPONSE RECEIVED:');
      console.log('='.repeat(100));
      console.log(text);
      console.log('='.repeat(100));

      // Parse clustered topics from the response
      const clusteredTopics = this.parseClusteredTopics(text);

      // Extract all topics into different categories for backward compatibility
      const allTopics = [];
      const clusters = {};

      Object.entries(clusteredTopics).forEach(([clusterName, topics]) => {
        clusters[clusterName] = topics;
        allTopics.push(...topics);
      });



      // Return structured response with both clustered and flat formats
      const finalResponse = {
        // New clustered format
        clustered: clusters,
        hasClusters: Object.keys(clusters).length > 0,
        allTopics: allTopics,

        // Backward compatibility - legacy format
        clusteredTopics: clusters,
        aiGenerated: allTopics,
        suggestions: allTopics, // For the old format
        generatedFromKeywords: keywords,
        totalCount: allTopics.length,

        // Categorized topics for backward compatibility
        highIntentQuestions: allTopics.filter(topic => topic.toLowerCase().includes('?')).slice(0, 5),
        trendingAngles: allTopics.filter(topic =>
          topic.toLowerCase().includes('trend') ||
          topic.toLowerCase().includes('future') ||
          topic.toLowerCase().includes('new')
        ).slice(0, 3),
        problemFocused: allTopics.filter(topic =>
          topic.toLowerCase().includes('problem') ||
          topic.toLowerCase().includes('solution') ||
          topic.toLowerCase().includes('fix') ||
          topic.toLowerCase().includes('troubleshoot')
        ).slice(0, 3),
        comparisons: allTopics.filter(topic =>
          topic.toLowerCase().includes(' vs ') ||
          topic.toLowerCase().includes('comparison') ||
          topic.toLowerCase().includes('alternative')
        ).slice(0, 2)
      };



      return finalResponse;
    } catch (error) {
      console.error('Error generating custom topics:', error);
      throw new Error(`Failed to generate custom topics: ${error.message}`);
    }
  }

  async generateArticle(articleData) {
    await this.ensureInitialized();

    if (!this.ai) {
      throw new Error('Google GenAI not initialized. Please check your API key and network connection.');
    }

    try {
      const {
        topics, // EXPECTS AN ARRAY of 1 to 5 strings
        primaryKeywords,
        secondaryKeywords,
        contentType
      } = articleData;

      // Input Validation (Basic)
      if (!Array.isArray(topics) || topics.length === 0) {
        throw new Error('Topics must be a non-empty array');
      }

      if (topics.length > 5) {
        throw new Error('Maximum 5 topics allowed');
      }

      // Validate each topic is a string
      for (const topic of topics) {
        if (typeof topic !== 'string' || topic.trim().length === 0) {
          throw new Error('Each topic must be a non-empty string');
        }
      }

      console.log('=== ARTICLE GENERATION DEBUG ===');
      console.log('Topics received:', topics);
      console.log('Content type:', contentType);
      console.log('Primary keywords:', primaryKeywords);
      console.log('Secondary keywords:', secondaryKeywords);
      console.log('================================');

      // Use prompt template service for all prompt generation
      console.log('Generating prompt using template service...');
      const prompt = await promptTemplateService.buildDynamicPrompt(contentType, articleData);

      // Log only the prompt content
      console.log(prompt);

      const response = await this.ai.models.generateContent({
        model: this.currentModel,
        contents: prompt,
        config: {
          responseMimeType: 'text/plain',
        }
      });

      return response.text;
    } catch (error) {
      console.error('Error generating article:', error);
      throw new Error(`Failed to generate article: ${error.message}`);
    }
  }

  getWordCountFromLength(length) {
    const lengthMap = {
      'snippet': 500,
      'short_post': 800,       // Corresponds to 'Short Post'
      'medium_article': 1500,  // Corresponds to 'Medium Article'
      'long_guide': 2500,      // Corresponds to 'Long-Form Guide'
      'pillar_module': 700     // Corresponds to 'Pillar Page Module'
    };

    // More robust default value handling
    const selectedKey = typeof length === 'string' ? length.toLowerCase() : 'medium_article';

    console.log('=== LENGTH MAPPING DEBUG ===');
    console.log('Input length:', length);
    console.log('Selected key:', selectedKey);
    console.log('Mapped word count:', lengthMap[selectedKey] || 1500);
    console.log('===========================');

    return lengthMap[selectedKey] || 1500;
  }

  parseClusteredTopics(text) {
    const clusters = {};
    const lines = text.split('\n');
    let currentCluster = null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();

      // Check for cluster headers (### Cluster Name, ## Cluster Name or # Cluster Name)
      if (trimmedLine.startsWith('###') || trimmedLine.startsWith('##') || trimmedLine.startsWith('# ')) {
        currentCluster = trimmedLine.replace(/^#+\s*/, '').trim();
        clusters[currentCluster] = [];
      }
      // Check for numbered topics (1. Topic title)
      else if (trimmedLine.match(/^\d+\.\s+/)) {
        const topic = trimmedLine.replace(/^\d+\.\s+/, '').trim();
        if (topic && topic.length > 0) {
          if (currentCluster) {
            clusters[currentCluster].push(topic);
          } else {
            // Create a default cluster if none exists
            if (!clusters['Generated Topics']) {
              clusters['Generated Topics'] = [];
            }
            clusters['Generated Topics'].push(topic);
          }
        }
      }
      // Also check for bullet points (- Topic or * Topic)
      else if (trimmedLine.match(/^[-*]\s+/) && currentCluster) {
        const topic = trimmedLine.replace(/^[-*]\s+/, '').trim();
        if (topic && topic.length > 0) {
          clusters[currentCluster].push(topic);
        }
      }
    }

    return clusters;
  }





  extractListFromText(text, type) {
    const lines = text.split('\n');
    const items = [];

    for (const line of lines) {
      if (line.trim() && (line.includes('?') || line.includes(type))) {
        const cleaned = line.replace(/^\d+\.?\s*/, '').replace(/^[-*]\s*/, '').trim();
        if (cleaned && !items.includes(cleaned)) {
          items.push(cleaned);
        }
      }
    }

    return items.slice(0, 5);
  }

  async updateModel(newModel) {
    try {
      // Save to database
      await settingsService.setAiModel(newModel);

      // Update current instance
      this.currentModel = newModel;

      console.log(`AI model updated to: ${newModel}`);
      return true;
    } catch (error) {
      console.error('Failed to update AI model:', error);
      throw error;
    }
  }

  async updateApiKey(newApiKey) {
    try {
      // Save to database
      await settingsService.setApiKey(newApiKey);

      // Reset initialization state to force reinit with new API key
      this.initialized = false;
      this.ai = null;

      // Reinitialize with new API key
      await this.ensureInitialized();

      console.log('API key updated successfully');
      return true;
    } catch (error) {
      console.error('Failed to update API key:', error);
      throw error;
    }
  }

  getCurrentModel() {
    return this.currentModel;
  }

  async refreshModel() {
    try {
      this.currentModel = await settingsService.getAiModel();
      console.log(`AI model refreshed to: ${this.currentModel}`);
      return this.currentModel;
    } catch (error) {
      console.error('Failed to refresh AI model:', error);
      throw error;
    }
  }
}

module.exports = new GeminiService();
