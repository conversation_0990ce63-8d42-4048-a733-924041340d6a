const database = require('../config/database');

class SettingsService {
  constructor() {
    this.cache = new Map();
    this.initialized = false;
  }

  async ensureInitialized() {
    if (this.initialized) {
      return;
    }

    try {
      await this.initializeDefaultSettings();
      await this.loadAllSettings();
      this.initialized = true;
      console.log('Settings service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize settings service:', error);
      throw error;
    }
  }

  async initializeDefaultSettings() {
    const defaultSettings = [
      {
        key: 'ai_model',
        value: 'gemini-2.0-flash-001',
        description: 'Current AI model used for content generation'
      },
      {
        key: 'max_tokens',
        value: '8192',
        description: 'Maximum tokens for AI responses'
      },
      {
        key: 'temperature',
        value: '0.7',
        description: 'AI creativity temperature (0.0 - 1.0)'
      }
    ];

    for (const setting of defaultSettings) {
      try {
        // Use PostgreSQL syntax only (SQLite support removed)
        await database.run(
          `INSERT INTO system_settings (setting_key, setting_value, description)
           VALUES (?, ?, ?)
           ON CONFLICT (setting_key) DO NOTHING`,
          [setting.key, setting.value, setting.description]
        );
      } catch (error) {
        console.error(`Failed to initialize setting ${setting.key}:`, error);
        // Continue with other settings even if one fails
      }
    }
  }

  async loadAllSettings() {
    try {
      const settings = await database.all('SELECT setting_key, setting_value FROM system_settings');
      this.cache.clear();
      
      for (const setting of settings) {
        this.cache.set(setting.setting_key, setting.setting_value);
      }
      
      console.log(`Loaded ${settings.length} system settings into cache`);
    } catch (error) {
      console.error('Failed to load settings:', error);
      throw error;
    }
  }

  async getSetting(key, defaultValue = null) {
    await this.ensureInitialized();
    
    if (this.cache.has(key)) {
      return this.cache.get(key);
    }
    
    try {
      const setting = await database.get(
        'SELECT setting_value FROM system_settings WHERE setting_key = ?',
        [key]
      );
      
      const value = setting ? setting.setting_value : defaultValue;
      if (value !== null) {
        this.cache.set(key, value);
      }
      
      return value;
    } catch (error) {
      console.error(`Failed to get setting ${key}:`, error);
      return defaultValue;
    }
  }

  async setSetting(key, value, description = null) {
    await this.ensureInitialized();
    
    try {
      const existingSetting = await database.get(
        'SELECT id FROM system_settings WHERE setting_key = ?',
        [key]
      );

      if (existingSetting) {
        // Update existing setting
        await database.run(
          `UPDATE system_settings 
           SET setting_value = ?, updated_at = ${database.isPostgres ? 'CURRENT_TIMESTAMP' : 'CURRENT_TIMESTAMP'} 
           WHERE setting_key = ?`,
          [value, key]
        );
      } else {
        // Insert new setting
        await database.run(
          `INSERT INTO system_settings (setting_key, setting_value, description) 
           VALUES (?, ?, ?)`,
          [key, value, description || `Setting for ${key}`]
        );
      }

      // Update cache
      this.cache.set(key, value);
      
      console.log(`Setting updated: ${key} = ${value}`);
      return true;
    } catch (error) {
      console.error(`Failed to set setting ${key}:`, error);
      throw error;
    }
  }

  async getAllSettings() {
    await this.ensureInitialized();
    
    try {
      const settings = await database.all(
        'SELECT setting_key, setting_value, description, updated_at FROM system_settings ORDER BY setting_key'
      );
      
      return settings.reduce((acc, setting) => {
        acc[setting.setting_key] = {
          value: setting.setting_value,
          description: setting.description,
          updated_at: setting.updated_at
        };
        return acc;
      }, {});
    } catch (error) {
      console.error('Failed to get all settings:', error);
      throw error;
    }
  }

  async getAiModel() {
    return await this.getSetting('ai_model', 'gemini-2.0-flash-001');
  }

  async setAiModel(modelName) {
    return await this.setSetting('ai_model', modelName, 'Current AI model used for content generation');
  }

  async getApiKey() {
    return await this.getSetting('api_key', null);
  }

  async setApiKey(apiKey) {
    return await this.setSetting('api_key', apiKey, 'Google Gemini API key for content generation');
  }

  // Clear cache to force reload from database
  clearCache() {
    this.cache.clear();
  }
}

module.exports = new SettingsService();
