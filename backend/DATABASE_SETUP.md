# Database Setup Guide

This guide explains how to set up and use the PostgreSQL database from Railway with your J-Writer application.

## Overview

The application now supports both SQLite (for local development) and PostgreSQL (for production on Railway). The database configuration automatically detects which database to use based on the `DATABASE_URL` environment variable.

## Railway PostgreSQL Setup

### Step 1: Environment Variables

Based on the Railway connection guide you provided, you need to set up the database URL in your environment variables.

1. **For Railway Deployment:**
   - In your Railway project dashboard, go to Variables
   - Create a new variable: `DATABASE_URL`
   - Set the value to: `${{ Postgres.DATABASE_URL }}`
   - This will automatically use Railway's PostgreSQL connection string

2. **For Local Testing with Railway Database:**
   - Copy the actual PostgreSQL connection string from Railway
   - Add it to your `backend/.env` file:
   ```
   DATABASE_URL=postgresql://username:password@host:port/database
   ```

### Step 2: Database Initialization

The database will be automatically initialized when you start the application. However, you can also run the initialization manually:

```bash
# Navigate to backend directory
cd backend

# Run database initialization
npm run init-db
```

### Step 3: Verify Setup

1. **Check Connection:**
   ```bash
   npm run init-db
   ```
   
2. **Start the Application:**
   ```bash
   npm start
   ```

3. **Look for Success Messages:**
   - "Connected to PostgreSQL database"
   - "Database tables initialized successfully"
   - "Database connection test passed"

## Database Schema

The application creates the following tables:

### Users Table
- `id` (SERIAL PRIMARY KEY)
- `email` (VARCHAR(255) UNIQUE)
- `password_hash` (VARCHAR(255))
- `full_name` (VARCHAR(255))
- `plan_type` (VARCHAR(50))
- `email_verified` (BOOLEAN)
- `verification_token` (VARCHAR(255))
- `reset_token` (VARCHAR(255))
- `reset_token_expires` (TIMESTAMP)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

### Tasks Table
- `id` (VARCHAR(255) PRIMARY KEY)
- `user_id` (INTEGER, FOREIGN KEY)
- `name` (VARCHAR(255))
- `status` (VARCHAR(100))
- `current_step` (INTEGER)
- `keywords` (TEXT - JSON)
- `selected_topics` (TEXT - JSON)
- `topic_suggestions` (TEXT - JSON)
- `keyword_research_data` (TEXT - JSON)
- `keyword_research_selections` (TEXT - JSON)
- `sources` (TEXT - JSON)
- `topic_sources` (TEXT - JSON)
- `product_info` (TEXT - JSON)
- `eeat_profile` (TEXT - JSON)
- `output_parameters` (TEXT - JSON)
- `generated_article` (TEXT)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

### User Presets Table
- `id` (SERIAL PRIMARY KEY)
- `user_id` (INTEGER, FOREIGN KEY)
- `preset_type` (VARCHAR(50))
- `preset_name` (VARCHAR(255))
- `preset_data` (TEXT - JSON)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

### Blog Posts Table
- `id` (SERIAL PRIMARY KEY)
- `title` (VARCHAR(255))
- `slug` (VARCHAR(255) UNIQUE)
- `content` (TEXT)
- `excerpt` (TEXT)
- `featured_image` (VARCHAR(500))
- `author` (VARCHAR(255))
- `status` (VARCHAR(50))
- `categories` (TEXT - JSON)
- `tags` (TEXT - JSON)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)
- `published_at` (TIMESTAMP)

### User Sessions Table
- `id` (SERIAL PRIMARY KEY)
- `user_id` (INTEGER, FOREIGN KEY)
- `token_hash` (VARCHAR(255))
- `expires_at` (TIMESTAMP)
- `created_at` (TIMESTAMP)

## Troubleshooting

### Connection Issues

1. **Check Environment Variables:**
   ```bash
   echo $DATABASE_URL
   ```

2. **Verify Railway Database Status:**
   - Check Railway dashboard for database status
   - Ensure the PostgreSQL service is running

3. **Test Connection Manually:**
   ```bash
   npm run init-db
   ```

### Common Errors

1. **"Connection refused":**
   - Check if DATABASE_URL is correctly set
   - Verify Railway database is running

2. **"SSL required":**
   - The configuration automatically handles SSL for production
   - For Railway, SSL is enabled by default

3. **"Table already exists":**
   - This is normal - the script uses `CREATE TABLE IF NOT EXISTS`
   - Tables will only be created if they don't exist

## Migration from SQLite

If you have existing data in SQLite and want to migrate to PostgreSQL:

1. **Export SQLite Data:**
   ```bash
   sqlite3 backend/data/development.db .dump > backup.sql
   ```

2. **Convert to PostgreSQL Format:**
   - Manual conversion required for data types
   - Consider using migration tools like `pgloader`

3. **Import to PostgreSQL:**
   ```bash
   psql $DATABASE_URL < converted_backup.sql
   ```

## Development vs Production

- **Development:** Uses SQLite by default (no DATABASE_URL set)
- **Production:** Uses PostgreSQL when DATABASE_URL is set
- **Testing:** Can use either database depending on environment setup

The application automatically detects which database to use based on the presence and format of the `DATABASE_URL` environment variable.
