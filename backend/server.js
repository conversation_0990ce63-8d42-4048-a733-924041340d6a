const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const ideationRoutes = require('./routes/ideation');
const sourcesRoutes = require('./routes/sources');
const generateRoutes = require('./routes/generate');
const authRoutes = require('./routes/auth');
const tasksRoutes = require('./routes/tasks');
const presetsRoutes = require('./routes/presets');
const promptTemplatesRoutes = require('./routes/promptTemplates');
const riskAnalysisRoutes = require('./routes/riskAnalysis');
const adminRoutes = require('./routes/admin');
const contentRoutes = require('./routes/content');
const publicRoutes = require('./routes/public');
const affiliateRoutes = require('./routes/affiliate');
const fixContentTypesRoutes = require('./routes/fix-content-types');
const articlesRoutes = require('./routes/articles');
const tagsRoutes = require('./routes/tags');
const debugArticlesRoutes = require('./routes/debug-articles');
const { router: redditOAuthRoutes } = require('./routes/reddit-oauth');
const redditRoutes = require('./routes/reddit');
const uploadRoutes = require('./routes/upload');
const { validateApiKey, sanitizeInput, validateRequestSize, validateContentType } = require('./middleware/security');
const database = require('./config/database');
const promptTemplateService = require('./services/promptTemplateService');
const settingsService = require('./services/settingsService');

const app = express();
const PORT = process.env.PORT || 3001;

// Trust proxy for Railway deployment (fixes rate limiting issues)
app.set('trust proxy', 1);

// Set NODE_ENV to production if running on Railway (has PORT env var but no explicit NODE_ENV)
if (process.env.PORT && !process.env.NODE_ENV) {
  process.env.NODE_ENV = 'production';
}

// Health check endpoint - MUST be before any middleware that could interfere
app.get('/health', (req, res) => {
  res.status(200).send('OK');
});

// Minimal production logging
if (process.env.NODE_ENV !== 'production') {
  console.log('=== DEVELOPMENT MODE ===');
  console.log('PORT:', PORT);
  console.log('NODE_ENV:', process.env.NODE_ENV);
  console.log('DATABASE_URL exists:', !!process.env.DATABASE_URL);
  console.log('========================');
}

// Rate limiting - generous limits for production use
const isDevelopment = process.env.NODE_ENV === 'development';

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: isDevelopment ? 2000 : 2000, // Significantly increased for production
  message: {
    error: 'Too many requests from this IP, please wait a few minutes before trying again.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: isDevelopment ? 1000 : 1000, // Significantly increased API limits for production
  message: {
    error: 'Too many API requests from this IP, please wait a few minutes before trying again.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// CORS configuration - support both environment variable and default origins
const getAllowedOrigins = () => {
  const defaultOrigins = [
    'https://www.writer777.com',
    'https://writer777.com',
    'https://writer777-git-main-hicrubens-projects.vercel.app',
    'https://writer777.vercel.app',
    'https://writer-777.vercel.app',
    /^https:\/\/writer777.*\.vercel\.app$/,
    /^https:\/\/writer-777.*\.vercel\.app$/,
    /^https:\/\/.*\.vercel\.app$/,
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:3000',
    // 添加管理员面板域名
    'https://admin.writer777.com',
    /^https:\/\/admin-.*\.vercel\.app$/
  ];

  if (process.env.ALLOWED_ORIGINS) {
    const envOrigins = process.env.ALLOWED_ORIGINS.split(',').map(origin => origin.trim());
    return [...defaultOrigins, ...envOrigins];
  }

  return defaultOrigins;
};

// 增强CORS配置 - 简化以避免CORS错误
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = getAllowedOrigins();
    
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    // Check if origin is in allowed list (including regex patterns)
    const isAllowed = allowedOrigins.some(allowedOrigin => {
      if (typeof allowedOrigin === 'string') {
        return allowedOrigin === origin;
      } else if (allowedOrigin instanceof RegExp) {
        return allowedOrigin.test(origin);
      }
      return false;
    });
    
    if (isAllowed) {
      callback(null, true);
    } else {
      // For production stability, allow all origins as fallback
      if (process.env.NODE_ENV === 'production') {
        callback(null, true);
      } else {
        console.log('CORS check - Origin blocked:', origin);
        callback(new Error('Not allowed by CORS'));
      }
    }
  },
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type', 
    'Authorization', 
    'X-API-Key', 
    'x-api-key',
    'Origin',
    'X-Requested-With',
    'Accept',
    'Accept-Version',
    'Accept-Encoding',
    'Accept-Language',
    'Connection',
    'User-Agent'
  ],
  exposedHeaders: ['Content-Length', 'X-Foo', 'X-Bar'],
  preflightContinue: false
};

// Apply CORS first, before any other middleware
app.use(cors(corsOptions));
console.log('CORS configuration applied with dynamic origin checking');

// Handle preflight OPTIONS requests explicitly
app.options('*', cors(corsOptions));

// Middleware
app.use(helmet({
  crossOriginEmbedderPolicy: false, // Allow embedding for development
  crossOriginResourcePolicy: false, // Disable to allow cross-origin resource access
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:", "*"], // Allow all image sources
      connectSrc: ["'self'", "https:", "*"] // Allow all connections
    },
  },
}));

app.use(morgan('combined'));
app.use(limiter); // Apply general rate limiting to all requests
app.use(validateRequestSize); // Validate request size before parsing
app.use(express.json({ limit: '10mb' })); // Increased limit for large prompts
app.use(express.urlencoded({ extended: true, limit: '1mb' }));
app.use(sanitizeInput); // Sanitize all input data

// Serve static files from public directory (for admin.html)
const path = require('path');
app.use(express.static(path.join(__dirname, 'public')));
console.log('Static files served from:', path.join(__dirname, 'public'));

// Serve uploaded files with proper CORS headers
// In Railway, the volume is mounted at /data/uploads (absolute path)
const uploadsPath = process.env.NODE_ENV === 'production' ? '/data/uploads' : path.join(__dirname, 'data/uploads');

// Log all requests to /uploads for debugging
app.use('/uploads', (req, res, next) => {
  console.log(`[UPLOADS] Request for: ${req.path}`);
  console.log(`[UPLOADS] Full URL: ${req.url}`);
  console.log(`[UPLOADS] Method: ${req.method}`);
  next();
});

app.use('/uploads', cors(corsOptions), express.static(uploadsPath, {
  setHeaders: (res, path) => {
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Cross-Origin-Resource-Policy', 'cross-origin');
    res.set('Cache-Control', 'public, max-age=3600');
  }
}));
console.log('Upload files served from:', uploadsPath);

// Routes with API key validation, rate limiting and content validation
app.use('/api/ideation', apiLimiter, validateContentType, ideationRoutes);
app.use('/api/sources', apiLimiter, validateApiKey, validateContentType, sourcesRoutes);
app.use('/api/generate', apiLimiter, validateApiKey, validateContentType, generateRoutes);

// Authentication routes (no API key required for public auth endpoints)
app.use('/api/auth', apiLimiter, validateContentType, authRoutes);

// Public routes (no authentication required)
app.use('/api/public', apiLimiter, validateContentType, publicRoutes);

// Protected routes (require authentication)
app.use('/api/tasks', apiLimiter, validateContentType, tasksRoutes);
app.use('/api/presets', apiLimiter, validateContentType, presetsRoutes);
app.use('/api/content', apiLimiter, validateContentType, contentRoutes);

// Reddit OAuth and API routes (require authentication)
app.use('/api/reddit-oauth', apiLimiter, validateContentType, redditOAuthRoutes);
app.use('/api/reddit', apiLimiter, validateContentType, redditRoutes);

// Utility routes (no authentication required for now - in production these should be protected)
app.use('/api/prompt-templates', apiLimiter, validateContentType, promptTemplatesRoutes);
app.use('/api/risk-analysis', apiLimiter, validateContentType, riskAnalysisRoutes);

// Admin routes (protected with admin key)
app.use('/api/admin', apiLimiter, validateContentType, adminRoutes);

// Affiliate marketing routes (some endpoints require authentication)
app.use('/api/affiliate', apiLimiter, validateContentType, affiliateRoutes);

// Tags routes (public access for browsing)
app.use('/api/tags', apiLimiter, validateContentType, tagsRoutes);

// Content type fix route (temporary utility)
app.use('/api/fix-content-types', apiLimiter, validateContentType, fixContentTypesRoutes);

// Article management routes (protected with authentication)
app.use('/api/articles', apiLimiter, validateContentType, articlesRoutes);

// Upload routes (requires authentication)
app.use('/api/upload', apiLimiter, uploadRoutes);

// Debug routes (temporary)
app.use('/api/debug', debugArticlesRoutes);

// Public debug endpoint for uploads (temporary)
app.get('/api/debug/uploads', (req, res) => {
  const fs = require('fs');
  const uploadsPath = process.env.NODE_ENV === 'production' ? '/data/uploads' : path.join(__dirname, 'data/uploads');
  
  try {
    console.log('Checking uploads directory:', uploadsPath);
    console.log('NODE_ENV:', process.env.NODE_ENV);
    
    if (!fs.existsSync(uploadsPath)) {
      return res.json({
        success: false,
        message: 'Uploads directory does not exist',
        path: uploadsPath,
        nodeEnv: process.env.NODE_ENV
      });
    }
    
    const files = fs.readdirSync(uploadsPath);
    const fileInfo = files.slice(0, 10).map(file => {
      try {
        const stats = fs.statSync(path.join(uploadsPath, file));
        return {
          name: file,
          size: stats.size,
          isFile: stats.isFile()
        };
      } catch (e) {
        return { name: file, error: e.message };
      }
    });
    
    res.json({
      success: true,
      path: uploadsPath,
      exists: true,
      fileCount: files.length,
      sampleFiles: fileInfo,
      nodeEnv: process.env.NODE_ENV
    });
  } catch (error) {
    console.error('Debug uploads error:', error);
    res.json({
      success: false,
      error: error.message,
      path: uploadsPath,
      nodeEnv: process.env.NODE_ENV
    });
  }
});

// Root endpoint - Also serves as health check
app.get('/', (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'AI Article Generator API is running',
    endpoints: {
      health: '/health',
      ideation: '/api/ideation',
      sources: '/api/sources',
      generate: '/api/generate'
    }
  });
});


// Specific route for admin.html
app.get('/admin.html', (req, res) => {
  console.log('Admin.html requested');
  res.sendFile(path.join(__dirname, 'public', 'admin.html'));
});


// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Something went wrong!',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Initialize database and start server
const startServer = async () => {
  let databaseConnected = false;
  
  // Try to connect to database but don't fail if it fails
  try {
    await database.connect();
    console.log('Database initialized successfully');
    
    // Set server mode to prevent accidental pool closure
    database.setServerMode(true);
    console.log('Database set to server mode - pool will not be closed by scripts');
    
    databaseConnected = true;

    // Database migrations are no longer needed - PostgreSQL schema is managed by config/database.js
    console.log('✅ Database schema managed by config/database.js - no migrations needed');

    // Initialize system settings
    try {
      await settingsService.ensureInitialized();
      console.log('System settings initialized successfully');
    } catch (error) {
      console.warn('Failed to initialize system settings:', error.message);
      // Don't fail startup if settings can't be initialized
    }

    // Initialize default prompt templates
    try {
      await promptTemplateService.initializeDefaultTemplates();
      console.log('Prompt templates initialized successfully');
    } catch (error) {
      console.warn('Failed to initialize prompt templates:', error.message);
      // Don't fail startup if templates can't be initialized
    }
  } catch (error) {
    console.warn('Database connection failed, starting server without database:', error.message);
    console.warn('Some features may not work without database connection');
    databaseConnected = false;
  }

  // Start the server regardless of database status
  console.log('=== STARTING SERVER ===');
  console.log(`Attempting to bind to 0.0.0.0:${PORT}`);
  
  try {
    const server = app.listen(PORT, '0.0.0.0', () => {
      console.log('=== SERVER STARTED SUCCESSFULLY ===');
      console.log(`Server is running on port ${PORT}`);
      console.log(`Health check endpoint available at: http://0.0.0.0:${PORT}/health`);
      console.log('Environment:', process.env.NODE_ENV || 'development');
      console.log('Database connected:', databaseConnected);
      console.log('=================================');
      
      // Test health endpoint immediately after startup
      const http = require('http');
      http.get(`http://localhost:${PORT}/health`, (res) => {
        console.log('Health check self-test status:', res.statusCode);
      }).on('error', (err) => {
        console.error('Health check self-test failed:', err);
      });
    });
    
    server.on('error', (err) => {
      console.error('=== SERVER STARTUP ERROR ===');
      console.error('Error details:', err);
      console.error('Error code:', err.code);
      console.error('Error syscall:', err.syscall);
      console.error('Error address:', err.address);
      console.error('Error port:', err.port);
      console.error('===========================');
      process.exit(1);
    });
    
    // Add timeout handler - reduce to 30 seconds to identify issues faster
    server.setTimeout(30000); // 30 seconds timeout
    server.on('timeout', (socket) => {
      console.warn('Socket timeout occurred - request took longer than 30 seconds');
      console.warn('Consider optimizing long-running operations');
    });
    
  } catch (error) {
    console.error('=== CRITICAL SERVER ERROR ===');
    console.error('Failed to start server:', error);
    console.error('=============================');
    process.exit(1);
  }
};

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('\nShutting down gracefully...');
  try {
    await database.forceClose();
    console.log('Database connection closed');
    process.exit(0);
  } catch (error) {
    console.error('Error during shutdown:', error);
    process.exit(1);
  }
});

process.on('SIGTERM', async () => {
  console.log('\nReceived SIGTERM, shutting down gracefully...');
  try {
    await database.forceClose();
    console.log('Database connection closed');
    process.exit(0);
  } catch (error) {
    console.error('Error during shutdown:', error);
    process.exit(1);
  }
});

startServer();
