# Railway Deployment Guide

This guide explains how to deploy the AI Article Generator backend to Railway.

## Prerequisites

1. **Railway Account**: Sign up at [railway.app](https://railway.app)
2. **GitHub Repository**: Your code should be in a GitHub repository
3. **API Keys**: Obtain required API keys (see Environment Variables section)

## Deployment Steps

### 1. Create New Project on Railway

1. Go to [railway.app](https://railway.app) and sign in
2. Click "New Project"
3. Select "Deploy from GitHub repo"
4. Choose your repository
5. Select the `backend` folder as the root directory

### 2. Configure Environment Variables

In the Railway dashboard, go to your project → Variables tab and add:

#### Required Variables:
```
NODE_ENV=production
GEMINI_API_KEY=your_gemini_api_key_here
SERPER_API_KEY=your_serper_api_key_here
JWT_SECRET=your_jwt_secret_here_minimum_32_characters
API_KEY=your_api_key_here
```

#### Optional Variables:
```
ALLOWED_ORIGINS=https://your-frontend.vercel.app
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
API_RATE_LIMIT_MAX_REQUESTS=50
LOG_LEVEL=info
```

### 3. Add Database (Optional)

For production, you can add PostgreSQL:

1. In Railway dashboard, click "New" → "Database" → "PostgreSQL"
2. Railway will automatically set the `DATABASE_URL` environment variable
3. The app will automatically use PostgreSQL instead of SQLite

### 4. Deploy

1. Railway will automatically deploy when you push to your main branch
2. Monitor the deployment in the Railway dashboard
3. Check the logs for any issues

### 5. Get Your API URL

1. In Railway dashboard, go to Settings → Domains
2. Copy the generated domain (e.g., `https://your-app.railway.app`)
3. Update your frontend configuration to use this URL

## Health Check

Your deployed API will be available at:
- **Health Check**: `https://your-app.railway.app/health`
- **API Root**: `https://your-app.railway.app/api`

## Environment Variables Reference

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `NODE_ENV` | Environment mode | Yes | `production` |
| `PORT` | Server port | No | `3001` |
| `GEMINI_API_KEY` | Google Gemini API key | Yes | - |
| `SERPER_API_KEY` | Serper search API key | Yes | - |
| `JWT_SECRET` | JWT signing secret | Yes | - |
| `API_KEY` | API authentication key | Yes | - |
| `DATABASE_URL` | PostgreSQL connection string | No | SQLite |
| `ALLOWED_ORIGINS` | CORS allowed origins | No | Auto-detected |

## Troubleshooting

### Common Issues:

1. **Build Fails**: Check that all dependencies are in `package.json`
2. **App Crashes**: Check environment variables are set correctly
3. **Database Issues**: Ensure DATABASE_URL is set if using PostgreSQL
4. **CORS Errors**: Add your frontend URL to ALLOWED_ORIGINS

### Checking Logs:

1. Go to Railway dashboard
2. Click on your service
3. Go to "Deployments" tab
4. Click on the latest deployment to view logs

### Manual Deployment:

If auto-deployment fails, you can trigger manual deployment:
1. Go to Deployments tab
2. Click "Deploy Now"

## API Endpoints

Once deployed, your API will have these endpoints:

- `GET /health` - Health check
- `POST /api/ideation` - Topic ideation
- `POST /api/sources` - Source research
- `POST /api/generate` - Article generation
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/tasks` - Get user tasks
- `POST /api/tasks` - Create new task
- `GET /api/presets` - Get presets
- `POST /api/prompt-templates/initialize` - Initialize prompt templates
- `POST /api/risk-analysis/analyze-text` - Risk analysis

## Security Notes

1. **Never commit `.env` files** to your repository
2. **Use strong JWT secrets** (minimum 32 characters)
3. **Regularly rotate API keys**
4. **Monitor usage** through Railway dashboard
5. **Set up proper CORS** for your frontend domain

## Scaling

Railway automatically handles scaling, but you can:
1. Monitor resource usage in the dashboard
2. Upgrade your plan if needed
3. Set up custom domains
4. Configure custom build commands if needed

## Support

- Railway Documentation: [docs.railway.app](https://docs.railway.app)
- Railway Discord: [discord.gg/railway](https://discord.gg/railway)
- GitHub Issues: Create issues in your repository
