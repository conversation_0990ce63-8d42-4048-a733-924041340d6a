#!/bin/bash

# Railway Deployment Script
# This script helps prepare the backend for Railway deployment

echo "🚀 Preparing backend for Railway deployment..."

# Check if required files exist
echo "📋 Checking required files..."

if [ ! -f "package.json" ]; then
    echo "❌ package.json not found!"
    exit 1
fi

if [ ! -f "server.js" ]; then
    echo "❌ server.js not found!"
    exit 1
fi

if [ ! -f ".env.example" ]; then
    echo "❌ .env.example not found!"
    exit 1
fi

echo "✅ All required files found"

# Check if node_modules exists and is not empty
if [ ! -d "node_modules" ] || [ -z "$(ls -A node_modules)" ]; then
    echo "📦 Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        exit 1
    fi
    echo "✅ Dependencies installed"
else
    echo "✅ Dependencies already installed"
fi

# Test the application locally
echo "🧪 Testing application..."
npm run dev &
SERVER_PID=$!

# Wait a moment for server to start
sleep 3

# Test health endpoint
HEALTH_CHECK=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3001/health)

# Kill the test server
kill $SERVER_PID 2>/dev/null

if [ "$HEALTH_CHECK" = "200" ]; then
    echo "✅ Health check passed"
else
    echo "❌ Health check failed (HTTP $HEALTH_CHECK)"
    echo "⚠️  Make sure your .env file is configured correctly"
fi

echo ""
echo "🎯 Next steps for Railway deployment:"
echo "1. Push your code to GitHub"
echo "2. Create a new project on Railway (https://railway.app)"
echo "3. Connect your GitHub repository"
echo "4. Set the root directory to 'backend'"
echo "5. Configure environment variables (see .env.example)"
echo "6. Deploy!"
echo ""
echo "📚 For detailed instructions, see RAILWAY_DEPLOYMENT.md"
echo ""
echo "✨ Backend is ready for Railway deployment!"
