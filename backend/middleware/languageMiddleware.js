/**
 * Language detection and handling middleware
 */

const SUPPORTED_LANGUAGES = ['en', 'pt', 'es'];
const DEFAULT_LANGUAGE = 'en';

const languageMiddleware = (req, res, next) => {
  // Extract language from various sources
  let detectedLanguage = DEFAULT_LANGUAGE;

  // 1. Check URL path parameter (e.g., /pt/articles)
  const pathSegments = req.path.split('/').filter(Boolean);
  if (pathSegments.length > 0 && SUPPORTED_LANGUAGES.includes(pathSegments[0])) {
    detectedLanguage = pathSegments[0];
    // Remove language from path for route matching
    req.pathWithoutLanguage = '/' + pathSegments.slice(1).join('/');
  } else {
    req.pathWithoutLanguage = req.path;
  }

  // 2. Check query parameter (support both 'lang' and 'language')
  if (req.query.lang && SUPPORTED_LANGUAGES.includes(req.query.lang)) {
    detectedLanguage = req.query.lang;
  } else if (req.query.language && SUPPORTED_LANGUAGES.includes(req.query.language)) {
    detectedLanguage = req.query.language;
  }

  // 3. Check Accept-Language header as fallback
  if (!req.query.lang && !pathSegments[0]) {
    const acceptLanguage = req.headers['accept-language'];
    if (acceptLanguage) {
      const preferredLangs = acceptLanguage
        .split(',')
        .map(lang => lang.split(';')[0].trim().toLowerCase())
        .map(lang => lang.split('-')[0]); // Extract main language code

      for (const lang of preferredLangs) {
        if (SUPPORTED_LANGUAGES.includes(lang)) {
          detectedLanguage = lang;
          break;
        }
      }
    }
  }

  // Add language info to request object
  req.language = detectedLanguage;
  req.isDefaultLanguage = detectedLanguage === DEFAULT_LANGUAGE;
  req.supportedLanguages = SUPPORTED_LANGUAGES;

  // Add language helper functions
  req.getLocalizedContent = (content) => {
    // This could be enhanced to return language-specific content
    // For now, it returns the content as-is
    return content;
  };

  req.getContentLanguageFilter = () => {
    // This could be used to filter content by language in database queries
    return {
      language: detectedLanguage,
      // Add additional filters based on language preferences
    };
  };

  next();
};

module.exports = languageMiddleware;