const database = require('../config/database');

/**
 * 管理员权限认证中间件
 * 检查用户是否具有管理员或超级管理员权限
 */

// 检查管理员权限的中间件
const adminAuth = async (req, res, next) => {
  try {
    // 首先检查用户是否已通过基础认证
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'Please log in to access this feature'
      });
    }

    // 检查用户角色是否为管理员或超级管理员
    const allowedRoles = ['admin', 'super_admin'];
    
    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'Administrator privileges required to access this resource'
      });
    }

    // 验证用户在数据库中的角色状态（防止token过期后角色变更）
    const user = await database.get(
      'SELECT id, role, email FROM users WHERE id = ? AND role IN (?, ?)',
      [req.user.id, 'admin', 'super_admin']
    );

    if (!user) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'Administrator privileges not found or revoked'
      });
    }

    // 更新请求对象中的用户信息
    req.user.role = user.role;
    req.user.isAdmin = true;
    req.user.isSuperAdmin = user.role === 'super_admin';

    next();
  } catch (error) {
    console.error('Admin authentication error:', error);
    return res.status(500).json({
      error: 'Server error',
      message: 'Failed to verify administrator privileges'
    });
  }
};

// 检查超级管理员权限的中间件
const superAdminAuth = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'Please log in to access this feature'
      });
    }

    // 检查用户角色是否为超级管理员
    if (req.user.role !== 'super_admin') {
      return res.status(403).json({
        error: 'Access denied',
        message: 'Super administrator privileges required to access this resource'
      });
    }

    // 验证用户在数据库中的超级管理员状态
    const user = await database.get(
      'SELECT id, role, email FROM users WHERE id = ? AND role = ?',
      [req.user.id, 'super_admin']
    );

    if (!user) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'Super administrator privileges not found or revoked'
      });
    }

    req.user.isSuperAdmin = true;
    next();
  } catch (error) {
    console.error('Super admin authentication error:', error);
    return res.status(500).json({
      error: 'Server error',
      message: 'Failed to verify super administrator privileges'
    });
  }
};

// 检查管理员或资源所有者权限的中间件
const adminOrOwnerAuth = (resourceType = 'task') => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          error: 'Authentication required',
          message: 'Please log in to access this feature'
        });
      }

      // 如果是管理员，直接通过
      if (['admin', 'super_admin'].includes(req.user.role)) {
        req.user.isAdmin = true;
        req.user.isSuperAdmin = req.user.role === 'super_admin';
        return next();
      }

      // 如果不是管理员，检查是否为资源所有者
      const resourceId = req.params.id || req.params.taskId || req.params.articleId;
      
      if (!resourceId) {
        return res.status(400).json({
          error: 'Bad request',
          message: 'Resource ID is required'
        });
      }

      let query;
      switch (resourceType) {
        case 'task':
          query = 'SELECT user_id FROM tasks WHERE id = ?';
          break;
        case 'article':
          query = 'SELECT user_id FROM blog_posts WHERE id = ?';
          break;
        case 'preset':
          query = 'SELECT user_id FROM user_presets WHERE id = ?';
          break;
        default:
          return res.status(500).json({
            error: 'Server error',
            message: 'Invalid resource type'
          });
      }

      const resource = await database.get(query, [resourceId]);
      
      if (!resource) {
        return res.status(404).json({
          error: 'Not found',
          message: `${resourceType} not found`
        });
      }

      if (resource.user_id !== req.user.id) {
        return res.status(403).json({
          error: 'Access denied',
          message: `You don't have permission to access this ${resourceType}`
        });
      }

      next();
    } catch (error) {
      console.error('Admin or owner authentication error:', error);
      return res.status(500).json({
        error: 'Server error',
        message: 'Failed to verify permissions'
      });
    }
  };
};

// 基于API密钥的管理员认证（用于某些特殊操作）
const apiKeyAdminAuth = (req, res, next) => {
  try {
    const apiKey = req.headers['x-admin-api-key'] || req.query.admin_key;
    const validApiKey = process.env.ADMIN_API_KEY;

    if (!validApiKey) {
      return res.status(500).json({
        error: 'Server configuration error',
        message: 'Admin API key not configured'
      });
    }

    if (!apiKey || apiKey !== validApiKey) {
      return res.status(401).json({
        error: 'Access denied',
        message: 'Valid admin API key required'
      });
    }

    // 设置管理员权限标识
    req.user = req.user || {};
    req.user.isAdmin = true;
    req.user.isSuperAdmin = true;
    req.user.role = 'super_admin';

    next();
  } catch (error) {
    console.error('API key admin authentication error:', error);
    return res.status(500).json({
      error: 'Server error',
      message: 'Failed to verify admin API key'
    });
  }
};

module.exports = {
  adminAuth,
  superAdminAuth,
  adminOrOwnerAuth,
  apiKeyAdminAuth
};

// 默认导出管理员认证中间件
module.exports.default = adminAuth;