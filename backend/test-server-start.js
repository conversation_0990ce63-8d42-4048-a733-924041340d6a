#!/usr/bin/env node

/**
 * 测试服务器启动是否正常
 */

// 设置环境变量
process.env.DATABASE_URL = 'postgresql://postgres:<EMAIL>:21666/railway';
process.env.NODE_ENV = 'production';
process.env.PORT = 3001;

const express = require('express');

async function testServerModules() {
  console.log('🧪 测试服务器模块加载...');

  try {
    // 测试数据库连接
    console.log('📋 测试数据库模块...');
    const Database = require('./config/database');
    await Database.connect();
    console.log('✅ 数据库模块加载成功');

    // 测试中间件
    console.log('📋 测试认证中间件...');
    const { authenticateToken } = require('./middleware/auth');
    const { adminAuth } = require('./middleware/adminAuth');
    console.log('✅ 认证中间件加载成功');

    // 测试联盟路由
    console.log('📋 测试联盟路由...');
    const affiliateRoutes = require('./routes/affiliate');
    console.log('✅ 联盟路由加载成功');

    // 测试联盟追踪服务
    console.log('📋 测试联盟追踪服务...');
    const affiliateTrackingService = require('./services/affiliateTrackingService');
    console.log('✅ 联盟追踪服务加载成功');

    console.log('\n🎉 所有模块加载成功！服务器应该能正常启动');

  } catch (error) {
    console.error('❌ 模块加载失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testServerModules()
    .then(() => {
      console.log('✅ 服务器模块测试通过');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 服务器模块测试失败:', error);
      process.exit(1);
    });
}

module.exports = { testServerModules };