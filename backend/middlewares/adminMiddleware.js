const adminMiddleware = (req, res, next) => {
    try {
        // 检查用户是否为超级管理员
        if (req.user && req.user.email === '<EMAIL>') {
            return next();
        }

        // 记录未授权访问尝试
        console.warn(`Unauthorized admin access attempt from: ${req.user?.email || 'unknown'}`);
        return res.status(403).json({ 
            error: 'Forbidden: Only super admin can access this resource' 
        });
    } catch (error) {
        console.error('Admin middleware error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};

module.exports = adminMiddleware;