/**
 * 游戏信息数据结构定义
 * 用于Game Guide文章的结构化信息存储
 */

const gameInfoSchema = {
  // 基本游戏信息
  provider: {
    type: 'string',
    required: true,
    default: 'Unknown Provider',
    description: '游戏提供商，如 Pragmatic Play, NetEnt 等'
  },
  
  rtp: {
    type: 'string', 
    required: true,
    default: '96.00%',
    pattern: /^\d{1,3}\.\d{1,2}%$/,
    description: 'RTP百分比，格式如 96.50%'
  },
  
  volatility: {
    type: 'string',
    required: true,
    default: 'Medium',
    enum: ['Low', 'Medium', 'High', 'Very High'],
    description: '波动性等级'
  },
  
  minBet: {
    type: 'string',
    required: true, 
    default: '$0.20',
    description: '最小投注金额'
  },
  
  maxBet: {
    type: 'string',
    required: true,
    default: '$100',
    description: '最大投注金额'
  },
  
  maxWin: {
    type: 'string',
    required: true,
    default: '1000x',
    description: '最大获胜倍数'
  },
  
  // 游戏特性
  reels: {
    type: 'number',
    required: false,
    default: 5,
    description: '转轴数量'
  },
  
  rows: {
    type: 'number', 
    required: false,
    default: 3,
    description: '行数'
  },
  
  paylines: {
    type: 'string',
    required: false,
    default: '25 paylines',
    description: '支付线描述'
  },
  
  bonusFeatures: {
    type: 'array',
    required: false,
    default: ['Free Spins', 'Wild Symbols'],
    description: '奖励功能列表'
  },
  
  // 游戏主题和风格
  theme: {
    type: 'string',
    required: false,
    default: 'Classic',
    description: '游戏主题'
  },
  
  gameType: {
    type: 'string',
    required: false,
    default: 'Video Slot',
    enum: ['Video Slot', 'Classic Slot', 'Jackpot Slot', 'Table Game', 'Live Casino'],
    description: '游戏类型'
  },
  
  // 评分信息
  rating: {
    type: 'number',
    required: false,
    default: 4.2,
    min: 1,
    max: 5,
    description: '游戏评分 (1-5分)'
  },
  
  // 移动端支持
  mobileOptimized: {
    type: 'boolean',
    required: false,
    default: true,
    description: '是否支持移动端'
  },
  
  // 演示模式
  demoAvailable: {
    type: 'boolean',
    required: false,
    default: true,
    description: '是否提供演示模式'
  },
  
  // 推荐赌场
  recommendedCasinos: {
    type: 'array',
    required: false,
    default: [],
    description: '推荐的赌场列表',
    items: {
      name: 'string',
      bonus: 'string',
      rating: 'number',
      playUrl: 'string'
    }
  }
};

/**
 * 生成默认的游戏信息对象
 */
function getDefaultGameInfo() {
  const defaultInfo = {};
  
  Object.keys(gameInfoSchema).forEach(key => {
    defaultInfo[key] = gameInfoSchema[key].default;
  });
  
  return defaultInfo;
}

/**
 * 验证游戏信息数据
 */
function validateGameInfo(gameInfo) {
  const errors = [];
  
  Object.keys(gameInfoSchema).forEach(key => {
    const schema = gameInfoSchema[key];
    const value = gameInfo[key];
    
    // 检查必填字段
    if (schema.required && (value === undefined || value === null || value === '')) {
      errors.push(`${key} is required`);
      return;
    }
    
    // 检查数据类型
    if (value !== undefined && value !== null) {
      const expectedType = schema.type;
      const actualType = Array.isArray(value) ? 'array' : typeof value;
      
      if (expectedType !== actualType) {
        errors.push(`${key} should be ${expectedType}, got ${actualType}`);
      }
      
      // 检查枚举值
      if (schema.enum && !schema.enum.includes(value)) {
        errors.push(`${key} should be one of: ${schema.enum.join(', ')}`);
      }
      
      // 检查数值范围
      if (schema.min !== undefined && value < schema.min) {
        errors.push(`${key} should be at least ${schema.min}`);
      }
      
      if (schema.max !== undefined && value > schema.max) {
        errors.push(`${key} should be at most ${schema.max}`);
      }
      
      // 检查正则表达式
      if (schema.pattern && !schema.pattern.test(value)) {
        errors.push(`${key} format is invalid`);
      }
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

module.exports = {
  gameInfoSchema,
  getDefaultGameInfo,
  validateGameInfo
};