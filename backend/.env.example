# Railway Environment Variables Template
# Copy this to .env for local development
# Set these variables in Railway dashboard for production

# Server Configuration
NODE_ENV=production
PORT=3001

# API Keys (Required)
GEMINI_API_KEY=your_gemini_api_key_here
SERPER_API_KEY=your_serper_api_key_here

# Database Configuration (Required)
# PostgreSQL connection string is required
# Railway provides this automatically when you add PostgreSQL service
DATABASE_URL=postgresql://username:password@hostname:port/database

# Security
JWT_SECRET=your_jwt_secret_here_minimum_32_characters
API_KEY=your_api_key_here

# CORS Origins (add your Vercel frontend URL)
ALLOWED_ORIGINS=https://your-frontend.vercel.app,http://localhost:5173

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
API_RATE_LIMIT_MAX_REQUESTS=50

# Logging
LOG_LEVEL=info
