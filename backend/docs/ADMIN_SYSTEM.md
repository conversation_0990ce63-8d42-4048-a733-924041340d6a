# Writer 777 Admin System

## Overview
The Writer 777 admin system provides a comprehensive interface for managing prompt templates, system settings, and AI model configuration. The system features role-based access control with a designated super admin.

## Access Levels

### Super Admin
- **Email**: `<EMAIL>` (<PERSON>)
- **Permissions**: Full access to all admin features
- **Authentication**: Uses existing Writer 777 account credentials
- **Capabilities**:
  - Manage all prompt templates (create, edit, delete)
  - Update system settings and AI model configuration
  - Import/export template configurations
  - Access system status and diagnostics
  - Manage other admin users (future feature)

### Regular Admin
- **Authentication**: Writer 777 account with admin role
- **Permissions**: Limited access
- **Capabilities**:
  - View and edit prompt templates
  - Basic system information access
  - Template import/export

## Authentication Flow

### Super Admin Authentication
1. **Email + Password**: `<EMAIL>` + existing account password
2. **JWT Token**: Bearer token with super admin privileges
3. **Session**: 24-hour token validity

### Regular Admin Authentication
1. **Email + Password**: Writer 777 account credentials with admin role
2. **Session**: 24-hour token validity

## Admin Panel Features

### 1. Prompt Template Management
- **View Templates**: Grid view of all active templates
- **Create Templates**: Form-based template creation
- **Edit Templates**: Full template editing with preview
- **Delete Templates**: Soft delete (deactivate)
- **Template Preview**: Real-time preview with sample data
- **Version Control**: Template versioning support

### 2. System Settings
- **AI Model Configuration**: Select and update Gemini model
- **System Status**: API key status, environment info
- **Configuration Management**: Runtime settings updates

### 3. Import/Export
- **Export Templates**: Download all templates as JSON
- **Import Templates**: Upload and import template configurations
- **Backup/Restore**: Full template backup functionality

## API Endpoints

### Authentication
```
POST /api/admin/login
- Body: { email, password }
- Returns: JWT token + user info

GET /api/admin/me
- Headers: Authorization: Bearer <token>
- Returns: Current user info and permissions
```

### Template Management
```
GET /api/admin/templates
- Returns: All active templates

GET /api/admin/templates/:id
- Returns: Specific template details

POST /api/admin/templates
- Body: Template data
- Creates: New template

PUT /api/admin/templates/:id
- Body: Updated template data
- Updates: Existing template

DELETE /api/admin/templates/:id
- Soft deletes: Template (sets inactive)

POST /api/admin/templates/preview
- Body: { template, sampleData }
- Returns: Rendered template preview
```

### System Management
```
GET /api/admin/settings
- Returns: Current system settings

PUT /api/admin/settings
- Body: { ai_model, ... }
- Updates: System configuration

GET /api/admin/templates/export
- Returns: JSON file download

POST /api/admin/templates/import
- Body: { templates, overwrite }
- Imports: Template configurations
```

## Security Features

### Authentication Security
- **JWT Tokens**: Secure token-based authentication
- **Role-based Access**: Super admin vs regular admin permissions
- **Session Management**: 24-hour token expiration
- **Email Verification**: Super admin email validation

### API Security
- **Header Authentication**: Bearer token + email headers
- **Request Validation**: Input sanitization and validation
- **Rate Limiting**: API rate limiting applied
- **Error Handling**: Secure error messages

### Data Protection
- **Soft Deletes**: Templates are deactivated, not deleted
- **Audit Trail**: Template change tracking
- **Backup Support**: Export/import for data safety

## Environment Variables

```bash
# Required for admin system
JWT_SECRET=your_jwt_secret_minimum_32_characters

# Optional for enhanced security
NODE_ENV=production
```

## Usage Instructions

### Initial Setup
1. Ensure `<EMAIL>` user exists in the system
2. Set `JWT_SECRET` for token signing
3. Access admin panel at `/admin.html`
4. Login with `<EMAIL>` + existing password

### Template Management
1. **Create Template**: Click "Create New Template"
2. **Edit Template**: Click on any template card
3. **Preview**: Use preview button to test template
4. **Save**: Submit form to save changes

### System Configuration
1. Navigate to "System Settings" tab
2. Select desired AI model
3. Click "Update Settings"
4. Restart server for changes to take effect

### Backup/Restore
1. **Export**: Go to Import/Export tab, click "Export All Templates"
2. **Import**: Upload JSON file, choose overwrite option
3. **Verify**: Check templates after import

## File Structure

```
backend/
├── routes/
│   └── admin.js              # Admin API endpoints
├── docs/
│   └── ADMIN_SYSTEM.md       # This documentation
└── .env.example              # Environment variables template

frontend/
├── admin.html                # Admin panel interface
└── js/
    └── admin.js              # Admin panel JavaScript
```

## Troubleshooting

### Common Issues
1. **Login Failed**: Check email and password
2. **Token Expired**: Re-login to get new token
3. **Template Save Failed**: Check required fields
4. **Import Failed**: Verify JSON file format
5. **Access Denied**: Ensure user has admin privileges

### Debug Information
- Check browser console for JavaScript errors
- Check server logs for API errors
- Verify environment variables are set
- Confirm database connectivity

## Future Enhancements
- Multi-user admin management
- Template approval workflow
- Advanced role permissions
- Activity logging and audit trails
- Template collaboration features
