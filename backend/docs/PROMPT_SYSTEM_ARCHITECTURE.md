# Writer 777 Prompt System Architecture

## Overview
The Writer 777 prompt system has been **completely consolidated** to use a single, clear source of truth: **database templates only**. All hardcoded prompts have been removed and moved to external configuration.

## System Architecture

### 1. **Single Source of Truth: Database Templates**
- **Primary Source**: `prompt_templates` table in the database
- **Configuration**: `backend/config/defaultTemplates.js` (for initial seeding only)
- **Service**: `backend/services/promptTemplateService.js` (clean, no hardcoded prompts)

### 2. **Template Types**
The system supports 7 content types:
- `casino_review` - Casino review articles
- `game_guide` - Game instruction guides  
- `strategy_article` - Strategy and analysis content
- `brand_copy` - Marketing and brand content
- `industry_news` - Industry news and updates
- `sports_betting` - Sports betting analysis
- `generic` - Fallback for any other content type

### 3. **Prompt Generation Flow**

```
Article Generation Request
         ↓
1. Try specific content type template (e.g., 'casino_review')
         ↓
2. If not found, try 'generic' template
         ↓
3. If still not found, use generic template from configuration
         ↓
4. If configuration missing, throw error (system misconfiguration)
         ↓
Generate final prompt with dynamic content
```

### 4. **Key Components**

#### **A. promptTemplateService.js** (Clean Version)
- **No hardcoded prompts** - only uses database and config
- **Dynamic prompt building** with placeholder replacement
- **Smart fallback logic** for missing templates
- **Template management** (CRUD operations)

#### **B. geminiService.js** (Simplified)
- **Removed all hardcoded fallback prompts** (was 400+ lines)
- **Single prompt generation path** through promptTemplateService
- **Clean, focused on AI interaction** only

#### **C. defaultTemplates.js** (Configuration)
- **External configuration** for default templates
- **Used only for database seeding** during initialization
- **Shared constants** for safety directives and compliance rules

### 5. **Template Structure**
Each template contains:
```javascript
{
  content_type: 'casino_review',
  template_name: 'Casino Review Template',
  template_version: '1.0',
  persona_description: 'AI personality/role description',
  specialized_instructions: 'Content-specific guidance',
  prompt_content: 'Main template with {{PLACEHOLDERS}}',
  compliance_rules: ['safety rule 1', 'safety rule 2'],
  is_active: true
}
```

### 6. **Dynamic Placeholder System**
Templates use placeholders that get replaced with dynamic content:
- `{{PERSONA_DESCRIPTION}}` - AI role/personality
- `{{SPECIALIZED_INSTRUCTIONS}}` - Content-specific guidance
- `{{TOPICS_SECTION}}` - Article topics to cover
- `{{KEYWORDS_SECTION}}` - SEO keywords
- `{{SOURCES_SECTION}}` - Reference sources
- `{{COMPLIANCE_SECTION}}` - Legal/compliance requirements
- `{{PARAMETERS_SECTION}}` - Article parameters (length, tone, etc.)
- `{{JURISDICTION}}` - Legal jurisdiction
- `{{CONTENT_TYPE}}` - Type of content being generated

### 7. **Benefits of New System**

#### **✅ Clarity**
- **Single prompt source** - no confusion about which prompt is used
- **Clear flow** - easy to trace prompt generation logic
- **Consistent structure** - all templates follow same format

#### **✅ Maintainability**
- **Database-driven** - edit templates without code changes
- **Version control** - track template changes over time
- **Modular design** - separate concerns cleanly

#### **✅ Scalability**
- **Easy to add new content types** - just add to database
- **Template inheritance** - shared safety directives and structure
- **API-ready** - can build template management UI

#### **✅ Compliance**
- **Centralized safety directives** - consistent across all templates
- **Audit trail** - track template changes for compliance
- **Jurisdiction support** - templates can be customized per region

### 8. **File Structure**
```
backend/
├── config/
│   └── defaultTemplates.js          # Template configuration (seeding only)
├── services/
│   ├── promptTemplateService.js     # Clean template service (no hardcoded prompts)
│   └── geminiService.js             # Simplified AI service
└── docs/
    └── PROMPT_SYSTEM_ARCHITECTURE.md # This documentation
```

### 9. **Migration Summary**

#### **Before (Confusing)**
- ❌ Hardcoded prompts in `geminiService.js` (400+ lines)
- ❌ Hardcoded prompts in `promptTemplateService.js` (400+ lines)  
- ❌ Duplicate safety directives everywhere
- ❌ Complex fallback chains
- ❌ Unclear which prompt was being used

#### **After (Clean)**
- ✅ **Single source**: Database templates only
- ✅ **External config**: `defaultTemplates.js` for seeding and final fallback
- ✅ **Clean services**: Zero hardcoded prompts in code
- ✅ **Clear logging**: Shows which template is used
- ✅ **Simple fallback**: specific → generic → config fallback → error

### 10. **Usage Examples**

#### **Adding a New Content Type**
1. Add template to `defaultTemplates.js`
2. Restart server (auto-seeds database)
3. Use new content type in API calls

#### **Editing Templates**
1. Update database directly, OR
2. Build template management UI, OR  
3. Update `defaultTemplates.js` and re-seed

#### **Debugging Prompts**
Check server logs for:
```
Building prompt for content type: casino_review
Using template: Casino Review Template (casino_review)
✓ Dynamic prompt built successfully
```

## Conclusion
The prompt system is now **crystal clear**, **maintainable**, and **scalable**. All confusion has been eliminated by using a single source of truth: database templates with smart fallback logic.
