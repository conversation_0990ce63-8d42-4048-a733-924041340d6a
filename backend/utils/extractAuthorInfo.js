/**
 * Extract real author information from article content or game_info
 * Backend version of the author extraction utility
 */

/**
 * Extract author from E-E-A-T configuration in game_info
 * @param {Object|string} gameInfo - The game_info object or JSON string
 * @returns {string|null} - Extracted author name or null
 */
function extractAuthorFromGameInfo(gameInfo) {
  if (!gameInfo) return null;
  
  try {
    // Parse JSON if it's a string
    const info = typeof gameInfo === 'string' ? JSON.parse(gameInfo) : gameInfo;
    
    // Check E-E-A-T configuration
    if (info.eeat && info.eeat.author) {
      const author = info.eeat.author;
      
      // If author is an object with name field
      if (typeof author === 'object' && author.name) {
        return author.name;
      }
      
      // If author is a string
      if (typeof author === 'string') {
        return author;
      }
    }
    
    // Check for author in seo configuration
    if (info.seo && info.seo.author) {
      return info.seo.author;
    }
    
  } catch (error) {
    console.log('Error extracting author from game_info:', error);
  }
  
  return null;
}

/**
 * Extract author from article content (look for "About the Author" sections)
 * @param {string} content - Article HTML content
 * @returns {string|null} - Extracted author name or null
 */
function extractAuthorFromContent(content) {
  if (!content) return null;
  
  try {
    // Split content into lines to work line by line
    const lines = content.split(/\n/);
    
    // Look for author header patterns first
    let inAuthorSection = false;
    let authorSectionStart = -1;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Check if this line is an author section header (with or without HTML tags)
      const cleanHeaderLine = line.replace(/<[^>]*>/g, '').trim();
      if (/(?:About the Author|Sobre o Autor|关于作者|作者简介)/i.test(cleanHeaderLine)) {
        inAuthorSection = true;
        authorSectionStart = i;
        continue;
      }
      
      // If we're in author section, look for the name
      if (inAuthorSection && line.length > 0) {
        // Skip HTML tags and get clean text
        const cleanLine = line.replace(/<[^>]*>/g, '').trim();
        
        if (!cleanLine) continue;
        
        // Chinese pattern: "李明是一位..."
        const chineseMatch = cleanLine.match(/([\u4e00-\u9fff]{2,5})(?:是|为)/);
        if (chineseMatch) {
          return chineseMatch[1];
        }
        
        // Western pattern: "Maria Santos is a..." or "Alex Chen é um..."
        const westernMatch = cleanLine.match(/^([A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ]+(?:\s+[A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ]+)*)\s+(?:is|é|has|tem|specializes|se especializa)/i);
        if (westernMatch) {
          return westernMatch[1];
        }
        
        // Simple name pattern after author header (just first line with name)
        const nameMatch = cleanLine.match(/^([A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ]+(?:\s+[A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ]+)+)/);
        if (nameMatch && !nameMatch[1].toLowerCase().includes('writer') && !nameMatch[1].toLowerCase().includes('ai')) {
          return nameMatch[1];
        }
        
        // Stop at next section or after checking a few lines
        if (line.startsWith('#') || (i > authorSectionStart + 5)) {
          break;
        }
      }
    }
    
    // If no author section found, look for direct author attribution patterns
    const directPatterns = [
      // "作者: 张伟"
      /(?:Author|作者)[:\s]+([A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+(?:\s+[A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+)*)/i,
      
      // "By: John Smith"
      /(?:By|Escrito por)[:\s]+([A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+(?:\s+[A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+)*)/i,
      
      // "Written by John Smith"
      /Written by\s+([A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+(?:\s+[A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+)*)/i,
    ];
    
    for (const pattern of directPatterns) {
      const match = content.match(pattern);
      if (match && match[1]) {
        const authorName = match[1].trim();
        // Skip generic names and ensure reasonable length
        if (!authorName.toLowerCase().includes('writer') && 
            !authorName.toLowerCase().includes('ai') &&
            authorName.length >= 2 &&  // Changed to >= for Chinese names
            authorName.length < 30) { // More conservative max length
          return authorName;
        }
      }
    }
    
  } catch (error) {
    console.log('Error extracting author from content:', error);
  }
  
  return null;
}

/**
 * Extract author from E-E-A-T profile configuration
 * @param {Object} eeatProfile - The E-E-A-T profile object
 * @returns {string|null} - Extracted author name or null
 */
function extractAuthorFromEEAT(eeatProfile) {
  if (!eeatProfile) return null;
  
  try {
    // Check for author_name field (primary)
    if (eeatProfile.author_name && typeof eeatProfile.author_name === 'string') {
      return eeatProfile.author_name.trim();
    }
    
    // Check for nested author object
    if (eeatProfile.author) {
      if (typeof eeatProfile.author === 'string') {
        return eeatProfile.author.trim();
      }
      if (typeof eeatProfile.author === 'object' && eeatProfile.author.name) {
        return eeatProfile.author.name.trim();
      }
    }
    
  } catch (error) {
    console.log('Error extracting author from E-E-A-T:', error);
  }
  
  return null;
}

/**
 * Extract author information with priority logic
 * @param {Object} eeatProfile - E-E-A-T profile object
 * @param {string} content - Article content
 * @returns {Object} - Author info object with name and source
 */
function extractAuthorInfo(eeatProfile, content) {
  // Priority 1: E-E-A-T profile
  const eeeatAuthor = extractAuthorFromEEAT(eeatProfile);
  if (eeeatAuthor) {
    return {
      name: eeeatAuthor,
      source: 'eeat',
      title: eeatProfile.author_title || null,
      bio: eeatProfile.author_bio || null
    };
  }
  
  // Priority 2: Content extraction
  const contentAuthor = extractAuthorFromContent(content);
  if (contentAuthor) {
    return {
      name: contentAuthor,
      source: 'content',
      title: null,
      bio: null
    };
  }
  
  // Fallback: Default author
  return {
    name: 'Writer 777',
    source: 'default',
    title: null,
    bio: null
  };
}

/**
 * Format author info for database storage
 * @param {Object|string} authorInfo - Author info object or string
 * @returns {string} - Formatted author name for database
 */
function formatAuthorForDatabase(authorInfo) {
  if (!authorInfo) return 'Writer 777';
  
  // If it's already a string, return it
  if (typeof authorInfo === 'string') {
    return authorInfo.trim() || 'Writer 777';
  }
  
  // If it's an object, extract the name
  if (typeof authorInfo === 'object' && authorInfo.name) {
    return authorInfo.name.trim() || 'Writer 777';
  }
  
  return 'Writer 777';
}

/**
 * Get proper author name with fallback logic
 * @param {Object} article - Article object with author, content, game_info
 * @returns {string} - Best available author name
 */
function getProperAuthor(article) {
  // Check if current author is already good (not generic)
  if (article.author && 
      !article.author.toLowerCase().includes('writer') &&
      !article.author.toLowerCase().includes('ai') &&
      article.author.trim() !== '') {
    return article.author;
  }
  
  // Try to extract from game_info
  if (article.game_info) {
    const gameInfoAuthor = extractAuthorFromGameInfo(article.game_info);
    if (gameInfoAuthor) {
      return gameInfoAuthor;
    }
  }
  
  // Try to extract from content
  const contentAuthor = extractAuthorFromContent(article.content);
  if (contentAuthor) {
    return contentAuthor;
  }
  
  // Fallback to current author or default
  return article.author || 'Writer 777';
}

module.exports = {
  extractAuthorFromGameInfo,
  extractAuthorFromContent,
  extractAuthorFromEEAT,
  extractAuthorInfo,
  formatAuthorForDatabase,
  getProperAuthor
};