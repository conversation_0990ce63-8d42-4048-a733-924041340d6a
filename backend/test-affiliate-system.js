#!/usr/bin/env node

/**
 * 测试联盟营销系统功能
 */

// 设置环境变量
process.env.DATABASE_URL = 'postgresql://postgres:<EMAIL>:21666/railway';
process.env.NODE_ENV = 'production';

const Database = require('./config/database');
const affiliateTrackingService = require('./services/affiliateTrackingService');

async function testAffiliateSystem() {
  console.log('🧪 测试联盟营销系统...');

  try {
    await Database.connect();
    console.log('✅ 数据库连接成功');

    // 测试 1: 检查数据库表是否存在
    console.log('\n📋 测试 1: 检查数据库表...');
    
    const tables = ['affiliate_links', 'article_affiliate_links', 'affiliate_clicks'];
    for (const tableName of tables) {
      const result = await Database.get(`
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_name = ?
      `, [tableName]);
      
      if (result.count > 0) {
        console.log(`✅ 表 ${tableName} 存在`);
      } else {
        console.log(`❌ 表 ${tableName} 不存在`);
      }
    }

    // 测试 2: 检查示例数据
    console.log('\n📊 测试 2: 检查示例数据...');
    
    const linksCount = await Database.get('SELECT COUNT(*) as count FROM affiliate_links');
    console.log(`✅ 联盟链接数量: ${linksCount.count}`);

    if (linksCount.count > 0) {
      const sampleLink = await Database.get('SELECT * FROM affiliate_links LIMIT 1');
      console.log(`✅ 示例链接: ${sampleLink.name} - ${sampleLink.display_text}`);
    }

    // 测试 3: 测试推荐服务
    console.log('\n🎯 测试 3: 测试推荐服务...');
    
    const criteria = {
      content_type: 'casino_review',
      language: 'pt',
      country: 'BR',
      limit: 2
    };

    const recommendations = await affiliateTrackingService.getRecommendedLinks(criteria);
    console.log(`✅ 推荐链接数量: ${recommendations.length}`);
    
    if (recommendations.length > 0) {
      console.log(`✅ 第一个推荐: ${recommendations[0].name}`);
    }

    // 测试 4: 测试点击追踪
    console.log('\n📈 测试 4: 测试点击追踪...');
    
    if (recommendations.length > 0) {
      const clickData = {
        affiliate_link_id: recommendations[0].id,
        article_id: null, // 设置为null，因为我们在测试环境中没有真实的文章ID
        article_url: 'https://example.com/test-article',
        article_title: '测试文章',
        click_position: 'primary_cta',
        user_ip: '127.0.0.1',
        user_agent: 'Test Agent',
        referer: 'https://example.com',
        session_id: 'test_session_' + Date.now()
      };

      const trackResult = await affiliateTrackingService.trackClick(clickData);
      console.log(`✅ 点击追踪成功: ID ${trackResult.clickId}`);
    }

    // 测试 5: 检查统计功能
    console.log('\n📊 测试 5: 检查统计功能...');
    
    if (recommendations.length > 0) {
      const analytics = await affiliateTrackingService.getPerformanceAnalytics(
        recommendations[0].id, 
        '30d'
      );
      console.log(`✅ 统计数据获取成功: ${analytics.basicStats.total_clicks} 总点击`);
    }

    console.log('\n🎉 所有测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testAffiliateSystem()
    .then(() => {
      console.log('✅ 联盟营销系统测试通过');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 测试失败:', error);
      process.exit(1);
    });
}

module.exports = { testAffiliateSystem };