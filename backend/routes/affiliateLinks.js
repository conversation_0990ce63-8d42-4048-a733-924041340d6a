const express = require('express');
const router = express.Router();
const database = require('../config/database');
const { validateApiKey } = require('../middleware/security');

// GET /api/affiliate-links - Get all affiliate links with optional filtering
router.get('/', async (req, res) => {
  try {
    const { 
      contentType, 
      category, 
      active = 'true',
      page = 1, 
      limit = 20 
    } = req.query;
    
    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    let query = `
      SELECT 
        id,
        name,
        description,
        affiliate_url,
        display_text,
        content_type,
        category,
        commission_rate,
        tracking_code,
        click_count,
        conversion_count,
        is_active,
        created_at,
        updated_at
      FROM affiliate_links
      WHERE 1=1
    `;
    
    const params = [];
    
    // Add filters
    if (contentType) {
      query += ' AND content_type = ?';
      params.push(contentType);
    }
    
    if (category) {
      query += ' AND category = ?';
      params.push(category);
    }
    
    if (active !== 'all') {
      query += ' AND is_active = ?';
      params.push(active === 'true');
    }
    
    // Add sorting and pagination
    query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), offset);
    
    const links = await database.all(query, params);
    
    // Get total count for pagination
    let countQuery = 'SELECT COUNT(*) as total FROM affiliate_links WHERE 1=1';
    const countParams = [];
    
    if (contentType) {
      countQuery += ' AND content_type = ?';
      countParams.push(contentType);
    }
    
    if (category) {
      countQuery += ' AND category = ?';
      countParams.push(category);
    }
    
    if (active !== 'all') {
      countQuery += ' AND is_active = ?';
      countParams.push(active === 'true');
    }
    
    const countResult = await database.get(countQuery, countParams);
    
    res.json({
      success: true,
      links,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(countResult.total / parseInt(limit)),
        totalLinks: countResult.total,
        hasMore: countResult.total > offset + parseInt(limit),
        limit: parseInt(limit)
      }
    });
    
  } catch (error) {
    console.error('Error fetching affiliate links:', error);
    res.status(500).json({
      error: 'Failed to fetch affiliate links',
      message: error.message
    });
  }
});

// GET /api/affiliate-links/:id - Get single affiliate link
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const link = await database.get(`
      SELECT * FROM affiliate_links WHERE id = ?
    `, [id]);
    
    if (!link) {
      return res.status(404).json({
        error: 'Affiliate link not found'
      });
    }
    
    res.json({
      success: true,
      link
    });
    
  } catch (error) {
    console.error('Error fetching affiliate link:', error);
    res.status(500).json({
      error: 'Failed to fetch affiliate link',
      message: error.message
    });
  }
});

// POST /api/affiliate-links - Create new affiliate link (requires API key)
router.post('/', validateApiKey, async (req, res) => {
  try {
    const {
      name,
      description,
      affiliate_url,
      display_text,
      content_type,
      category,
      commission_rate,
      tracking_code
    } = req.body;
    
    // Validation
    if (!name || !affiliate_url || !display_text) {
      return res.status(400).json({
        error: 'Missing required fields: name, affiliate_url, display_text'
      });
    }
    
    const result = await database.run(`
      INSERT INTO affiliate_links (
        name, description, affiliate_url, display_text, 
        content_type, category, commission_rate, tracking_code
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      name, description, affiliate_url, display_text,
      content_type, category, commission_rate, tracking_code
    ]);
    
    res.status(201).json({
      success: true,
      message: 'Affiliate link created successfully',
      linkId: result.id
    });
    
  } catch (error) {
    console.error('Error creating affiliate link:', error);
    res.status(500).json({
      error: 'Failed to create affiliate link',
      message: error.message
    });
  }
});

// PUT /api/affiliate-links/:id - Update affiliate link (requires API key)
router.put('/:id', validateApiKey, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      affiliate_url,
      display_text,
      content_type,
      category,
      commission_rate,
      tracking_code,
      is_active
    } = req.body;
    
    // Check if link exists
    const existingLink = await database.get('SELECT id FROM affiliate_links WHERE id = ?', [id]);
    if (!existingLink) {
      return res.status(404).json({
        error: 'Affiliate link not found'
      });
    }
    
    await database.run(`
      UPDATE affiliate_links SET
        name = ?, description = ?, affiliate_url = ?, display_text = ?,
        content_type = ?, category = ?, commission_rate = ?, tracking_code = ?,
        is_active = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      name, description, affiliate_url, display_text,
      content_type, category, commission_rate, tracking_code,
      is_active, id
    ]);
    
    res.json({
      success: true,
      message: 'Affiliate link updated successfully'
    });
    
  } catch (error) {
    console.error('Error updating affiliate link:', error);
    res.status(500).json({
      error: 'Failed to update affiliate link',
      message: error.message
    });
  }
});

// DELETE /api/affiliate-links/:id - Delete affiliate link (requires API key)
router.delete('/:id', validateApiKey, async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await database.run('DELETE FROM affiliate_links WHERE id = ?', [id]);
    
    if (result.changes === 0) {
      return res.status(404).json({
        error: 'Affiliate link not found'
      });
    }
    
    res.json({
      success: true,
      message: 'Affiliate link deleted successfully'
    });
    
  } catch (error) {
    console.error('Error deleting affiliate link:', error);
    res.status(500).json({
      error: 'Failed to delete affiliate link',
      message: error.message
    });
  }
});

// POST /api/affiliate-links/:id/track-click - Track affiliate link click
router.post('/:id/track-click', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Update click count
    await database.run(`
      UPDATE affiliate_links 
      SET click_count = click_count + 1, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [id]);
    
    // Get the affiliate URL for redirect
    const link = await database.get('SELECT affiliate_url FROM affiliate_links WHERE id = ?', [id]);
    
    if (!link) {
      return res.status(404).json({
        error: 'Affiliate link not found'
      });
    }
    
    res.json({
      success: true,
      redirect_url: link.affiliate_url
    });
    
  } catch (error) {
    console.error('Error tracking affiliate click:', error);
    res.status(500).json({
      error: 'Failed to track click',
      message: error.message
    });
  }
});

// GET /api/affiliate-links/by-content-type/:contentType - Get links for specific content type
router.get('/by-content-type/:contentType', async (req, res) => {
  try {
    const { contentType } = req.params;
    const { limit = 10 } = req.query;
    
    const links = await database.all(`
      SELECT 
        id, name, description, display_text, affiliate_url,
        category, commission_rate, click_count
      FROM affiliate_links 
      WHERE content_type = ? AND is_active = true
      ORDER BY click_count DESC, created_at DESC
      LIMIT ?
    `, [contentType, parseInt(limit)]);
    
    res.json({
      success: true,
      links,
      contentType
    });
    
  } catch (error) {
    console.error('Error fetching content type links:', error);
    res.status(500).json({
      error: 'Failed to fetch content type links',
      message: error.message
    });
  }
});

module.exports = router;