const express = require('express');
const router = express.Router();
const database = require('../config/database');

// Debug route to check articles and their slugs
router.get('/articles', async (req, res) => {
  try {
    const articles = await database.all(`
      SELECT 
        id,
        title,
        slug,
        content_type,
        status,
        LENGTH(content) as content_length,
        published_at
      FROM blog_posts 
      ORDER BY id
    `);
    
    res.json({
      success: true,
      total: articles.length,
      articles: articles.map(article => ({
        ...article,
        content_length: article.content_length + ' chars'
      }))
    });
    
  } catch (error) {
    console.error('Error debugging articles:', error);
    res.status(500).json({
      error: 'Failed to debug articles',
      message: error.message
    });
  }
});

// Debug route to check a specific slug
router.get('/slug/:slug', async (req, res) => {
  try {
    const { slug } = req.params;
    
    const article = await database.get(`
      SELECT 
        id,
        title,
        slug,
        content_type,
        status,
        LENGTH(content) as content_length,
        SUBSTR(content, 1, 200) as content_preview,
        published_at
      FROM blog_posts 
      WHERE slug = ?
    `, [slug]);
    
    if (!article) {
      return res.status(404).json({
        error: 'Article not found',
        slug_searched: slug
      });
    }
    
    res.json({
      success: true,
      article: {
        ...article,
        content_length: article.content_length + ' chars'
      }
    });
    
  } catch (error) {
    console.error('Error debugging slug:', error);
    res.status(500).json({
      error: 'Failed to debug slug',
      message: error.message
    });
  }
});

module.exports = router;