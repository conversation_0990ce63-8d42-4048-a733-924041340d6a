const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Create uploads directory if it doesn't exist
// In Railway, the volume is mounted at /data/uploads (absolute path)
const uploadsDir = process.env.NODE_ENV === 'production' ? '/data/uploads' : path.join(__dirname, '../data/uploads');
try {
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
    console.log('Created uploads directory:', uploadsDir);
  }
} catch (error) {
  console.error('Failed to create uploads directory:', error);
  // Fallback to a temporary directory or use /tmp
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename with timestamp
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    cb(null, 'image-' + uniqueSuffix + extension);
  }
});

// File filter to only allow images
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed (JPEG, PNG, GIF, WebP)'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  }
});

// Upload image endpoint
router.post('/image', authenticateToken, upload.single('image'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No file uploaded'
      });
    }

    // Return the file URL
    const fileUrl = `/uploads/${req.file.filename}`;
    
    res.json({
      success: true,
      fileUrl: fileUrl,
      fileName: req.file.filename,
      originalName: req.file.originalname,
      size: req.file.size
    });
  } catch (error) {
    console.error('File upload error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to upload file'
    });
  }
});

// Error handling middleware for multer
router.use((err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        error: 'File too large. Maximum size is 5MB.'
      });
    }
  }
  
  if (err.message === 'Only image files are allowed (JPEG, PNG, GIF, WebP)') {
    return res.status(400).json({
      success: false,
      error: err.message
    });
  }
  
  res.status(500).json({
    success: false,
    error: 'Upload failed'
  });
});

// Debug endpoint to list uploaded files
router.get('/debug', authenticateToken, (req, res) => {
  try {
    console.log('Debug: Checking uploads directory:', uploadsDir);
    
    if (!fs.existsSync(uploadsDir)) {
      return res.json({
        success: false,
        uploadsDir: uploadsDir,
        exists: false,
        message: 'Uploads directory does not exist'
      });
    }
    
    const files = fs.readdirSync(uploadsDir);
    const fileDetails = files.map(file => {
      const filePath = path.join(uploadsDir, file);
      const stats = fs.statSync(filePath);
      return {
        name: file,
        size: stats.size,
        created: stats.birthtime,
        isFile: stats.isFile()
      };
    });
    
    res.json({
      success: true,
      uploadsDir: uploadsDir,
      exists: true,
      fileCount: files.length,
      files: fileDetails,
      nodeEnv: process.env.NODE_ENV
    });
  } catch (error) {
    console.error('Debug error:', error);
    res.json({
      success: false,
      error: error.message,
      uploadsDir: uploadsDir
    });
  }
});

module.exports = router;