const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const database = require('../config/database');

// All routes require authentication
router.use(authenticateToken);

// GET /api/content/types - Get all content types with statistics
router.get('/types', async (req, res) => {
  try {
    // Use hardcoded content types since we moved to code-based prompt management
    const defaultContentTypes = [
      { content_type: 'casino_review' },
      { content_type: 'game_guide' },
      { content_type: 'strategy_article' },
      { content_type: 'brand_copy' },
      { content_type: 'industry_news' },
      { content_type: 'sports_betting' },
      { content_type: 'bonus_analysis' },
      { content_type: 'regulatory_update' },
      { content_type: 'generic' }
    ];
    
    // Get task statistics by content type
    const taskStatsQuery = `
      SELECT 
        content_type,
        COUNT(*) as total_tasks,
        SUM(CASE WHEN status LIKE '%Completed%' THEN 1 ELSE 0 END) as completed_tasks
      FROM tasks 
      WHERE user_id = ? AND content_type IS NOT NULL
      GROUP BY content_type
    `;
    
    const taskStats = await database.all(taskStatsQuery, [req.user.id]);
    
    // Combine content types with statistics
    const contentTypesWithStats = defaultContentTypes.map(type => {
      const stats = taskStats.find(stat => stat.content_type === type.content_type) || {
        total_tasks: 0,
        completed_tasks: 0
      };
      
      return {
        content_type: type.content_type,
        template_count: 1, // All types have one template (code-based)
        total_tasks: stats.total_tasks,
        completed_tasks: stats.completed_tasks
      };
    });
    
    res.json({
      success: true,
      contentTypes: contentTypesWithStats
    });
  } catch (error) {
    console.error('Error fetching content types:', error);
    res.status(500).json({
      error: 'Failed to fetch content types',
      message: error.message
    });
  }
});

// GET /api/content/categories - Get content organized by categories
router.get('/categories', async (req, res) => {
  try {
    const { status, limit = 50 } = req.query;
    
    let query = `
      SELECT 
        t.id,
        t.name,
        t.status,
        t.content_type,
        t.current_step,
        t.created_at,
        t.updated_at,
        CASE WHEN t.generated_article IS NOT NULL THEN 1 ELSE 0 END as has_article
      FROM tasks t
      WHERE t.user_id = ?
    `;
    
    const params = [req.user.id];
    
    if (status) {
      query += ' AND t.status = ?';
      params.push(status);
    }
    
    query += ' ORDER BY t.updated_at DESC LIMIT ?';
    params.push(parseInt(limit));
    
    const tasks = await database.all(query, params);
    
    // Group tasks by content type
    const categorizedContent = {};
    
    tasks.forEach(task => {
      const contentType = task.content_type || 'uncategorized';
      if (!categorizedContent[contentType]) {
        categorizedContent[contentType] = [];
      }
      categorizedContent[contentType].push(task);
    });
    
    res.json({
      success: true,
      categories: categorizedContent,
      totalTasks: tasks.length
    });
  } catch (error) {
    console.error('Error fetching categorized content:', error);
    res.status(500).json({
      error: 'Failed to fetch categorized content',
      message: error.message
    });
  }
});

// GET /api/content/category/:contentType - Get content for specific category
router.get('/category/:contentType', async (req, res) => {
  const startTime = Date.now();
  try {
    const { contentType } = req.params;
    const { status, limit = 20, offset = 0 } = req.query;
    
    console.log(`[Category API] 查询开始 - 用户ID: ${req.user.id}, 内容类型: ${contentType}`);
    
    let query = `
      SELECT 
        t.id,
        t.name,
        t.status,
        t.content_type,
        t.current_step,
        t.created_at,
        t.updated_at,
        CASE WHEN t.generated_article IS NOT NULL THEN 1 ELSE 0 END as has_article
      FROM tasks t
      WHERE t.user_id = ? AND t.content_type = ?
    `;
    
    const params = [req.user.id, contentType];
    
    if (status) {
      query += ' AND t.status = ?';
      params.push(status);
    }
    
    query += ' ORDER BY t.updated_at DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));
    
    console.log(`[Category API] 执行查询: ${query}`);
    console.log(`[Category API] 参数: ${JSON.stringify(params)}`);
    
    const queryStartTime = Date.now();
    const tasks = await database.all(query, params);
    const queryTime = Date.now() - queryStartTime;
    
    console.log(`[Category API] 主查询完成，耗时: ${queryTime}ms，结果数量: ${tasks.length}`);
    
    // 简化的计数查询 - 如果没有任务就直接返回0
    let total = 0;
    if (tasks.length > 0 || parseInt(offset) > 0) {
      const countStartTime = Date.now();
      let countQuery = `SELECT COUNT(*) as total FROM tasks WHERE user_id = ? AND content_type = ?`;
      const countParams = [req.user.id, contentType];
      
      if (status) {
        countQuery += ' AND status = ?';
        countParams.push(status);
      }
      
      const countResult = await database.get(countQuery, countParams);
      total = countResult.total;
      const countTime = Date.now() - countStartTime;
      console.log(`[Category API] 计数查询完成，耗时: ${countTime}ms，总数: ${total}`);
    }
    
    const totalTime = Date.now() - startTime;
    console.log(`[Category API] 请求完成，总耗时: ${totalTime}ms`);
    
    res.json({
      success: true,
      tasks,
      pagination: {
        total: total,
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: total > parseInt(offset) + parseInt(limit)
      }
    });
  } catch (error) {
    const totalTime = Date.now() - startTime;
    console.error(`[Category API] 错误，耗时: ${totalTime}ms，错误:`, error);
    res.status(500).json({
      error: 'Failed to fetch category content',
      message: error.message
    });
  }
});

// GET /api/content/stats - Get content statistics
router.get('/stats', async (req, res) => {
  try {
    // Overall stats
    const overallStatsQuery = `
      SELECT 
        COUNT(*) as total_tasks,
        SUM(CASE WHEN status LIKE '%Completed%' THEN 1 ELSE 0 END) as completed_tasks,
        SUM(CASE WHEN status LIKE '%Generating%' THEN 1 ELSE 0 END) as generating_tasks,
        SUM(CASE WHEN status LIKE '%Draft%' THEN 1 ELSE 0 END) as draft_tasks
      FROM tasks 
      WHERE user_id = ?
    `;
    
    const overallStats = await database.get(overallStatsQuery, [req.user.id]);
    
    // Content type breakdown
    const contentTypeStatsQuery = `
      SELECT 
        content_type,
        COUNT(*) as count,
        SUM(CASE WHEN status LIKE '%Completed%' THEN 1 ELSE 0 END) as completed
      FROM tasks 
      WHERE user_id = ? AND content_type IS NOT NULL
      GROUP BY content_type
      ORDER BY count DESC
    `;
    
    const contentTypeStats = await database.all(contentTypeStatsQuery, [req.user.id]);
    
    res.json({
      success: true,
      overall: overallStats,
      byContentType: contentTypeStats
    });
  } catch (error) {
    console.error('Error fetching content stats:', error);
    res.status(500).json({
      error: 'Failed to fetch content statistics',
      message: error.message
    });
  }
});

module.exports = router;
