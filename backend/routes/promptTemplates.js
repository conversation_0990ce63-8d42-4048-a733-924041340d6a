const express = require('express');
const router = express.Router();
const promptTemplateService = require('../services/promptTemplateService');
const adminMiddleware = require('../middlewares/adminMiddleware'); // 导入管理员中间件

// Initialize default templates (admin only) - 现在为兼容性而保留
router.post('/initialize', adminMiddleware, async (req, res) => {
  try {
    await promptTemplateService.initializeDefaultTemplates();
    res.json({
      message: 'Prompt templates are now managed in code. No database initialization needed.',
      notice: 'Templates are loaded from backend/config/promptTemplates.js'
    });
  } catch (error) {
    console.error('Error with template initialization:', error);
    res.status(500).json({
      error: 'Template system error',
      message: error.message
    });
  }
});

// Get all active templates
router.get('/', async (req, res) => {
  try {
    const templates = await promptTemplateService.getAllActiveTemplates();
    res.json({
      templates,
      count: templates.length
    });
  } catch (error) {
    console.error('Error getting templates:', error);
    res.status(500).json({
      error: 'Failed to get templates',
      message: error.message
    });
  }
});

// Get template by content type
router.get('/content-type/:contentType', async (req, res) => {
  try {
    const { contentType } = req.params;
    const template = await promptTemplateService.getTemplateByContentType(contentType);
    
    if (!template) {
      return res.status(404).json({
        error: 'Template not found',
        message: `No template found for content type: ${contentType}`
      });
    }

    res.json({ template });
  } catch (error) {
    console.error('Error getting template by content type:', error);
    res.status(500).json({
      error: 'Failed to get template',
      message: error.message
    });
  }
});

// Create new template (admin only) - 现在不支持，需要修改代码
router.post('/', adminMiddleware, async (req, res) => {
  try {
    await promptTemplateService.createTemplate(req.body);
  } catch (error) {
    res.status(501).json({
      error: 'Not implemented',
      message: error.message,
      notice: 'Templates are now managed in code. Please add new templates to backend/config/promptTemplates.js'
    });
  }
});

// Update template (admin only) - 现在不支持，需要修改代码
router.put('/:id', adminMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    await promptTemplateService.updateTemplate(id, req.body);
  } catch (error) {
    res.status(501).json({
      error: 'Not implemented', 
      message: error.message,
      notice: 'Templates are now managed in code. Please modify templates in backend/config/promptTemplates.js'
    });
  }
});

// Get available content types - 新增API
router.get('/content-types', async (req, res) => {
  try {
    const contentTypes = promptTemplateService.getAvailableContentTypes();
    res.json({
      contentTypes,
      count: contentTypes.length
    });
  } catch (error) {
    console.error('Error getting content types:', error);
    res.status(500).json({
      error: 'Failed to get content types',
      message: error.message
    });
  }
});

// Build dynamic prompt for testing
router.post('/build-prompt', async (req, res) => {
  try {
    const { contentType, articleData } = req.body;

    if (!contentType || !articleData) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'contentType and articleData are required'
      });
    }

    const prompt = await promptTemplateService.buildDynamicPrompt(contentType, articleData);

    res.json({
      prompt,
      contentType,
      message: 'Prompt built successfully'
    });
  } catch (error) {
    console.error('Error building prompt:', error);
    res.status(500).json({
      error: 'Failed to build prompt',
      message: error.message
    });
  }
});

module.exports = router;
