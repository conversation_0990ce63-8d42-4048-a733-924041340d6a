const express = require('express');
const router = express.Router();
const database = require('../config/database');
const languageMiddleware = require('../middleware/languageMiddleware');
const { getProperAuthor } = require('../utils/extractAuthorInfo');

// Helper function to generate slug from title
function generateSlug(title) {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim('-'); // Remove leading/trailing hyphens
}

// Helper function to extract excerpt from content
function extractExcerpt(content, maxLength = 200) {
  if (!content) return '';
  
  // Remove HTML tags and get plain text
  const plainText = content.replace(/<[^>]*>/g, '');
  
  if (plainText.length <= maxLength) {
    return plainText;
  }
  
  // Find the last complete sentence within the limit
  const truncated = plainText.substring(0, maxLength);
  const lastSentence = truncated.lastIndexOf('.');
  
  if (lastSentence > maxLength * 0.7) {
    return truncated.substring(0, lastSentence + 1);
  }
  
  return truncated + '...';
}

// Helper function to check if excerpt is in wrong format
function isInvalidExcerpt(excerpt) {
  if (!excerpt) return true;
  
  // Check for common invalid format patterns
  const invalidPatterns = [
    /^SEO Title:/i,
    /^Meta Description:/i,
    /^Focus Keywords:/i,
    /^Tags:/i,
    /^<strong>SEO Title:/i
  ];
  
  return invalidPatterns.some(pattern => pattern.test(excerpt)) || 
         excerpt.trim() === '' || 
         excerpt.length > 300;
}

// Helper function to get proper excerpt
function getProperExcerpt(article) {
  // First check if the existing excerpt is valid
  if (!isInvalidExcerpt(article.excerpt)) {
    return article.excerpt;
  }
  
  // Try to get excerpt from game_info.seo.description
  if (article.game_info) {
    try {
      const gameInfo = typeof article.game_info === 'string' ? 
        JSON.parse(article.game_info) : article.game_info;
      
      if (gameInfo.seo && gameInfo.seo.description && 
          !isInvalidExcerpt(gameInfo.seo.description)) {
        return gameInfo.seo.description;
      }
    } catch (e) {
      // Ignore JSON parse errors
    }
  }
  
  // Fallback to extracting from content
  return extractExcerpt(article.content);
}

// Helper function to get content type info
function getContentTypeInfo(contentType) {
  const typeMap = {
    'bonus_analysis': { 
      icon: '🎁', 
      label: 'Bonus Analysis', 
      description: 'Comprehensive bonus reviews and comparisons',
      color: 'from-emerald-500 to-teal-500'
    },
    'casino_review': { 
      icon: '🎰', 
      label: 'Casino Review', 
      description: 'In-depth casino reviews and ratings',
      color: 'from-purple-500 to-pink-500'
    },
    'game_guide': { 
      icon: '🎮', 
      label: 'Game Guide', 
      description: 'Step-by-step game tutorials and guides',
      color: 'from-blue-500 to-cyan-500'
    },
    'industry_news': { 
      icon: '📰', 
      label: 'Industry News', 
      description: 'Latest industry news and updates',
      color: 'from-red-500 to-pink-500'
    },
    'regulatory_update': { 
      icon: '⚖️', 
      label: 'Regulatory Update', 
      description: 'Legal and regulatory developments',
      color: 'from-slate-500 to-gray-500'
    },
    'sports_betting': { 
      icon: '⚽', 
      label: 'Sports Betting', 
      description: 'Sports betting analysis and predictions',
      color: 'from-indigo-500 to-purple-500'
    },
  };
  
  return typeMap[contentType] || { 
    icon: '📄', 
    label: contentType?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Article',
    description: 'Quality content',
    color: 'from-gray-500 to-slate-500'
  };
}

// Apply language middleware to all routes
router.use(languageMiddleware);


// GET /api/public/articles - Get published articles (paginated, filtered by language)
router.get('/articles', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 12, 
      contentType, 
      search,
      sortBy = 'published_at',
      sortOrder = 'DESC',
      language = 'en'
    } = req.query;
    
    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    // First, check if we have any published blog posts for the specified language
    let query = `
      SELECT 
        id,
        title,
        slug,
        excerpt,
        content,
        content_type,
        game_info,
        author,
        featured_image,
        published_at,
        categories,
        tags,
        language
      FROM blog_posts 
      WHERE status = 'published' AND language = ?
    `;
    
    const params = [language];
    
    // Language parameter is already added above
    
    // Add content type filter
    if (contentType) {
      query += ' AND content_type = ?';
      params.push(contentType);
    }
    
    // Add search filter
    if (search) {
      query += ' AND (title LIKE ? OR excerpt LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
    }
    
    // Add sorting
    const validSortFields = ['published_at', 'title', 'created_at'];
    const validSortOrders = ['ASC', 'DESC'];
    
    if (validSortFields.includes(sortBy) && validSortOrders.includes(sortOrder.toUpperCase())) {
      query += ` ORDER BY ${sortBy} ${sortOrder.toUpperCase()}`;
    } else {
      query += ' ORDER BY published_at DESC';
    }
    
    // Add pagination
    query += ' LIMIT ? OFFSET ?';
    params.push(parseInt(limit), offset);
    
    const articles = await database.all(query, params);
    
    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM blog_posts 
      WHERE status = 'published'
    `;
    
    const countParams = [];
    
    if (contentType) {
      countQuery += ' AND content_type = ?';
      countParams.push(contentType);
    }
    
    if (search) {
      countQuery += ' AND (title LIKE ? OR excerpt LIKE ?)';
      countParams.push(`%${search}%`, `%${search}%`);
    }
    
    const countResult = await database.get(countQuery, countParams);
    
    // Parse JSON fields and add content type info
    const processedArticles = articles.map(article => ({
      ...article,
      categories: article.categories ? JSON.parse(article.categories) : [],
      tags: article.tags ? JSON.parse(article.tags) : [],
      contentTypeInfo: getContentTypeInfo(article.content_type),
      excerpt: getProperExcerpt(article),
      author: getProperAuthor(article)
    }));
    
    res.json({
      success: true,
      articles: processedArticles,
      language: req.language,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(countResult.total / parseInt(limit)),
        totalArticles: countResult.total,
        hasMore: countResult.total > offset + parseInt(limit),
        limit: parseInt(limit)
      }
    });
    
  } catch (error) {
    console.error('Error fetching public articles:', error);
    res.status(500).json({
      error: 'Failed to fetch articles',
      message: error.message
    });
  }
});

// GET /api/public/articles/:slug - Get single article by slug
router.get('/articles/:slug', async (req, res) => {
  try {
    const { slug } = req.params;
    const { language = 'en' } = req.query;
    
    const article = await database.get(`
      SELECT 
        id,
        title,
        slug,
        content,
        excerpt,
        content_type,
        game_info,
        author,
        featured_image,
        published_at,
        categories,
        tags,
        language,
        created_at,
        updated_at
      FROM blog_posts 
      WHERE slug = ? AND status = 'published' AND language = ?
    `, [slug, language]);
    
    if (!article) {
      return res.status(404).json({
        error: 'Article not found',
        message: 'The requested article could not be found'
      });
    }
    
    // Parse JSON fields and add content type info
    const processedArticle = {
      ...article,
      categories: article.categories ? JSON.parse(article.categories) : [],
      tags: article.tags ? JSON.parse(article.tags) : [],
      game_info: article.game_info ? (typeof article.game_info === 'string' ? JSON.parse(article.game_info) : article.game_info) : null,
      contentTypeInfo: getContentTypeInfo(article.content_type),
      excerpt: getProperExcerpt(article),
      author: getProperAuthor(article)
    };
    
    res.json({
      success: true,
      article: processedArticle,
      language: req.language
    });
    
  } catch (error) {
    console.error('Error fetching article:', error);
    res.status(500).json({
      error: 'Failed to fetch article',
      message: error.message
    });
  }
});

// GET /api/public/articles/category/:contentType - Get articles by category
router.get('/articles/category/:contentType', async (req, res) => {
  try {
    const { contentType } = req.params;
    const { page = 1, limit = 12, language = 'en' } = req.query;
    
    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    const articles = await database.all(`
      SELECT 
        id,
        title,
        slug,
        excerpt,
        content,
        content_type,
        game_info,
        author,
        featured_image,
        published_at,
        categories,
        tags,
        language
      FROM blog_posts 
      WHERE content_type = ? AND status = 'published' AND language = ?
      ORDER BY published_at DESC
      LIMIT ? OFFSET ?
    `, [contentType, language, parseInt(limit), offset]);
    
    // Get total count
    const countResult = await database.get(`
      SELECT COUNT(*) as total
      FROM blog_posts 
      WHERE content_type = ? AND status = 'published' AND language = ?
    `, [contentType, language]);
    
    // Process articles
    const processedArticles = articles.map(article => ({
      ...article,
      categories: article.categories ? JSON.parse(article.categories) : [],
      tags: article.tags ? JSON.parse(article.tags) : [],
      contentTypeInfo: getContentTypeInfo(article.content_type),
      excerpt: getProperExcerpt(article),
      author: getProperAuthor(article)
    }));
    
    res.json({
      success: true,
      articles: processedArticles,
      contentType,
      contentTypeInfo: getContentTypeInfo(contentType),
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(countResult.total / parseInt(limit)),
        totalArticles: countResult.total,
        hasMore: countResult.total > offset + parseInt(limit),
        limit: parseInt(limit)
      }
    });
    
  } catch (error) {
    console.error('Error fetching category articles:', error);
    res.status(500).json({
      error: 'Failed to fetch category articles',
      message: error.message
    });
  }
});

// GET /api/public/content/categories - Get content categories with task counts (for dashboard)
router.get('/content/categories', async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    // Define all supported content types in correct order
    const allContentTypes = [
      'casino_review',
      'game_guide',
      'sports_betting',
      'bonus_analysis',
      'industry_news',
      'regulatory_update'
    ];
    
    // Get published articles count by content type
    const articleStats = await database.all(`
      SELECT 
        content_type,
        COUNT(*) as article_count
      FROM blog_posts 
      WHERE status = 'published' AND content_type IS NOT NULL
      GROUP BY content_type
    `);
    
    // Create count map
    const articleCountMap = {};
    articleStats.forEach(stat => {
      articleCountMap[stat.content_type] = parseInt(stat.article_count);
    });
    
    // Generate result for all content types
    const categories = {};
    allContentTypes.forEach(contentType => {
      const count = articleCountMap[contentType] || 0;
      const typeInfo = getContentTypeInfo(contentType);
      
      categories[contentType] = Array(count).fill().map((_, index) => ({
        id: `${contentType}_${index + 1}`,
        name: `${typeInfo.label} ${index + 1}`,
        content_type: contentType,
        status: 'Completed',
        updated_at: new Date().toISOString()
      }));
    });
    
    res.json({
      success: true,
      categories: categories
    });
    
  } catch (error) {
    console.error('Error fetching content categories:', error);
    res.status(500).json({
      error: 'Failed to fetch content categories',
      message: error.message
    });
  }
});

// GET /api/public/content-types - Get available content types with counts
router.get('/content-types', async (req, res) => {
  try {
    const language = req.language || req.query.language || 'en';
    
    // 定义所有支持的内容类型 - 按显示顺序排列
    const allContentTypes = [
      'casino_review',
      'game_guide',
      'sports_betting',
      'bonus_analysis',
      'industry_news',
      'regulatory_update'
    ];
    
    // 获取实际的文章统计
    const result = await database.pool.query(`
      SELECT 
        content_type,
        COUNT(*) as article_count
      FROM blog_posts 
      WHERE status = 'published' AND content_type IS NOT NULL AND language = $1
      GROUP BY content_type
    `, [language]);
    
    // 创建统计映射
    const countMap = {};
    result.rows.forEach(row => {
      countMap[row.content_type] = parseInt(row.article_count);
    });
    
    // 为所有内容类型生成结果，保持预定义的顺序
    const processedTypes = allContentTypes.map(contentType => ({
      content_type: contentType,
      article_count: countMap[contentType] || 0,
      ...getContentTypeInfo(contentType)
    })); // 保持allContentTypes数组中定义的顺序
    
    res.json({
      success: true,
      contentTypes: processedTypes
    });
    
  } catch (error) {
    console.error('Error fetching content types:', error);
    res.status(500).json({
      error: 'Failed to fetch content types',
      message: error.message
    });
  }
});

module.exports = router;
