const express = require('express');
const router = express.Router();
const promptTemplateService = require('../services/promptTemplateService');
const settingsService = require('../services/settingsService');
const geminiService = require('../services/geminiService');
const database = require('../config/database');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

// Super admin email
// Remove hardcoded email - use role-based access

// Middleware for admin authentication
const requireAdmin = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Unauthorized - Admin access required',
        hint: 'Please login with valid credentials'
      });
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Check if user exists in database
    const user = await database.get(
      'SELECT id, email, full_name, role FROM users WHERE email = ?',
      [decoded.email]
    );

    if (!user) {
      return res.status(401).json({
        error: 'User not found',
        hint: 'Please contact administrator'
      });
    }

    // Check if user has admin role
    if (user.role === 'admin' || user.role === 'super_admin') {
      req.adminUser = {
        id: user.id,
        email: user.email,
        name: user.full_name,
        role: user.role
      };
      return next();
    }

    return res.status(403).json({
      error: 'Insufficient permissions',
      hint: 'Admin access required'
    });
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: 'Invalid token',
        hint: 'Please login again'
      });
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: 'Token expired',
        hint: 'Please login again'
      });
    }

    console.error('Admin auth error:', error);
    return res.status(500).json({ error: 'Authentication failed' });
  }
};

// Admin login endpoint
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        error: 'Email and password are required'
      });
    }

    // Find user in database
    const user = await database.get(
      'SELECT id, email, full_name, password_hash, role FROM users WHERE email = ?',
      [email]
    );

    if (!user) {
      return res.status(401).json({
        error: 'Invalid credentials',
        hint: 'User not found'
      });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({
        error: 'Invalid credentials',
        hint: 'Incorrect password'
      });
    }

    // Check if user has admin privileges
    const isAdmin = user.role === 'admin' || user.role === 'super_admin';
    if (!isAdmin) {
      return res.status(403).json({
        error: 'Access denied',
        hint: 'Admin privileges required'
      });
    }

    // Generate JWT token
    const token = jwt.sign(
      {
        id: user.id,
        email: user.email,
        role: user.role,
        iat: Math.floor(Date.now() / 1000)
      },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.json({
      success: true,
      data: {
        token,
        user: {
          id: user.id,
          email: user.email,
          name: user.full_name,
          role: user.role
        }
      },
      message: `${user.role === 'super_admin' ? 'Super admin' : 'Admin'} authenticated successfully`
    });
  } catch (error) {
    console.error('Admin login error:', error);
    res.status(500).json({ error: 'Login failed' });
  }
});

// Get current admin user info
router.get('/me', requireAdmin, (req, res) => {
  res.json({
    success: true,
    data: {
      user: req.adminUser,
      permissions: req.adminUser.role === 'super_admin' ? 'full' : 'limited'
    }
  });
});

// Get all prompt templates
router.get('/templates', requireAdmin, async (req, res) => {
  try {
    const templates = await promptTemplateService.getAllActiveTemplates();
    res.json({
      success: true,
      data: templates,
      count: templates.length
    });
  } catch (error) {
    console.error('Error fetching templates:', error);
    res.status(500).json({ error: 'Failed to fetch templates' });
  }
});

// Get specific template by ID - Updated for code-based template system
router.get('/templates/:id', requireAdmin, async (req, res) => {
  try {
    // Since we moved to code-based templates, return helpful error message
    res.status(404).json({ 
      error: 'Template system has been migrated to code-based configuration. Individual template fetching is no longer supported.',
      info: 'Use /api/prompt-templates endpoints instead for template operations.'
    });
  } catch (error) {
    console.error('Error in deprecated template endpoint:', error);
    res.status(500).json({ error: 'Deprecated endpoint - template system migrated to code-based configuration' });
  }
});

// Create new template
router.post('/templates', requireAdmin, async (req, res) => {
  try {
    const {
      content_type,
      template_name,
      template_version,
      prompt_content,
      persona_description,
      specialized_instructions,
      compliance_rules,
      is_active
    } = req.body;

    // Validation
    if (!content_type || !template_name || !prompt_content) {
      return res.status(400).json({ 
        error: 'Missing required fields: content_type, template_name, prompt_content' 
      });
    }

    const templateId = await promptTemplateService.createTemplate({
      content_type,
      template_name,
      template_version: template_version || '1.0',
      prompt_content,
      persona_description,
      specialized_instructions,
      compliance_rules: compliance_rules || [],
      is_active: is_active !== false
    });

    res.json({
      success: true,
      data: { id: templateId },
      message: 'Template created successfully'
    });
  } catch (error) {
    console.error('Error creating template:', error);
    res.status(500).json({ error: 'Failed to create template' });
  }
});

// Update template
router.put('/templates/:id', requireAdmin, async (req, res) => {
  try {
    const templateId = req.params.id;
    const updateData = req.body;

    // Check if template exists
    const existing = await database.get(
      'SELECT id FROM prompt_templates WHERE id = ?',
      [templateId]
    );

    if (!existing) {
      return res.status(404).json({ error: 'Template not found' });
    }

    await promptTemplateService.updateTemplate(templateId, updateData);

    res.json({
      success: true,
      message: 'Template updated successfully'
    });
  } catch (error) {
    console.error('Error updating template:', error);
    res.status(500).json({ error: 'Failed to update template' });
  }
});

// Delete template (soft delete - set inactive)
router.delete('/templates/:id', requireAdmin, async (req, res) => {
  try {
    const templateId = req.params.id;

    await database.run(
      'UPDATE prompt_templates SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [templateId]
    );

    res.json({
      success: true,
      message: 'Template deactivated successfully'
    });
  } catch (error) {
    console.error('Error deleting template:', error);
    res.status(500).json({ error: 'Failed to delete template' });
  }
});

// Preview template with sample data
router.post('/templates/preview', requireAdmin, async (req, res) => {
  try {
    const { template, sampleData } = req.body;

    if (!template || !template.prompt_content) {
      return res.status(400).json({ error: 'Template content required' });
    }

    // Create a temporary template service instance for preview
    const tempService = Object.create(promptTemplateService);
    
    // Build preview prompt
    const previewPrompt = template.prompt_content
      .replace('{{PERSONA_DESCRIPTION}}', template.persona_description || '')
      .replace('{{SPECIALIZED_INSTRUCTIONS}}', template.specialized_instructions || '')
      .replace('{{TOPICS_SECTION}}', tempService.buildTopicsSection(sampleData?.topics || []))
      .replace('{{KEYWORDS_SECTION}}', tempService.buildKeywordsSection(sampleData?.primaryKeywords || [], sampleData?.secondaryKeywords || []))
      .replace('{{SOURCES_SECTION}}', tempService.buildSourcesSection(sampleData?.sources || []))
      .replace('{{COMPLIANCE_SECTION}}', tempService.buildComplianceSection(sampleData?.complianceTemplate, sampleData?.customDisclaimers, sampleData?.additionalRequirements))
      .replace('{{PARAMETERS_SECTION}}', tempService.buildParametersSection(sampleData?.tonality, sampleData?.length, sampleData?.format, sampleData?.targetAudience, sampleData?.articleGoal))
      .replace('{{JURISDICTION}}', sampleData?.jurisdiction || 'international')
      .replace('{{CONTENT_TYPE}}', template.content_type || 'general');

    res.json({
      success: true,
      data: {
        preview: previewPrompt,
        length: previewPrompt.length,
        wordCount: previewPrompt.split(' ').length
      }
    });
  } catch (error) {
    console.error('Error generating preview:', error);
    res.status(500).json({ error: 'Failed to generate preview' });
  }
});

// Get system settings (model configuration, etc.)
router.get('/settings', requireAdmin, async (req, res) => {
  try {
    // Get current settings from database
    const currentModel = await settingsService.getAiModel();
    const databaseApiKey = await settingsService.getApiKey();

    // Check if API key is configured (either in database or environment)
    const apiKeyConfigured = !!(databaseApiKey || process.env.GOOGLE_GEMINI_API_KEY);

    const settings = {
      ai_model: currentModel,
      api_key_configured: apiKeyConfigured,
      admin_key_configured: !!process.env.ADMIN_KEY,
      database_type: database.isPostgres ? 'PostgreSQL' : 'SQLite',
      environment: process.env.NODE_ENV || 'development'
    };

    res.json({
      success: true,
      data: settings
    });
  } catch (error) {
    console.error('Error fetching settings:', error);
    res.status(500).json({ error: 'Failed to fetch settings' });
  }
});

// Update system settings
router.put('/settings', requireAdmin, async (req, res) => {
  try {
    const { ai_model, api_key } = req.body;

    if (!ai_model && !api_key) {
      return res.status(400).json({ error: 'Either AI model name or API key is required' });
    }

    let updateMessage = '';
    let updateData = {
      updated_by: req.adminUser.email,
      updated_at: new Date().toISOString()
    };

    // Update AI model if provided
    if (ai_model) {
      // Validate model name format
      if (!ai_model.startsWith('gemini-')) {
        return res.status(400).json({
          error: 'Invalid model name format',
          message: 'Model name must start with "gemini-"'
        });
      }

      // Update model in database and apply immediately
      await geminiService.updateModel(ai_model);
      updateData.ai_model = ai_model;
      updateMessage += `AI model updated to ${ai_model}`;
      console.log(`Admin ${req.adminUser.email} updated AI model to: ${ai_model}`);
    }

    // Update API key if provided
    if (api_key) {
      // Validate API key format (basic validation)
      if (!api_key.trim() || api_key.length < 10) {
        return res.status(400).json({
          error: 'Invalid API key format',
          message: 'API key must be at least 10 characters long'
        });
      }

      // Update API key in database and apply immediately
      await geminiService.updateApiKey(api_key.trim());
      updateData.api_key_configured = true;
      updateMessage += (updateMessage ? ' and ' : '') + 'API key updated';
      console.log(`Admin ${req.adminUser.email} updated API key`);
    }

    res.json({
      success: true,
      message: updateMessage + ' successfully and applied immediately',
      data: updateData
    });
  } catch (error) {
    console.error('Error updating settings:', error);
    res.status(500).json({
      error: 'Failed to update settings',
      message: error.message
    });
  }
});

// Export templates
router.get('/templates/export', requireAdmin, async (req, res) => {
  try {
    const templates = await promptTemplateService.getAllActiveTemplates();
    
    const exportData = {
      export_date: new Date().toISOString(),
      version: '1.0',
      templates: templates.map(t => ({
        content_type: t.content_type,
        template_name: t.template_name,
        template_version: t.template_version,
        prompt_content: t.prompt_content,
        persona_description: t.persona_description,
        specialized_instructions: t.specialized_instructions,
        compliance_rules: t.compliance_rules,
        is_active: t.is_active
      }))
    };

    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', 'attachment; filename=prompt-templates-export.json');
    res.json(exportData);
  } catch (error) {
    console.error('Error exporting templates:', error);
    res.status(500).json({ error: 'Failed to export templates' });
  }
});

// Import templates
router.post('/templates/import', requireAdmin, async (req, res) => {
  try {
    const { templates, overwrite = false } = req.body;

    if (!Array.isArray(templates)) {
      return res.status(400).json({ error: 'Templates must be an array' });
    }

    let imported = 0;
    let skipped = 0;
    let errors = [];

    for (const template of templates) {
      try {
        // Check if template exists
        const existing = await database.get(
          'SELECT id FROM prompt_templates WHERE content_type = ? AND template_name = ?',
          [template.content_type, template.template_name]
        );

        if (existing && !overwrite) {
          skipped++;
          continue;
        }

        if (existing && overwrite) {
          await promptTemplateService.updateTemplate(existing.id, template);
        } else {
          await promptTemplateService.createTemplate(template);
        }
        imported++;
      } catch (error) {
        errors.push(`Failed to import ${template.template_name}: ${error.message}`);
      }
    }

    res.json({
      success: true,
      data: {
        imported,
        skipped,
        errors
      },
      message: `Import completed: ${imported} imported, ${skipped} skipped`
    });
  } catch (error) {
    console.error('Error importing templates:', error);
    res.status(500).json({ error: 'Failed to import templates' });
  }
});

// Cache for website images to reduce database calls
let websiteImagesCache = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Get website images settings
router.get('/website-images', requireAdmin, async (req, res) => {
  try {
    // Set aggressive caching headers
    res.set({
      'Cache-Control': 'public, max-age=300', // 5 minutes
      'ETag': `"images-${Date.now()}"`,
      'Last-Modified': new Date().toUTCString()
    });

    // Check cache first
    const now = Date.now();
    if (websiteImagesCache && (now - cacheTimestamp) < CACHE_DURATION) {
      return res.json({
        success: true,
        data: websiteImagesCache,
        cached: true
      });
    }

    // Create a timeout promise
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Database query timeout')), 5000); // 5 second timeout
    });

    // Create the database query promise
    const queryPromise = database.get(
      'SELECT setting_value FROM system_settings WHERE setting_key = ?',
      ['website_images']
    );

    // Race between timeout and query
    let imagesSettings;
    try {
      imagesSettings = await Promise.race([queryPromise, timeoutPromise]);
    } catch (timeoutError) {
      console.warn('Website images query timed out, returning default values');
      // Return default values on timeout
      const defaultImages = {
        hero: {
          main: '',
          tech: '',
          business: '',
          gaming: ''
        },
        contentTypes: {}
      };
      return res.json({
        success: true,
        data: defaultImages,
        cached: false,
        defaulted: true
      });
    }

    let images = {
      hero: {
        main: '',
        tech: '',
        business: '',
        gaming: ''
      },
      contentTypes: {}
    };

    if (imagesSettings && imagesSettings.setting_value) {
      try {
        images = JSON.parse(imagesSettings.setting_value);
      } catch (error) {
        console.error('Error parsing website images settings:', error);
      }
    }

    // Update cache
    websiteImagesCache = images;
    cacheTimestamp = now;

    res.json({
      success: true,
      data: images,
      cached: false
    });
  } catch (error) {
    console.error('Error fetching website images:', error);
    // Return default values on any error
    const defaultImages = {
      hero: {
        main: '',
        tech: '',
        business: '',
        gaming: ''
      },
      contentTypes: {}
    };
    res.json({
      success: true,
      data: defaultImages,
      cached: false,
      defaulted: true,
      error: error.message
    });
  }
});

// Update website images settings
router.put('/website-images', requireAdmin, async (req, res) => {
  try {
    const { images } = req.body;

    if (!images) {
      return res.status(400).json({
        error: 'Images data is required'
      });
    }

    // Save or update website images in system_settings
    const existingSetting = await database.get(
      'SELECT id FROM system_settings WHERE setting_key = ?',
      ['website_images']
    );

    if (existingSetting) {
      // Update existing setting
      await database.run(
        'UPDATE system_settings SET setting_value = ?, updated_at = CURRENT_TIMESTAMP WHERE setting_key = ?',
        [JSON.stringify(images), 'website_images']
      );
    } else {
      // Insert new setting
      await database.run(
        'INSERT INTO system_settings (setting_key, setting_value, created_at, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)',
        ['website_images', JSON.stringify(images)]
      );
    }

    // Clear cache when images are updated
    websiteImagesCache = null;
    cacheTimestamp = 0;

    res.json({
      success: true,
      message: 'Website images updated successfully',
      data: images
    });
  } catch (error) {
    console.error('Error updating website images:', error);
    res.status(500).json({
      error: 'Failed to update website images',
      message: error.message
    });
  }
});

module.exports = router;
