const express = require('express');
const axios = require('axios');
const NodeCache = require('node-cache');
const { authenticateToken } = require('../middleware/auth');
const { getUserRedditToken } = require('./reddit-oauth');

const router = express.Router();

// Cache setup - 1 hour TTL
const redditCache = new NodeCache({ stdTTL: 3600 });

// Reddit API configuration
const REDDIT_API_BASE = 'https://oauth.reddit.com';
const REDDIT_PUBLIC_BASE = 'https://www.reddit.com';

// Middleware to check authentication
router.use(authenticateToken);

// Helper function to build cache key
const getCacheKey = (subreddit, keyword, sortBy, userId) => {
  return `reddit:${userId}:${subreddit}:${keyword}:${sortBy}`;
};

// Helper function to clean and process Reddit posts
const processRedditPosts = (posts) => {
  return posts.map(post => ({
    id: post.data.id,
    title: post.data.title,
    url: `https://www.reddit.com${post.data.permalink}`,
    subreddit: post.data.subreddit,
    author: post.data.author,
    created_utc: post.data.created_utc,
    upvotes: post.data.ups,
    score: post.data.score,
    num_comments: post.data.num_comments,
    selftext: post.data.selftext ? post.data.selftext.substring(0, 500) : '',
    body: post.data.selftext ? post.data.selftext.substring(0, 500) : '', // Add alias for compatibility
    is_self: post.data.is_self,
    link_flair_text: post.data.link_flair_text,
    // Extract potential insights
    awards: post.data.total_awards_received || 0,
    upvote_ratio: post.data.upvote_ratio,
    // Engagement score (combination of upvotes and comments)
    engagement_score: post.data.ups + (post.data.num_comments * 2)
  }));
};

// GET /api/reddit/post/:postId - Get detailed post with comments
router.get('/post/:postId', async (req, res) => {
  try {
    const { postId } = req.params;
    const { subreddit } = req.query;

    if (!postId) {
      return res.status(400).json({
        error: 'Missing post ID',
        message: 'Post ID is required'
      });
    }

    // Get user's Reddit access token
    const accessToken = await getUserRedditToken(req.user.id);

    // Build Reddit post detail URL
    const postUrl = subreddit 
      ? `${REDDIT_API_BASE}/r/${subreddit}/comments/${postId}`
      : `${REDDIT_API_BASE}/comments/${postId}`;

    console.log(`Fetching Reddit post details from: ${postUrl}`);

    // Make request to Reddit using user's OAuth token
    const response = await axios.get(postUrl, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'User-Agent': 'Writer777:1.0.0 (by /u/Writer777App)',
        'Accept': 'application/json'
      },
      timeout: 15000 // 15 second timeout for post details
    });

    if (!response.data || !Array.isArray(response.data) || response.data.length < 2) {
      throw new Error('Invalid post response from Reddit API');
    }

    // Reddit returns [post_data, comments_data]
    const postData = response.data[0].data.children[0].data;
    const commentsData = response.data[1].data.children;

    // Process comments recursively
    const processComments = (comments, depth = 0, maxDepth = 3) => {
      if (depth > maxDepth) return [];
      
      return comments
        .filter(comment => comment.kind === 't1' && comment.data.body && comment.data.body !== '[deleted]')
        .map(comment => ({
          id: comment.data.id,
          author: comment.data.author,
          body: comment.data.body,
          score: comment.data.score,
          created_utc: comment.data.created_utc,
          depth: depth,
          replies: comment.data.replies && comment.data.replies.data
            ? processComments(comment.data.replies.data.children, depth + 1, maxDepth)
            : []
        }))
        .slice(0, depth === 0 ? 20 : 10); // Limit top-level and nested comments
    };

    const processedComments = processComments(commentsData);

    // Extract key insights from post and comments
    const postInsights = {
      id: postData.id,
      title: postData.title,
      selftext: postData.selftext || '',
      author: postData.author,
      score: postData.score,
      upvote_ratio: postData.upvote_ratio,
      num_comments: postData.num_comments,
      created_utc: postData.created_utc,
      url: `https://www.reddit.com${postData.permalink}`,
      subreddit: postData.subreddit,
      flair: postData.link_flair_text,
      comments: processedComments,
      // Extract key themes and pain points
      insights: {
        total_comments: processedComments.length,
        high_scored_comments: processedComments.filter(c => c.score > 5).length,
        common_themes: extractCommonThemes(postData, processedComments),
        user_pain_points: extractPainPoints(postData, processedComments)
      }
    };

    res.json({
      post: postInsights,
      success: true
    });

  } catch (error) {
    console.error('Reddit post detail error:', error);
    
    // Handle specific Reddit errors
    if (error.response) {
      if (error.response.status === 401) {
        return res.status(401).json({
          error: 'Reddit authorization expired',
          message: 'Please reconnect your Reddit account'
        });
      } else if (error.response.status === 404) {
        return res.status(404).json({
          error: 'Post not found',
          message: 'The requested Reddit post could not be found'
        });
      }
    }

    res.status(500).json({
      error: 'Failed to fetch post details',
      message: error.message || 'An unexpected error occurred'
    });
  }
});

// Helper function to extract common themes from post and comments
const extractCommonThemes = (postData, comments) => {
  const text = [postData.title, postData.selftext, ...comments.map(c => c.body)].join(' ');
  const words = text.toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 3)
    .filter(word => !['this', 'that', 'with', 'have', 'been', 'they', 'were', 'said', 'each', 'which', 'their', 'time', 'will', 'about', 'would', 'could', 'should', 'really', 'think', 'know', 'just', 'like', 'good', 'well', 'also', 'much', 'more', 'very', 'many', 'most', 'other', 'some', 'what', 'only', 'into', 'over', 'after', 'first', 'work', 'way'].includes(word));
  
  const wordCount = {};
  words.forEach(word => {
    wordCount[word] = (wordCount[word] || 0) + 1;
  });
  
  return Object.entries(wordCount)
    .filter(([word, count]) => count >= 2)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .map(([word, count]) => ({ word, count }));
};

// Helper function to extract pain points and problems mentioned
const extractPainPoints = (postData, comments) => {
  const painKeywords = ['problem', 'issue', 'difficult', 'hard', 'struggle', 'trouble', 'challenge', 'frustrated', 'annoying', 'hate', 'worst', 'bad', 'terrible', 'awful'];
  const text = [postData.title, postData.selftext, ...comments.slice(0, 10).map(c => c.body)].join(' ').toLowerCase();
  
  const foundPainPoints = [];
  painKeywords.forEach(keyword => {
    const regex = new RegExp(`\\b${keyword}\\b.*?[.!?]`, 'gi');
    const matches = text.match(regex);
    if (matches) {
      foundPainPoints.push(...matches.slice(0, 2));
    }
  });
  
  return foundPainPoints.slice(0, 5);
};

// GET /api/reddit/subreddits/search - Search and suggest subreddits
router.get('/subreddits/search', async (req, res) => {
  try {
    const { q } = req.query;

    if (!q || q.trim().length < 2) {
      return res.status(400).json({
        error: 'Query too short',
        message: 'Search query must be at least 2 characters'
      });
    }

    // Get user's Reddit access token
    const accessToken = await getUserRedditToken(req.user.id);

    // Search for subreddits
    const searchUrl = `${REDDIT_API_BASE}/subreddits/search`;
    const params = {
      q: q.trim(),
      type: 'sr',
      sort: 'relevance',
      limit: 10
    };

    console.log(`Searching subreddits for: ${q}`);

    const response = await axios.get(searchUrl, {
      params,
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'User-Agent': 'Writer777:1.0.0 (by /u/Writer777App)',
        'Accept': 'application/json'
      },
      timeout: 10000
    });

    if (!response.data || !response.data.data || !response.data.data.children) {
      throw new Error('Invalid response from Reddit API');
    }

    const subreddits = response.data.data.children
      .filter(sub => sub.data.subreddit_type === 'public')
      .map(sub => ({
        name: sub.data.display_name,
        title: sub.data.title,
        description: sub.data.public_description || sub.data.description,
        subscribers: sub.data.subscribers,
        over18: sub.data.over18,
        icon: sub.data.icon_img || sub.data.community_icon,
        url: `https://www.reddit.com/r/${sub.data.display_name}`
      }))
      .slice(0, 8);

    res.json({
      subreddits,
      query: q,
      count: subreddits.length
    });

  } catch (error) {
    console.error('Subreddit search error:', error);
    
    // Handle specific Reddit errors
    if (error.response) {
      if (error.response.status === 401) {
        return res.status(401).json({
          error: 'Reddit authorization expired',
          message: 'Please reconnect your Reddit account'
        });
      }
    }

    res.status(500).json({
      error: 'Failed to search subreddits',
      message: error.message || 'An unexpected error occurred'
    });
  }
});

// POST /api/reddit/search - Search Reddit posts
router.post('/search', async (req, res) => {
  try {
    const { subreddit, keyword, sortBy = 'hot', limit = 20 } = req.body;

    // Validate inputs
    if (!subreddit || !keyword) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'Both subreddit and keyword are required'
      });
    }

    // Clean subreddit name (remove r/ prefix if present)
    const cleanSubreddit = subreddit.replace(/^r\//, '').trim();

    // Check cache first (include user ID for cache isolation)
    const cacheKey = getCacheKey(cleanSubreddit, keyword, 'all', req.user.id);
    const cachedData = redditCache.get(cacheKey);
    
    if (cachedData) {
      console.log(`Returning cached Reddit data for ${cacheKey}`);
      return res.json({
        posts: cachedData,
        cached: true
      });
    }

    // Get user's Reddit access token
    const accessToken = await getUserRedditToken(req.user.id);

    console.log(`Fetching Reddit data from all sort types for: ${cleanSubreddit} with keyword: ${keyword}`);

    // Try different sort types in sequence for better results, starting with top posts
    let allPosts = [];
    const sortTypes = ['top', 'hot', 'new'];
    const maxPostsPerType = Math.ceil(limit / 2); // Get fewer posts per type to avoid timeout
    
    for (const sort of sortTypes) {
      try {
        let searchUrl, params;
        
        if (sort === 'hot') {
          // For hot posts, use subreddit listing
          searchUrl = `${REDDIT_API_BASE}/r/${cleanSubreddit}/hot`;
          params = {
            limit: Math.min(maxPostsPerType, 25),
            raw_json: 1
          };
        } else {
          // For top and new, use search endpoint
          searchUrl = `${REDDIT_API_BASE}/r/${cleanSubreddit}/search`;
          params = {
            q: keyword,
            restrict_sr: 'on',
            sort: sort,
            limit: Math.min(maxPostsPerType, 25),
            raw_json: 1
          };
        }

        console.log(`Fetching ${sort} posts from: ${searchUrl}`);

        const response = await axios.get(searchUrl, {
          params,
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'User-Agent': 'Writer777:1.0.0 (by /u/Writer777App)',
            'Accept': 'application/json'
          },
          timeout: 8000 // Reduce timeout to 8 seconds
        });

        if (response.data && response.data.data && response.data.data.children) {
          let posts = response.data.data.children;
          
          // Filter by keyword for hot posts
          if (sort === 'hot') {
            const keywordLower = keyword.toLowerCase();
            posts = posts.filter(post => 
              post.data.title.toLowerCase().includes(keywordLower) ||
              (post.data.selftext && post.data.selftext.toLowerCase().includes(keywordLower))
            );
            
            // If no exact matches, try broader search
            if (posts.length === 0) {
              const keywordParts = keyword.toLowerCase().split(/\s+/);
              posts = response.data.data.children.filter(post => {
                const titleLower = post.data.title.toLowerCase();
                const selftextLower = (post.data.selftext || '').toLowerCase();
                return keywordParts.some(part => 
                  part.length > 2 && (titleLower.includes(part) || selftextLower.includes(part))
                );
              });
            }
          }

          // Add sort type metadata and avoid duplicates
          posts.forEach(post => {
            if (!allPosts.some(p => p.data.id === post.data.id)) {
              post.sortType = sort;
              allPosts.push(post);
            }
          });
          
          console.log(`${sort} posts found: ${posts.length}, total so far: ${allPosts.length}`);
          
          // Break early if we have enough posts
          if (allPosts.length >= limit * 1.5) {
            console.log(`Got enough posts (${allPosts.length}), stopping early`);
            break;
          }
        }
      } catch (error) {
        console.error(`Error fetching ${sort} posts:`, error.message);
        // Continue with other sort types even if one fails
      }
    }

    console.log(`Total combined posts: ${allPosts.length}`);

    // Process and clean the posts
    const processedPosts = processRedditPosts(allPosts);

    // Sort by engagement score
    processedPosts.sort((a, b) => b.engagement_score - a.engagement_score);

    // Limit final results
    const finalPosts = processedPosts.slice(0, limit);

    console.log(`Final processed posts count: ${finalPosts.length}`);
    if (finalPosts.length > 0) {
      console.log('Sample post titles:', finalPosts.slice(0, 3).map(p => p.title));
    }

    // Cache the results
    redditCache.set(cacheKey, finalPosts);

    res.json({
      posts: finalPosts,
      cached: false,
      subreddit: cleanSubreddit,
      keyword,
      sortBy: 'mixed', // Indicate we searched multiple types
      count: finalPosts.length
    });

  } catch (error) {
    console.error('Reddit API error:', error);

    // Handle specific Reddit errors
    if (error.response) {
      if (error.response.status === 401) {
        return res.status(401).json({
          error: 'Reddit authorization expired',
          message: 'Please reconnect your Reddit account'
        });
      } else if (error.response.status === 404) {
        return res.status(404).json({
          error: 'Subreddit not found',
          message: `The subreddit "${cleanSubreddit}" does not exist`
        });
      } else if (error.response.status === 403) {
        return res.status(403).json({
          error: 'Access denied',
          message: 'This subreddit may be private or restricted'
        });
      } else if (error.response.status === 429) {
        return res.status(429).json({
          error: 'Rate limit exceeded',
          message: 'Too many requests to Reddit API. Please try again later.'
        });
      }
    }

    // Handle authentication errors
    if (error.message.includes('No Reddit authorization')) {
      return res.status(401).json({
        error: 'Reddit authorization required',
        message: 'Please connect your Reddit account first'
      });
    }

    res.status(500).json({
      error: 'Failed to search Reddit',
      message: error.message || 'An unexpected error occurred'
    });
  }
});

module.exports = router;