const express = require('express');
const router = express.Router();
const riskKeywordService = require('../services/riskKeywordService');

// Analyze text for risk keywords
router.post('/analyze-text', async (req, res) => {
  try {
    const { text, contentType, jurisdiction, languageCode, useAI = true } = req.body;

    if (!text) {
      return res.status(400).json({
        error: 'Missing required field',
        message: 'text is required'
      });
    }

    let analysis;
    if (useAI) {
      // Use full analysis with AI
      analysis = await riskKeywordService.analyzeText(text, contentType, jurisdiction, languageCode);
    } else {
      // Use keyword-only analysis
      analysis = riskKeywordService.analyzeKeywords(text, languageCode, contentType, jurisdiction);
    }

    res.json({
      analysis,
      message: 'Text analysis completed',
      languageCode: languageCode || 'en',
      analysisType: useAI ? 'hybrid' : 'keyword_only'
    });
  } catch (error) {
    console.error('Error analyzing text:', error);
    res.status(500).json({
      error: 'Failed to analyze text',
      message: error.message
    });
  }
});

// Check specific keywords for risks
router.post('/check-keywords', async (req, res) => {
  try {
    const { keywords, languageCode, contentType, useAI = false } = req.body;

    if (!Array.isArray(keywords)) {
      return res.status(400).json({
        error: 'Invalid input',
        message: 'keywords must be an array'
      });
    }

    let analysis;
    if (useAI) {
      // Use full analysis with AI
      analysis = await riskKeywordService.checkKeywordsWithAI(keywords, languageCode, contentType);
    } else {
      // Use keyword-only analysis
      analysis = riskKeywordService.checkKeywords(keywords, languageCode);
    }

    res.json({
      analysis,
      message: 'Keyword analysis completed',
      languageCode: languageCode || 'en',
      analysisType: useAI ? 'hybrid' : 'keyword_only'
    });
  } catch (error) {
    console.error('Error checking keywords:', error);
    res.status(500).json({
      error: 'Failed to check keywords',
      message: error.message
    });
  }
});

// Get safe alternatives for risky terms
router.post('/safe-alternatives', async (req, res) => {
  try {
    const { riskyTerm, languageCode } = req.body;

    if (!riskyTerm) {
      return res.status(400).json({
        error: 'Missing required field',
        message: 'riskyTerm is required'
      });
    }

    const alternatives = riskKeywordService.getSafeAlternatives(riskyTerm, languageCode);

    res.json({
      riskyTerm,
      alternatives,
      languageCode: languageCode || 'en',
      message: 'Safe alternatives provided'
    });
  } catch (error) {
    console.error('Error getting safe alternatives:', error);
    res.status(500).json({
      error: 'Failed to get safe alternatives',
      message: error.message
    });
  }
});

// Comprehensive content analysis (combines text and keyword analysis)
router.post('/comprehensive-analysis', async (req, res) => {
  try {
    const {
      text,
      keywords = [],
      contentType = '',
      jurisdiction = 'international',
      languageCode = 'en',
      useAI = true
    } = req.body;

    if (!text && (!keywords || keywords.length === 0)) {
      return res.status(400).json({
        error: 'Missing required data',
        message: 'Either text or keywords array is required'
      });
    }

    const results = {
      timestamp: new Date().toISOString(),
      contentType,
      jurisdiction,
      languageCode,
      analysisType: useAI ? 'hybrid' : 'keyword_only'
    };

    // Analyze text if provided
    if (text) {
      if (useAI) {
        results.textAnalysis = await riskKeywordService.analyzeText(text, contentType, jurisdiction, languageCode);
      } else {
        results.textAnalysis = riskKeywordService.analyzeKeywords(text, languageCode, contentType, jurisdiction);
      }
    }

    // Analyze keywords if provided
    if (keywords && keywords.length > 0) {
      if (useAI) {
        results.keywordAnalysis = await riskKeywordService.checkKeywordsWithAI(keywords, languageCode, contentType);
      } else {
        results.keywordAnalysis = riskKeywordService.checkKeywords(keywords, languageCode);
      }
    }

    // Generate overall risk assessment
    const overallRiskLevel = results.textAnalysis?.riskLevel === 'high' || 
                           !results.keywordAnalysis?.safe ? 'high' : 
                           results.textAnalysis?.riskLevel === 'medium' ? 'medium' : 'low';

    const isCompliant = results.textAnalysis?.isCompliant !== false && 
                       results.keywordAnalysis?.safe !== false;

    results.overallAssessment = {
      riskLevel: overallRiskLevel,
      isCompliant,
      requiresReview: overallRiskLevel !== 'low' || !isCompliant,
      recommendations: []
    };

    // Combine recommendations
    if (results.textAnalysis?.recommendations) {
      results.overallAssessment.recommendations.push(...results.textAnalysis.recommendations);
    }

    if (results.keywordAnalysis?.risks?.length > 0) {
      results.overallAssessment.recommendations.push({
        type: 'keyword_replacement',
        priority: 'medium',
        message: 'Consider replacing flagged keywords with safer alternatives',
        examples: results.keywordAnalysis.risks.map(risk => 
          `Replace "${risk.keyword}" with safer alternatives`
        )
      });
    }

    res.json({
      results,
      message: 'Comprehensive analysis completed'
    });
  } catch (error) {
    console.error('Error performing comprehensive analysis:', error);
    res.status(500).json({
      error: 'Failed to perform comprehensive analysis',
      message: error.message
    });
  }
});

// Get supported languages
router.get('/supported-languages', async (req, res) => {
  try {
    const supportedLanguages = riskKeywordService.getSupportedLanguages();
    const languageInfo = {
      'en': { name: 'English', nativeName: 'English' },
      'zh': { name: 'Chinese', nativeName: '中文' },
      'pt': { name: 'Portuguese', nativeName: 'Português' },
      'es': { name: 'Spanish', nativeName: 'Español' },
      'de': { name: 'German', nativeName: 'Deutsch' },
      'fr': { name: 'French', nativeName: 'Français' },
      'it': { name: 'Italian', nativeName: 'Italiano' },
      'ja': { name: 'Japanese', nativeName: '日本語' }
    };

    const languages = supportedLanguages.map(code => ({
      code,
      ...languageInfo[code],
      supported: true
    }));

    res.json({
      languages,
      defaultLanguage: 'en',
      message: 'Supported languages retrieved'
    });
  } catch (error) {
    console.error('Error getting supported languages:', error);
    res.status(500).json({
      error: 'Failed to get supported languages',
      message: error.message
    });
  }
});

// Get risk keyword categories and examples (for educational purposes)
router.get('/risk-categories', async (req, res) => {
  try {
    const { languageCode = 'en' } = req.query;

    const categories = {
      predatory: {
        level: 'high',
        description: 'Terms that make unrealistic promises and should be avoided',
        examples: languageCode === 'zh' ?
          ['保证获胜', '轻松赚钱', '不可能输', '无风险'] :
          languageCode === 'es' ?
          ['ganancia garantizada', 'dinero fácil', 'no se puede perder', 'sin riesgo'] :
          languageCode === 'de' ?
          ['garantierter gewinn', 'leichtes geld', 'kann nicht verlieren', 'risikofrei'] :
          languageCode === 'fr' ?
          ['gain garanti', 'argent facile', 'impossible de perdre', 'sans risque'] :
          languageCode === 'pt' ?
          ['ganho garantido', 'dinheiro fácil', 'impossível perder', 'sem risco'] :
          languageCode === 'it' ?
          ['vincita garantita', 'soldi facili', 'impossibile perdere', 'senza rischi'] :
          languageCode === 'nl' ?
          ['gegarandeerde winst', 'makkelijk geld', 'kan niet verliezen', 'risicovrij'] :
          ['guaranteed win', 'easy money', 'can\'t lose', 'risk-free']
      },
      addiction_triggers: {
        level: 'high',
        description: 'Terms that may trigger addictive behaviors',
        examples: languageCode === 'zh' ?
          ['停不下来', '再来一把', '追损', '继续玩'] :
          languageCode === 'es' ?
          ['no puedo parar', 'una apuesta más', 'perseguir pérdidas', 'sigue jugando'] :
          languageCode === 'de' ?
          ['kann nicht aufhören', 'noch eine wette', 'verluste jagen', 'weiterspielen'] :
          languageCode === 'fr' ?
          ['ne peut pas s\'arrêter', 'encore un pari', 'poursuivre les pertes', 'continuer à jouer'] :
          languageCode === 'pt' ?
          ['não consegue parar', 'mais uma aposta', 'perseguir perdas', 'continuar jogando'] :
          languageCode === 'it' ?
          ['non riesco a smettere', 'ancora una scommessa', 'inseguire le perdite', 'continuare a giocare'] :
          languageCode === 'nl' ?
          ['kan niet stoppen', 'nog een weddenschap', 'verliezen najagen', 'blijven spelen'] :
          ['can\'t stop', 'one more bet', 'chase losses', 'keep playing']
      },
      financial_pressure: {
        level: 'high',
        description: 'Terms that encourage dangerous financial behavior',
        examples: languageCode === 'zh' ?
          ['赌上积蓄', '借钱赌博', '全部资金'] :
          languageCode === 'es' ?
          ['apuesta tus ahorros', 'pide prestado para apostar', 'todo tu dinero'] :
          languageCode === 'de' ?
          ['setze deine ersparnisse', 'leihe zum wetten', 'all dein geld'] :
          languageCode === 'fr' ?
          ['parie tes économies', 'emprunte pour parier', 'tout ton argent'] :
          languageCode === 'pt' ?
          ['aposte suas economias', 'empreste para apostar', 'todo seu dinheiro'] :
          languageCode === 'it' ?
          ['scommetti i tuoi risparmi', 'prendi in prestito per scommettere', 'tutti i tuoi soldi'] :
          languageCode === 'nl' ?
          ['zet je spaargeld in', 'leen om te wedden', 'al je geld'] :
          ['bet your savings', 'borrow to bet', 'all your money']
      },
      age_inappropriate: {
        level: 'high',
        description: 'Terms that inappropriately target minors',
        examples: ['kids love', 'family fun', 'teen favorite']
      },
      promotional: {
        level: 'medium',
        description: 'Promotional terms that need careful use with disclaimers',
        examples: ['hot streak', 'winning streak', 'exclusive offer']
      },
      probability_misleading: {
        level: 'medium',
        description: 'Terms that misrepresent how probability works',
        examples: ['due to win', 'hot machine', 'pattern', 'system']
      }
    };

    res.json({
      categories,
      message: 'Risk categories retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting risk categories:', error);
    res.status(500).json({
      error: 'Failed to get risk categories',
      message: error.message
    });
  }
});

module.exports = router;
