const express = require('express');
const router = express.Router();
const database = require('../config/database');

// POST /api/fix-content-types - Fix content types for existing articles
router.post('/', async (req, res) => {
  try {
    console.log('🔧 Starting content type fix...');
    
    // Valid content types
    const validTypes = [
      'bonus_analysis',
      'brand_copy', 
      'casino_review',
      'game_guide',
      'industry_news',
      'regulatory_update',
      'sports_betting',
      'strategy_article'
    ];
    
    // Get all articles
    const articles = await database.all(`
      SELECT id, title, content_type, status
      FROM blog_posts 
      ORDER BY created_at DESC
    `);
    
    console.log(`📊 Found ${articles.length} total articles`);
    
    // Find articles with invalid content types
    const invalidArticles = articles.filter(article => 
      !validTypes.includes(article.content_type)
    );
    
    console.log(`🔍 Found ${invalidArticles.length} articles with invalid content types`);
    
    let fixedCount = 0;
    
    if (invalidArticles.length > 0) {
      for (const article of invalidArticles) {
        // Map old types to new types
        let newContentType = 'strategy_article'; // default
        
        if (article.content_type === 'article') {
          newContentType = 'strategy_article';
        } else if (article.content_type === null || article.content_type === '') {
          newContentType = 'casino_review';
        }
        
        console.log(`🔄 Updating "${article.title}": "${article.content_type}" -> "${newContentType}"`);
        
        await database.run(`
          UPDATE blog_posts 
          SET content_type = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `, [newContentType, article.id]);
        
        fixedCount++;
      }
    }
    
    // Get updated statistics
    const updatedStats = await database.all(`
      SELECT content_type, COUNT(*) as count, status
      FROM blog_posts 
      GROUP BY content_type, status
      ORDER BY content_type, status
    `);
    
    console.log('✅ Content type fix completed');
    
    res.json({
      success: true,
      message: 'Content types fixed successfully',
      statistics: {
        totalArticles: articles.length,
        invalidFound: invalidArticles.length,
        articlesFixed: fixedCount,
        breakdown: updatedStats
      }
    });
    
  } catch (error) {
    console.error('Error fixing content types:', error);
    res.status(500).json({
      error: 'Failed to fix content types',
      message: error.message
    });
  }
});

module.exports = router;