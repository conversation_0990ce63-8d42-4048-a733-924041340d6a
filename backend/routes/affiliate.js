const express = require('express');
const router = express.Router();
const Database = require('../config/database');
const { authenticateToken: auth } = require('../middleware/auth');
const { adminAuth } = require('../middleware/adminAuth');
const affiliateTrackingService = require('../services/affiliateTrackingService');

/**
 * 联盟营销管理API路由
 * 包括：联盟链接CRUD、文章关联、点击统计、批量操作
 */

// ==================== 智能推荐 ====================

// 获取推荐的联盟链接
router.post('/recommend', async (req, res) => {
  try {
    const criteria = req.body;
    const recommendedLinks = await affiliateTrackingService.getRecommendedLinks(criteria);
    
    res.json({
      success: true,
      data: recommendedLinks,
      criteria: criteria
    });
  } catch (error) {
    console.error('获取推荐联盟链接失败:', error);
    res.status(500).json({ success: false, message: '获取推荐链接失败', error: error.message });
  }
});

// ==================== 联盟链接管理 ====================

// 获取所有联盟链接（简化版本用于前端管理）
router.get('/links', auth, async (req, res) => {
  try {
    const { search, category, is_active } = req.query;
    
    let sql = `
      SELECT 
        id,
        name,
        description,
        affiliate_url as url,
        display_text as "shortCode",
        category,
        CASE 
          WHEN target_languages IS NOT NULL AND target_languages != ''
          THEN COALESCE(target_languages::json->>0, 'en')
          ELSE 'en'
        END as language,
        CASE 
          WHEN target_countries IS NOT NULL AND target_countries != ''
          THEN COALESCE(target_countries::json->>0, 'US')
          ELSE 'US'
        END as country,
        click_count as clicks,
        conversion_count as conversions,
        is_active as "isActive",
        created_at as "createdAt"
      FROM affiliate_links 
      WHERE 1=1
    `;
    
    const params = [];
    let paramIndex = 1;
    
    if (search) {
      sql += ` AND (name ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
      paramIndex++;
    }
    
    if (category) {
      sql += ` AND category = $${paramIndex}`;
      params.push(category);
      paramIndex++;
    }
    
    if (is_active !== undefined) {
      sql += ` AND is_active = $${paramIndex}`;
      params.push(is_active === 'true');
      paramIndex++;
    }
    
    sql += ` ORDER BY created_at DESC`;
    
    const links = await Database.all(sql, params);
    
    res.json({
      success: true,
      data: links
    });
    
  } catch (error) {
    console.error('获取联盟链接失败:', error);
    res.status(500).json({ success: false, message: '获取联盟链接失败', error: error.message });
  }
});

// 获取单个联盟链接
router.get('/links/:id', auth, async (req, res) => {
  try {
    const link = await Database.get('SELECT * FROM affiliate_links WHERE id = ?', [req.params.id]);
    
    if (!link) {
      return res.status(404).json({ success: false, message: '联盟链接不存在' });
    }

    // 解析JSON字段
    const processedLink = {
      ...link,
      content_types: link.content_types ? JSON.parse(link.content_types) : [],
      categories: link.categories ? JSON.parse(link.categories) : [],
      target_countries: link.target_countries ? JSON.parse(link.target_countries) : [],
      target_languages: link.target_languages ? JSON.parse(link.target_languages) : [],
      tracking_params: link.tracking_params ? JSON.parse(link.tracking_params) : {}
    };

    res.json({ success: true, data: processedLink });
  } catch (error) {
    console.error('获取联盟链接失败:', error);
    res.status(500).json({ success: false, message: '获取联盟链接失败', error: error.message });
  }
});

// 创建新联盟链接（简化版本）
router.post('/links', auth, async (req, res) => {
  try {
    const { name, url, shortCode, description, category, language, country, isActive } = req.body;
    
    if (!name || !url || !shortCode) {
      return res.status(400).json({
        success: false,
        error: 'Name, URL, and short code are required'
      });
    }
    
    const sql = `
      INSERT INTO affiliate_links (
        name, 
        description, 
        affiliate_url, 
        display_text, 
        category,
        target_languages,
        target_countries,
        is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      name,
      description || '',
      url,
      shortCode,
      category || 'casino_review',
      JSON.stringify([language || 'en']),
      JSON.stringify([country || 'US']),
      Boolean(isActive)
    ];
    
    const result = await Database.run(sql, params);
    
    // 获取新创建的记录
    const newLink = await Database.get(`
      SELECT 
        id,
        name,
        description,
        affiliate_url as url,
        display_text as "shortCode",
        category,
        CASE 
          WHEN target_languages IS NOT NULL AND target_languages != ''
          THEN COALESCE(target_languages::json->>0, 'en')
          ELSE 'en'
        END as language,
        CASE 
          WHEN target_countries IS NOT NULL AND target_countries != ''
          THEN COALESCE(target_countries::json->>0, 'US')
          ELSE 'US'
        END as country,
        click_count as clicks,
        conversion_count as conversions,
        is_active as "isActive",
        created_at as "createdAt"
      FROM affiliate_links 
      WHERE id = ?
    `, [result.id]);
    
    res.status(201).json({
      success: true,
      data: newLink
    });
    
  } catch (error) {
    console.error('创建联盟链接失败:', error);
    res.status(500).json({ success: false, message: '创建联盟链接失败', error: error.message });
  }
});

// 更新联盟链接（简化版本）
router.put('/links/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, url, shortCode, description, category, language, country, isActive } = req.body;
    
    if (!name || !url || !shortCode) {
      return res.status(400).json({
        success: false,
        error: 'Name, URL, and short code are required'
      });
    }
    
    const sql = `
      UPDATE affiliate_links 
      SET 
        name = ?, 
        description = ?, 
        affiliate_url = ?, 
        display_text = ?, 
        category = ?,
        target_languages = ?,
        target_countries = ?,
        is_active = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    const params = [
      name,
      description || '',
      url,
      shortCode,
      category || 'casino_review',
      JSON.stringify([language || 'en']),
      JSON.stringify([country || 'US']),
      Boolean(isActive),
      id
    ];
    
    const result = await Database.run(sql, params);
    
    if (result.changes === 0) {
      return res.status(404).json({
        success: false,
        error: 'Affiliate link not found'
      });
    }
    
    // 获取更新后的记录
    const updatedLink = await Database.get(`
      SELECT 
        id,
        name,
        description,
        affiliate_url as url,
        display_text as "shortCode",
        category,
        CASE 
          WHEN target_languages IS NOT NULL AND target_languages != ''
          THEN COALESCE(target_languages::json->>0, 'en')
          ELSE 'en'
        END as language,
        CASE 
          WHEN target_countries IS NOT NULL AND target_countries != ''
          THEN COALESCE(target_countries::json->>0, 'US')
          ELSE 'US'
        END as country,
        click_count as clicks,
        conversion_count as conversions,
        is_active as "isActive",
        created_at as "createdAt"
      FROM affiliate_links 
      WHERE id = ?
    `, [id]);
    
    res.json({
      success: true,
      data: updatedLink
    });
    
  } catch (error) {
    console.error('更新联盟链接失败:', error);
    res.status(500).json({ success: false, message: '更新联盟链接失败', error: error.message });
  }
});

// 删除联盟链接
router.delete('/links/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await Database.run('DELETE FROM affiliate_links WHERE id = ?', [id]);
    
    if (result.changes === 0) {
      return res.status(404).json({
        success: false,
        error: 'Affiliate link not found'
      });
    }
    
    res.json({
      success: true,
      message: 'Affiliate link deleted successfully'
    });
    
  } catch (error) {
    console.error('删除联盟链接失败:', error);
    res.status(500).json({ success: false, message: '删除联盟链接失败', error: error.message });
  }
});

// ==================== 文章联盟链接关联管理 ====================

// 获取文章的联盟链接
router.get('/articles/:articleId/links', auth, async (req, res) => {
  try {
    const { articleId } = req.params;
    
    const links = await Database.all(`
      SELECT aal.*, al.name, al.description, al.affiliate_url, al.display_text, 
             al.button_style, al.commission_rate, al.is_active as link_active
      FROM article_affiliate_links aal
      JOIN affiliate_links al ON aal.affiliate_link_id = al.id
      WHERE (aal.article_id = ? OR aal.article_id IS NULL)
      AND aal.is_active = true
      ORDER BY aal.position, al.priority DESC
    `, [articleId]);

    res.json({ success: true, data: links });
  } catch (error) {
    console.error('获取文章联盟链接失败:', error);
    res.status(500).json({ success: false, message: '获取文章联盟链接失败', error: error.message });
  }
});

// 为文章设置联盟链接
router.post('/articles/:articleId/links', auth, async (req, res) => {
  try {
    const { articleId } = req.params;
    const { 
      affiliate_link_id, 
      position = 'primary_cta', 
      custom_display_text, 
      custom_button_style 
    } = req.body;

    if (!affiliate_link_id) {
      return res.status(400).json({ success: false, message: '联盟链接ID为必填项' });
    }

    const result = await Database.run(`
      INSERT INTO article_affiliate_links (
        article_id, affiliate_link_id, position, custom_display_text, custom_button_style
      ) VALUES (?, ?, ?, ?, ?)
    `, [articleId, affiliate_link_id, position, custom_display_text, custom_button_style]);

    res.status(201).json({ 
      success: true, 
      message: '文章联盟链接设置成功', 
      data: { id: result.id } 
    });
  } catch (error) {
    console.error('设置文章联盟链接失败:', error);
    res.status(500).json({ success: false, message: '设置文章联盟链接失败', error: error.message });
  }
});

// 批量设置文章联盟链接
router.post('/articles/batch-links', auth, async (req, res) => {
  try {
    const { 
      article_ids, 
      affiliate_link_id, 
      position = 'primary_cta', 
      custom_display_text, 
      custom_button_style,
      filters = {} // 支持按内容类型、语言等筛选
    } = req.body;

    if (!affiliate_link_id) {
      return res.status(400).json({ success: false, message: '联盟链接ID为必填项' });
    }

    let targetArticleIds = [];

    if (article_ids && article_ids.length > 0) {
      // 使用指定的文章ID列表
      targetArticleIds = article_ids;
    } else {
      // 根据筛选条件查找文章
      let whereConditions = [];
      let params = [];
      let paramIndex = 1;

      if (filters.content_type) {
        whereConditions.push(`content_type = $${paramIndex}`);
        params.push(filters.content_type);
        paramIndex++;
      }

      if (filters.language) {
        whereConditions.push(`target_language = $${paramIndex}`);
        params.push(filters.language);
        paramIndex++;
      }

      if (filters.status) {
        whereConditions.push(`status = $${paramIndex}`);
        params.push(filters.status);
        paramIndex++;
      }

      if (filters.date_from) {
        whereConditions.push(`created_at >= $${paramIndex}`);
        params.push(filters.date_from);
        paramIndex++;
      }

      if (filters.date_to) {
        whereConditions.push(`created_at <= $${paramIndex}`);
        params.push(filters.date_to);
        paramIndex++;
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
      const articlesQuery = `SELECT id FROM tasks ${whereClause}`;
      const articles = await Database.all(articlesQuery, params);
      targetArticleIds = articles.map(article => article.id);
    }

    if (targetArticleIds.length === 0) {
      return res.status(400).json({ success: false, message: '没有找到符合条件的文章' });
    }

    // 批量插入联盟链接关联
    let successCount = 0;
    const errors = [];

    for (const articleId of targetArticleIds) {
      try {
        await Database.run(`
          INSERT INTO article_affiliate_links (
            article_id, affiliate_link_id, position, custom_display_text, custom_button_style
          ) VALUES (?, ?, ?, ?, ?)
          ON CONFLICT (article_id, affiliate_link_id, position) DO UPDATE SET
            custom_display_text = EXCLUDED.custom_display_text,
            custom_button_style = EXCLUDED.custom_button_style,
            updated_at = CURRENT_TIMESTAMP
        `, [articleId, affiliate_link_id, position, custom_display_text, custom_button_style]);
        successCount++;
      } catch (error) {
        errors.push({ articleId, error: error.message });
      }
    }

    res.json({ 
      success: true, 
      message: `批量设置完成，成功设置 ${successCount} 篇文章`,
      data: { successCount, totalAttempted: targetArticleIds.length, errors }
    });
  } catch (error) {
    console.error('批量设置联盟链接失败:', error);
    res.status(500).json({ success: false, message: '批量设置联盟链接失败', error: error.message });
  }
});

// 删除文章联盟链接关联
router.delete('/articles/:articleId/links/:linkId', auth, async (req, res) => {
  try {
    const { articleId, linkId } = req.params;
    
    const result = await Database.run(
      'DELETE FROM article_affiliate_links WHERE article_id = ? AND affiliate_link_id = ?', 
      [articleId, linkId]
    );
    
    if (result.changes === 0) {
      return res.status(404).json({ success: false, message: '文章联盟链接关联不存在' });
    }

    res.json({ success: true, message: '文章联盟链接关联删除成功' });
  } catch (error) {
    console.error('删除文章联盟链接关联失败:', error);
    res.status(500).json({ success: false, message: '删除文章联盟链接关联失败', error: error.message });
  }
});

// 删除文章联盟链接关联 (基于记录ID)
router.delete('/articles/:articleId/links/record/:recordId', auth, async (req, res) => {
  try {
    const { articleId, recordId } = req.params;
    
    const result = await Database.run(
      'DELETE FROM article_affiliate_links WHERE article_id = ? AND id = ?', 
      [articleId, recordId]
    );
    
    if (result.changes === 0) {
      return res.status(404).json({ success: false, message: '文章联盟链接关联不存在' });
    }

    res.json({ success: true, message: '文章联盟链接关联删除成功' });
  } catch (error) {
    console.error('删除文章联盟链接关联失败:', error);
    res.status(500).json({ success: false, message: '删除文章联盟链接关联失败', error: error.message });
  }
});

// ==================== 点击统计和追踪 ====================

// 记录联盟链接点击
router.post('/clicks', async (req, res) => {
  try {
    const {
      affiliate_link_id,
      article_id,
      article_url,
      article_title,
      click_position,
      user_ip,
      user_agent,
      referer,
      country_code,
      language_code,
      device_type,
      browser,
      utm_source,
      utm_medium,
      utm_campaign,
      utm_content,
      utm_term,
      session_id
    } = req.body;

    if (!affiliate_link_id) {
      return res.status(400).json({ success: false, message: '联盟链接ID为必填项' });
    }

    // 记录点击
    const result = await Database.run(`
      INSERT INTO affiliate_clicks (
        affiliate_link_id, article_id, article_url, article_title, click_position,
        user_ip, user_agent, referer, country_code, language_code, device_type, browser,
        utm_source, utm_medium, utm_campaign, utm_content, utm_term, session_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      affiliate_link_id, article_id, article_url, article_title, click_position,
      user_ip, user_agent, referer, country_code, language_code, device_type, browser,
      utm_source, utm_medium, utm_campaign, utm_content, utm_term, session_id
    ]);

    // 更新联盟链接的点击计数
    await Database.run(
      'UPDATE affiliate_links SET click_count = click_count + 1 WHERE id = ?',
      [affiliate_link_id]
    );

    res.status(201).json({ 
      success: true, 
      message: '点击记录成功', 
      data: { id: result.id } 
    });
  } catch (error) {
    console.error('记录联盟链接点击失败:', error);
    res.status(500).json({ success: false, message: '记录联盟链接点击失败', error: error.message });
  }
});

// 获取联盟链接统计数据
router.get('/stats/links/:linkId', auth, async (req, res) => {
  try {
    const { linkId } = req.params;
    const { date_from, date_to, group_by = 'day' } = req.query;

    let whereConditions = ['affiliate_link_id = ?'];
    let params = [linkId];
    let paramIndex = 2;

    if (date_from) {
      whereConditions.push(`click_timestamp >= $${paramIndex}`);
      params.push(date_from);
      paramIndex++;
    }

    if (date_to) {
      whereConditions.push(`click_timestamp <= $${paramIndex}`);
      params.push(date_to);
      paramIndex++;
    }

    const whereClause = `WHERE ${whereConditions.join(' AND ')}`;

    // 基础统计
    const basicStats = await Database.get(`
      SELECT 
        COUNT(*) as total_clicks,
        COUNT(DISTINCT article_id) as unique_articles,
        COUNT(DISTINCT session_id) as unique_sessions,
        COUNT(DISTINCT user_ip) as unique_visitors
      FROM affiliate_clicks ${whereClause}
    `, params);

    // 按时间分组统计
    let dateFormat;
    switch (group_by) {
      case 'hour':
        dateFormat = "DATE_TRUNC('hour', click_timestamp)";
        break;
      case 'day':
        dateFormat = "DATE_TRUNC('day', click_timestamp)";
        break;
      case 'week':
        dateFormat = "DATE_TRUNC('week', click_timestamp)";
        break;
      case 'month':
        dateFormat = "DATE_TRUNC('month', click_timestamp)";
        break;
      default:
        dateFormat = "DATE_TRUNC('day', click_timestamp)";
    }

    const timeStats = await Database.all(`
      SELECT 
        ${dateFormat} as period,
        COUNT(*) as clicks
      FROM affiliate_clicks ${whereClause}
      GROUP BY ${dateFormat}
      ORDER BY period DESC
    `, params);

    // 按位置统计
    const positionStats = await Database.all(`
      SELECT 
        click_position,
        COUNT(*) as clicks
      FROM affiliate_clicks ${whereClause}
      GROUP BY click_position
      ORDER BY clicks DESC
    `, params);

    // 按国家统计
    const countryStats = await Database.all(`
      SELECT 
        country_code,
        COUNT(*) as clicks
      FROM affiliate_clicks ${whereClause}
      GROUP BY country_code
      ORDER BY clicks DESC
      LIMIT 10
    `, params);

    res.json({
      success: true,
      data: {
        basic: basicStats,
        timeline: timeStats,
        positions: positionStats,
        countries: countryStats
      }
    });
  } catch (error) {
    console.error('获取联盟链接统计失败:', error);
    res.status(500).json({ success: false, message: '获取联盟链接统计失败', error: error.message });
  }
});

// 获取整体统计报表
router.get('/stats/overview', auth, async (req, res) => {
  try {
    const { date_from, date_to } = req.query;

    let whereConditions = [];
    let params = [];
    let paramIndex = 1;

    if (date_from) {
      whereConditions.push(`ac.click_timestamp >= $${paramIndex}`);
      params.push(date_from);
      paramIndex++;
    }

    if (date_to) {
      whereConditions.push(`ac.click_timestamp <= $${paramIndex}`);
      params.push(date_to);
      paramIndex++;
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 联盟链接性能统计
    const linkPerformance = await Database.all(`
      SELECT 
        al.id,
        al.name,
        al.display_text,
        COUNT(ac.id) as total_clicks,
        COUNT(DISTINCT ac.session_id) as unique_sessions,
        al.commission_rate,
        al.total_earnings
      FROM affiliate_links al
      LEFT JOIN affiliate_clicks ac ON al.id = ac.affiliate_link_id ${whereClause.replace('WHERE', 'AND')}
      WHERE al.is_active = true
      GROUP BY al.id, al.name, al.display_text, al.commission_rate, al.total_earnings
      ORDER BY total_clicks DESC
    `, params);

    // 整体统计
    const overallStats = await Database.get(`
      SELECT 
        COUNT(DISTINCT al.id) as total_active_links,
        COUNT(ac.id) as total_clicks,
        COUNT(DISTINCT ac.session_id) as unique_sessions,
        COUNT(DISTINCT ac.article_id) as articles_with_clicks,
        SUM(al.total_earnings) as total_earnings
      FROM affiliate_links al
      LEFT JOIN affiliate_clicks ac ON al.id = ac.affiliate_link_id ${whereClause.replace('WHERE', 'AND')}
      WHERE al.is_active = true
    `, params);

    res.json({
      success: true,
      data: {
        overview: overallStats,
        linkPerformance: linkPerformance
      }
    });
  } catch (error) {
    console.error('获取统计报表失败:', error);
    res.status(500).json({ success: false, message: '获取统计报表失败', error: error.message });
  }
});

module.exports = router;