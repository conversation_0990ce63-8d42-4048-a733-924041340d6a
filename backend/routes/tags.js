const express = require('express');
const router = express.Router();
const Database = require('../config/database');

/**
 * Tags API路由
 * 处理标签相关的查询和统计
 */

// 获取所有标签及其文章数量
router.get('/all', async (req, res) => {
  try {
    const { limit = 100, sort = 'count' } = req.query;

    // 获取所有已发布文章的标签
    const articles = await Database.all(`
      SELECT tags FROM blog_posts 
      WHERE status = 'published' 
      AND tags IS NOT NULL 
      AND tags != '[]'
      AND tags != ''
    `);

    // 统计标签使用频率
    const tagCounts = {};
    
    articles.forEach(article => {
      try {
        const tags = JSON.parse(article.tags || '[]');
        if (Array.isArray(tags)) {
          tags.forEach(tag => {
            if (tag && typeof tag === 'string') {
              const normalizedTag = tag.trim().toLowerCase();
              tagCounts[normalizedTag] = (tagCounts[normalizedTag] || 0) + 1;
            }
          });
        }
      } catch (error) {
        console.log('Error parsing tags for article:', error);
      }
    });

    // 转换为数组并排序
    let tagList = Object.entries(tagCounts).map(([tag, count]) => ({
      name: tag,
      count: count,
      urlName: encodeURIComponent(tag.replace(/\s+/g, '-'))
    }));

    // 排序
    if (sort === 'count') {
      tagList.sort((a, b) => b.count - a.count);
    } else if (sort === 'name') {
      tagList.sort((a, b) => a.name.localeCompare(b.name));
    }

    // 限制数量
    if (limit && limit > 0) {
      tagList = tagList.slice(0, parseInt(limit));
    }

    res.json({
      success: true,
      data: tagList,
      total: tagList.length
    });

  } catch (error) {
    console.error('获取标签列表失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '获取标签列表失败', 
      error: error.message 
    });
  }
});

// 根据标签获取文章列表
router.get('/:tagName/articles', async (req, res) => {
  try {
    const { tagName } = req.params;
    const { page = 1, limit = 12, language } = req.query;
    
    // URL解码并标准化标签名
    const decodedTag = decodeURIComponent(tagName).replace(/-/g, ' ').toLowerCase();
    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereConditions = [
      "status = 'published'",
      "tags IS NOT NULL",
      "tags != '[]'",
      "tags != ''"
    ];
    let params = [];
    let paramIndex = 1;

    // 添加语言筛选
    if (language) {
      whereConditions.push(`language = $${paramIndex}`);
      params.push(language);
      paramIndex++;
    }

    const whereClause = whereConditions.join(' AND ');

    // 获取匹配标签的文章
    const articlesQuery = `
      SELECT id, title, excerpt, featured_image, slug, content_type, 
             created_at, updated_at, tags, language
      FROM blog_posts 
      WHERE ${whereClause}
      ORDER BY created_at DESC
    `;

    const allArticles = await Database.all(articlesQuery, params);

    // 过滤包含指定标签的文章
    const matchingArticles = allArticles.filter(article => {
      try {
        const tags = JSON.parse(article.tags || '[]');
        if (Array.isArray(tags)) {
          return tags.some(tag => 
            tag && typeof tag === 'string' && 
            tag.trim().toLowerCase() === decodedTag
          );
        }
        return false;
      } catch (error) {
        return false;
      }
    });

    // 分页
    const total = matchingArticles.length;
    const paginatedArticles = matchingArticles.slice(offset, offset + parseInt(limit));

    // 处理文章数据
    const processedArticles = paginatedArticles.map(article => ({
      ...article,
      tags: article.tags ? JSON.parse(article.tags) : [],
      excerpt: article.excerpt || (article.content ? 
        article.content.substring(0, 200).replace(/<[^>]*>/g, '') + '...' : 
        ''
      )
    }));

    res.json({
      success: true,
      data: processedArticles,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / limit)
      },
      tag: {
        name: decodedTag,
        urlName: tagName,
        displayName: decodedTag.split(' ').map(word => 
          word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ')
      }
    });

  } catch (error) {
    console.error('根据标签获取文章失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '根据标签获取文章失败', 
      error: error.message 
    });
  }
});

// 获取相关标签（与指定标签经常一起出现的标签）
router.get('/:tagName/related', async (req, res) => {
  try {
    const { tagName } = req.params;
    const { limit = 10 } = req.query;
    
    const decodedTag = decodeURIComponent(tagName).replace(/-/g, ' ').toLowerCase();

    // 获取包含指定标签的所有文章
    const articles = await Database.all(`
      SELECT tags FROM blog_posts 
      WHERE status = 'published' 
      AND tags IS NOT NULL 
      AND tags != '[]'
      AND tags != ''
    `);

    // 找到包含目标标签的文章，统计其他标签出现频率
    const relatedTagCounts = {};
    
    articles.forEach(article => {
      try {
        const tags = JSON.parse(article.tags || '[]');
        if (Array.isArray(tags)) {
          const hasTargetTag = tags.some(tag => 
            tag && typeof tag === 'string' && 
            tag.trim().toLowerCase() === decodedTag
          );
          
          if (hasTargetTag) {
            tags.forEach(tag => {
              if (tag && typeof tag === 'string') {
                const normalizedTag = tag.trim().toLowerCase();
                if (normalizedTag !== decodedTag) { // 排除目标标签本身
                  relatedTagCounts[normalizedTag] = (relatedTagCounts[normalizedTag] || 0) + 1;
                }
              }
            });
          }
        }
      } catch (error) {
        console.log('Error parsing tags for related analysis:', error);
      }
    });

    // 转换为数组并排序
    let relatedTags = Object.entries(relatedTagCounts)
      .map(([tag, count]) => ({
        name: tag,
        count: count,
        urlName: encodeURIComponent(tag.replace(/\s+/g, '-'))
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, parseInt(limit));

    res.json({
      success: true,
      data: relatedTags,
      total: relatedTags.length,
      sourceTag: decodedTag
    });

  } catch (error) {
    console.error('获取相关标签失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '获取相关标签失败', 
      error: error.message 
    });
  }
});

// 搜索标签
router.get('/search', async (req, res) => {
  try {
    const { q, limit = 20 } = req.query;
    
    if (!q || q.trim().length < 2) {
      return res.status(400).json({
        success: false,
        message: '搜索词至少需要2个字符'
      });
    }

    const searchTerm = q.trim().toLowerCase();

    // 获取所有标签
    const articles = await Database.all(`
      SELECT tags FROM blog_posts 
      WHERE status = 'published' 
      AND tags IS NOT NULL 
      AND tags != '[]'
      AND tags != ''
    `);

    const tagCounts = {};
    
    articles.forEach(article => {
      try {
        const tags = JSON.parse(article.tags || '[]');
        if (Array.isArray(tags)) {
          tags.forEach(tag => {
            if (tag && typeof tag === 'string') {
              const normalizedTag = tag.trim().toLowerCase();
              if (normalizedTag.includes(searchTerm)) {
                tagCounts[normalizedTag] = (tagCounts[normalizedTag] || 0) + 1;
              }
            }
          });
        }
      } catch (error) {
        console.log('Error parsing tags for search:', error);
      }
    });

    // 转换为数组并排序
    let matchingTags = Object.entries(tagCounts)
      .map(([tag, count]) => ({
        name: tag,
        count: count,
        urlName: encodeURIComponent(tag.replace(/\s+/g, '-'))
      }))
      .sort((a, b) => {
        // 优先显示精确匹配，然后按使用频率排序
        if (a.name === searchTerm) return -1;
        if (b.name === searchTerm) return 1;
        return b.count - a.count;
      })
      .slice(0, parseInt(limit));

    res.json({
      success: true,
      data: matchingTags,
      total: matchingTags.length,
      query: searchTerm
    });

  } catch (error) {
    console.error('搜索标签失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '搜索标签失败', 
      error: error.message 
    });
  }
});

module.exports = router;