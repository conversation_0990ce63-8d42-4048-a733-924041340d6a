const express = require('express');
const { authenticateToken, checkResourceOwnership } = require('../middleware/auth');
const database = require('../config/database');

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// GET /api/presets - Get all presets for the authenticated user
router.get('/', async (req, res) => {
  try {
    const { type } = req.query; // 'author' or 'product'
    
    let query = 'SELECT * FROM user_presets WHERE user_id = ?';
    const params = [req.user.id];

    if (type && (type === 'author' || type === 'product')) {
      query += ' AND preset_type = ?';
      params.push(type);
    }

    query += ' ORDER BY created_at DESC';

    const presets = await database.all(query, params);

    // Parse JSON data for each preset
    const formattedPresets = presets.map(preset => ({
      id: preset.id,
      presetType: preset.preset_type,
      presetName: preset.preset_name,
      presetData: JSON.parse(preset.preset_data),
      createdAt: preset.created_at,
      updatedAt: preset.updated_at
    }));

    res.json({ presets: formattedPresets });

  } catch (error) {
    console.error('Get presets error:', error);
    res.status(500).json({
      error: 'Failed to retrieve presets',
      message: 'An error occurred while retrieving your presets'
    });
  }
});

// GET /api/presets/:id - Get a specific preset
router.get('/:id', checkResourceOwnership('preset'), async (req, res) => {
  try {
    const preset = await database.get(
      'SELECT * FROM user_presets WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    if (!preset) {
      return res.status(404).json({
        error: 'Preset not found',
        message: 'The requested preset could not be found'
      });
    }

    const formattedPreset = {
      id: preset.id,
      presetType: preset.preset_type,
      presetName: preset.preset_name,
      presetData: JSON.parse(preset.preset_data),
      createdAt: preset.created_at,
      updatedAt: preset.updated_at
    };

    res.json({ preset: formattedPreset });

  } catch (error) {
    console.error('Get preset error:', error);
    res.status(500).json({
      error: 'Failed to retrieve preset',
      message: 'An error occurred while retrieving the preset'
    });
  }
});

// POST /api/presets - Create a new preset
router.post('/', async (req, res) => {
  try {
    const { presetType, presetName, presetData } = req.body;

    // Validation
    if (!presetType || !['author', 'product'].includes(presetType)) {
      return res.status(400).json({
        error: 'Invalid preset type',
        message: 'Preset type must be either "author" or "product"'
      });
    }

    if (!presetName || typeof presetName !== 'string' || presetName.trim().length === 0) {
      return res.status(400).json({
        error: 'Invalid preset name',
        message: 'Preset name is required and must be a non-empty string'
      });
    }

    if (!presetData || typeof presetData !== 'object') {
      return res.status(400).json({
        error: 'Invalid preset data',
        message: 'Preset data is required and must be an object'
      });
    }

    // Validate preset data structure based on type
    if (presetType === 'author') {
      // For author presets, we require at least one meaningful field
      const hasContent = presetData.authorName || presetData.authorBio ||
                        presetData.targetAudience || presetData.articleGoal;

      if (!hasContent) {
        return res.status(400).json({
          error: 'Missing required author fields',
          message: 'Author preset must include at least one field: authorName, authorBio, targetAudience, or articleGoal'
        });
      }
    } else if (presetType === 'product') {
      // For product presets, we require at least one meaningful field
      const hasContent = presetData.name || presetData.description ||
                        presetData.link || (presetData.features && presetData.features.length > 0);

      if (!hasContent) {
        return res.status(400).json({
          error: 'Missing required product fields',
          message: 'Product preset must include at least one field: name, description, link, or features'
        });
      }
    }

    // Check for duplicate preset names for this user and type
    const existingPreset = await database.get(
      'SELECT id FROM user_presets WHERE user_id = ? AND preset_type = ? AND preset_name = ?',
      [req.user.id, presetType, presetName.trim()]
    );

    if (existingPreset) {
      return res.status(409).json({
        error: 'Preset name already exists',
        message: `A ${presetType} preset with this name already exists`
      });
    }

    // Create preset
    const result = await database.run(
      'INSERT INTO user_presets (user_id, preset_type, preset_name, preset_data) VALUES (?, ?, ?, ?)',
      [req.user.id, presetType, presetName.trim().slice(0, 255), JSON.stringify(presetData)]
    );

    // Return the created preset
    const createdPreset = await database.get(
      'SELECT * FROM user_presets WHERE id = ?',
      [result.id]
    );

    const formattedPreset = {
      id: createdPreset.id,
      presetType: createdPreset.preset_type,
      presetName: createdPreset.preset_name,
      presetData: JSON.parse(createdPreset.preset_data),
      createdAt: createdPreset.created_at,
      updatedAt: createdPreset.updated_at
    };

    res.status(201).json({
      message: 'Preset created successfully',
      preset: formattedPreset
    });

  } catch (error) {
    console.error('Create preset error:', error);
    res.status(500).json({
      error: 'Failed to create preset',
      message: 'An error occurred while creating the preset'
    });
  }
});

// PUT /api/presets/:id - Update a preset
router.put('/:id', checkResourceOwnership('preset'), async (req, res) => {
  try {
    const { presetName, presetData } = req.body;

    // Build update query dynamically
    const updates = [];
    const params = [];

    if (presetName !== undefined) {
      if (typeof presetName !== 'string' || presetName.trim().length === 0) {
        return res.status(400).json({
          error: 'Invalid preset name',
          message: 'Preset name must be a non-empty string'
        });
      }

      // Check for duplicate names (excluding current preset)
      const existingPreset = await database.get(
        'SELECT id FROM user_presets WHERE user_id = ? AND preset_name = ? AND id != ?',
        [req.user.id, presetName.trim(), req.params.id]
      );

      if (existingPreset) {
        return res.status(409).json({
          error: 'Preset name already exists',
          message: 'A preset with this name already exists'
        });
      }

      updates.push('preset_name = ?');
      params.push(presetName.trim().slice(0, 255));
    }

    if (presetData !== undefined) {
      if (typeof presetData !== 'object') {
        return res.status(400).json({
          error: 'Invalid preset data',
          message: 'Preset data must be an object'
        });
      }

      updates.push('preset_data = ?');
      params.push(JSON.stringify(presetData));
    }

    if (updates.length === 0) {
      return res.status(400).json({
        error: 'No updates provided',
        message: 'At least one field must be provided for update'
      });
    }

    // Always update the updated_at timestamp
    updates.push('updated_at = CURRENT_TIMESTAMP');

    // Add preset ID and user ID to params
    params.push(req.params.id, req.user.id);

    const query = `UPDATE user_presets SET ${updates.join(', ')} WHERE id = ? AND user_id = ?`;
    
    const result = await database.run(query, params);

    if (result.changes === 0) {
      return res.status(404).json({
        error: 'Preset not found',
        message: 'The requested preset could not be found or updated'
      });
    }

    // Return updated preset
    const updatedPreset = await database.get(
      'SELECT * FROM user_presets WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    const formattedPreset = {
      id: updatedPreset.id,
      presetType: updatedPreset.preset_type,
      presetName: updatedPreset.preset_name,
      presetData: JSON.parse(updatedPreset.preset_data),
      createdAt: updatedPreset.created_at,
      updatedAt: updatedPreset.updated_at
    };

    res.json({
      message: 'Preset updated successfully',
      preset: formattedPreset
    });

  } catch (error) {
    console.error('Update preset error:', error);
    res.status(500).json({
      error: 'Failed to update preset',
      message: 'An error occurred while updating the preset'
    });
  }
});

// DELETE /api/presets/:id - Delete a preset
router.delete('/:id', checkResourceOwnership('preset'), async (req, res) => {
  try {
    const result = await database.run(
      'DELETE FROM user_presets WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    if (result.changes === 0) {
      return res.status(404).json({
        error: 'Preset not found',
        message: 'The requested preset could not be found'
      });
    }

    res.json({
      message: 'Preset deleted successfully'
    });

  } catch (error) {
    console.error('Delete preset error:', error);
    res.status(500).json({
      error: 'Failed to delete preset',
      message: 'An error occurred while deleting the preset'
    });
  }
});

module.exports = router;
