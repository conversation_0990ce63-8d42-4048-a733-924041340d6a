const express = require('express');
const router = express.Router();
const database = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { marked } = require('marked');
const { extractAuthorInfo, formatAuthorForDatabase } = require('../utils/extractAuthorInfo');

// Configure marked for HTML conversion
marked.setOptions({
  breaks: true,
  gfm: true,
  sanitize: false
});

// Helper function to convert markdown to HTML
function markdownToHtml(markdown) {
  if (!markdown) return '';
  try {
    return marked(markdown);
  } catch (error) {
    console.error('Error converting markdown to HTML:', error);
    return markdown;
  }
}

// Helper function to generate slug from title
function generateSlug(title) {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim('-');
}

// Helper function to ensure unique slug
async function ensureUniqueSlug(baseSlug, excludeId = null) {
  let slug = baseSlug;
  let counter = 1;
  
  while (true) {
    let query = 'SELECT id FROM blog_posts WHERE slug = ?';
    const params = [slug];
    
    if (excludeId) {
      query += ' AND id != ?';
      params.push(excludeId);
    }
    
    const existing = await database.get(query, params);
    if (!existing) {
      return slug;
    }
    
    slug = `${baseSlug}-${counter}`;
    counter++;
  }
}

// Helper function to extract excerpt
function extractExcerpt(content, maxLength = 200) {
  if (!content) return '';
  
  const plainText = content.replace(/<[^>]*>/g, '');
  
  if (plainText.length <= maxLength) {
    return plainText;
  }
  
  const truncated = plainText.substring(0, maxLength);
  const lastSentence = truncated.lastIndexOf('.');
  
  if (lastSentence > maxLength * 0.7) {
    return truncated.substring(0, lastSentence + 1);
  }
  
  return truncated + '...';
}

// All routes require authentication
router.use(authenticateToken);

// GET /api/articles - Get all articles with pagination and filtering
router.get('/', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      status = 'all',
      contentType = 'all',
      search = '',
      sortBy = 'updated_at',
      sortOrder = 'DESC'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Build query
    let query = `
      SELECT 
        id, title, slug, excerpt, content_type, author, 
        status, featured_image, categories, tags, game_info,
        created_at, updated_at, published_at
      FROM blog_posts 
      WHERE 1=1
    `;
    
    const params = [];

    // Add filters
    if (status !== 'all') {
      query += ' AND status = ?';
      params.push(status);
    }

    if (contentType !== 'all') {
      query += ' AND content_type = ?';
      params.push(contentType);
    }

    if (search) {
      query += ' AND (title LIKE ? OR excerpt LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
    }

    // Add sorting
    const validSortFields = ['title', 'created_at', 'updated_at', 'published_at', 'status'];
    const validSortOrders = ['ASC', 'DESC'];
    
    if (validSortFields.includes(sortBy) && validSortOrders.includes(sortOrder.toUpperCase())) {
      query += ` ORDER BY ${sortBy} ${sortOrder.toUpperCase()}`;
    } else {
      query += ' ORDER BY updated_at DESC';
    }

    // Add pagination
    query += ' LIMIT ? OFFSET ?';
    params.push(parseInt(limit), offset);

    const articles = await database.all(query, params);

    // Get total count for pagination
    let countQuery = 'SELECT COUNT(*) as total FROM blog_posts WHERE 1=1';
    const countParams = [];

    if (status !== 'all') {
      countQuery += ' AND status = ?';
      countParams.push(status);
    }

    if (contentType !== 'all') {
      countQuery += ' AND content_type = ?';
      countParams.push(contentType);
    }

    if (search) {
      countQuery += ' AND (title LIKE ? OR excerpt LIKE ?)';
      countParams.push(`%${search}%`, `%${search}%`);
    }

    const countResult = await database.get(countQuery, countParams);

    // Process articles (parse JSON fields)
    const processedArticles = articles.map(article => ({
      ...article,
      categories: article.categories ? JSON.parse(article.categories) : [],
      tags: article.tags ? JSON.parse(article.tags) : [],
      game_info: article.game_info ? (typeof article.game_info === 'string' ? JSON.parse(article.game_info) : article.game_info) : null
    }));

    res.json({
      success: true,
      articles: processedArticles,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(countResult.total / parseInt(limit)),
        totalArticles: countResult.total,
        hasMore: countResult.total > offset + parseInt(limit),
        limit: parseInt(limit)
      }
    });

  } catch (error) {
    console.error('Error fetching articles:', error);
    res.status(500).json({
      error: 'Failed to fetch articles',
      message: error.message
    });
  }
});

// GET /api/articles/:id - Get single article by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const article = await database.get(`
      SELECT * FROM blog_posts WHERE id = ?
    `, [id]);

    if (!article) {
      return res.status(404).json({
        error: 'Article not found'
      });
    }

    // Parse JSON fields
    const processedArticle = {
      ...article,
      categories: article.categories ? JSON.parse(article.categories) : [],
      tags: article.tags ? JSON.parse(article.tags) : [],
      game_info: article.game_info ? (typeof article.game_info === 'string' ? JSON.parse(article.game_info) : article.game_info) : null
    };

    res.json({
      success: true,
      article: processedArticle
    });

  } catch (error) {
    console.error('Error fetching article:', error);
    res.status(500).json({
      error: 'Failed to fetch article',
      message: error.message
    });
  }
});

// PUT /api/articles/:id - Update article
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      content,
      excerpt,
      content_type,
      status,
      featured_image,
      categories = [],
      tags = [],
      game_info = null
    } = req.body;

    // Validate required fields
    if (!title || !content) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'Title and content are required'
      });
    }

    // Check if article exists
    const existingArticle = await database.get('SELECT * FROM blog_posts WHERE id = ?', [id]);
    if (!existingArticle) {
      return res.status(404).json({
        error: 'Article not found'
      });
    }

    // Generate new slug if title changed
    let slug = existingArticle.slug;
    if (title !== existingArticle.title) {
      const baseSlug = generateSlug(title);
      slug = await ensureUniqueSlug(baseSlug, id);
    }

    // Auto-generate excerpt if not provided
    const finalExcerpt = excerpt || extractExcerpt(content);

    // Convert content to HTML if it looks like markdown
    const htmlContent = content.includes('#') || content.includes('**') || content.includes('*') 
      ? markdownToHtml(content) 
      : content;

    // Update the article
    await database.run(`
      UPDATE blog_posts SET
        title = ?,
        slug = ?,
        content = ?,
        excerpt = ?,
        content_type = ?,
        status = ?,
        featured_image = ?,
        categories = ?,
        tags = ?,
        game_info = ?,
        updated_at = CURRENT_TIMESTAMP,
        published_at = CASE 
          WHEN status = 'published' AND ? != 'published' THEN CURRENT_TIMESTAMP
          WHEN status = 'published' THEN published_at
          ELSE NULL
        END
      WHERE id = ?
    `, [
      title,
      slug,
      htmlContent,
      finalExcerpt,
      content_type,
      status,
      featured_image,
      JSON.stringify(categories),
      JSON.stringify(tags),
      game_info ? JSON.stringify(game_info) : null,
      existingArticle.status, // for comparison in CASE statement
      id
    ]);

    res.json({
      success: true,
      message: 'Article updated successfully',
      article: {
        id: parseInt(id),
        title,
        slug,
        content_type,
        status,
        updated_at: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error updating article:', error);
    res.status(500).json({
      error: 'Failed to update article',
      message: error.message
    });
  }
});

// DELETE /api/articles/:id - Delete article
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if article exists
    const existingArticle = await database.get('SELECT title FROM blog_posts WHERE id = ?', [id]);
    if (!existingArticle) {
      return res.status(404).json({
        error: 'Article not found'
      });
    }

    // Delete the article
    const result = await database.run('DELETE FROM blog_posts WHERE id = ?', [id]);

    if (result.changes === 0) {
      return res.status(404).json({
        error: 'Article not found'
      });
    }

    res.json({
      success: true,
      message: 'Article deleted successfully',
      deletedArticle: {
        id: parseInt(id),
        title: existingArticle.title
      }
    });

  } catch (error) {
    console.error('Error deleting article:', error);
    res.status(500).json({
      error: 'Failed to delete article',
      message: error.message
    });
  }
});

// POST /api/articles - Create new article
router.post('/', async (req, res) => {
  try {
    const {
      title,
      content,
      excerpt,
      content_type = 'strategy_article',
      status = 'draft',
      featured_image,
      categories = [],
      tags = []
    } = req.body;

    // Validate required fields
    if (!title || !content) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'Title and content are required'
      });
    }

    // Generate slug
    const baseSlug = generateSlug(title);
    const slug = await ensureUniqueSlug(baseSlug);

    // Auto-generate excerpt if not provided
    const finalExcerpt = excerpt || extractExcerpt(content);

    // Extract author information from content
    const authorInfo = extractAuthorInfo({}, content); // No E-E-A-T profile for manual articles
    const authorName = formatAuthorForDatabase(authorInfo);

    // Convert content to HTML if it looks like markdown
    const htmlContent = content.includes('#') || content.includes('**') || content.includes('*') 
      ? markdownToHtml(content) 
      : content;

    // Insert the article
    const result = await database.run(`
      INSERT INTO blog_posts (
        title, slug, content, excerpt, content_type, author,
        status, featured_image, categories, tags,
        created_at, updated_at, published_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      title,
      slug,
      htmlContent,
      finalExcerpt,
      content_type,
      authorName, // 使用提取的作者信息
      status,
      featured_image,
      JSON.stringify(categories),
      JSON.stringify(tags),
      new Date().toISOString(),
      new Date().toISOString(),
      status === 'published' ? new Date().toISOString() : null
    ]);

    res.status(201).json({
      success: true,
      message: 'Article created successfully',
      article: {
        id: result.id || result.lastInsertRowid,
        title,
        slug,
        content_type,
        status,
        created_at: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error creating article:', error);
    res.status(500).json({
      error: 'Failed to create article',
      message: error.message
    });
  }
});

// GET /api/articles/game-guides - Get all published game guide articles for selection
router.get('/game-guides', async (req, res) => {
  try {
    // Return empty array for now to prevent 500 errors
    // TODO: Implement proper data fetching when database has game guide articles
    console.log('Game guides endpoint called - returning empty array (functionality disabled)');
    
    res.json({
      success: true,
      gameGuides: [],
      message: 'Game guides data temporarily unavailable'
    });

  } catch (error) {
    console.error('Error in game guides endpoint:', error);
    res.json({
      success: true,
      gameGuides: [],
      message: 'Game guides data temporarily unavailable'
    });
  }
});

// GET /api/articles/casino-reviews - Get all published casino review articles for selection
router.get('/casino-reviews', async (req, res) => {
  try {
    // Return empty array for now to prevent 500 errors
    // TODO: Implement proper data fetching when database has casino review articles
    console.log('Casino reviews endpoint called - returning empty array (functionality disabled)');
    
    res.json({
      success: true,
      casinoReviews: [],
      message: 'Casino reviews data temporarily unavailable'
    });

  } catch (error) {
    console.error('Error in casino reviews endpoint:', error);
    res.json({
      success: true,
      casinoReviews: [],
      message: 'Casino reviews data temporarily unavailable'
    });
  }
});

// GET /api/articles/stats - Get article statistics
router.get('/admin/stats', async (req, res) => {
  try {
    // Get overall stats
    const totalArticles = await database.get('SELECT COUNT(*) as count FROM blog_posts');
    const publishedArticles = await database.get('SELECT COUNT(*) as count FROM blog_posts WHERE status = \'published\'');
    const draftArticles = await database.get('SELECT COUNT(*) as count FROM blog_posts WHERE status = \'draft\'');

    // Get content type breakdown
    const contentTypeStats = await database.all(`
      SELECT content_type, COUNT(*) as count, status
      FROM blog_posts 
      GROUP BY content_type, status
      ORDER BY content_type, status
    `);

    // Get recent activity
    const recentArticles = await database.all(`
      SELECT id, title, status, content_type, updated_at
      FROM blog_posts 
      ORDER BY updated_at DESC
      LIMIT 5
    `);

    res.json({
      success: true,
      stats: {
        total: totalArticles.count,
        published: publishedArticles.count,
        draft: draftArticles.count,
        contentTypes: contentTypeStats,
        recentActivity: recentArticles
      }
    });

  } catch (error) {
    console.error('Error fetching article stats:', error);
    res.status(500).json({
      error: 'Failed to fetch statistics',
      message: error.message
    });
  }
});

module.exports = router;