# Quick Railway Setup

## 1. One-Click Deploy Button

You can deploy this backend to Railway with one click:

[![Deploy on Railway](https://railway.app/button.svg)](https://railway.app/template/your-template-id)

## 2. Manual Setup

### Step 1: Create Railway Project
1. Go to [railway.app](https://railway.app)
2. Click "New Project"
3. Select "Deploy from GitHub repo"
4. Choose your repository
5. Set root directory to `backend`

### Step 2: Environment Variables
Add these in Railway dashboard → Variables:

```bash
# Required
NODE_ENV=production
GEMINI_API_KEY=your_gemini_api_key
SERPER_API_KEY=your_serper_api_key
JWT_SECRET=your_jwt_secret_32_chars_minimum
API_KEY=your_api_key

# Optional
ALLOWED_ORIGINS=https://your-frontend.vercel.app
```

### Step 3: Add Database (Optional)
1. Click "New" → "Database" → "PostgreSQL"
2. Railway auto-sets `DATABASE_URL`

### Step 4: Deploy
- Railway auto-deploys on git push
- Check logs in Railway dashboard

## 3. Environment Variables Guide

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `NODE_ENV` | Yes | Environment | `production` |
| `GEMINI_API_KEY` | Yes | Google AI API key | `AIza...` |
| `SERPER_API_KEY` | Yes | Search API key | `abc123...` |
| `JWT_SECRET` | Yes | JWT signing secret | `your-secret-32-chars-min` |
| `API_KEY` | Yes | API authentication | `your-api-key` |
| `DATABASE_URL` | No | PostgreSQL URL | Auto-set by Railway |
| `ALLOWED_ORIGINS` | No | CORS origins | `https://app.vercel.app` |

## 4. Testing Deployment

After deployment, test these endpoints:

```bash
# Health check
curl https://your-app.railway.app/health

# API root
curl https://your-app.railway.app/

# Test with API key
curl -H "X-API-Key: your-api-key" https://your-app.railway.app/api/ideation
```

## 5. Common Issues

**Build fails**: Check package.json and dependencies
**App crashes**: Verify environment variables
**CORS errors**: Add frontend URL to ALLOWED_ORIGINS
**Database errors**: Check DATABASE_URL or use SQLite

## 6. Monitoring

- **Logs**: Railway dashboard → Deployments → View logs
- **Metrics**: Railway dashboard → Metrics tab
- **Health**: `https://your-app.railway.app/health`

## 7. Custom Domain (Optional)

1. Railway dashboard → Settings → Domains
2. Add your custom domain
3. Update DNS records as shown
4. Update CORS settings if needed

## 8. Scaling

Railway auto-scales, but you can:
- Monitor usage in dashboard
- Upgrade plan if needed
- Set resource limits
- Configure auto-scaling rules
