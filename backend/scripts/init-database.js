#!/usr/bin/env node

/**
 * Database Initialization Script
 * 
 * This script initializes the database (PostgreSQL or SQLite) with all required tables and indexes.
 * It can be run manually or as part of the deployment process.
 * 
 * Usage:
 *   node scripts/init-database.js
 * 
 * Environment Variables Required:
 *   - DATABASE_URL (for PostgreSQL)
 *   - NODE_ENV (development/production)
 */

require('dotenv').config();
const database = require('../config/database');

async function initializeDatabase() {
  console.log('🚀 Starting database initialization...');
  
  try {
    // Connect to the database
    console.log('📡 Connecting to database...');
    await database.connect();
    
    console.log('✅ Database initialization completed successfully!');
    console.log('📊 All tables and indexes have been created.');
    
    // Test the connection with a simple query
    console.log('🧪 Testing database connection...');
    
    if (database.isPostgres) {
      const result = await database.get('SELECT NOW() as current_time');
      console.log('⏰ PostgreSQL current time:', result.current_time);
    } else {
      const result = await database.get('SELECT datetime("now") as current_time');
      console.log('⏰ SQLite current time:', result.current_time);
    }
    
    console.log('✅ Database connection test passed!');
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    try {
      await database.close();
      console.log('🔌 Database connection closed.');
    } catch (closeError) {
      console.error('⚠️  Error closing database connection:', closeError);
    }
  }
}

// Run the initialization
if (require.main === module) {
  initializeDatabase()
    .then(() => {
      console.log('🎉 Database initialization script completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Fatal error during database initialization:', error);
      process.exit(1);
    });
}

module.exports = { initializeDatabase };
