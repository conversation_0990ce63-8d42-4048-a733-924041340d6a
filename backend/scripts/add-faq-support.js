#!/usr/bin/env node

/**
 * Add FAQ Support to Articles Table
 * This script adds FAQ JSON field to the articles table to support FAQ sections
 */

require('dotenv').config();
const database = require('../config/database');

async function addFAQSupport() {
  console.log('🚀 Adding FAQ support to articles table...');
  
  try {
    // Connect to database
    await database.connect();
    
    // Check if faq_data column already exists (PostgreSQL version)
    const checkColumn = await database.get(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'articles' 
      AND column_name = 'faq_data'
      AND table_schema = 'public'
    `);
    
    if (checkColumn) {
      console.log('✅ FAQ data column already exists!');
      return;
    }
    
    // Add faq_data column to articles table
    console.log('📝 Adding faq_data column to articles table...');
    await database.run(`
      ALTER TABLE articles 
      ADD COLUMN faq_data TEXT
    `);
    
    console.log('✅ FAQ support added successfully!');
    
    // Verify the column was added
    const verifyColumn = await database.get(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'articles' 
      AND column_name = 'faq_data'
    `);
    
    if (verifyColumn) {
      console.log('✅ Verified: faq_data column exists');
    } else {
      console.error('❌ Warning: Could not verify faq_data column');
    }
    
  } catch (error) {
    console.error('❌ Error adding FAQ support:', error);
    process.exit(1);
  } finally {
    await database.close();
  }
}

// Run if called directly
if (require.main === module) {
  addFAQSupport()
    .then(() => {
      console.log('🎉 FAQ support script completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Fatal error:', error);
      process.exit(1);
    });
}

module.exports = { addFAQSupport };