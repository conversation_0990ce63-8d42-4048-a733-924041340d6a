const Database = require('../config/database');

/**
 * 检查文章的excerpt字段格式问题
 * 识别可能存在的问题格式，如"SEO Title:"等
 */
async function checkExcerptFormat() {
  try {
    console.log('检查文章excerpt字段格式问题...\n');
    
    await Database.connect();
    
    // 查询所有已发布文章的excerpt字段
    const articles = await Database.all(`
      SELECT 
        id,
        title,
        slug,
        excerpt,
        content_type,
        created_at
      FROM blog_posts 
      WHERE status = 'published' 
      AND excerpt IS NOT NULL 
      AND excerpt != ''
      ORDER BY created_at DESC
    `);
    
    console.log(`找到 ${articles.length} 篇有excerpt的已发布文章\n`);
    
    // 检查各种问题格式
    const problemPatterns = [
      { pattern: /^SEO Title:/i, name: 'SEO Title format' },
      { pattern: /^Meta Description:/i, name: 'Meta Description format' },
      { pattern: /^Focus Keywords:/i, name: 'Focus Keywords format' },
      { pattern: /^Tags:/i, name: 'Tags format' },
      { pattern: /^<strong>SEO Title:/i, name: 'HTML SEO Title format' },
      { pattern: /^\s*$/, name: 'Empty or whitespace only' },
      { pattern: /.{300,}/, name: 'Too long (>300 chars)' }
    ];
    
    let problemCount = 0;
    let problemsByType = {};
    
    articles.forEach(article => {
      let hasProblems = false;
      
      problemPatterns.forEach(({ pattern, name }) => {
        if (pattern.test(article.excerpt)) {
          hasProblems = true;
          
          if (!problemsByType[name]) {
            problemsByType[name] = [];
          }
          
          problemsByType[name].push({
            id: article.id,
            title: article.title.substring(0, 50) + '...',
            excerpt: article.excerpt.substring(0, 100) + '...',
            slug: article.slug
          });
        }
      });
      
      if (hasProblems) {
        problemCount++;
      }
    });
    
    // 报告结果
    console.log('=== 检查结果 ===');
    console.log(`总文章数: ${articles.length}`);
    console.log(`有问题的文章数: ${problemCount}`);
    console.log(`正常的文章数: ${articles.length - problemCount}\n`);
    
    if (problemCount > 0) {
      console.log('=== 发现的问题类型 ===');
      Object.entries(problemsByType).forEach(([problemType, articles]) => {
        console.log(`\n📋 ${problemType} (${articles.length} 篇):`);
        articles.forEach(article => {
          console.log(`  - ID ${article.id}: "${article.title}"`);
          console.log(`    Excerpt: "${article.excerpt}"`);
          console.log(`    URL: /articles/${article.slug}`);
        });
      });
      
      console.log('\n=== 修复建议 ===');
      console.log('1. 对于"SEO Title"等格式错误，需要从content或game_info中重新提取excerpt');
      console.log('2. 对于过长的excerpt，需要截取到合适长度');
      console.log('3. 对于空的excerpt，需要从content中自动生成');
    } else {
      console.log('✅ 所有文章的excerpt格式都正常！');
    }
    
    // 显示一些好的例子
    const goodExamples = articles.filter(article => {
      return !problemPatterns.some(({ pattern }) => pattern.test(article.excerpt));
    }).slice(0, 3);
    
    if (goodExamples.length > 0) {
      console.log('\n=== 正确格式的例子 ===');
      goodExamples.forEach(article => {
        console.log(`✓ "${article.title.substring(0, 40)}..."`);
        console.log(`  Excerpt: "${article.excerpt.substring(0, 100)}..."`);
      });
    }
    
  } catch (error) {
    console.error('检查失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  checkExcerptFormat()
    .then(() => {
      console.log('\n✅ 检查完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 检查失败:', error);
      process.exit(1);
    });
}

module.exports = { checkExcerptFormat };