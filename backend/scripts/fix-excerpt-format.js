const Database = require('../config/database');

/**
 * 修复文章的excerpt字段格式问题
 * 自动从内容或game_info中生成正确的excerpt
 */

// 辅助函数：从内容中提取摘要
function extractExcerptFromContent(content, maxLength = 200) {
  if (!content) return '';
  
  // 移除HTML标签获取纯文本
  let plainText = content.replace(/<[^>]*>/g, '');
  
  // 移除开头的SEO信息部分
  plainText = plainText.replace(/^SEO Title:.*?\n/i, '');
  plainText = plainText.replace(/^Meta Description:.*?\n/i, '');
  plainText = plainText.replace(/^Focus Keywords:.*?\n/i, '');
  plainText = plainText.replace(/^Tags:.*?\n/i, '');
  
  // 清理空白字符
  plainText = plainText.trim();
  
  if (plainText.length <= maxLength) {
    return plainText;
  }
  
  // 查找最后一个完整句子
  const truncated = plainText.substring(0, maxLength);
  const lastSentence = truncated.lastIndexOf('.');
  
  if (lastSentence > maxLength * 0.7) {
    return truncated.substring(0, lastSentence + 1);
  }
  
  return truncated + '...';
}

// 辅助函数：从game_info中提取SEO描述
function extractExcerptFromGameInfo(gameInfoString) {
  try {
    const gameInfo = JSON.parse(gameInfoString);
    if (gameInfo.seo && gameInfo.seo.description) {
      return gameInfo.seo.description;
    }
  } catch (e) {
    console.log('  ⚠️  无法解析game_info JSON');
  }
  return null;
}

async function fixExcerptFormat() {
  try {
    console.log('修复文章excerpt字段格式问题...\n');
    
    await Database.connect();
    
    // 查询有问题的文章
    const articles = await Database.all(`
      SELECT 
        id,
        title,
        slug,
        excerpt,
        content,
        game_info,
        content_type
      FROM blog_posts 
      WHERE status = 'published' 
      AND excerpt IS NOT NULL 
      AND (
        excerpt LIKE 'SEO Title:%' OR
        excerpt LIKE 'Meta Description:%' OR
        excerpt LIKE 'Focus Keywords:%' OR
        excerpt LIKE 'Tags:%' OR
        excerpt LIKE '<strong>SEO Title:%' OR
        LENGTH(excerpt) > 300 OR
        TRIM(excerpt) = ''
      )
      ORDER BY created_at DESC
    `);
    
    console.log(`找到 ${articles.length} 篇需要修复的文章\n`);
    
    if (articles.length === 0) {
      console.log('✅ 没有发现需要修复的excerpt格式问题！');
      return;
    }
    
    let fixedCount = 0;
    
    for (const article of articles) {
      console.log(`\n=== 修复文章 ID ${article.id} ===`);
      console.log(`标题: "${article.title.substring(0, 50)}..."`);
      console.log(`当前excerpt: "${article.excerpt.substring(0, 100)}..."`);
      
      let newExcerpt = '';
      
      // 方法1：尝试从game_info中的SEO description获取
      if (article.game_info) {
        newExcerpt = extractExcerptFromGameInfo(article.game_info);
        if (newExcerpt) {
          console.log('  ✓ 从game_info.seo.description获取excerpt');
        }
      }
      
      // 方法2：如果没有SEO description，从内容中提取
      if (!newExcerpt && article.content) {
        newExcerpt = extractExcerptFromContent(article.content, 200);
        if (newExcerpt) {
          console.log('  ✓ 从文章内容中提取excerpt');
        }
      }
      
      // 方法3：如果还是没有，使用默认格式
      if (!newExcerpt) {
        newExcerpt = `Análise completa e detalhada. Confira nossa avaliação especializada sobre ${article.title.split(' ')[0] || 'este conteúdo'}.`;
        console.log('  ⚠️  使用默认excerpt格式');
      }
      
      console.log(`  新excerpt: "${newExcerpt.substring(0, 100)}..."`);
      
      // 执行更新
      if (newExcerpt && newExcerpt !== article.excerpt) {
        try {
          await Database.run(
            'UPDATE blog_posts SET excerpt = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [newExcerpt, article.id]
          );
          
          console.log('  ✅ 更新成功');
          fixedCount++;
          
        } catch (updateError) {
          console.error(`  ❌ 更新失败:`, updateError.message);
        }
      } else {
        console.log('  ⚠️  跳过更新（无有效的新excerpt）');
      }
    }
    
    console.log(`\n=== 修复完成 ===`);
    console.log(`总共修复: ${fixedCount} 篇文章`);
    console.log(`失败: ${articles.length - fixedCount} 篇文章`);
    
    // 验证修复结果
    if (fixedCount > 0) {
      console.log('\n=== 验证修复结果 ===');
      const verifyResult = await Database.all(`
        SELECT id, title, excerpt 
        FROM blog_posts 
        WHERE id IN (${articles.slice(0, 3).map(a => a.id).join(',')})
      `);
      
      verifyResult.forEach(article => {
        console.log(`✓ ID ${article.id}: "${article.excerpt.substring(0, 80)}..."`);
      });
    }
    
  } catch (error) {
    console.error('修复失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  fixExcerptFormat()
    .then(() => {
      console.log('\n✅ 修复完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 修复失败:', error);
      process.exit(1);
    });
}

module.exports = { fixExcerptFormat };