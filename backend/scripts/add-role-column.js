#!/usr/bin/env node

/**
 * Add Role Column Migration Script
 * 
 * This script adds the 'role' column to the users table if it doesn't exist.
 * 
 * Usage:
 *   node scripts/add-role-column.js
 */

require('dotenv').config();
const database = require('../config/database');

async function addRoleColumn() {
  console.log('🚀 Adding role column to users table...');
  
  try {
    // Connect to the database
    console.log('📡 Connecting to database...');
    await database.connect();
    
    // Check if role column already exists
    console.log('🔍 Checking if role column exists...');
    
    try {
      if (database.isPostgres) {
        // PostgreSQL: Check if column exists
        const columnExists = await database.get(`
          SELECT column_name 
          FROM information_schema.columns 
          WHERE table_name = 'users' AND column_name = 'role'
        `);
        
        if (!columnExists) {
          console.log('➕ Adding role column to PostgreSQL users table...');
          await database.run(`
            ALTER TABLE users 
            ADD COLUMN role VARCHAR(50) DEFAULT 'user'
          `);
          console.log('✅ Role column added to PostgreSQL users table!');
        } else {
          console.log('✅ Role column already exists in PostgreSQL users table!');
        }
      } else {
        // SQLite: Try to add column (will fail if exists)
        try {
          console.log('➕ Adding role column to SQLite users table...');
          await database.run(`
            ALTER TABLE users 
            ADD COLUMN role TEXT DEFAULT 'user'
          `);
          console.log('✅ Role column added to SQLite users table!');
        } catch (error) {
          if (error.message.includes('duplicate column name')) {
            console.log('✅ Role column already exists in SQLite users table!');
          } else {
            throw error;
          }
        }
      }
      
      // Update super admin user to have admin role
      console.log('👑 Setting super admin <NAME_EMAIL>...');
      const result = await database.run(`
        UPDATE users 
        SET role = 'admin' 
        WHERE email = '<EMAIL>'
      `);
      
      if (result.changes > 0) {
        console.log('✅ Super admin role set successfully!');
      } else {
        console.log('ℹ️  Super admin user not found or role already set.');
      }
      
      // Verify the changes
      console.log('🔍 Verifying role column and super admin...');
      const superAdmin = await database.get(`
        SELECT id, email, full_name, role 
        FROM users 
        WHERE email = '<EMAIL>'
      `);
      
      if (superAdmin) {
        console.log('✅ Super admin verification:');
        console.log(`   📧 Email: ${superAdmin.email}`);
        console.log(`   👤 Name: ${superAdmin.full_name}`);
        console.log(`   🎭 Role: ${superAdmin.role}`);
        console.log(`   🆔 ID: ${superAdmin.id}`);
      } else {
        console.log('⚠️  Super admin user not found!');
      }
      
    } catch (error) {
      console.error('❌ Error during migration:', error);
      throw error;
    }
    
    console.log('');
    console.log('🎉 Role column migration completed successfully!');
    console.log('🌐 You can now login to the admin panel at: http://localhost:5173/admin.html');
    console.log('🔑 Credentials: <EMAIL> / Admin123!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    try {
      await database.close();
      console.log('🔌 Database connection closed.');
    } catch (closeError) {
      console.error('⚠️  Error closing database connection:', closeError);
    }
  }
}

// Run the migration
if (require.main === module) {
  addRoleColumn()
    .then(() => {
      console.log('🎉 Migration script completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Fatal error during migration:', error);
      process.exit(1);
    });
}

module.exports = { addRoleColumn };
