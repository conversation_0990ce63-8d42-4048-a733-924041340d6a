/**
 * 专门测试中文作者提取功能
 */

const { extractAuthorFromContent } = require('../utils/extractAuthorInfo');

console.log('=== 中文作者提取专项测试 ===\n');

// 测试用例
const testCases = [
  {
    name: '标准格式',
    content: `
# 在线21点完整指南

## 关于作者

李明是一位资深赌场分析师，在iGaming行业拥有超过12年的经验。他专业于赌场评测、游戏分析和负责任博彩实践。
    `,
    expected: '李明'
  },
  {
    name: '作者简介格式',
    content: `
# 赌场游戏指南

## 作者简介

王小红是专业的博彩顾问，拥有丰富的行业经验。
    `,
    expected: '王小红'
  },
  {
    name: '作者:格式',
    content: `
# 游戏策略文章

作者: 张伟
    `,
    expected: '张伟'
  },
  {
    name: '三个字的名字',
    content: `
## 关于作者

欧阳明轩是一位资深的游戏分析专家。
    `,
    expected: '欧阳明轩'
  },
  {
    name: '四个字的名字',
    content: `
## 关于作者

司马相如是古代的文学大师，现在专注于现代博彩分析。
    `,
    expected: '司马相如'
  }
];

console.log('测试各种中文作者提取格式:\n');

for (const testCase of testCases) {
  console.log(`测试: ${testCase.name}`);
  console.log(`内容: ${testCase.content.trim().replace(/\n/g, ' ')}`);
  
  const result = extractAuthorFromContent(testCase.content);
  console.log(`提取结果: "${result || 'null'}"`);
  console.log(`期望结果: "${testCase.expected}"`);
  console.log(`测试结果: ${result === testCase.expected ? '✅ 通过' : '❌ 失败'}`);
  console.log('---');
}

// 调试正则表达式
console.log('\n调试正则表达式匹配:\n');

const debugContent = `
## 关于作者

李明是一位资深赌场分析师，在iGaming行业拥有超过12年的经验。
`;

const patterns = [
  {
    name: '通用模式',
    regex: /(?:About the Author|Sobre o Autor|关于作者)[^]*?([A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+(?:\s+[A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+)*)\s+(?:is|é|是)/i
  },
  {
    name: '中文专用模式',
    regex: /(?:关于作者|作者简介)[^]*?([\u4e00-\u9fff]{2,4})是/i
  },
  {
    name: '新的中文模式',
    regex: /(?:关于作者|作者简介)[^]*?([\u4e00-\u9fff]{2,5})(?:是|为)/i
  }
];

for (const pattern of patterns) {
  console.log(`模式: ${pattern.name}`);
  const match = debugContent.match(pattern.regex);
  if (match) {
    console.log(`匹配成功: "${match[1]}"`);
  } else {
    console.log('匹配失败');
  }
  console.log('---');
}

console.log('\n=== 测试完成 ===');