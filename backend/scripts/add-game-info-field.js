#!/usr/bin/env node

const database = require('../config/database');

async function addGameInfoField() {
  try {
    console.log('连接数据库...');
    await database.connect();
    
    // 检查game_info字段是否已存在
    const tableInfo = await database.all("PRAGMA table_info(blog_posts)");
    const gameInfoExists = tableInfo.some(column => column.name === 'game_info');
    
    if (gameInfoExists) {
      console.log('game_info字段已存在，跳过添加');
      await database.close();
      return;
    }
    
    console.log('添加game_info字段到blog_posts表...');
    
    // 添加game_info JSON字段
    await database.run(`
      ALTER TABLE blog_posts 
      ADD COLUMN game_info TEXT
    `);
    
    console.log('✅ game_info字段添加成功！');
    
    // 验证字段添加
    const updatedTableInfo = await database.all("PRAGMA table_info(blog_posts)");
    console.log('\n更新后的表结构:');
    updatedTableInfo.forEach(column => {
      console.log(`- ${column.name}: ${column.type}`);
    });
    
    await database.close();
    console.log('\n✅ 数据库迁移完成！');
    
  } catch (error) {
    console.error('❌ 数据库迁移失败:', error);
    process.exit(1);
  }
}

addGameInfoField();