#!/usr/bin/env node

/**
 * Reset Super Admin Password Script
 * 
 * This script resets the password for the super admin user (<EMAIL>).
 * 
 * Usage:
 *   node scripts/reset-super-admin-password.js [new-password]
 */

require('dotenv').config();
const database = require('../config/database');
const bcrypt = require('bcryptjs');

const SUPER_ADMIN_EMAIL = '<EMAIL>';
const DEFAULT_PASSWORD = 'Admin123!'; // Default password if none provided

async function resetSuperAdminPassword() {
  console.log('🚀 Resetting super admin password...');
  
  try {
    // Get new password from command line or use default
    const newPassword = process.argv[2] || DEFAULT_PASSWORD;
    
    // Connect to the database
    console.log('📡 Connecting to database...');
    await database.connect();
    
    // Check if super admin exists
    console.log('🔍 Finding super admin user...');
    const user = await database.get(
      'SELECT id, email, full_name FROM users WHERE email = ?',
      [SUPER_ADMIN_EMAIL]
    );
    
    if (!user) {
      console.error('❌ Super admin user not found!');
      console.log('💡 Run create-super-admin.js first to create the user.');
      process.exit(1);
    }
    
    console.log(`👤 Found user: ${user.full_name || user.email} (ID: ${user.id})`);
    
    // Hash the new password
    console.log('🔐 Hashing new password...');
    const passwordHash = await bcrypt.hash(newPassword, 12);
    
    // Update the password
    console.log('💾 Updating password in database...');
    await database.run(
      'UPDATE users SET password_hash = ?, email_verified = ? WHERE email = ?',
      [passwordHash, true, SUPER_ADMIN_EMAIL]
    );
    
    console.log('✅ Super admin password reset successfully!');
    console.log('');
    console.log('🎯 Login Credentials:');
    console.log(`📧 Email: ${SUPER_ADMIN_EMAIL}`);
    console.log(`🔑 Password: ${newPassword}`);
    console.log('');
    console.log('🌐 Admin Panel: http://localhost:5173/admin.html');
    console.log('');
    console.log('🎉 You can now login to the admin panel!');
    
  } catch (error) {
    console.error('❌ Failed to reset super admin password:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    try {
      await database.close();
      console.log('🔌 Database connection closed.');
    } catch (closeError) {
      console.error('⚠️  Error closing database connection:', closeError);
    }
  }
}

// Run the script
if (require.main === module) {
  resetSuperAdminPassword()
    .then(() => {
      console.log('🎉 Password reset script completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Fatal error during password reset:', error);
      process.exit(1);
    });
}

module.exports = { resetSuperAdminPassword };
