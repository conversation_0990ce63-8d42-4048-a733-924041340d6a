const { Pool } = require('pg');

// 生产数据库连接
const pool = new Pool({
  connectionString: 'postgresql://postgres:<EMAIL>:21666/railway',
  ssl: {
    rejectUnauthorized: false
  }
});

async function analyze735BetDetailed() {
  try {
    console.log('详细分析735 Bet文章...\n');
    
    // 查询具体的文章
    const query = `
      SELECT 
        id,
        title,
        slug,
        content,
        game_info,
        faq_data,
        author,
        status,
        language,
        content_type,
        created_at,
        updated_at
      FROM blog_posts 
      WHERE id = 19
    `;
    
    const result = await pool.query(query);
    
    if (result.rows.length === 0) {
      console.log('未找到ID为19的文章');
      return;
    }
    
    const article = result.rows[0];
    
    console.log('=== 文章基本信息 ===');
    console.log(`ID: ${article.id}`);
    console.log(`标题: "${article.title}"`);
    console.log(`Slug: ${article.slug}`);
    console.log(`作者: ${article.author}`);
    console.log(`状态: ${article.status}`);
    console.log(`语言: ${article.language}`);
    console.log(`内容类型: ${article.content_type}`);
    console.log(`创建时间: ${article.created_at}`);
    console.log(`更新时间: ${article.updated_at}`);
    
    // 问题1: 检查标题中的**符号
    console.log('\n=== 问题1: 标题分析 ===');
    if (article.title.includes('**')) {
      console.log('⚠️  问题发现: 标题包含多余的**符号');
      console.log(`原标题: "${article.title}"`);
      const cleanTitle = article.title.replace(/\*\*/g, '');
      console.log(`清理后: "${cleanTitle}"`);
    } else {
      console.log('✓ 标题正常，没有**符号');
    }
    
    // 问题2: 检查game_info数据
    console.log('\n=== 问题2: game_info数据分析 ===');
    if (article.game_info) {
      console.log('game_info字段存在，内容:');
      try {
        const gameInfo = JSON.parse(article.game_info);
        console.log(JSON.stringify(gameInfo, null, 2));
        
        // 检查是否有SEO数据
        if (gameInfo.seo) {
          console.log('\n检查SEO数据中的**符号:');
          if (gameInfo.seo.title && gameInfo.seo.title.includes('**')) {
            console.log(`⚠️  SEO标题包含**: "${gameInfo.seo.title}"`);
          }
          if (gameInfo.seo.description && gameInfo.seo.description.includes('**')) {
            console.log(`⚠️  SEO描述包含**: "${gameInfo.seo.description}"`);
          }
          if (gameInfo.seo.keywords && gameInfo.seo.keywords.includes('**')) {
            console.log(`⚠️  SEO关键词包含**: "${gameInfo.seo.keywords}"`);
          }
        }
      } catch (e) {
        console.log(`❌ 解析game_info失败: ${e.message}`);
        console.log('原始内容:', article.game_info.substring(0, 500) + '...');
      }
    } else {
      console.log('❌ game_info字段为空');
    }
    
    // 问题3: 检查faq_data
    console.log('\n=== 问题3: FAQ数据分析 ===');
    if (article.faq_data) {
      console.log('faq_data字段存在，内容:');
      try {
        const faqData = JSON.parse(article.faq_data);
        console.log(JSON.stringify(faqData, null, 2));
      } catch (e) {
        console.log(`❌ 解析faq_data失败: ${e.message}`);
        console.log('原始内容:', article.faq_data.substring(0, 500) + '...');
      }
    } else {
      console.log('❌ faq_data字段为空');
    }
    
    // 问题4: 检查content内容
    console.log('\n=== 问题4: 内容分析 ===');
    const content = article.content;
    console.log(`内容长度: ${content.length} 字符`);
    
    // 检查CASINO_INFO标记
    const casinoInfoStart = content.indexOf('<!-- CASINO_INFO_START');
    const casinoInfoEnd = content.indexOf('<!-- CASINO_INFO_END -->');
    console.log(`CASINO_INFO标记: ${casinoInfoStart !== -1 && casinoInfoEnd !== -1 ? '存在' : '不存在'}`);
    
    // 检查FAQ标记
    const faqStart = content.indexOf('<!-- FAQ_START');
    const faqEnd = content.indexOf('<!-- FAQ_END -->');
    console.log(`FAQ标记: ${faqStart !== -1 && faqEnd !== -1 ? '存在' : '不存在'}`);
    
    // 统计所有**符号
    const starMatches = content.match(/\*\*[^*]*\*\*/g);
    if (starMatches) {
      console.log(`\n内容中的**符号统计: ${starMatches.length} 处`);
      console.log('前10个**标记:');
      starMatches.slice(0, 10).forEach((match, index) => {
        console.log(`${index + 1}. ${match}`);
      });
    } else {
      console.log('\n内容中没有找到**符号');
    }
    
    // 显示内容开头
    console.log('\n内容开头 (前1000字符):');
    console.log('---');
    console.log(content.substring(0, 1000));
    console.log('---');
    
    // 搜索可能的JSON数据
    console.log('\n=== 搜索可能的JSON数据 ===');
    const jsonMatches = content.match(/\{[\s\S]*?\}/g);
    if (jsonMatches) {
      console.log(`找到 ${jsonMatches.length} 个可能的JSON块:`);
      jsonMatches.slice(0, 3).forEach((match, index) => {
        console.log(`\nJSON块 ${index + 1}:`);
        console.log(match.substring(0, 300) + (match.length > 300 ? '...' : ''));
        try {
          JSON.parse(match);
          console.log('✓ 有效的JSON');
        } catch (e) {
          console.log('❌ 无效的JSON');
        }
      });
    }
    
  } catch (error) {
    console.error('分析失败:', error);
  } finally {
    await pool.end();
  }
}

// 执行分析
analyze735BetDetailed();