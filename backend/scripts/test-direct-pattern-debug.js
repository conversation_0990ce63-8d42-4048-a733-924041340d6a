/**
 * 调试直接作者模式为什么不工作
 */

const testContent = `
# 游戏策略文章

作者: 张伟
`;

console.log('测试内容:');
console.log(JSON.stringify(testContent));
console.log('---');

// 手动测试直接模式
console.log('手动测试直接模式:');

const directPatterns = [
  // "作者: 张伟"
  /(?:Author|作者)[:\s]+([A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+(?:\s+[A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+)*)/i,
  
  // "By: <PERSON>"
  /(?:By|Escrito por)[:\s]+([A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+(?:\s+[A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+)*)/i,
  
  // "Written by <PERSON>"
  /Written by\s+([A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+(?:\s+[A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+)*)/i,
];

for (let i = 0; i < directPatterns.length; i++) {
  const pattern = directPatterns[i];
  console.log(`模式 ${i + 1}: ${pattern}`);
  
  const match = testContent.match(pattern);
  if (match && match[1]) {
    const authorName = match[1].trim();
    console.log(`  匹配: "${authorName}"`);
    console.log(`  长度: ${authorName.length}`);
    console.log(`  包含writer: ${authorName.toLowerCase().includes('writer')}`);
    console.log(`  包含ai: ${authorName.toLowerCase().includes('ai')}`);
    console.log(`  长度检查: ${authorName.length > 2 && authorName.length < 30}`);
    
    // 完整检查条件
    if (!authorName.toLowerCase().includes('writer') && 
        !authorName.toLowerCase().includes('ai') &&
        authorName.length > 2 &&
        authorName.length < 30) {
      console.log(`  ✅ 符合所有条件！应该返回: "${authorName}"`);
    } else {
      console.log(`  ❌ 不符合条件`);
    }
  } else {
    console.log(`  无匹配`);
  }
  console.log('---');
}

// 模拟完整函数逻辑
console.log('\n模拟完整函数逻辑:');

// 检查是否找到了author section
const lines = testContent.split(/\n/);
let inAuthorSection = false;
let authorSectionStart = -1;

console.log('检查author section:');
for (let i = 0; i < lines.length; i++) {
  const line = lines[i].trim();
  console.log(`行 ${i}: "${line}"`);
  
  if (/^##?\s*(?:About the Author|Sobre o Autor|关于作者|作者简介)/i.test(line)) {
    inAuthorSection = true;
    authorSectionStart = i;
    console.log(`  找到author section在行 ${i}`);
    break;
  }
}

console.log(`inAuthorSection: ${inAuthorSection}`);
console.log(`authorSectionStart: ${authorSectionStart}`);

if (!inAuthorSection) {
  console.log('没有找到author section，应该检查直接模式');
} else {
  console.log('找到了author section，会跳过直接模式');
}

console.log('\n=== 调试完成 ===');