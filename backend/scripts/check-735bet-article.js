const { Pool } = require('pg');

// 生产数据库连接
const pool = new Pool({
  connectionString: 'postgresql://postgres:<EMAIL>:21666/railway',
  ssl: {
    rejectUnauthorized: false
  }
});

async function check735BetArticle() {
  try {
    console.log('连接到生产数据库...');
    
    // 首先检查数据库中有哪些表
    console.log('检查数据库表结构...');
    const tablesQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `;
    
    const tablesResult = await pool.query(tablesQuery);
    console.log('数据库中的表:');
    tablesResult.rows.forEach(row => console.log(`- ${row.table_name}`));
    
    // 检查可能的文章相关表
    let articleTable = null;
    if (tablesResult.rows.some(row => row.table_name === 'articles')) {
      articleTable = 'articles';
    } else if (tablesResult.rows.some(row => row.table_name === 'blog_posts')) {
      articleTable = 'blog_posts';
    } else if (tablesResult.rows.some(row => row.table_name === 'tasks')) {
      articleTable = 'tasks';
    }
    
    if (!articleTable) {
      console.log('\n❌ 未找到文章相关表！');
      return;
    }
    
    console.log(`\n使用表: ${articleTable}`);
    
    // 先检查表结构
    const columnsQuery = `
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = $1 AND table_schema = 'public'
      ORDER BY ordinal_position
    `;
    
    const columnsResult = await pool.query(columnsQuery, [articleTable]);
    console.log(`\n${articleTable}表的列:)`);
    columnsResult.rows.forEach(row => console.log(`- ${row.column_name} (${row.data_type})`));
    
    // 构建查询语句
    let query;
    if (articleTable === 'blog_posts') {
      query = `
        SELECT 
          id,
          title,
          slug,
          SUBSTRING(content, 1, 3000) as content_preview,
          created_at,
          updated_at
        FROM blog_posts 
        WHERE title LIKE '%735%Bet%' OR slug LIKE '%735%bet%' OR title LIKE '%735%' OR slug LIKE '%735%'
        ORDER BY created_at DESC
        LIMIT 10
      `;
    } else if (articleTable === 'tasks') {
      query = `
        SELECT 
          id,
          title,
          SUBSTRING(task_data::text, 1, 3000) as content_preview,
          created_at,
          updated_at
        FROM tasks 
        WHERE title LIKE '%735%Bet%' OR title LIKE '%735%' OR task_data::text LIKE '%735%'
        ORDER BY created_at DESC
        LIMIT 10
      `;
    } else {
      query = `
        SELECT 
          id,
          title,
          slug,
          category,
          SUBSTRING(content, 1, 3000) as content_preview,
          created_at,
          updated_at
        FROM articles 
        WHERE title LIKE '%735%Bet%' OR slug LIKE '%735%bet%' OR title LIKE '%735%'
        ORDER BY created_at DESC
        LIMIT 10
      `;
    }
    
    const result = await pool.query(query);
    
    if (result.rows.length === 0) {
      console.log('未找到735 Bet相关文章');
      return;
    }
    
    console.log(`\n找到 ${result.rows.length} 篇相关文章:\n`);
    
    for (const article of result.rows) {
      console.log('=====================================');
      console.log(`ID: ${article.id}`);
      console.log(`标题: "${article.title}"`);
      console.log(`Slug: ${article.slug}`);
      console.log(`分类: ${article.category}`);
      console.log(`创建时间: ${article.created_at}`);
      console.log(`更新时间: ${article.updated_at}`);
      
      // 检查标题中的特殊字符
      if (article.title.includes('**')) {
        console.log('\n⚠️  标题包含**符号!');
      }
      
      // 分析内容结构
      const content = article.content_preview;
      console.log('\n内容分析:');
      
      // 检查CASINO_INFO
      const casinoInfoStart = content.indexOf('<!-- CASINO_INFO_START');
      const casinoInfoEnd = content.indexOf('<!-- CASINO_INFO_END -->');
      
      if (casinoInfoStart !== -1 && casinoInfoEnd !== -1) {
        console.log('✓ 找到CASINO_INFO标记');
        
        try {
          // 提取JSON内容
          const jsonMatch = content.substring(casinoInfoStart, casinoInfoEnd).match(/<!-- CASINO_INFO_START -->\s*([\s\S]*?)\s*<!-- CASINO_INFO_END -->/);
          if (jsonMatch && jsonMatch[1]) {
            const casinoInfo = JSON.parse(jsonMatch[1]);
            console.log('\nCASINO_INFO内容:');
            console.log(JSON.stringify(casinoInfo, null, 2).substring(0, 1000) + '...');
            
            // 检查SEO数据
            if (casinoInfo.seo) {
              console.log('\nSEO数据:');
              console.log(`- Title: "${casinoInfo.seo.title}"`);
              console.log(`- Description: "${casinoInfo.seo.description}"`);
              console.log(`- Keywords: "${casinoInfo.seo.keywords}"`);
              
              // 检查是否有多余的**
              if (casinoInfo.seo.title && casinoInfo.seo.title.includes('**')) {
                console.log('\n⚠️  SEO标题包含**符号!');
              }
            } else {
              console.log('\n⚠️  未找到SEO数据!');
            }
          }
        } catch (e) {
          console.log(`\n❌ 解析CASINO_INFO失败: ${e.message}`);
        }
      } else {
        console.log('❌ 未找到CASINO_INFO标记');
      }
      
      // 检查FAQ部分
      const faqStart = content.indexOf('<!-- FAQ_START');
      const faqEnd = content.indexOf('<!-- FAQ_END -->');
      console.log(`\nFAQ标记: ${faqStart !== -1 && faqEnd !== -1 ? '✓ 存在' : '❌ 不存在'}`);
      
      console.log('\n');
    }
    
    // 获取完整内容进行深入分析
    if (result.rows.length > 0) {
      console.log('\n深入分析第一篇文章的完整内容...\n');
      
      const fullQuery = `
        SELECT content, game_info, faq_data
        FROM ${articleTable}
        WHERE id = $1
      `;
      
      const fullResult = await pool.query(fullQuery, [result.rows[0].id]);
      const fullContent = fullResult.rows[0].content;
      
      // 分析完整的CASINO_INFO
      const casinoInfoMatch = fullContent.match(/<!-- CASINO_INFO_START -->\s*([\s\S]*?)\s*<!-- CASINO_INFO_END -->/);
      if (casinoInfoMatch && casinoInfoMatch[1]) {
        try {
          const casinoInfo = JSON.parse(casinoInfoMatch[1]);
          
          console.log('完整的CASINO_INFO结构:');
          console.log('- name:', casinoInfo.name);
          console.log('- rating:', casinoInfo.rating);
          console.log('- established:', casinoInfo.established);
          console.log('- license:', casinoInfo.license);
          console.log('- 包含SEO数据:', !!casinoInfo.seo);
          console.log('- 包含FAQ数据:', !!casinoInfo.faq);
          
          if (casinoInfo.seo) {
            console.log('\n完整SEO数据:');
            console.log(JSON.stringify(casinoInfo.seo, null, 2));
          }
          
          // 检查所有可能包含**的字段
          console.log('\n检查所有字段中的**符号:');
          function checkStars(obj, path = '') {
            for (const [key, value] of Object.entries(obj)) {
              if (typeof value === 'string' && value.includes('**')) {
                console.log(`⚠️  ${path}${key}: "${value}"`);
              } else if (typeof value === 'object' && value !== null) {
                checkStars(value, `${path}${key}.`);
              }
            }
          }
          checkStars(casinoInfo);
          
        } catch (e) {
          console.log(`解析错误: ${e.message}`);
        }
      }
      
      // 检查文章中所有的**
      const starMatches = fullContent.match(/\*\*[^*]+\*\*/g);
      if (starMatches) {
        console.log(`\n文章中找到 ${starMatches.length} 处**标记:`);
        starMatches.slice(0, 10).forEach(match => {
          console.log(`- ${match}`);
        });
        if (starMatches.length > 10) {
          console.log(`... 还有 ${starMatches.length - 10} 处`);
        }
      }
    }
    
  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    await pool.end();
  }
}

// 执行检查
check735BetArticle();