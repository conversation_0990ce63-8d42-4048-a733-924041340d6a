/**
 * 迁移现有文章的作者信息
 * 从文章内容中提取真实作者信息并更新数据库
 */

const database = require('../config/database');
const { extractAuthorInfo, formatAuthorForDatabase } = require('../utils/extractAuthorInfo');

async function migrateAuthorInfo() {
  console.log('=== 开始迁移现有文章的作者信息 ===\n');

  try {
    // 获取所有需要更新的文章（作者为默认值的文章）
    const articles = await database.pool.query(`
      SELECT id, title, content, author 
      FROM blog_posts 
      WHERE author IN ('Writer 777 AI', 'Writer 777 Admin')
      ORDER BY created_at DESC
    `);

    console.log(`找到 ${articles.rows.length} 篇需要更新作者信息的文章\n`);

    if (articles.rows.length === 0) {
      console.log('没有需要更新的文章。');
      return;
    }

    let updatedCount = 0;
    let skippedCount = 0;

    for (const article of articles.rows) {
      console.log(`处理文章: ${article.title}`);
      console.log(`当前作者: ${article.author}`);
      
      try {
        // 从文章内容提取作者信息（没有E-E-A-T配置）
        const authorInfo = extractAuthorInfo({}, article.content);
        const newAuthorName = formatAuthorForDatabase(authorInfo);
        
        // 如果提取到的作者信息与当前不同，则更新
        if (newAuthorName && newAuthorName !== article.author) {
          await database.pool.query(
            'UPDATE blog_posts SET author = $1, updated_at = NOW() WHERE id = $2',
            [newAuthorName, article.id]
          );
          
          console.log(`✅ 已更新: ${article.author} → ${newAuthorName}`);
          updatedCount++;
        } else {
          console.log(`⏭️ 跳过: 作者信息无变化 (${newAuthorName})`);
          skippedCount++;
        }
        
        console.log('---');
        
      } catch (error) {
        console.error(`❌ 处理文章失败 (ID: ${article.id}):`, error.message);
        skippedCount++;
        console.log('---');
      }
    }

    console.log('\n=== 迁移完成 ===');
    console.log(`总文章数: ${articles.rows.length}`);
    console.log(`成功更新: ${updatedCount}`);
    console.log(`跳过/失败: ${skippedCount}`);

    // 显示更新后的作者统计
    const authorStats = await database.pool.query(`
      SELECT author, COUNT(*) as count
      FROM blog_posts 
      GROUP BY author
      ORDER BY count DESC
    `);

    console.log('\n=== 更新后的作者统计 ===');
    authorStats.rows.forEach(stat => {
      console.log(`${stat.author}: ${stat.count} 篇文章`);
    });

  } catch (error) {
    console.error('迁移过程中出现错误:', error);
  }
}

// 运行迁移
migrateAuthorInfo().then(() => {
  console.log('\n脚本执行完成。');
}).catch(error => {
  console.error('脚本执行失败:', error);
});