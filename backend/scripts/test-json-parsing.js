const { Pool } = require('pg');

const pool = new Pool({
  connectionString: 'postgresql://postgres:<EMAIL>:21666/railway',
  ssl: { rejectUnauthorized: false }
});

// 模拟前端的JSON修复逻辑
function repairCasinoJSON(jsonString) {
  console.log('=== 开始JSON修复流程 ===');
  console.log('原始长度:', jsonString.length);
  
  // Step 1: HTML解码
  let repairedJson = jsonString;
  if (jsonString.includes('&quot;') || jsonString.includes('<br>') || jsonString.includes('<p>')) {
    console.log('检测到HTML编码，开始解码...');
    
    // 模拟DOM解码（简化版）
    repairedJson = repairedJson
      .replace(/<p>/g, '')
      .replace(/<\/p>/g, '')
      .replace(/<br>/g, '\n')
      .replace(/&quot;/g, '"')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>');
      
    // Additional cleanup for problematic HTML elements
    repairedJson = repairedJson
      // Fix HTML links that break JSON structure
      .replace(/<a\s+href="([^"]*)"[^>]*>([^<]*)<\/a>/g, '"$2"')
      // Remove any remaining HTML tags
      .replace(/<[^>]*>/g, '')
      // Fix common JSON issues
      .replace(/"\s*:\s*"([^"]*)"([^",}\]]*)"([^",}\]]*)/g, '": "$1$2$3"')
      // Handle broken quotes in URLs
      .replace(/https:\/\/([^"]*)"([^"]*)"([^"]*)/g, 'https://$1$2$3')
      // Clean up extra whitespace
      .replace(/\s+/g, ' ')
      .trim();
  }
  
  console.log('HTML解码后长度:', repairedJson.length);
  
  // Step 2: 尝试基础解析
  try {
    const parsed = JSON.parse(repairedJson);
    console.log('✅ 基础解析成功');
    return parsed;
  } catch (error) {
    console.log('❌ 基础解析失败:', error.message);
    
    // Step 3: 激进修复
    console.log('开始激进修复...');
    
    // Fix screenshot URLs with broken quotes
    repairedJson = repairedJson.replace(
      /"url":\s*"<a href="([^"]*)"[^>]*>([^<]*)<\/a>"/g,
      '"url": "$1"'
    );
    
    // Fix any remaining broken links
    repairedJson = repairedJson.replace(
      /<a\s+href="([^"]*)"[^>]*>([^<]*)<\/a>/g,
      '$1'
    );
    
    // Remove HTML tags that might still be present
    repairedJson = repairedJson.replace(/<[^>]*>/g, '');
    
    // Fix broken URLs missing closing quotes
    repairedJson = repairedJson.replace(
      /"url":\s*"([^"]*\.jpg),/g,
      '"url": "$1",'
    );
    
    // Fix URLs without quotes at all
    repairedJson = repairedJson.replace(
      /"url":\s*([^",\s]+\.(jpg|png|gif)),/g,
      '"url": "$1",'
    );
    
    // Fix common quote escaping issues
    repairedJson = repairedJson.replace(/\\"/g, '"');
    
    // Try to balance quotes
    const openQuotes = (repairedJson.match(/"/g) || []).length;
    if (openQuotes % 2 !== 0) {
      console.log('检测到不平衡引号，尝试修复...');
      // Simple fix: add closing quote at the end if missing
      if (!repairedJson.trim().endsWith('}')) {
        repairedJson = repairedJson.trim() + '"}]}';
      }
    }
    
    try {
      const parsed = JSON.parse(repairedJson);
      console.log('✅ 激进修复成功');
      return parsed;
    } catch (repairError) {
      console.log('❌ 激进修复也失败:', repairError.message);
      console.log('修复后JSON前200字符:', repairedJson.substring(0, 200));
      console.log('修复后JSON后200字符:', repairedJson.substring(Math.max(0, repairedJson.length - 200)));
      return null;
    }
  }
}

async function testJSONParsing() {
  try {
    // 获取735 bet文章
    const result = await pool.query(`
      SELECT id, title, content, slug 
      FROM blog_posts 
      WHERE slug LIKE '%735%bet%' OR title ILIKE '%735%bet%'
      ORDER BY created_at DESC 
      LIMIT 1
    `);

    if (result.rows.length === 0) {
      console.log('未找到735 bet文章');
      return;
    }

    const article = result.rows[0];
    console.log(`测试文章: ${article.title}`);
    console.log('');

    // 提取JSON
    const startTag = '<!-- CASINO_INFO_START -->';
    const endTag = '<!-- CASINO_INFO_END -->';
    
    const startIndex = article.content.indexOf(startTag);
    const endIndex = article.content.indexOf(endTag);
    
    if (startIndex === -1 || endIndex === -1) {
      console.log('未找到CASINO_INFO标记');
      return;
    }
    
    const jsonContent = article.content.substring(startIndex + startTag.length, endIndex).trim();
    
    // 测试修复逻辑
    const casinoData = repairCasinoJSON(jsonContent);
    
    if (casinoData) {
      console.log('\n=== 解析结果 ===');
      console.log('FAQ数量:', casinoData.faq ? casinoData.faq.length : 0);
      
      if (casinoData.faq && casinoData.faq.length > 0) {
        console.log('\nFAQ内容:');
        casinoData.faq.forEach((item, index) => {
          console.log(`${index + 1}. ${item.question}`);
          console.log(`   ${item.answer.substring(0, 80)}...`);
          console.log('');
        });
        
        // 检查FAQ语言
        const firstQuestion = casinoData.faq[0].question.toLowerCase();
        if (firstQuestion.includes('plataforma') || firstQuestion.includes('segura')) {
          console.log('✅ 确认为葡萄牙语FAQ');
        } else if (firstQuestion.includes('legitimate') || firstQuestion.includes('safe')) {
          console.log('❌ 这是英语FAQ');
        }
      }
      
      console.log('\n其他数据:');
      console.log('评分:', casinoData.ratings || 'N/A');
      console.log('优点数量:', casinoData.pros ? casinoData.pros.length : 0);
      console.log('缺点数量:', casinoData.cons ? casinoData.cons.length : 0);
      console.log('截图数量:', casinoData.screenshots ? casinoData.screenshots.length : 0);
    }
    
  } catch (error) {
    console.error('测试错误:', error);
  } finally {
    await pool.end();
  }
}

testJSONParsing();