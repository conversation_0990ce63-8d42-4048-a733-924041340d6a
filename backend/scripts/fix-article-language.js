#!/usr/bin/env node

/**
 * Fix Article Language Script
 * 
 * This script updates all existing articles to Portuguese language
 * since they are all Portuguese content.
 */

require('dotenv').config();
const database = require('../config/database');

async function fixArticleLanguage() {
  console.log('🇵🇹 Starting article language fix...');
  
  try {
    // Connect to the database
    console.log('📡 Connecting to database...');
    await database.connect();
    
    console.log('📝 Updating articles to Portuguese language...');
    
    // First check how many articles we have
    const countResult = await database.get(`
      SELECT COUNT(*) as total FROM blog_posts
    `);
    
    console.log(`📊 Found ${countResult.total} articles to update`);
    
    if (countResult.total > 0) {
      // Update all articles to Portuguese
      const updateResult = await database.run(`
        UPDATE blog_posts 
        SET language = 'pt' 
        WHERE language = 'en' OR language IS NULL
      `);
      
      console.log(`✅ Updated ${updateResult.changes || 'unknown'} articles to Portuguese`);
      
      // Verify the update
      const verifyResult = await database.all(`
        SELECT language, COUNT(*) as count 
        FROM blog_posts 
        GROUP BY language
      `);
      
      console.log('📊 Language distribution after update:');
      verifyResult.forEach(row => {
        console.log(`  ${row.language}: ${row.count} articles`);
      });
    } else {
      console.log('ℹ️  No articles found to update');
    }
    
    console.log('🎉 Article language fix completed successfully!');
    
  } catch (error) {
    console.error('❌ Article language fix failed:', error);
    process.exit(1);
  } finally {
    try {
      await database.close();
      console.log('🔌 Database connection closed.');
    } catch (closeError) {
      console.error('⚠️  Error closing database connection:', closeError);
    }
  }
}

// Run the fix
if (require.main === module) {
  fixArticleLanguage()
    .then(() => {
      console.log('🎉 Article language fix script completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Fatal error during language fix:', error);
      process.exit(1);
    });
}

module.exports = { fixArticleLanguage };