#!/usr/bin/env node

/**
 * Create Super Admin User Script
 * 
 * This script creates the super admin user (<EMAIL>) if it doesn't exist.
 * 
 * Usage:
 *   node scripts/create-super-admin.js
 */

require('dotenv').config();
const database = require('../config/database');
const bcrypt = require('bcryptjs');

const SUPER_ADMIN_EMAIL = '<EMAIL>';
const SUPER_ADMIN_NAME = 'Jerry <PERSON>';
const SUPER_ADMIN_PASSWORD = 'Admin123!'; // Change this to a secure password

async function createSuperAdmin() {
  console.log('🚀 Creating super admin user...');
  
  try {
    // Connect to the database
    console.log('📡 Connecting to database...');
    await database.connect();
    
    // Check if super admin already exists
    console.log('🔍 Checking if super admin already exists...');
    const existingUser = await database.get(
      'SELECT id, email FROM users WHERE email = ?',
      [SUPER_ADMIN_EMAIL]
    );
    
    if (existingUser) {
      console.log('✅ Super admin user already exists!');
      console.log(`📧 Email: ${existingUser.email}`);
      console.log(`🆔 ID: ${existingUser.id}`);
      return;
    }
    
    // Hash the password
    console.log('🔐 Hashing password...');
    const passwordHash = await bcrypt.hash(SUPER_ADMIN_PASSWORD, 12);
    
    // Create the super admin user
    console.log('👤 Creating super admin user...');
    const result = await database.run(
      `INSERT INTO users (email, password_hash, full_name, plan_type, email_verified) 
       VALUES (?, ?, ?, ?, ?)`,
      [SUPER_ADMIN_EMAIL, passwordHash, SUPER_ADMIN_NAME, 'V1_DEFAULT_ACCESS', true]
    );
    
    console.log('✅ Super admin user created successfully!');
    console.log(`📧 Email: ${SUPER_ADMIN_EMAIL}`);
    console.log(`👤 Name: ${SUPER_ADMIN_NAME}`);
    console.log(`🆔 ID: ${result.id || result.lastID}`);
    console.log(`🔑 Password: ${SUPER_ADMIN_PASSWORD}`);
    console.log('');
    console.log('🎉 You can now login to the admin panel with these credentials!');
    console.log('🌐 Admin Panel: http://localhost:5173/admin.html');
    
  } catch (error) {
    console.error('❌ Failed to create super admin user:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    try {
      await database.close();
      console.log('🔌 Database connection closed.');
    } catch (closeError) {
      console.error('⚠️  Error closing database connection:', closeError);
    }
  }
}

// Run the script
if (require.main === module) {
  createSuperAdmin()
    .then(() => {
      console.log('🎉 Super admin creation script completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Fatal error during super admin creation:', error);
      process.exit(1);
    });
}

module.exports = { createSuperAdmin };
