const { Pool } = require('pg');

// 生产数据库连接
const pool = new Pool({
  connectionString: 'postgresql://postgres:<EMAIL>:21666/railway',
  ssl: {
    rejectUnauthorized: false
  }
});

async function check735BetExcerpt() {
  try {
    console.log('检查735 Bet文章的excerpt字段...\n');
    
    // 查询具体的文章信息
    const query = `
      SELECT 
        id,
        title,
        slug,
        excerpt,
        SUBSTRING(content, 1, 500) as content_preview,
        game_info,
        content_type,
        language,
        status,
        created_at,
        updated_at
      FROM blog_posts 
      WHERE slug LIKE '%735%bet%' OR title ILIKE '%735%bet%'
      ORDER BY created_at DESC 
      LIMIT 1
    `;
    
    const result = await pool.query(query);
    
    if (result.rows.length === 0) {
      console.log('未找到735 bet文章');
      return;
    }
    
    const article = result.rows[0];
    
    console.log('=== 文章基本信息 ===');
    console.log(`ID: ${article.id}`);
    console.log(`标题: "${article.title}"`);
    console.log(`Slug: ${article.slug}`);
    console.log(`内容类型: ${article.content_type}`);
    console.log(`语言: ${article.language}`);
    console.log(`状态: ${article.status}`);
    console.log(`创建时间: ${article.created_at}`);
    console.log(`更新时间: ${article.updated_at}`);
    
    console.log('\n=== Excerpt字段分析 ===');
    console.log(`Excerpt字段是否为空: ${!article.excerpt ? '是' : '否'}`);
    if (article.excerpt) {
      console.log(`Excerpt内容: "${article.excerpt}"`);
      console.log(`Excerpt长度: ${article.excerpt.length} 字符`);
    } else {
      console.log('Excerpt字段为空或null');
    }
    
    console.log('\n=== 内容开头分析 ===');
    console.log('内容开头（前500字符）:');
    console.log('---');
    console.log(article.content_preview);
    console.log('---');
    
    console.log('\n=== game_info字段分析 ===');
    if (article.game_info) {
      console.log('game_info字段存在');
      try {
        const gameInfo = JSON.parse(article.game_info);
        console.log('game_info包含以下字段:');
        console.log('- name:', gameInfo.name);
        console.log('- seo title:', gameInfo.seo?.title);
        console.log('- seo description:', gameInfo.seo?.description);
        
        // 重点检查seo.title是否被误用为excerpt
        if (gameInfo.seo?.title) {
          console.log('\n⚠️  发现潜在问题:');
          console.log(`SEO Title: "${gameInfo.seo.title}"`);
          if (gameInfo.seo.title.startsWith('SEO Title:')) {
            console.log('❌ SEO标题格式错误：包含"SEO Title:"前缀');
          }
        }
      } catch (e) {
        console.log(`❌ 解析game_info失败: ${e.message}`);
      }
    } else {
      console.log('game_info字段为空');
    }
    
    // 检查前端可能如何处理这个数据
    console.log('\n=== 前端处理逻辑分析 ===');
    console.log('根据/api/public/articles路由代码，前端获取excerpt的逻辑是:');
    console.log('1. 优先使用数据库中的excerpt字段');
    console.log('2. 如果excerpt为空，则使用extractExcerpt(content)函数从content提取');
    
    if (!article.excerpt) {
      console.log('\n由于excerpt字段为空，前端会从content中提取excerpt');
      // 模拟extractExcerpt函数
      const content = article.content_preview;
      const plainText = content.replace(/<[^>]*>/g, '');
      const excerpt = plainText.length > 200 ? plainText.substring(0, 200) + '...' : plainText;
      console.log(`模拟提取的excerpt: "${excerpt}"`);
    }
    
  } catch (error) {
    console.error('检查失败:', error);
  } finally {
    await pool.end();
  }
}

// 执行检查
check735BetExcerpt();