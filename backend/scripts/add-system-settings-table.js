#!/usr/bin/env node

/**
 * Add System Settings Table Migration Script
 * 
 * This script adds the 'system_settings' table if it doesn't exist and initializes default settings.
 * 
 * Usage:
 *   node scripts/add-system-settings-table.js
 */

require('dotenv').config();
const database = require('../config/database');
const settingsService = require('../services/settingsService');

async function addSystemSettingsTable() {
  console.log('🚀 Adding system_settings table...');
  
  try {
    // Connect to the database
    console.log('📡 Connecting to database...');
    await database.connect();
    
    // Check if system_settings table exists
    console.log('🔍 Checking if system_settings table exists...');
    
    try {
      if (database.isPostgres) {
        // PostgreSQL: Check if table exists
        const tableExists = await database.get(`
          SELECT table_name 
          FROM information_schema.tables 
          WHERE table_name = 'system_settings'
        `);
        
        if (!tableExists) {
          console.log('➕ Creating system_settings table for PostgreSQL...');
          await database.run(`
            CREATE TABLE system_settings (
              id SERIAL PRIMARY KEY,
              setting_key VARCHAR(255) UNIQUE NOT NULL,
              setting_value TEXT,
              description TEXT,
              created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
              updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
          `);
          console.log('✅ System_settings table created for PostgreSQL!');
        } else {
          console.log('✅ System_settings table already exists in PostgreSQL!');
        }
      } else {
        // SQLite: Try to create table (will fail if exists)
        try {
          console.log('➕ Creating system_settings table for SQLite...');
          await database.run(`
            CREATE TABLE IF NOT EXISTS system_settings (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              setting_key TEXT UNIQUE NOT NULL,
              setting_value TEXT,
              description TEXT,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
          `);
          console.log('✅ System_settings table created for SQLite!');
        } catch (error) {
          if (error.message.includes('table system_settings already exists')) {
            console.log('✅ System_settings table already exists in SQLite!');
          } else {
            throw error;
          }
        }
      }
      
      // Initialize default settings using the settings service
      console.log('⚙️ Initializing default system settings...');
      await settingsService.ensureInitialized();
      
      // Verify the settings
      console.log('🔍 Verifying system settings...');
      const allSettings = await settingsService.getAllSettings();
      
      console.log('✅ System settings verification:');
      for (const [key, setting] of Object.entries(allSettings)) {
        console.log(`   🔧 ${key}: ${setting.value}`);
      }
      
      // Test AI model setting specifically
      const currentModel = await settingsService.getAiModel();
      console.log(`🤖 Current AI model: ${currentModel}`);
      
    } catch (error) {
      console.error('❌ Error during migration:', error);
      throw error;
    }
    
    console.log('');
    console.log('🎉 System settings table migration completed successfully!');
    console.log('🌐 You can now update AI model settings in the admin panel!');
    console.log('🔗 Admin Panel: http://localhost:5173/admin.html');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    try {
      await database.close();
      console.log('🔌 Database connection closed.');
    } catch (closeError) {
      console.error('⚠️  Error closing database connection:', closeError);
    }
  }
}

// Run the migration
if (require.main === module) {
  addSystemSettingsTable()
    .then(() => {
      console.log('🎉 Migration script completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Fatal error during migration:', error);
      process.exit(1);
    });
}

module.exports = { addSystemSettingsTable };
