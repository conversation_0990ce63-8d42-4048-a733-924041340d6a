#!/usr/bin/env node

/**
 * PostgreSQL Connection Test Script
 * 
 * This script tests the PostgreSQL connection configuration without initializing tables.
 * Useful for verifying Railway database connectivity before full deployment.
 * 
 * Usage:
 *   DATABASE_URL="your_postgres_url" node scripts/test-postgres-connection.js
 */

require('dotenv').config();
const { Pool } = require('pg');

async function testPostgresConnection() {
  console.log('🧪 Testing PostgreSQL Connection...');
  
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.log('❌ DATABASE_URL environment variable not set');
    console.log('💡 To test PostgreSQL connection, set DATABASE_URL:');
    console.log('   DATABASE_URL="postgresql://user:pass@host:port/db" node scripts/test-postgres-connection.js');
    process.exit(1);
  }
  
  if (!databaseUrl.startsWith('postgresql://')) {
    console.log('❌ DATABASE_URL does not appear to be a PostgreSQL connection string');
    console.log('📝 Expected format: postgresql://username:password@host:port/database');
    console.log('🔍 Current value:', databaseUrl);
    process.exit(1);
  }
  
  console.log('🔗 Database URL detected:', databaseUrl.replace(/:[^:@]*@/, ':****@'));
  
  const pool = new Pool({
    connectionString: databaseUrl,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
  });
  
  try {
    console.log('📡 Attempting to connect...');
    
    // Test basic connection
    const client = await pool.connect();
    console.log('✅ Successfully connected to PostgreSQL!');
    
    // Test basic query
    const result = await client.query('SELECT NOW() as current_time, version() as postgres_version');
    console.log('⏰ Current time:', result.rows[0].current_time);
    console.log('🐘 PostgreSQL version:', result.rows[0].postgres_version);
    
    // Test database info
    const dbInfo = await client.query(`
      SELECT 
        current_database() as database_name,
        current_user as username,
        inet_server_addr() as server_address,
        inet_server_port() as server_port
    `);
    
    console.log('📊 Database info:');
    console.log('   Database:', dbInfo.rows[0].database_name);
    console.log('   User:', dbInfo.rows[0].username);
    console.log('   Server:', dbInfo.rows[0].server_address || 'localhost');
    console.log('   Port:', dbInfo.rows[0].server_port || 'default');
    
    // Test table creation permissions
    console.log('🔧 Testing table creation permissions...');
    
    try {
      await client.query(`
        CREATE TABLE IF NOT EXISTS connection_test (
          id SERIAL PRIMARY KEY,
          test_message VARCHAR(255),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
      console.log('✅ Table creation permission: OK');
      
      // Test insert
      await client.query(`
        INSERT INTO connection_test (test_message) 
        VALUES ('Connection test successful at ' || NOW())
      `);
      console.log('✅ Insert permission: OK');
      
      // Test select
      const testResult = await client.query('SELECT COUNT(*) as count FROM connection_test');
      console.log('✅ Select permission: OK (', testResult.rows[0].count, 'test records)');
      
      // Clean up test table
      await client.query('DROP TABLE IF EXISTS connection_test');
      console.log('✅ Drop table permission: OK');
      
    } catch (permError) {
      console.log('⚠️  Permission test failed:', permError.message);
      console.log('   This might be normal for read-only database users');
    }
    
    client.release();
    
    console.log('🎉 PostgreSQL connection test completed successfully!');
    console.log('✅ Your database is ready for J-Writer deployment');
    
  } catch (error) {
    console.error('❌ PostgreSQL connection test failed:');
    console.error('   Error:', error.message);
    
    if (error.code) {
      console.error('   Error Code:', error.code);
    }
    
    console.log('\n🔍 Troubleshooting tips:');
    console.log('   1. Verify DATABASE_URL is correct');
    console.log('   2. Check if PostgreSQL service is running');
    console.log('   3. Verify network connectivity to database host');
    console.log('   4. Check username/password credentials');
    console.log('   5. Ensure database exists and is accessible');
    
    if (error.code === 'ENOTFOUND') {
      console.log('   🌐 DNS resolution failed - check hostname');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('   🚫 Connection refused - check host and port');
    } else if (error.code === '28P01') {
      console.log('   🔐 Authentication failed - check username/password');
    } else if (error.code === '3D000') {
      console.log('   🗄️  Database does not exist');
    }
    
    process.exit(1);
  } finally {
    await pool.end();
    console.log('🔌 Connection pool closed');
  }
}

// Run the test
if (require.main === module) {
  testPostgresConnection()
    .then(() => {
      console.log('🏁 Test completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testPostgresConnection };
