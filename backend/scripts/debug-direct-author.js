/**
 * 调试直接作者格式："作者: 张伟"
 */

const { extractAuthorFromContent } = require('../utils/extractAuthorInfo');

const testContent = `
# 游戏策略文章

作者: 张伟
`;

console.log('测试内容:');
console.log(testContent);
console.log('---');

// 测试直接模式
const directPatterns = [
  {
    name: '作者: 格式',
    regex: /(?:Author|作者)[:\s]+([A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+(?:\s+[A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+)*)/i
  },
  {
    name: '简化作者格式',
    regex: /作者[:\s]+([^\n\r]+)/i
  },
  {
    name: '中文作者专用',
    regex: /作者[:\s]+([\u4e00-\u9fff]+)/i
  }
];

for (const pattern of directPatterns) {
  console.log(`测试模式: ${pattern.name}`);
  const match = testContent.match(pattern.regex);
  if (match) {
    console.log(`匹配成功: "${match[1]}"`);
  } else {
    console.log('匹配失败');
  }
  console.log('---');
}

// 测试实际函数
console.log('实际函数测试:');
const result = extractAuthorFromContent(testContent);
console.log(`函数结果: "${result || 'null'}"`);

// 分行调试
console.log('\n分行调试:');
const lines = testContent.split(/\n/);
for (let i = 0; i < lines.length; i++) {
  const line = lines[i].trim();
  console.log(`行 ${i}: "${line}"`);
  
  if (line) {
    const match = line.match(/(?:Author|作者)[:\s]+([A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+(?:\s+[A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+)*)/i);
    if (match) {
      console.log(`  匹配到: "${match[1]}"`);
    }
  }
}

console.log('\n=== 调试完成 ===');