const { Pool } = require('pg');

// 生产数据库连接
const pool = new Pool({
  connectionString: 'postgresql://postgres:<EMAIL>:21666/railway',
  ssl: {
    rejectUnauthorized: false
  }
});

async function fix735BetArticle() {
  try {
    console.log('修复735 Bet文章的问题...\n');
    
    // 获取当前文章
    const selectQuery = `
      SELECT 
        id, title, content, game_info, faq_data
      FROM blog_posts 
      WHERE id = 19
    `;
    
    const result = await pool.query(selectQuery);
    
    if (result.rows.length === 0) {
      console.log('未找到ID为19的文章');
      return;
    }
    
    const article = result.rows[0];
    let needsUpdate = false;
    let updates = [];
    let values = [];
    let paramIndex = 1;
    
    // 问题1: 修复标题中的**符号
    console.log('=== 修复1: 标题中的**符号 ===');
    const originalTitle = article.title;
    const cleanTitle = originalTitle.replace(/^\*\*/, '').replace(/\*\*$/, '');
    
    if (originalTitle !== cleanTitle) {
      console.log(`原标题: "${originalTitle}"`);
      console.log(`新标题: "${cleanTitle}"`);
      updates.push(`title = $${paramIndex}`);
      values.push(cleanTitle);
      paramIndex++;
      needsUpdate = true;
    } else {
      console.log('✓ 标题无需修改');
    }
    
    // 问题2: 修复内容中的JSON格式
    console.log('\n=== 修复2: 内容中的JSON格式 ===');
    let content = article.content;
    let contentFixed = false;
    
    // 查找CASINO_INFO部分
    const casinoInfoMatch = content.match(/(<!-- CASINO_INFO_START -->)([\s\S]*?)(<!-- CASINO_INFO_END -->)/);
    if (casinoInfoMatch) {
      const casinoInfoContent = casinoInfoMatch[2];
      console.log('找到CASINO_INFO部分，检查JSON格式...');
      
      // 修复HTML实体编码问题
      let fixedCasinoInfo = casinoInfoContent
        .replace(/&quot;/g, '"')
        .replace(/<br>/g, '\n')
        .replace(/<br\/>/g, '\n')
        .replace(/<br \/>/g, '\n')
        .trim();
      
      // 尝试解析JSON
      try {
        const jsonData = JSON.parse(fixedCasinoInfo);
        console.log('✓ JSON格式正确');
        
        // 检查是否需要提取到game_info字段
        if (!article.game_info) {
          console.log('将CASINO_INFO提取到game_info字段...');
          updates.push(`game_info = $${paramIndex}`);
          values.push(JSON.stringify(jsonData));
          paramIndex++;
          needsUpdate = true;
        }
        
        // 检查是否需要提取FAQ数据
        if (jsonData.faq && !article.faq_data) {
          console.log('将FAQ数据提取到faq_data字段...');
          updates.push(`faq_data = $${paramIndex}`);
          values.push(JSON.stringify(jsonData.faq));
          paramIndex++;
          needsUpdate = true;
        }
        
      } catch (e) {
        console.log(`尝试修复JSON格式错误: ${e.message}`);
        
        // 更强力的修复尝试
        fixedCasinoInfo = fixedCasinoInfo
          .replace(/&quot;/g, '"')
          .replace(/<br\s*\/?>/g, '')
          .replace(/\s+/g, ' ')
          .trim();
        
        try {
          const jsonData = JSON.parse(fixedCasinoInfo);
          console.log('✓ JSON修复成功');
          
          // 更新内容
          const newCasinoContent = `<!-- CASINO_INFO_START -->\n${JSON.stringify(jsonData, null, 2)}\n<!-- CASINO_INFO_END -->`;
          content = content.replace(casinoInfoMatch[0], newCasinoContent);
          contentFixed = true;
          
        } catch (e2) {
          console.log(`❌ JSON修复失败: ${e2.message}`);
        }
      }
    }
    
    if (contentFixed) {
      updates.push(`content = $${paramIndex}`);
      values.push(content);
      paramIndex++;
      needsUpdate = true;
    }
    
    // 执行更新
    if (needsUpdate) {
      console.log('\n=== 执行数据库更新 ===');
      
      const updateQuery = `
        UPDATE blog_posts 
        SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE id = $${paramIndex}
      `;
      
      values.push(19); // 文章ID
      
      console.log('更新SQL:', updateQuery);
      console.log('参数值:', values.map(v => typeof v === 'string' && v.length > 100 ? v.substring(0, 100) + '...' : v));
      
      const updateResult = await pool.query(updateQuery, values);
      console.log(`✓ 更新完成，影响行数: ${updateResult.rowCount}`);
      
      // 验证更新结果
      console.log('\n=== 验证更新结果 ===');
      const verifyResult = await pool.query('SELECT title, game_info IS NOT NULL as has_game_info, faq_data IS NOT NULL as has_faq_data FROM blog_posts WHERE id = 19');
      const updated = verifyResult.rows[0];
      console.log('更新后的标题:', updated.title);
      console.log('是否有game_info:', updated.has_game_info);
      console.log('是否有faq_data:', updated.has_faq_data);
      
    } else {
      console.log('\n=== 无需更新 ===');
      console.log('文章数据已经正确');
    }
    
  } catch (error) {
    console.error('修复失败:', error);
  } finally {
    await pool.end();
  }
}

// 执行修复
fix735BetArticle();