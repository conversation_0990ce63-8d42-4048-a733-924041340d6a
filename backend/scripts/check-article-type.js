const { Pool } = require('pg');

const pool = new Pool({
  connectionString: 'postgresql://postgres:<EMAIL>:21666/railway',
  ssl: { rejectUnauthorized: false }
});

async function checkArticleType() {
  try {
    const result = await pool.query(`
      SELECT 
        id, title, slug, language, content_type, status, 
        published_at, created_at
      FROM blog_posts 
      WHERE slug = 'viso-geral-e-fatos-chave-da-735-bet'
    `);

    if (result.rows.length === 0) {
      console.log('❌ 文章不存在');
      return;
    }

    const article = result.rows[0];
    console.log('📋 文章信息:');
    console.log(`  ID: ${article.id}`);
    console.log(`  标题: ${article.title}`);
    console.log(`  Slug: ${article.slug}`);
    console.log(`  语言: ${article.language}`);
    console.log(`  内容类型: ${article.content_type}`);
    console.log(`  状态: ${article.status}`);
    console.log(`  发布时间: ${article.published_at}`);
    console.log(`  创建时间: ${article.created_at}`);
    console.log('');

    // 检查问题
    const issues = [];
    
    if (article.status !== 'published') {
      issues.push(`❌ 文章状态不是'published'，当前是'${article.status}'`);
    }
    
    if (article.content_type !== 'casino_review') {
      issues.push(`❌ 内容类型不是'casino_review'，当前是'${article.content_type}'`);
    }
    
    if (!article.language || article.language !== 'pt') {
      issues.push(`❌ 语言设置问题，当前是'${article.language}'`);
    }

    if (issues.length > 0) {
      console.log('🚨 发现问题:');
      issues.forEach(issue => console.log(issue));
      console.log('');
      
      // 提供修复建议
      console.log('🔧 修复建议:');
      if (article.content_type !== 'casino_review') {
        console.log(`UPDATE blog_posts SET content_type = 'casino_review' WHERE id = ${article.id};`);
      }
      if (article.status !== 'published') {
        console.log(`UPDATE blog_posts SET status = 'published' WHERE id = ${article.id};`);
      }
      if (!article.published_at) {
        console.log(`UPDATE blog_posts SET published_at = NOW() WHERE id = ${article.id};`);
      }
    } else {
      console.log('✅ 文章配置看起来正确');
    }
    
    // 检查前端路由是否正确匹配
    console.log('🔍 前端路由分析:');
    console.log(`  预期URL: /pt/articles/${article.slug}`);
    console.log(`  文章语言: ${article.language}`);
    console.log(`  内容类型: ${article.content_type}`);
    
  } catch (error) {
    console.error('错误:', error);
  } finally {
    await pool.end();
  }
}

checkArticleType();