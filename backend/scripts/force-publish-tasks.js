const database = require('../config/database');
const { marked } = require('marked');

// Configure marked for HTML conversion
marked.setOptions({
  breaks: true,
  gfm: true,
  sanitize: false
});

// Helper function to convert markdown to HTML
function markdownToHtml(markdown) {
  if (!markdown) return '';

  try {
    return marked(markdown);
  } catch (error) {
    console.error('Error converting markdown to HTML:', error);
    return markdown; // Return original markdown if conversion fails
  }
}

// Helper function to generate slug from title
function generateSlug(title) {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim('-'); // Remove leading/trailing hyphens
}

// Helper function to extract title from article content
function extractTitle(content, fallbackTitle) {
  if (!content) return fallbackTitle || 'Untitled Article';
  
  // Try to find H1 heading
  const h1Match = content.match(/^#\s+(.+)$/m);
  if (h1Match) {
    return h1Match[1].trim();
  }
  
  // Try to find any heading
  const headingMatch = content.match(/^#+\s+(.+)$/m);
  if (headingMatch) {
    return headingMatch[1].trim();
  }
  
  // Try to find title in first line
  const lines = content.split('\n').filter(line => line.trim());
  if (lines.length > 0) {
    const firstLine = lines[0].replace(/^#+\s*/, '').trim();
    if (firstLine.length > 0 && firstLine.length < 200) {
      return firstLine;
    }
  }
  
  return fallbackTitle || 'Untitled Article';
}

// Helper function to extract excerpt from article content
function extractExcerpt(content, maxLength = 200) {
  if (!content) return '';
  
  // Remove markdown formatting
  let text = content
    .replace(/^#+\s+/gm, '') // Remove headings
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
    .replace(/\*(.*?)\*/g, '$1') // Remove italic
    .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Remove links
    .replace(/```[\s\S]*?```/g, '') // Remove code blocks
    .replace(/`(.*?)`/g, '$1') // Remove inline code
    .replace(/\n+/g, ' ') // Replace newlines with spaces
    .trim();
  
  // Get first meaningful paragraph
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 20);
  if (sentences.length > 0) {
    let excerpt = sentences[0].trim();
    if (excerpt.length > maxLength) {
      excerpt = excerpt.substring(0, maxLength).trim();
      // Try to end at a word boundary
      const lastSpace = excerpt.lastIndexOf(' ');
      if (lastSpace > maxLength * 0.8) {
        excerpt = excerpt.substring(0, lastSpace);
      }
      excerpt += '...';
    }
    return excerpt;
  }
  
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
}

// Helper function to ensure unique slug
async function ensureUniqueSlug(baseSlug) {
  let slug = baseSlug;
  let counter = 1;
  
  while (true) {
    const existing = await database.get(
      'SELECT id FROM blog_posts WHERE slug = ?',
      [slug]
    );
    
    if (!existing) {
      return slug;
    }
    
    slug = `${baseSlug}-${counter}`;
    counter++;
  }
}

async function forcePublishTasks() {
  try {
    console.log('🚀 Force publishing tasks with generated articles...');
    
    await database.connect();
    console.log('✅ Database connected');
    
    // Get ALL tasks with generated articles, regardless of status
    const tasksWithArticles = await database.all(`
      SELECT 
        id,
        name,
        content_type,
        generated_article,
        status,
        created_at,
        updated_at
      FROM tasks 
      WHERE generated_article IS NOT NULL 
        AND generated_article != ''
        AND LENGTH(generated_article) > 100
      ORDER BY updated_at DESC
    `);
    
    console.log(`📝 Found ${tasksWithArticles.length} tasks with generated articles`);
    
    if (tasksWithArticles.length === 0) {
      console.log('❌ No tasks with generated articles found');
      return;
    }
    
    let publishedCount = 0;
    let skippedCount = 0;
    
    for (const task of tasksWithArticles) {
      try {
        console.log(`\n📄 Processing: ${task.name} (${task.status})`);
        
        // Extract title from article content or use task name
        const title = extractTitle(task.generated_article, task.name);
        
        // Generate unique slug
        const baseSlug = generateSlug(title);
        const uniqueSlug = await ensureUniqueSlug(baseSlug);
        
        // Extract excerpt
        const excerpt = extractExcerpt(task.generated_article);
        
        // Check if already published
        const existingPost = await database.get(
          'SELECT id FROM blog_posts WHERE slug = ?',
          [uniqueSlug]
        );
        
        if (existingPost) {
          console.log(`⚠️  Already published: ${title}`);
          skippedCount++;
          continue;
        }
        
        // Convert markdown to HTML for proper display
        const htmlContent = markdownToHtml(task.generated_article);

        // Insert into blog_posts table
        await database.run(`
          INSERT INTO blog_posts (
            title,
            slug,
            content,
            excerpt,
            content_type,
            author,
            status,
            created_at,
            updated_at,
            published_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          title,
          uniqueSlug,
          htmlContent,
          excerpt,
          task.content_type || 'article',
          'Writer 777 AI',
          'published',
          task.created_at,
          new Date().toISOString(),
          new Date().toISOString()
        ]);
        
        // Also mark the task as completed if it isn't already
        if (!task.status.includes('Completed')) {
          await database.run(
            'UPDATE tasks SET status = ?, current_step = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            ['Completed', 6, task.id]
          );
          console.log(`✅ Published & Completed: ${title} (${uniqueSlug})`);
        } else {
          console.log(`✅ Published: ${title} (${uniqueSlug})`);
        }
        
        publishedCount++;
        
      } catch (error) {
        console.error(`❌ Error processing task ${task.id}:`, error);
      }
    }
    
    console.log('\n🎉 === PUBLISHING SUMMARY ===');
    console.log(`📊 Total tasks processed: ${tasksWithArticles.length}`);
    console.log(`✅ Successfully published: ${publishedCount}`);
    console.log(`⚠️  Skipped (already exists): ${skippedCount}`);
    console.log(`❌ Errors: ${tasksWithArticles.length - publishedCount - skippedCount}`);
    
    // Show published articles
    const publishedArticles = await database.all(`
      SELECT title, slug, content_type, published_at
      FROM blog_posts 
      WHERE status = 'published'
      ORDER BY published_at DESC
      LIMIT 10
    `);
    
    console.log('\n📚 === PUBLISHED ARTICLES ===');
    publishedArticles.forEach(article => {
      console.log(`📄 ${article.title} (${article.content_type})`);
      console.log(`   🔗 /articles/${article.slug}`);
    });
    
  } catch (error) {
    console.error('💥 Force publishing failed:', error);
    throw error;
  } finally {
    await database.close();
    console.log('🔌 Database connection closed');
  }
}

// Run if called directly
if (require.main === module) {
  forcePublishTasks()
    .then(() => {
      console.log('\n🎯 Force publishing completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Force publishing failed:', error);
      process.exit(1);
    });
}

module.exports = { forcePublishTasks };
