#!/usr/bin/env node

/**
 * Test Prompt System Script
 * 
 * 测试新的基于代码的prompt系统功能
 * 
 * Usage:
 *   node scripts/test-prompt-system.js
 */

require('dotenv').config();
const promptTemplateService = require('../services/promptTemplateService');

async function testPromptSystem() {
  console.log('🧪 开始测试新的prompt系统...\n');
  
  try {
    // 测试1: 获取所有可用的内容类型
    console.log('=== 测试1: 获取所有可用的内容类型 ===');
    const contentTypes = promptTemplateService.getAvailableContentTypes();
    console.log('✅ 可用内容类型:', contentTypes);
    console.log(`📊 共找到 ${contentTypes.length} 种内容类型\n`);
    
    // 测试2: 获取所有活跃模板
    console.log('=== 测试2: 获取所有活跃模板 ===');
    const activeTemplates = await promptTemplateService.getAllActiveTemplates();
    console.log(`✅ 活跃模板数量: ${activeTemplates.length}`);
    activeTemplates.forEach(template => {
      console.log(`  - ${template.content_type}: ${template.template_name}`);
    });
    console.log();
    
    // 测试3: 获取特定内容类型的模板
    console.log('=== 测试3: 获取特定内容类型的模板 ===');
    const testContentTypes = ['casino_review', 'game_guide', 'generic', 'nonexistent'];
    
    for (const contentType of testContentTypes) {
      const template = await promptTemplateService.getTemplateByContentType(contentType);
      if (template) {
        console.log(`✅ ${contentType}: ${template.template_name}`);
      } else {
        console.log(`❌ ${contentType}: 未找到模板`);
      }
    }
    console.log();
    
    // 测试4: 构建动态prompt
    console.log('=== 测试4: 构建动态prompt ===');
    const testArticleData = {
      topics: ['在线老虎机基础知识', '如何选择合适的老虎机'],
      primaryKeywords: ['在线老虎机', '老虎机攻略'],
      secondaryKeywords: ['赌场游戏', 'RTP', '波动性'],
      sources: [
        {
          type: 'url',
          title: '老虎机完整指南',
          url: 'https://example.com/slots-guide',
          content: '老虎机是赌场中最受欢迎的游戏之一...',
          description: '详细介绍老虎机游戏规则'
        }
      ],
      productInfo: {
        name: 'Mega Moolah',
        description: '经典累积奖池老虎机',
        features: ['累积奖池', '免费旋转', '奖金游戏'],
        link: 'https://example.com/mega-moolah'
      },
      authorName: '张明',
      authorBio: '资深游戏分析师，专注于在线赌场游戏研究',
      targetAudience: '新手玩家',
      articleGoal: '教育和指导',
      tonality: 'informative',
      length: 'medium_article',
      format: 'markdown',
      jurisdiction: 'international'
    };
    
    // 测试不同内容类型的prompt构建
    const testTypes = ['casino_review', 'game_guide', 'strategy_article'];
    
    for (const contentType of testTypes) {
      try {
        console.log(`--- 测试 ${contentType} prompt构建 ---`);
        const prompt = await promptTemplateService.buildDynamicPrompt(contentType, testArticleData);
        console.log(`✅ ${contentType} prompt构建成功`);
        console.log(`📏 Prompt长度: ${prompt.length} 字符`);
        
        // 检查关键占位符是否被正确替换
        const hasPlaceholders = prompt.includes('{{') && prompt.includes('}}');
        if (hasPlaceholders) {
          console.log(`⚠️  发现未替换的占位符`);
        } else {
          console.log(`✅ 所有占位符都已正确替换`);
        }
        
        // 显示prompt的前200个字符
        console.log(`📝 Prompt预览: ${prompt.substring(0, 200)}...`);
        console.log();
        
      } catch (error) {
        console.error(`❌ ${contentType} prompt构建失败:`, error.message);
      }
    }
    
    // 测试5: 测试不存在的内容类型
    console.log('=== 测试5: 测试不存在的内容类型 ===');
    try {
      const prompt = await promptTemplateService.buildDynamicPrompt('nonexistent_type', testArticleData);
      console.log('❌ 应该抛出错误，但没有');
    } catch (error) {
      console.log('✅ 正确处理了不存在的内容类型:', error.message);
    }
    console.log();
    
    // 测试6: 测试API兼容性方法
    console.log('=== 测试6: 测试API兼容性方法 ===');
    try {
      await promptTemplateService.initializeDefaultTemplates();
      console.log('✅ initializeDefaultTemplates 方法正常执行（现在为空操作）');
    } catch (error) {
      console.error('❌ initializeDefaultTemplates 方法出错:', error.message);
    }
    
    try {
      await promptTemplateService.createTemplate({});
      console.log('❌ createTemplate 应该抛出错误');
    } catch (error) {
      console.log('✅ createTemplate 正确抛出错误:', error.message);
    }
    
    try {
      await promptTemplateService.updateTemplate('test-id', {});
      console.log('❌ updateTemplate 应该抛出错误');
    } catch (error) {
      console.log('✅ updateTemplate 正确抛出错误:', error.message);
    }
    
    console.log('\n🎉 所有测试完成!');
    console.log('✅ 新的基于代码的prompt系统运行正常');
    
  } catch (error) {
    console.error('💥 测试过程中出现错误:', error);
    throw error;
  }
}

// 运行测试
if (require.main === module) {
  testPromptSystem()
    .then(() => {
      console.log('🏁 测试脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 测试脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { testPromptSystem };