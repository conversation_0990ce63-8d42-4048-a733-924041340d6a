const database = require('../config/database');

async function checkData() {
  await database.connect();

  console.log('=== TASKS TABLE DATA ===');
  const tasks = await database.all('SELECT id, name, status, content_type, created_at FROM tasks ORDER BY created_at DESC LIMIT 10');
  console.log('Found', tasks.length, 'tasks');
  tasks.forEach(task => {
    console.log('ID:', task.id, 'Name:', task.name, 'Status:', task.status, 'Type:', task.content_type);
  });

  console.log('\n=== CHECKING FOR ARTICLES ===');
  const articlesCheck = await database.all('SELECT id, name, status FROM tasks WHERE status LIKE "%Completed%" AND generated_article IS NOT NULL');
  console.log('Completed tasks with articles:', articlesCheck.length);

  console.log('\n=== CHECKING FOR GENERATED ARTICLES (ANY STATUS) ===');
  const generatedCheck = await database.all('SELECT id, name, status, LENGTH(generated_article) as article_length FROM tasks WHERE generated_article IS NOT NULL AND generated_article != ""');
  console.log('Tasks with generated articles:', generatedCheck.length);
  generatedCheck.forEach(task => {
    console.log('ID:', task.id, 'Name:', task.name, 'Status:', task.status, 'Article Length:', task.article_length);
  });

  console.log('\n=== BLOG_POSTS TABLE ===');
  const blogPosts = await database.all('SELECT id, title, content_type FROM blog_posts ORDER BY created_at DESC LIMIT 5');
  console.log('Current blog posts:', blogPosts.length);
  blogPosts.forEach(post => {
    console.log('ID:', post.id, 'Title:', post.title, 'Type:', post.content_type);
  });

  await database.close();
}

checkData().catch(console.error);
