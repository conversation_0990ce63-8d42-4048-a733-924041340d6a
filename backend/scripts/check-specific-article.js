const { Pool } = require('pg');

const pool = new Pool({
  connectionString: 'postgresql://postgres:<EMAIL>:21666/railway',
  ssl: { rejectUnauthorized: false }
});

async function checkSpecificArticle() {
  try {
    // 查找具体的735 bet文章
    const result = await pool.query(`
      SELECT id, title, content, slug, language 
      FROM blog_posts 
      WHERE slug LIKE '%735%bet%' OR title ILIKE '%735%bet%'
      ORDER BY created_at DESC 
      LIMIT 1
    `);

    if (result.rows.length === 0) {
      console.log('未找到735 bet文章');
      return;
    }

    const article = result.rows[0];
    console.log(`文章标题: ${article.title}`);
    console.log(`Slug: ${article.slug}`);
    console.log(`语言: ${article.language || '未设置'}`);
    console.log('');

    // 检查CASINO_INFO标记
    const hasStartTag = article.content.includes('<!-- CASINO_INFO_START -->');
    const hasEndTag = article.content.includes('<!-- CASINO_INFO_END -->');
    
    console.log(`包含CASINO_INFO标记: ${hasStartTag && hasEndTag ? '是' : '否'}`);
    
    if (hasStartTag && hasEndTag) {
      // 提取JSON数据
      const startIndex = article.content.indexOf('<!-- CASINO_INFO_START -->');
      const endIndex = article.content.indexOf('<!-- CASINO_INFO_END -->');
      const jsonContent = article.content.substring(startIndex + 26, endIndex).trim();
      
      console.log(`JSON内容总长度: ${jsonContent.length} 字符`);
      console.log('原始JSON内容（前500字符）:');
      console.log(jsonContent.substring(0, 500));
      console.log('');
      
      // 尝试HTML解码
      const he = require('he');
      let decodedJson = jsonContent;
      
      if (jsonContent.includes('&quot;') || jsonContent.includes('<br>') || jsonContent.includes('<p>')) {
        // 模拟DOM解码
        decodedJson = jsonContent
          .replace(/<p>/g, '')
          .replace(/<\/p>/g, '')
          .replace(/<br>/g, '\n')
          .replace(/&quot;/g, '"')
          .replace(/&amp;/g, '&')
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>');
      }
      
      console.log(`解码后JSON内容总长度: ${decodedJson.length} 字符`);
      console.log('解码后JSON内容（前500字符）:');
      console.log(decodedJson.substring(0, 500));
      console.log('');
      console.log('解码后JSON内容（最后500字符）:');
      console.log(decodedJson.substring(Math.max(0, decodedJson.length - 500)));
      console.log('');
      
      // 检查是否包含FAQ
      const containsFAQ = decodedJson.includes('"faq"');
      console.log(`包含FAQ字段: ${containsFAQ}`);
      
      if (containsFAQ) {
        const faqStart = decodedJson.indexOf('"faq"');
        console.log('FAQ部分内容:');
        console.log(decodedJson.substring(faqStart, faqStart + 1000));
      }
      
      try {
        const casinoData = JSON.parse(decodedJson);
        console.log(`FAQ数量: ${casinoData.faq ? casinoData.faq.length : 0}`);
        
        if (casinoData.faq && casinoData.faq.length > 0) {
          console.log('\n=== 文章中生成的FAQ ===');
          casinoData.faq.forEach((item, index) => {
            console.log(`${index + 1}. 问题: ${item.question}`);
            console.log(`   答案: ${item.answer.substring(0, 100)}...`);
            console.log('');
          });
          
          // 检查是否是葡萄牙语FAQ
          const firstQuestion = casinoData.faq[0].question.toLowerCase();
          if (firstQuestion.includes('casino') && firstQuestion.includes('seguro')) {
            console.log('✅ 这些是葡萄牙语FAQ，来自文章生成内容');
          } else if (firstQuestion.includes('legitimate') && firstQuestion.includes('safe')) {
            console.log('❌ 这些是英语FAQ，可能是默认内容');
          } else {
            console.log('🔍 FAQ语言需要进一步确认');
          }
        } else {
          console.log('❌ JSON中没有FAQ数据，会使用默认FAQ');
        }
      } catch (parseError) {
        console.log('❌ JSON解析失败:', parseError.message);
        console.log('会使用默认FAQ');
      }
    } else {
      console.log('❌ 没有找到CASINO_INFO标记，会使用默认FAQ');
    }
    
  } catch (error) {
    console.error('错误:', error);
  } finally {
    await pool.end();
  }
}

checkSpecificArticle();