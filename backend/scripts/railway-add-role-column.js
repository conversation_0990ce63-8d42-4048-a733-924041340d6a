#!/usr/bin/env node

/**
 * Railway Migration Script - Add Role Column
 * 
 * This script adds the 'role' column to the users table in PostgreSQL on Railway
 * and sets the super admin user role.
 * 
 * Usage:
 *   node scripts/railway-add-role-column.js
 */

require('dotenv').config();
const database = require('../config/database');

const SUPER_ADMIN_EMAIL = '<EMAIL>';

async function addRoleColumnRailway() {
  console.log('🚀 Adding role column to PostgreSQL users table on Railway...');
  
  try {
    // Connect to the database
    console.log('📡 Connecting to PostgreSQL database...');
    await database.connect();
    
    // Check if role column already exists
    console.log('🔍 Checking if role column exists...');
    
    const columnExists = await database.get(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'users' AND column_name = 'role'
    `);
    
    if (!columnExists) {
      console.log('➕ Adding role column to PostgreSQL users table...');
      await database.run(`
        ALTER TABLE users 
        ADD COLUMN role VARCHAR(50) DEFAULT 'user'
      `);
      console.log('✅ Role column added to PostgreSQL users table!');
    } else {
      console.log('✅ Role column already exists in PostgreSQL users table!');
    }
    
    // Update super admin user to have admin role
    console.log(`👑 Setting admin role for ${SUPER_ADMIN_EMAIL}...`);
    const result = await database.run(`
      UPDATE users 
      SET role = 'admin' 
      WHERE email = $1
    `, [SUPER_ADMIN_EMAIL]);
    
    if (result.changes > 0) {
      console.log('✅ Super admin role set successfully!');
    } else {
      console.log('ℹ️  Super admin user not found or role already set.');
    }
    
    // Verify the changes
    console.log('🔍 Verifying role column and super admin...');
    const superAdmin = await database.get(`
      SELECT id, email, full_name, role 
      FROM users 
      WHERE email = $1
    `, [SUPER_ADMIN_EMAIL]);
    
    if (superAdmin) {
      console.log('✅ Super admin verification:');
      console.log(`   📧 Email: ${superAdmin.email}`);
      console.log(`   👤 Name: ${superAdmin.full_name}`);
      console.log(`   🎭 Role: ${superAdmin.role}`);
      console.log(`   🆔 ID: ${superAdmin.id}`);
    } else {
      console.log('⚠️  Super admin user not found!');
    }
    
    // List all users with their roles
    console.log('📋 All users and their roles:');
    const allUsers = await database.all(`
      SELECT id, email, full_name, role 
      FROM users 
      ORDER BY id
    `);
    
    allUsers.forEach(user => {
      console.log(`   ${user.id}: ${user.email} (${user.full_name}) - Role: ${user.role}`);
    });
    
    console.log('');
    console.log('🎉 Role column migration completed successfully!');
    console.log('🌐 You can now login to the admin panel!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    try {
      await database.close();
      console.log('🔌 Database connection closed.');
    } catch (closeError) {
      console.error('⚠️  Error closing database connection:', closeError);
    }
  }
}

// Run the migration
if (require.main === module) {
  addRoleColumnRailway()
    .then(() => {
      console.log('🎉 Migration script completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Fatal error during migration:', error);
      process.exit(1);
    });
}

module.exports = { addRoleColumnRailway };
