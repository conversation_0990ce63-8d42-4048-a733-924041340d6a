/**
 * 调试作者信息提取问题
 * 检查数据库中的文章和任务数据，验证作者提取逻辑
 */

const pool = require('../config/database');
const { getProperAuthor, extractAuthorFromGameInfo, extractAuthorFromContent } = require('../utils/extractAuthorInfo');

async function debugAuthorInfo() {
  console.log('=== 作者信息调试报告 ===\n');
  
  try {
    // 1. 检查已发布文章的作者信息
    console.log('1. 检查已发布文章的作者信息:');
    const publishedArticles = await pool.query(`
      SELECT id, title, author, game_info, 
             CASE 
               WHEN LENGTH(content) > 100 THEN LEFT(content, 100) || '...'
               ELSE content
             END as content_preview
      FROM blog_posts 
      WHERE published = true 
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    
    if (publishedArticles.rows.length === 0) {
      console.log('   ❌ 没有找到已发布的文章');
    } else {
      for (const article of publishedArticles.rows) {
        console.log(`   📄 文章 ${article.id}: "${article.title}"`);
        console.log(`      当前作者: "${article.author}"`);
        console.log(`      game_info存在: ${article.game_info ? '是' : '否'}`);
        
        if (article.game_info) {
          try {
            const gameInfo = typeof article.game_info === 'string' ? 
              JSON.parse(article.game_info) : article.game_info;
            console.log(`      game_info keys: ${Object.keys(gameInfo)}`);
            
            // 检查E-E-A-T配置
            if (gameInfo.eeat) {
              console.log(`      E-E-A-T配置: 存在`);
              console.log(`      E-E-A-T.author: ${gameInfo.eeat.author || '无'}`);
            } else {
              console.log(`      E-E-A-T配置: 不存在`);
            }
          } catch (e) {
            console.log(`      game_info解析错误: ${e.message}`);
          }
        }
        
        // 测试作者提取
        const extractedAuthor = getProperAuthor(article);
        console.log(`      提取的作者: "${extractedAuthor}"`);
        console.log('      ---');
      }
    }
    
    console.log('\n2. 检查文章任务的E-E-A-T配置:');
    const tasks = await pool.query(`
      SELECT id, title, eeat_profile, 
             CASE 
               WHEN LENGTH(generated_content) > 100 THEN LEFT(generated_content, 100) || '...'
               ELSE generated_content
             END as content_preview
      FROM article_tasks 
      WHERE status = 'completed' AND generated_content IS NOT NULL
      ORDER BY created_at DESC 
      LIMIT 3
    `);
    
    if (tasks.rows.length === 0) {
      console.log('   ❌ 没有找到完成的任务');
    } else {
      for (const task of tasks.rows) {
        console.log(`   📋 任务 ${task.id}: "${task.title}"`);
        
        if (task.eeat_profile) {
          try {
            const eeaat = typeof task.eeat_profile === 'string' ? 
              JSON.parse(task.eeat_profile) : task.eeat_profile;
            console.log(`      E-E-A-T author_name: "${eeaat.author_name || '无'}"`);
            console.log(`      E-E-A-T author_title: "${eeaat.author_title || '无'}"`);
          } catch (e) {
            console.log(`      E-E-A-T解析错误: ${e.message}`);
          }
        } else {
          console.log(`      E-E-A-T配置: 不存在`);
        }
        
        // 测试从内容提取作者
        if (task.generated_content) {
          const contentAuthor = extractAuthorFromContent(task.generated_content);
          console.log(`      内容中提取的作者: "${contentAuthor || '无'}"`);
        }
        console.log('      ---');
      }
    }
    
    console.log('\n3. 测试作者提取函数:');
    
    // 测试E-E-A-T提取
    const testGameInfo = {
      eeat: {
        author: "João Silva",
        author_title: "Casino Expert"
      }
    };
    const gameInfoAuthor = extractAuthorFromGameInfo(testGameInfo);
    console.log(`   E-E-A-T提取测试: "${gameInfoAuthor}"`);
    
    // 测试内容提取
    const testContent = `
      # 赌场评论
      这是一个赌场评论文章。
      
      ## About the Author
      Maria Santos是一位资深的iGaming分析师，拥有超过8年的行业经验。
    `;
    const contentAuthor = extractAuthorFromContent(testContent);
    console.log(`   内容提取测试: "${contentAuthor}"`);
    
    console.log('\n4. 检查文章发布逻辑:');
    // 读取任务发布代码片段
    const fs = require('fs');
    const tasksRoute = fs.readFileSync('../routes/tasks.js', 'utf8');
    
    // 查找发布相关的代码
    const publishMatch = tasksRoute.match(/author[:\s]*['"](.*?)['"]|author[:\s]*=\s*['"](.*?)['"]/g);
    if (publishMatch) {
      console.log(`   发布逻辑中的作者设置: ${publishMatch.join(', ')}`);
    } else {
      console.log(`   未找到明确的作者设置代码`);
    }
    
  } catch (error) {
    console.error('调试过程中出错:', error);
  } finally {
    await pool.end();
  }
}

// 运行调试
debugAuthorInfo().then(() => {
  console.log('\n=== 调试完成 ===');
}).catch(console.error);