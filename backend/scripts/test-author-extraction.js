/**
 * 快速测试作者提取功能
 */

const { extractAuthorFromContent, getProperAuthor } = require('../utils/extractAuthorInfo');

function testAuthorExtraction() {
  console.log('=== 测试作者提取功能 ===\n');
  
  // 测试从实际API数据中提取作者
  const sampleContent = `
<h3>About the Author</h3>
<p><PERSON> é um jornalista veterano de jogos com mais de 12 anos de experiência cobrindo a indústria de jogos online. Como um ex-jogador profissional de esports que se tornou editor de jogos, Alex traz percepções únicas tanto da perspectiva de jogos competitivos quanto casuais.</p>
  `;
  
  console.log('测试内容:');
  console.log(sampleContent);
  
  // 调试：查看分行处理
  const lines = sampleContent.split(/\n/);
  console.log('\n--- 分行分析 ---');
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    console.log(`行 ${i}: "${line}"`);
    
    // 检查是否匹配author header
    const cleanHeaderLine = line.replace(/<[^>]*>/g, '').trim();
    console.log(`  清理后的标题: "${cleanHeaderLine}"`);
    
    if (/(?:About the Author|Sobre o Autor|关于作者|作者简介)/i.test(cleanHeaderLine)) {
      console.log('  ✅ 找到作者标题');
      
      // 检查下一行
      if (i + 1 < lines.length) {
        const nextLine = lines[i + 1].trim();
        const cleanLine = nextLine.replace(/<[^>]*>/g, '').trim();
        console.log(`  下一行: "${nextLine}"`);
        console.log(`  清理后: "${cleanLine}"`);
        
        // 测试正则
        const nameMatch = cleanLine.match(/^([A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ]+(?:\s+[A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ]+)+)/);
        console.log(`  名称匹配: ${nameMatch ? nameMatch[1] : '无匹配'}`);
      }
    }
  }
  
  console.log('\n--- 提取结果 ---');
  
  const extracted = extractAuthorFromContent(sampleContent);
  console.log('提取的作者:', extracted);
  
  // 测试完整的 getProperAuthor 函数
  const mockArticle = {
    author: 'Writer 777 AI',
    content: sampleContent,
    game_info: null
  };
  
  const properAuthor = getProperAuthor(mockArticle);
  console.log('getProperAuthor 结果:', properAuthor);
  
  console.log('\n--- 预期结果 ---');
  console.log('应该提取到: "Alex Chen"');
  console.log('实际结果:', extracted === 'Alex Chen' ? '✅ 成功' : '❌ 失败');
}

testAuthorExtraction();