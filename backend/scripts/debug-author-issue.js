const Database = require('../config/database');
const { getProperAuthor } = require('../utils/extractAuthorInfo');

/**
 * 调试作者信息显示问题
 */
async function debugAuthorIssue() {
  try {
    console.log('🔍 调试作者信息提取问题...\n');
    
    await Database.connect();
    
    // 获取最新的几篇文章
    const articles = await Database.all(`
      SELECT id, title, author, content, game_info, created_at
      FROM blog_posts 
      WHERE status = 'published' 
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    
    console.log(`找到 ${articles.length} 篇已发布文章\n`);
    
    for (const article of articles) {
      console.log(`=== 文章 ${article.id}: "${article.title.substring(0, 50)}..." ===`);
      console.log(`原始作者: "${article.author}"`);
      
      // 检查game_info内容
      if (article.game_info) {
        try {
          const gameInfo = JSON.parse(article.game_info);
          console.log('Game Info 结构:', {
            hasEEAT: !!gameInfo.eeat,
            eeAT_author: gameInfo.eeat?.author,
            seo_author: gameInfo.seo?.author
          });
        } catch (e) {
          console.log('Game Info JSON解析失败');
        }
      } else {
        console.log('无 game_info 数据');
      }
      
      // 检查内容中的作者信息
      const contentSnippet = article.content.substring(0, 500);
      const hasAuthorInfo = /(?:About the Author|关于作者|作者简介|Author|作者)[:\s]/i.test(contentSnippet);
      console.log(`内容包含作者信息: ${hasAuthorInfo}`);
      
      // 测试作者提取函数
      const extractedAuthor = getProperAuthor(article);
      console.log(`提取的作者: "${extractedAuthor}"`);
      
      console.log(`变化: ${article.author !== extractedAuthor ? '✅ 有变化' : '❌ 无变化'}`);
      console.log('---\n');
    }
    
    // 测试public API格式
    console.log('=== 测试API响应格式 ===');
    const sampleArticle = articles[0];
    if (sampleArticle) {
      const processedArticle = {
        ...sampleArticle,
        author: getProperAuthor(sampleArticle)
      };
      
      console.log('API会返回的作者:', processedArticle.author);
      console.log('是否不同于原始作者:', sampleArticle.author !== processedArticle.author);
    }
    
  } catch (error) {
    console.error('调试失败:', error);
    throw error;
  }
}

// 直接运行
if (require.main === module) {
  debugAuthorIssue()
    .then(() => {
      console.log('✅ 调试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 调试失败:', error);
      process.exit(1);
    });
}

module.exports = { debugAuthorIssue };