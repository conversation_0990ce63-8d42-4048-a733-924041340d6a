#!/usr/bin/env node

const database = require('../config/database');

async function checkPublishedArticles() {
  try {
    console.log('Connecting to database...');
    await database.connect();
    
    // Check total articles
    const totalArticles = await database.get('SELECT COUNT(*) as count FROM blog_posts');
    console.log(`Total articles in database: ${totalArticles.count}`);
    
    // Check published articles
    const publishedArticles = await database.get('SELECT COUNT(*) as count FROM blog_posts WHERE status = \'published\'');
    console.log(`Published articles: ${publishedArticles.count}`);
    
    // Check content types
    const contentTypes = await database.all(`
      SELECT content_type, COUNT(*) as count, status
      FROM blog_posts 
      GROUP BY content_type, status
      ORDER BY content_type, status
    `);
    
    console.log('\nContent types breakdown:');
    contentTypes.forEach(type => {
      console.log(`${type.content_type || 'NULL'} (${type.status}): ${type.count}`);
    });
    
    // Show sample published articles
    const sampleArticles = await database.all(`
      SELECT id, title, content_type, status, published_at, created_at
      FROM blog_posts 
      ORDER BY created_at DESC
      LIMIT 10
    `);
    
    console.log('\nSample articles:');
    sampleArticles.forEach(article => {
      console.log(`- ${article.title} [${article.content_type}] (${article.status}) - ${article.published_at || article.created_at}`);
    });
    
    await database.close();
    
  } catch (error) {
    console.error('Error checking articles:', error);
    process.exit(1);
  }
}

checkPublishedArticles();