const { Pool } = require('pg');

// 生产数据库连接
const pool = new Pool({
  connectionString: 'postgresql://postgres:<EMAIL>:21666/railway',
  ssl: {
    rejectUnauthorized: false
  }
});

async function checkAllExcerptIssues() {
  try {
    console.log('检查所有文章的excerpt字段问题...\n');
    
    // 查询所有已发布的文章，重点关注excerpt字段
    const query = `
      SELECT 
        id,
        title,
        slug,
        excerpt,
        content_type,
        language,
        LENGTH(excerpt) as excerpt_length
      FROM blog_posts 
      WHERE status = 'published'
      ORDER BY created_at DESC
    `;
    
    const result = await pool.query(query);
    
    console.log(`=== 检查 ${result.rows.length} 篇已发布文章 ===\n`);
    
    let issueCount = 0;
    const issues = [];
    
    for (const article of result.rows) {
      let hasIssue = false;
      let issueType = '';
      
      // 检查各种excerpt问题
      if (!article.excerpt || article.excerpt.trim() === '') {
        hasIssue = true;
        issueType = 'EMPTY_EXCERPT';
      } else if (article.excerpt.startsWith('SEO Title:')) {
        hasIssue = true;
        issueType = 'SEO_TITLE_FORMAT';
      } else if (article.excerpt.startsWith('Meta Description:')) {
        hasIssue = true;
        issueType = 'META_DESC_FORMAT';
      } else if (article.excerpt.startsWith('**')) {
        hasIssue = true;
        issueType = 'MARKDOWN_FORMAT';
      } else if (article.excerpt.length < 20) {
        hasIssue = true;
        issueType = 'TOO_SHORT';
      } else if (article.excerpt.length > 300) {
        hasIssue = true;
        issueType = 'TOO_LONG';
      }
      
      if (hasIssue) {
        issueCount++;
        issues.push({
          id: article.id,
          title: article.title,
          slug: article.slug,
          excerpt: article.excerpt,
          issueType: issueType,
          language: article.language,
          contentType: article.content_type
        });
        
        console.log(`❌ 问题 ${issueCount}: ${issueType}`);
        console.log(`   ID: ${article.id}`);
        console.log(`   标题: "${article.title}"`);
        console.log(`   Slug: ${article.slug}`);
        console.log(`   语言: ${article.language}`);
        console.log(`   类型: ${article.content_type}`);
        console.log(`   Excerpt: "${article.excerpt}"`);
        console.log(`   长度: ${article.excerpt_length} 字符\n`);
      }
    }
    
    // 统计报告
    console.log('=== 统计报告 ===');
    console.log(`总文章数: ${result.rows.length}`);
    console.log(`有问题的文章数: ${issueCount}`);
    console.log(`问题比例: ${(issueCount / result.rows.length * 100).toFixed(1)}%`);
    
    if (issueCount > 0) {
      console.log('\n=== 问题分类统计 ===');
      const issueStats = {};
      issues.forEach(issue => {
        issueStats[issue.issueType] = (issueStats[issue.issueType] || 0) + 1;
      });
      
      Object.entries(issueStats).forEach(([type, count]) => {
        console.log(`${type}: ${count} 篇文章`);
      });
      
      console.log('\n=== 修复建议 ===');
      console.log('1. SEO_TITLE_FORMAT 和 META_DESC_FORMAT: 需要重新从内容或game_info中提取正确的excerpt');
      console.log('2. EMPTY_EXCERPT: 需要从文章内容中自动生成excerpt');
      console.log('3. MARKDOWN_FORMAT: 需要清理markdown格式符号');
      console.log('4. TOO_SHORT: 需要生成更详细的摘要');
      console.log('5. TOO_LONG: 需要截断到合适长度');
    } else {
      console.log('\n✅ 所有文章的excerpt字段都正常！');
    }
    
    // 验证735 Bet文章的修复结果
    console.log('\n=== 验证735 Bet文章修复结果 ===');
    const bet735Query = `
      SELECT id, title, excerpt
      FROM blog_posts 
      WHERE slug LIKE '%735%bet%' OR title ILIKE '%735%bet%'
    `;
    
    const bet735Result = await pool.query(bet735Query);
    if (bet735Result.rows.length > 0) {
      const bet735Article = bet735Result.rows[0];
      console.log(`735 Bet文章当前excerpt: "${bet735Article.excerpt}"`);
      
      if (bet735Article.excerpt.startsWith('SEO Title:')) {
        console.log('❌ 735 Bet文章的excerpt仍然有问题');
      } else {
        console.log('✅ 735 Bet文章的excerpt已经修复');
      }
    }
    
  } catch (error) {
    console.error('检查失败:', error);
  } finally {
    await pool.end();
  }
}

// 执行检查
checkAllExcerptIssues();