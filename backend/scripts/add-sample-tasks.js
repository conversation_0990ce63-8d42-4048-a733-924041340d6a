const database = require('../config/database');

const sampleTasks = [
  {
    name: "Mega Fortune Casino Review - Complete Analysis",
    content_type: "casino_review",
    status: "Completed",
    current_step: 6,
    step_data: JSON.stringify({
      step0: { keywords: ["mega fortune casino", "casino review", "online gambling"] },
      step1: { topics: ["bonus offers", "game selection", "payment methods"] },
      step2: { sources: ["https://megafortune.com"] },
      step3: { products: ["Mega Fortune Casino"] },
      step4: { eeat: "expert casino reviewer" },
      step5: { article: "Complete review content" }
    }),
    generated_article: "Complete casino review article content here..."
  },
  {
    name: "Blackjack Strategy Guide for Beginners",
    content_type: "game_guide",
    status: "Review & Refine",
    current_step: 5,
    step_data: JSON.stringify({
      step0: { keywords: ["blackjack strategy", "card counting", "basic strategy"] },
      step1: { topics: ["basic strategy", "when to hit", "when to stand"] },
      step2: { sources: ["https://blackjack-guide.com"] },
      step3: { products: ["Blackjack Tables"] },
      step4: { eeat: "professional blackjack player" }
    })
  },
  {
    name: "Luxury VIP Casino Experience Review",
    content_type: "casino_review", 
    status: "Generating",
    current_step: 4,
    step_data: JSON.stringify({
      step0: { keywords: ["VIP casino", "luxury gambling", "high roller"] },
      step1: { topics: ["VIP perks", "exclusive games", "personal account manager"] },
      step2: { sources: ["https://luxurycasino.com"] },
      step3: { products: ["VIP Casino Platform"] }
    })
  },
  {
    name: "Poker Tournament Strategy Masterclass",
    content_type: "strategy_article",
    status: "Draft - Step 1", 
    current_step: 0,
    step_data: JSON.stringify({
      step0: { keywords: ["poker tournament", "poker strategy", "tournament tips"] }
    })
  },
  {
    name: "Royal Flush Casino Bonus Analysis",
    content_type: "bonus_analysis",
    status: "Ready to Generate",
    current_step: 3,
    step_data: JSON.stringify({
      step0: { keywords: ["casino bonus", "welcome bonus", "free spins"] },
      step1: { topics: ["wagering requirements", "bonus terms", "maximum cashout"] },
      step2: { sources: ["https://royalflush.com/bonuses"] }
    })
  },
  {
    name: "Bitcoin Casino Security and Privacy Guide",
    content_type: "casino_review",
    status: "Completed",
    current_step: 6,
    step_data: JSON.stringify({
      step0: { keywords: ["bitcoin casino", "crypto gambling", "anonymous betting"] },
      step1: { topics: ["security features", "privacy protection", "crypto deposits"] },
      step2: { sources: ["https://bitcoincasino.com"] },
      step3: { products: ["Bitcoin Casino Platform"] },
      step4: { eeat: "cryptocurrency expert" },
      step5: { article: "Complete bitcoin casino guide" }
    }),
    generated_article: "Comprehensive guide to bitcoin casino security..."
  }
];

async function addSampleTasks() {
  try {
    console.log('Starting to add sample tasks...');
    
    // First, get a user ID to associate tasks with
    // We'll look for the first user in the database
    const users = await database.all('SELECT id, email FROM users LIMIT 1');
    
    if (users.length === 0) {
      throw new Error('No users found in database. Please create a user first.');
    }
    
    const userId = users[0].id;
    console.log(`Using user ID ${userId} (${users[0].email}) for sample tasks`);
    
    let addedCount = 0;
    let skippedCount = 0;
    
    for (const task of sampleTasks) {
      try {
        // Check if task already exists (by name and user)
        const existingTask = await database.get(
          'SELECT id FROM tasks WHERE name = ? AND user_id = ?',
          [task.name, userId]
        );
        
        if (existingTask) {
          console.log(`Task already exists: ${task.name}`);
          skippedCount++;
          continue;
        }
        
        // Insert the task
        const result = await database.run(`
          INSERT INTO tasks (
            user_id, name, content_type, status, current_step, 
            step_data, generated_article, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        `, [
          userId,
          task.name,
          task.content_type,
          task.status,
          task.current_step,
          task.step_data,
          task.generated_article || null
        ]);
        
        console.log(`✓ Added task: ${task.name} (${task.content_type})`);
        addedCount++;
        
      } catch (error) {
        console.error(`Error adding task ${task.name}:`, error);
      }
    }
    
    console.log('\n=== Sample Tasks Summary ===');
    console.log(`Total tasks: ${sampleTasks.length}`);
    console.log(`Successfully added: ${addedCount}`);
    console.log(`Skipped (already exists): ${skippedCount}`);
    
    // Show tasks by content type
    const tasksByType = await database.all(`
      SELECT content_type, COUNT(*) as count, 
             SUM(CASE WHEN status LIKE '%Completed%' THEN 1 ELSE 0 END) as completed
      FROM tasks 
      WHERE user_id = ?
      GROUP BY content_type
      ORDER BY content_type
    `, [userId]);
    
    console.log('\n=== Tasks by Content Type ===');
    tasksByType.forEach(type => {
      console.log(`- ${type.content_type}: ${type.count} tasks (${type.completed} completed)`);
    });
    
  } catch (error) {
    console.error('Failed to add sample tasks:', error);
    throw error;
  } finally {
    await database.close();
    console.log('Database connection closed');
  }
}

// Run if called directly
if (require.main === module) {
  addSampleTasks()
    .then(() => {
      console.log('Sample tasks added successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Failed to add sample tasks:', error);
      process.exit(1);
    });
}

module.exports = { addSampleTasks };