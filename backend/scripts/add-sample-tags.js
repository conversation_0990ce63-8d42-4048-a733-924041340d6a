const Database = require('../config/database');

/**
 * 为现有文章添加示例标签
 */
async function addSampleTags() {
  try {
    console.log('🏷️ 开始为文章添加示例标签...');
    
    await Database.connect();
    
    // 获取现有文章
    const articles = await Database.all(`
      SELECT id, title, content_type 
      FROM blog_posts 
      WHERE status = 'published' 
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    
    console.log(`找到 ${articles.length} 篇文章`);
    
    // 定义标签映射
    const tagsByContentType = {
      'casino_review': ['online casino', 'casino review', 'gambling', 'slots', 'bonuses', 'casino games'],
      'game_guide': ['game guide', 'slot machine', 'casino games', 'how to play', 'gaming tips', 'strategy'],
      'sports_betting': ['sports betting', 'football betting', 'odds', 'betting tips', 'sports analysis'],
      'bonus_analysis': ['casino bonus', 'welcome bonus', 'free spins', 'no deposit', 'promotion'],
      'industry_news': ['gambling news', 'casino industry', 'gaming regulation', 'market trends'],
      'strategy_article': ['gaming strategy', 'winning tips', 'casino tips', 'gambling strategy'],
      'brand_copy': ['casino review', 'online gambling', 'gaming platform']
    };
    
    const generalTags = ['gambling', 'casino', 'online gaming', 'entertainment', 'gaming'];
    
    let updatedCount = 0;
    
    // 为每篇文章添加标签
    for (const article of articles) {
      const contentType = article.content_type || 'general';
      let articleTags = [];
      
      // 根据内容类型选择标签
      if (tagsByContentType[contentType]) {
        // 随机选择2-4个相关标签
        const relevantTags = tagsByContentType[contentType];
        const numTags = Math.floor(Math.random() * 3) + 2; // 2-4个标签
        
        for (let i = 0; i < numTags && i < relevantTags.length; i++) {
          const randomTag = relevantTags[Math.floor(Math.random() * relevantTags.length)];
          if (!articleTags.includes(randomTag)) {
            articleTags.push(randomTag);
          }
        }
      }
      
      // 添加1-2个通用标签
      const numGeneralTags = Math.floor(Math.random() * 2) + 1;
      for (let i = 0; i < numGeneralTags; i++) {
        const randomTag = generalTags[Math.floor(Math.random() * generalTags.length)];
        if (!articleTags.includes(randomTag)) {
          articleTags.push(randomTag);
        }
      }
      
      // 确保至少有2个标签
      if (articleTags.length < 2) {
        articleTags.push('casino', 'gaming');
      }
      
      // 更新文章标签
      const tagsJson = JSON.stringify(articleTags);
      await Database.run(
        'UPDATE blog_posts SET tags = ? WHERE id = ?',
        [tagsJson, article.id]
      );
      
      console.log(`✅ 更新文章 "${article.title}" 的标签:`, articleTags);
      updatedCount++;
    }
    
    console.log(`\n🎉 成功为 ${updatedCount} 篇文章添加了标签`);
    
    // 验证更新结果
    const updatedArticles = await Database.all(`
      SELECT id, title, tags 
      FROM blog_posts 
      WHERE tags IS NOT NULL AND tags != '[]' 
      LIMIT 5
    `);
    
    console.log('\n📊 验证结果:');
    updatedArticles.forEach(article => {
      const tags = JSON.parse(article.tags || '[]');
      console.log(`- "${article.title}": ${tags.join(', ')}`);
    });
    
  } catch (error) {
    console.error('❌ 添加标签失败:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  addSampleTags()
    .then(() => {
      console.log('✅ 标签添加完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 标签添加失败:', error);
      process.exit(1);
    });
}

module.exports = { addSampleTags };