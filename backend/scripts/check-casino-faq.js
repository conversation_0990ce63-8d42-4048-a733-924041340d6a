const { Pool } = require('pg');

const pool = new Pool({
  connectionString: 'postgresql://postgres:<EMAIL>:21666/railway',
  ssl: { rejectUnauthorized: false }
});

async function checkCasinoReviewFAQ() {
  try {
    // 先检查是否有public schema
    const schemaResult = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
    `);
    
    console.log('数据库中的表:');
    schemaResult.rows.forEach(row => console.log(`  - ${row.table_name}`));
    console.log('');
    
    // 先检查blog_posts表结构
    const blogPostsStructure = await pool.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'blog_posts' 
      ORDER BY ordinal_position
    `);
    
    console.log('blog_posts表结构:');
    blogPostsStructure.rows.forEach(row => console.log(`  - ${row.column_name}: ${row.data_type}`));
    console.log('');
    
    // 查找最新的casino review文章（可能在blog_posts表中）
    const result = await pool.query(`
      SELECT id, title, content, created_at 
      FROM public.blog_posts 
      WHERE content ILIKE '%casino%' OR title ILIKE '%casino%' OR content ILIKE '%735%bet%'
      ORDER BY created_at DESC 
      LIMIT 3
    `);

    console.log(`找到 ${result.rows.length} 篇casino review文章\n`);

    for (const article of result.rows) {
      console.log(`\n文章: ${article.title}`);
      console.log(`语言: ${article.language || '未设置'}`);
      console.log(`创建时间: ${article.created_at}`);
      
      // 检查是否有CASINO_INFO标记
      const hasStartTag = article.content.includes('<!-- CASINO_INFO_START -->');
      const hasEndTag = article.content.includes('<!-- CASINO_INFO_END -->');
      
      console.log(`包含CASINO_INFO标记: ${hasStartTag && hasEndTag ? '是' : '否'}`);
      
      if (hasStartTag && hasEndTag) {
        // 提取JSON数据
        const startIndex = article.content.indexOf('<!-- CASINO_INFO_START -->');
        const endIndex = article.content.indexOf('<!-- CASINO_INFO_END -->');
        const jsonContent = article.content.substring(startIndex + 26, endIndex).trim();
        
        try {
          const casinoData = JSON.parse(jsonContent);
          console.log(`FAQ数量: ${casinoData.faq ? casinoData.faq.length : 0}`);
          
          if (casinoData.faq && casinoData.faq.length > 0) {
            console.log('FAQ内容:');
            casinoData.faq.forEach((item, index) => {
              console.log(`  ${index + 1}. Q: ${item.question.substring(0, 50)}...`);
            });
          }
        } catch (parseError) {
          console.log('JSON解析错误:', parseError.message);
          console.log('原始JSON内容（前200字符）:', jsonContent.substring(0, 200));
          
          // 尝试HTML解码
          const he = require('he');
          const decodedJson = he.decode(jsonContent);
          console.log('HTML解码后（前200字符）:', decodedJson.substring(0, 200));
          
          try {
            const casinoData = JSON.parse(decodedJson);
            console.log(`FAQ数量（解码后）: ${casinoData.faq ? casinoData.faq.length : 0}`);
            
            if (casinoData.faq && casinoData.faq.length > 0) {
              console.log('FAQ内容:');
              casinoData.faq.forEach((item, index) => {
                console.log(`  ${index + 1}. Q: ${item.question.substring(0, 50)}...`);
              });
            }
          } catch (secondParseError) {
            console.log('HTML解码后仍然解析失败:', secondParseError.message);
          }
        }
      } else {
        // 检查是否有其他形式的FAQ
        const hasFAQ = article.content.toLowerCase().includes('faq') || 
                       article.content.includes('常见问题') ||
                       article.content.includes('perguntas frequentes');
        console.log(`文章中包含FAQ关键词: ${hasFAQ ? '是' : '否'}`);
      }
      
      console.log('---');
    }
  } catch (error) {
    console.error('错误:', error);
  } finally {
    await pool.end();
  }
}

checkCasinoReviewFAQ();