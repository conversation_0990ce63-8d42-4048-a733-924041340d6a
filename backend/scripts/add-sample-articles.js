const database = require('../config/database');

const sampleArticles = [
  {
    title: "Ultimate Guide to Online Casino Bonuses",
    slug: "ultimate-guide-online-casino-bonuses",
    content: `<h1>Ultimate Guide to Online Casino Bonuses</h1>

<p>Online casino bonuses are one of the most attractive features for players looking to maximize their gaming experience. Understanding the different types of bonuses and how to use them effectively can significantly enhance your chances of success.</p>

<h2>Types of Casino Bonuses</h2>

<h3>Welcome Bonuses</h3>
<p>Welcome bonuses are designed to attract new players to a casino platform. These typically include:</p>
<ul>
<li>Match deposit bonuses (100%, 200%, or even 300% of your first deposit)</li>
<li>Free spins on popular slot games</li>
<li>No-deposit bonuses for trying the platform risk-free</li>
</ul>

<h3>Reload Bonuses</h3>
<p>These bonuses reward existing players for making additional deposits. They're usually smaller than welcome bonuses but provide ongoing value.</p>

<h3>Cashback Bonuses</h3>
<p>Cashback bonuses return a percentage of your losses over a specific period, providing a safety net for your gaming sessions.</p>

<h2>Important Terms and Conditions</h2>

<p>Always read the wagering requirements carefully. Most bonuses come with conditions that must be met before you can withdraw winnings.</p>

<h3>Wagering Requirements</h3>
<p>These specify how many times you must play through the bonus amount before withdrawal. For example, a 30x wagering requirement on a $100 bonus means you need to wager $3,000.</p>

<h2>Responsible Gaming</h2>
<p><strong>Important:</strong> Always gamble responsibly. Set limits for yourself and never bet money you cannot afford to lose. If you feel you may have a gambling problem, seek help from professional organizations.</p>

<p>Remember that casino bonuses are marketing tools designed to attract players. While they can provide value, they should never be the primary reason for gambling.</p>`,
    excerpt: "Learn everything you need to know about online casino bonuses, from welcome offers to wagering requirements, and how to use them responsibly.",
    content_type: "casino_review",
    author: "Writer 777 AI",
    status: "published"
  },
  {
    title: "Blackjack Basic Strategy: A Beginner's Guide",
    slug: "blackjack-basic-strategy-beginners-guide",
    content: `<h1>Blackjack Basic Strategy: A Beginner's Guide</h1>

<p>Blackjack is one of the few casino games where skill can significantly impact your chances of winning. By learning basic strategy, you can reduce the house edge to less than 1%.</p>

<h2>Understanding the Basics</h2>

<p>The goal of blackjack is simple: get as close to 21 as possible without going over, while beating the dealer's hand.</p>

<h3>Card Values</h3>
<ul>
<li>Number cards (2-10): Face value</li>
<li>Face cards (J, Q, K): Worth 10 points</li>
<li>Aces: Worth 1 or 11 (whichever is better for your hand)</li>
</ul>

<h2>Basic Strategy Chart</h2>

<p>Basic strategy is a mathematically proven method for deciding when to hit, stand, double down, or split based on your cards and the dealer's up card.</p>

<h3>Hard Hands (No Ace or Ace counted as 1)</h3>
<p>When you have a hard total of 12-16 and the dealer shows 2-6, stand. If the dealer shows 7 or higher, hit.</p>

<h3>Soft Hands (Ace counted as 11)</h3>
<p>Soft hands offer more flexibility. Generally, you'll want to hit soft 17 or less, and stand on soft 18 or higher.</p>

<h2>Advanced Concepts</h2>

<h3>Doubling Down</h3>
<p>Double down on 11 against any dealer card except an Ace. Also consider doubling on 10 against dealer cards 2-9.</p>

<h3>Splitting Pairs</h3>
<p>Always split Aces and 8s. Never split 10s or 5s.</p>

<h2>Bankroll Management</h2>
<p>Even with perfect basic strategy, blackjack involves variance. Set a budget and stick to it. Never chase losses with bigger bets.</p>

<p><strong>Disclaimer:</strong> This guide is for educational purposes only. Gambling involves risk, and you should never bet more than you can afford to lose.</p>`,
    excerpt: "Master the fundamentals of blackjack basic strategy to minimize the house edge and improve your chances at the tables.",
    content_type: "game_guide",
    author: "Writer 777 AI",
    status: "published"
  },
  {
    title: "The Mathematics Behind Slot Machine RTP",
    slug: "mathematics-behind-slot-machine-rtp",
    content: `<h1>The Mathematics Behind Slot Machine RTP</h1>

<p>Return to Player (RTP) is one of the most important concepts for understanding slot machines. This mathematical principle determines how much money a slot machine returns to players over time.</p>

<h2>What is RTP?</h2>

<p>RTP stands for Return to Player and is expressed as a percentage. For example, a slot with 96% RTP will theoretically return $96 for every $100 wagered over millions of spins.</p>

<h3>Key Points About RTP</h3>
<ul>
<li>RTP is calculated over millions of spins, not individual sessions</li>
<li>Higher RTP generally means better odds for players</li>
<li>RTP includes all wins, including jackpots and bonus features</li>
</ul>

<h2>How RTP is Calculated</h2>

<p>RTP calculation involves complex mathematics considering:</p>
<ul>
<li>Symbol frequencies on each reel</li>
<li>Paytable values</li>
<li>Bonus feature probabilities</li>
<li>Progressive jackpot contributions</li>
</ul>

<h3>Example Calculation</h3>
<p>For a simple 3-reel slot with specific symbol distributions, the RTP is calculated by determining the probability of each winning combination and multiplying by its payout value.</p>

<h2>Volatility vs RTP</h2>

<p>While RTP tells you the theoretical return, volatility (or variance) describes how that return is distributed:</p>

<h3>Low Volatility</h3>
<p>Frequent small wins, steady gameplay, lower risk</p>

<h3>High Volatility</h3>
<p>Less frequent but larger wins, higher risk and reward</p>

<h2>Practical Implications</h2>

<p>Understanding RTP helps you make informed decisions about which games to play, but remember:</p>
<ul>
<li>Short-term results can vary dramatically from RTP</li>
<li>No strategy can overcome the house edge</li>
<li>RTP is just one factor in game selection</li>
</ul>

<h2>Responsible Gaming Reminder</h2>
<p><strong>Important:</strong> Slots are games of chance designed for entertainment. The house always has an edge, and you should never gamble with money you cannot afford to lose.</p>`,
    excerpt: "Understand the mathematical principles behind slot machine RTP and how it affects your gaming experience.",
    content_type: "strategy_article",
    author: "Writer 777 AI",
    status: "published"
  },
  {
    title: "2024 Gaming Industry Trends and Innovations",
    slug: "2024-gaming-industry-trends-innovations",
    content: `<h1>2024 Gaming Industry Trends and Innovations</h1>

<p>The gaming industry continues to evolve rapidly, with 2024 bringing exciting new developments in technology, regulation, and player experience.</p>

<h2>Technological Innovations</h2>

<h3>Artificial Intelligence Integration</h3>
<p>AI is revolutionizing game development and player experience:</p>
<ul>
<li>Personalized game recommendations</li>
<li>Enhanced fraud detection and security</li>
<li>Improved customer support through chatbots</li>
<li>Dynamic game balancing</li>
</ul>

<h3>Virtual and Augmented Reality</h3>
<p>VR and AR technologies are creating immersive gaming experiences that blur the line between digital and physical casinos.</p>

<h2>Regulatory Developments</h2>

<h3>Expanding Legal Markets</h3>
<p>More jurisdictions are legalizing and regulating online gaming, creating safer environments for players and operators.</p>

<h3>Enhanced Player Protection</h3>
<p>New regulations focus on:</p>
<ul>
<li>Mandatory spending limits</li>
<li>Improved age verification</li>
<li>Better addiction prevention tools</li>
<li>Transparent bonus terms</li>
</ul>

<h2>Mobile Gaming Dominance</h2>

<p>Mobile gaming continues to grow, with operators focusing on:</p>
<ul>
<li>Mobile-first game design</li>
<li>Faster loading times</li>
<li>Touch-optimized interfaces</li>
<li>Cross-platform compatibility</li>
</ul>

<h3>Progressive Web Apps</h3>
<p>PWAs offer app-like experiences without requiring downloads, improving accessibility and user experience.</p>

<h2>Cryptocurrency and Blockchain</h2>

<p>Digital currencies are gaining acceptance in gaming:</p>
<ul>
<li>Faster, more secure transactions</li>
<li>Enhanced privacy for players</li>
<li>Provably fair gaming through blockchain</li>
<li>Global accessibility</li>
</ul>

<h2>Social Gaming Features</h2>

<p>Gaming platforms are incorporating social elements:</p>
<ul>
<li>Live dealer games with chat features</li>
<li>Tournament and leaderboard systems</li>
<li>Community challenges and events</li>
<li>Social media integration</li>
</ul>

<h2>Sustainability Initiatives</h2>

<p>The industry is focusing on environmental responsibility through energy-efficient technologies and carbon offset programs.</p>

<h2>Looking Ahead</h2>

<p>The gaming industry's future looks bright, with continued innovation in technology and regulation creating better experiences for players while maintaining responsible gaming standards.</p>`,
    excerpt: "Explore the latest trends and innovations shaping the gaming industry in 2024, from AI integration to regulatory developments.",
    content_type: "industry_news",
    author: "Writer 777 AI",
    status: "published"
  },
  {
    title: "Sports Betting Strategies for Beginners",
    slug: "sports-betting-strategies-beginners",
    content: `<h1>Sports Betting Strategies for Beginners</h1>

<p>Sports betting can be an exciting way to engage with your favorite sports, but success requires knowledge, discipline, and proper strategy.</p>

<h2>Understanding the Basics</h2>

<h3>Types of Bets</h3>
<ul>
<li><strong>Moneyline:</strong> Betting on which team will win</li>
<li><strong>Point Spread:</strong> Betting on the margin of victory</li>
<li><strong>Over/Under:</strong> Betting on total points scored</li>
<li><strong>Prop Bets:</strong> Betting on specific events within a game</li>
</ul>

<h3>Reading Odds</h3>
<p>Understanding how odds work is crucial for making informed bets. Odds represent both the probability of an outcome and the potential payout.</p>

<h2>Essential Strategies</h2>

<h3>Bankroll Management</h3>
<p>This is the most important aspect of sports betting:</p>
<ul>
<li>Set a budget and stick to it</li>
<li>Never bet more than 1-5% of your bankroll on a single bet</li>
<li>Keep detailed records of all bets</li>
<li>Don't chase losses with bigger bets</li>
</ul>

<h3>Research and Analysis</h3>
<p>Successful betting requires thorough research:</p>
<ul>
<li>Study team statistics and trends</li>
<li>Consider injuries and roster changes</li>
<li>Analyze weather conditions for outdoor sports</li>
<li>Look for value in the betting lines</li>
</ul>

<h2>Common Mistakes to Avoid</h2>

<h3>Emotional Betting</h3>
<p>Don't let personal bias or emotions influence your betting decisions. Bet with your head, not your heart.</p>

<h3>Chasing Losses</h3>
<p>Trying to recover losses with bigger bets is a dangerous path that often leads to even greater losses.</p>

<h3>Betting Too Many Games</h3>
<p>Quality over quantity. It's better to make fewer, well-researched bets than to bet on everything.</p>

<h2>Advanced Concepts</h2>

<h3>Line Shopping</h3>
<p>Compare odds across different sportsbooks to find the best value for your bets.</p>

<h3>Understanding Market Movement</h3>
<p>Learn how and why betting lines move to identify potential value opportunities.</p>

<h2>Responsible Betting</h2>

<p><strong>Critical Reminder:</strong> Sports betting should be viewed as entertainment, not as a way to make money. The vast majority of bettors lose money over time.</p>

<ul>
<li>Never bet money you cannot afford to lose</li>
<li>Set time and money limits</li>
<li>Take regular breaks from betting</li>
<li>Seek help if betting becomes a problem</li>
</ul>

<p>Remember that even the best strategies cannot guarantee profits. Sports betting involves inherent risk, and you should always gamble responsibly.</p>`,
    excerpt: "Learn fundamental sports betting strategies and bankroll management techniques for responsible and informed wagering.",
    content_type: "sports_betting",
    author: "Writer 777 AI",
    status: "published"
  }
];

async function addSampleArticles() {
  try {
    console.log('Adding sample articles...');
    
    // Connect to database
    await database.connect();
    console.log('Database connected');
    
    let addedCount = 0;
    let skippedCount = 0;
    
    for (const article of sampleArticles) {
      try {
        // Check if article already exists
        const existing = await database.get(
          'SELECT id FROM blog_posts WHERE slug = ?',
          [article.slug]
        );
        
        if (existing) {
          console.log(`Skipping ${article.title} - already exists`);
          skippedCount++;
          continue;
        }
        
        // Insert article
        await database.run(`
          INSERT INTO blog_posts (
            title,
            slug,
            content,
            excerpt,
            content_type,
            author,
            status,
            published_at,
            created_at,
            updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          article.title,
          article.slug,
          article.content,
          article.excerpt,
          article.content_type,
          article.author,
          article.status,
          new Date().toISOString(),
          new Date().toISOString(),
          new Date().toISOString()
        ]);
        
        console.log(`✓ Added: ${article.title}`);
        addedCount++;
        
      } catch (error) {
        console.error(`Error adding article ${article.title}:`, error);
      }
    }
    
    console.log('\n=== Sample Articles Summary ===');
    console.log(`Total articles: ${sampleArticles.length}`);
    console.log(`Successfully added: ${addedCount}`);
    console.log(`Skipped (already exists): ${skippedCount}`);
    
    // Show added articles
    const articles = await database.all(`
      SELECT title, slug, content_type, published_at
      FROM blog_posts 
      WHERE status = 'published'
      ORDER BY published_at DESC
    `);
    
    console.log('\n=== Published Articles ===');
    articles.forEach(article => {
      console.log(`- ${article.title} (${article.content_type})`);
      console.log(`  Slug: ${article.slug}`);
      console.log('');
    });
    
  } catch (error) {
    console.error('Failed to add sample articles:', error);
    throw error;
  } finally {
    await database.close();
    console.log('Database connection closed');
  }
}

// Run if called directly
if (require.main === module) {
  addSampleArticles()
    .then(() => {
      console.log('Sample articles added successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Failed to add sample articles:', error);
      process.exit(1);
    });
}

module.exports = { addSampleArticles };
