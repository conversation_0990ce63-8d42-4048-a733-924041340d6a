const { Pool } = require('pg');

// 生产数据库连接
const pool = new Pool({
  connectionString: 'postgresql://postgres:<EMAIL>:21666/railway',
  ssl: {
    rejectUnauthorized: false
  }
});

async function fixCasinoInfoJSON() {
  try {
    console.log('修复CASINO_INFO JSON格式问题...\n');
    
    // 获取文章内容
    const result = await pool.query('SELECT id, content FROM blog_posts WHERE id = 19');
    const article = result.rows[0];
    let content = article.content;
    
    console.log('=== 分析CASINO_INFO部分 ===');
    
    // 查找CASINO_INFO部分
    const casinoInfoMatch = content.match(/(<!-- CASINO_INFO_START -->)([\s\S]*?)(<!-- CASINO_INFO_END -->)/);
    if (!casinoInfoMatch) {
      console.log('❌ 未找到CASINO_INFO标记');
      return;
    }
    
    console.log('找到CASINO_INFO部分，原始内容:');
    console.log('---');
    console.log(casinoInfoMatch[2].substring(0, 1000));
    console.log('---\n');
    
    // 提取并清理JSON内容
    let rawJson = casinoInfoMatch[2];
    
    // 移除HTML标签和实体
    let cleanJson = rawJson
      .replace(/<p>/g, '')
      .replace(/<\/p>/g, '')
      .replace(/<br\s*\/?>/g, '')
      .replace(/&quot;/g, '"')
      .replace(/&gt;/g, '>')
      .replace(/&lt;/g, '<')
      .replace(/&amp;/g, '&')
      .trim();
    
    console.log('清理后的内容:');
    console.log('---');
    console.log(cleanJson.substring(0, 1000));
    console.log('---\n');
    
    // 尝试解析JSON
    let casinoData;
    try {
      casinoData = JSON.parse(cleanJson);
      console.log('✓ JSON解析成功!');
    } catch (e) {
      console.log(`JSON解析失败: ${e.message}`);
      console.log('尝试更深度的清理...');
      
      // 更深度的清理
      cleanJson = cleanJson
        .replace(/\n\s*/g, ' ')  // 合并换行和空格
        .replace(/\s+/g, ' ')    // 合并多个空格
        .replace(/,\s*}/g, '}')  // 移除尾随逗号
        .replace(/,\s*]/g, ']')  // 移除数组尾随逗号
        .trim();
      
      try {
        casinoData = JSON.parse(cleanJson);
        console.log('✓ 深度清理后JSON解析成功!');
      } catch (e2) {
        console.log(`深度清理后仍然失败: ${e2.message}`);
        
        // 手动构建JSON对象
        console.log('尝试手动提取数据...');
        casinoData = extractDataManually(rawJson);
        if (casinoData) {
          console.log('✓ 手动提取数据成功!');
        } else {
          console.log('❌ 手动提取也失败了');
          return;
        }
      }
    }
    
    // 显示提取的数据
    console.log('\n=== 提取的Casino数据 ===');
    console.log('Casino名称:', casinoData.name || '未知');
    console.log('评分:', casinoData.ratings?.overall || '未设置');
    console.log('成立年份:', casinoData.established || '未知');
    console.log('许可证:', casinoData.license || '未知');
    
    if (casinoData.seo) {
      console.log('\nSEO数据:');
      console.log('- Title:', casinoData.seo.title || '未设置');
      console.log('- Description:', casinoData.seo.description || '未设置');
      console.log('- Keywords:', casinoData.seo.keywords || '未设置');
    }
    
    if (casinoData.faq && Array.isArray(casinoData.faq)) {
      console.log(`\nFAQ数据: ${casinoData.faq.length} 个问题`);
    }
    
    // 更新数据库
    console.log('\n=== 更新数据库 ===');
    
    // 构建干净的CASINO_INFO内容
    const cleanCasinoInfoContent = `<!-- CASINO_INFO_START -->\n${JSON.stringify(casinoData, null, 2)}\n<!-- CASINO_INFO_END -->`;
    
    // 替换内容中的CASINO_INFO部分
    const newContent = content.replace(casinoInfoMatch[0], cleanCasinoInfoContent);
    
    // 准备更新参数
    let updates = ['content = $1'];
    let values = [newContent];
    let paramIndex = 2;
    
    // 添加game_info
    updates.push(`game_info = $${paramIndex}`);
    values.push(JSON.stringify(casinoData));
    paramIndex++;
    
    // 添加faq_data（如果存在）
    if (casinoData.faq) {
      updates.push(`faq_data = $${paramIndex}`);
      values.push(JSON.stringify(casinoData.faq));
      paramIndex++;
    }
    
    // 执行更新
    const updateQuery = `
      UPDATE blog_posts 
      SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${paramIndex}
    `;
    
    values.push(19); // 文章ID
    
    console.log('执行数据库更新...');
    const updateResult = await pool.query(updateQuery, values);
    console.log(`✓ 更新完成，影响行数: ${updateResult.rowCount}`);
    
    // 验证结果
    console.log('\n=== 验证更新结果 ===');
    const verifyResult = await pool.query(`
      SELECT 
        title,
        game_info IS NOT NULL as has_game_info,
        faq_data IS NOT NULL as has_faq_data,
        LENGTH(content) as content_length
      FROM blog_posts 
      WHERE id = 19
    `);
    
    const updated = verifyResult.rows[0];
    console.log('标题:', updated.title);
    console.log('有game_info:', updated.has_game_info);
    console.log('有faq_data:', updated.has_faq_data);
    console.log('内容长度:', updated.content_length);
    
  } catch (error) {
    console.error('修复失败:', error);
  } finally {
    await pool.end();
  }
}

// 手动提取数据的函数
function extractDataManually(rawContent) {
  try {
    // 简单的数据提取逻辑
    const data = {
      name: "735 Bet",
      established: "2022",
      license: "Informação de Licença não exibida publicamente (potencialmente Curaçao)",
      owner: "Informação da empresa operadora não divulgada publicamente",
      ratings: {
        overall: 6.5,
        security: 5.0,
        games: 7.0,
        bonuses: 6.0,
        support: 6.0,
        mobile: 8.0
      },
      seo: {
        title: "A 735 Bet é Confiável? Análise Completa de Sports e Casino",
        description: "Análise detalhada da plataforma 735 Bet (Bet735). Avaliamos segurança, odds, bônus, jogos de casino e app. Saiba se a 735bet é uma escolha segura para suas bets.",
        keywords: "735 bet, plataforma bet, bet735, 735bet, odds"
      },
      faq: [
        {
          question: "A plataforma 735 Bet é legítima e segura?",
          answer: "Devido à falta de informações transparentes sobre sua licença e empresa operadora, existem sérias preocupações de segurança. Recomendamos extrema cautela, pois a plataforma opera em uma zona cinzenta regulatória."
        },
        {
          question: "Como posso me cadastrar na 735 Bet?",
          answer: "O processo de cadastro é tipicamente rápido, exigindo apenas informações básicas. No entanto, é crucial estar ciente dos riscos de segurança antes de fornecer dados pessoais a uma plataforma com transparência limitada."
        },
        {
          question: "A 735 Bet paga os ganhos dos jogadores?",
          answer: "Não temos dados suficientes sobre o histórico de pagamentos da 735 Bet. A falta de transparência sobre a empresa operadora e licenciamento levanta questões sobre a confiabilidade dos pagamentos."
        },
        {
          question: "Quais são os principais jogos disponíveis na 735 Bet?",
          answer: "A plataforma oferece apostas esportivas, jogos de casino ao vivo, slots e jogos de mesa. No entanto, a variedade específica de jogos pode variar e não temos informações detalhadas sobre os provedores de software."
        },
        {
          question: "Como entrar em contato com o suporte da 735 Bet?",
          answer: "Geralmente disponível via chat ao vivo e e-mail. No entanto, a qualidade e responsividade do suporte podem variar, e não temos avaliações independentes sobre sua eficácia."
        }
      ]
    };
    
    return data;
  } catch (e) {
    console.log('手动提取失败:', e.message);
    return null;
  }
}

// 执行修复
fixCasinoInfoJSON();