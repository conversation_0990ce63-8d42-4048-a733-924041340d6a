#!/usr/bin/env node

const database = require('../config/database');

async function fixContentTypes() {
  try {
    console.log('Connecting to database...');
    await database.connect();
    
    // Check current content types
    const currentArticles = await database.all(`
      SELECT id, title, content_type, status
      FROM blog_posts 
      ORDER BY created_at DESC
    `);
    
    console.log('\nCurrent articles and their content types:');
    currentArticles.forEach(article => {
      console.log(`- ${article.title}: "${article.content_type}" (${article.status})`);
    });
    
    // Find articles with invalid content types
    const validTypes = [
      'bonus_analysis',
      'brand_copy', 
      'casino_review',
      'game_guide',
      'industry_news',
      'regulatory_update',
      'sports_betting',
      'strategy_article'
    ];
    
    const invalidArticles = currentArticles.filter(article => 
      !validTypes.includes(article.content_type)
    );
    
    console.log(`\nFound ${invalidArticles.length} articles with invalid content types.`);
    
    if (invalidArticles.length > 0) {
      console.log('\nFixing invalid content types...');
      
      for (const article of invalidArticles) {
        // Map old types to new types
        let newContentType = 'strategy_article'; // default
        
        if (article.content_type === 'article') {
          newContentType = 'strategy_article';
        } else if (article.content_type === null || article.content_type === '') {
          newContentType = 'casino_review';
        }
        
        console.log(`Updating "${article.title}": "${article.content_type}" -> "${newContentType}"`);
        
        await database.run(`
          UPDATE blog_posts 
          SET content_type = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `, [newContentType, article.id]);
      }
      
      console.log('✅ Content types updated successfully!');
    }
    
    // Show updated results
    const updatedArticles = await database.all(`
      SELECT content_type, COUNT(*) as count, status
      FROM blog_posts 
      GROUP BY content_type, status
      ORDER BY content_type, status
    `);
    
    console.log('\nUpdated content type breakdown:');
    updatedArticles.forEach(type => {
      console.log(`${type.content_type} (${type.status}): ${type.count}`);
    });
    
    await database.close();
    console.log('\n✅ Database update completed!');
    
  } catch (error) {
    console.error('Error fixing content types:', error);
    process.exit(1);
  }
}

fixContentTypes();