#!/usr/bin/env node

/**
 * 创建完整的联盟营销系统数据库表结构
 * 包括：扩展联盟链接表、文章联盟关联表、点击统计表
 */

// 在导入Database模块之前设置环境变量
process.env.DATABASE_URL = 'postgresql://postgres:<EMAIL>:21666/railway';
process.env.NODE_ENV = 'production';

const Database = require('../config/database');

async function createAffiliateTables() {
  console.log('🚀 创建联盟营销系统数据库表...');

  try {
    await Database.connect();

    // 1. 删除旧的 affiliate_links 表（如果存在）并重新创建
    console.log('📋 删除旧的 affiliate_links 表...');
    await Database.run(`DROP TABLE IF EXISTS affiliate_links CASCADE`);

    // 2. 创建扩展的联盟链接表
    console.log('📋 创建 affiliate_links 表...');
    await Database.run(`
      CREATE TABLE affiliate_links (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        affiliate_url VARCHAR(1000) NOT NULL,
        display_text VARCHAR(255) NOT NULL,
        button_style VARCHAR(100) DEFAULT 'primary', -- 'primary', 'secondary', 'success', 'warning'
        content_types TEXT, -- JSON array: ['casino_review', 'sports_betting', 'game_guide']
        categories TEXT, -- JSON array: ['casino', 'sportsbook', 'crypto', 'software']
        target_countries TEXT, -- JSON array: ['US', 'BR', 'DE'] - 目标国家
        target_languages TEXT, -- JSON array: ['en', 'pt', 'es'] - 目标语言
        commission_rate DECIMAL(5,2), -- 佣金率百分比
        commission_type VARCHAR(50) DEFAULT 'percentage', -- 'percentage', 'fixed', 'cpa'
        tracking_code VARCHAR(100),
        tracking_params TEXT, -- JSON object: utm参数等
        priority INTEGER DEFAULT 0, -- 优先级，数字越大优先级越高
        click_count INTEGER DEFAULT 0,
        conversion_count INTEGER DEFAULT 0,
        conversion_rate DECIMAL(5,2) DEFAULT 0.00,
        total_earnings DECIMAL(10,2) DEFAULT 0.00,
        is_active BOOLEAN DEFAULT TRUE,
        start_date TIMESTAMP,
        end_date TIMESTAMP,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 3. 创建文章-联盟链接关联表
    console.log('📋 创建 article_affiliate_links 表...');
    await Database.run(`
      CREATE TABLE IF NOT EXISTS article_affiliate_links (
        id SERIAL PRIMARY KEY,
        article_id INTEGER, -- 可以为空，表示全局设置
        task_id VARCHAR(255), -- 任务ID，关联到tasks表
        affiliate_link_id INTEGER NOT NULL,
        position VARCHAR(100) DEFAULT 'primary_cta', -- 'primary_cta', 'secondary_cta', 'inline', 'footer'
        custom_display_text VARCHAR(255), -- 覆盖默认显示文字
        custom_button_style VARCHAR(100), -- 覆盖默认按钮样式
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (affiliate_link_id) REFERENCES affiliate_links (id) ON DELETE CASCADE
      )
    `);

    // 4. 创建点击统计表
    console.log('📋 创建 affiliate_clicks 表...');
    await Database.run(`
      CREATE TABLE IF NOT EXISTS affiliate_clicks (
        id SERIAL PRIMARY KEY,
        affiliate_link_id INTEGER NOT NULL,
        article_id INTEGER, -- 可以为空
        article_url VARCHAR(500), -- 文章URL
        article_title VARCHAR(500),
        click_position VARCHAR(100), -- 'primary_cta', 'secondary_cta', 'inline', 'footer'
        user_ip VARCHAR(45), -- IPv4/IPv6地址
        user_agent TEXT,
        referer VARCHAR(500),
        country_code VARCHAR(5), -- 用户国家代码
        language_code VARCHAR(10), -- 用户语言代码
        device_type VARCHAR(50), -- 'desktop', 'mobile', 'tablet'
        browser VARCHAR(100),
        utm_source VARCHAR(100),
        utm_medium VARCHAR(100),
        utm_campaign VARCHAR(100),
        utm_content VARCHAR(100),
        utm_term VARCHAR(100),
        session_id VARCHAR(255), -- 会话ID
        conversion_tracked BOOLEAN DEFAULT FALSE,
        conversion_value DECIMAL(10,2),
        conversion_date TIMESTAMP,
        click_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (affiliate_link_id) REFERENCES affiliate_links (id) ON DELETE CASCADE
      )
    `);

    // 5. 创建索引以优化查询性能
    console.log('📋 创建索引...');
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_affiliate_links_content_types ON affiliate_links USING GIN ((content_types::jsonb))',
      'CREATE INDEX IF NOT EXISTS idx_affiliate_links_categories ON affiliate_links USING GIN ((categories::jsonb))',
      'CREATE INDEX IF NOT EXISTS idx_affiliate_links_countries ON affiliate_links USING GIN ((target_countries::jsonb))',
      'CREATE INDEX IF NOT EXISTS idx_affiliate_links_languages ON affiliate_links USING GIN ((target_languages::jsonb))',
      'CREATE INDEX IF NOT EXISTS idx_affiliate_links_active_priority ON affiliate_links (is_active, priority DESC)',
      'CREATE INDEX IF NOT EXISTS idx_article_affiliate_links_article ON article_affiliate_links (article_id)',
      'CREATE INDEX IF NOT EXISTS idx_article_affiliate_links_task ON article_affiliate_links (task_id)',
      'CREATE INDEX IF NOT EXISTS idx_article_affiliate_links_active ON article_affiliate_links (is_active)',
      'CREATE INDEX IF NOT EXISTS idx_affiliate_clicks_link_id ON affiliate_clicks (affiliate_link_id)',
      'CREATE INDEX IF NOT EXISTS idx_affiliate_clicks_article_id ON affiliate_clicks (article_id)',
      'CREATE INDEX IF NOT EXISTS idx_affiliate_clicks_timestamp ON affiliate_clicks (click_timestamp)',
      'CREATE INDEX IF NOT EXISTS idx_affiliate_clicks_country ON affiliate_clicks (country_code)',
      'CREATE INDEX IF NOT EXISTS idx_affiliate_clicks_conversion ON affiliate_clicks (conversion_tracked)',
      'CREATE INDEX IF NOT EXISTS idx_affiliate_clicks_session ON affiliate_clicks (session_id)'
    ];

    for (const index of indexes) {
      await Database.run(index);
    }

    // 6. 添加示例联盟链接数据
    console.log('📋 添加示例联盟链接...');
    const sampleAffiliateLinks = [
      {
        name: 'Bet365 Casino',
        description: '世界领先的在线博彩平台，提供丰富的赌场游戏和体育博彩',
        affiliate_url: 'https://bet365.com/?affiliate=writer777',
        display_text: '访问 Bet365',
        button_style: 'primary',
        content_types: JSON.stringify(['casino_review', 'sports_betting']),
        categories: JSON.stringify(['casino', 'sportsbook']),
        target_countries: JSON.stringify(['US', 'BR', 'DE', 'ES']),
        target_languages: JSON.stringify(['en', 'pt', 'es', 'de']),
        commission_rate: 35.00,
        commission_type: 'percentage',
        tracking_code: 'WR777BT365',
        tracking_params: JSON.stringify({
          utm_source: 'writer777',
          utm_medium: 'review',
          utm_campaign: 'casino-reviews'
        }),
        priority: 100
      },
      {
        name: 'PokerStars Casino',
        description: '专业的扑克和赌场游戏平台',
        affiliate_url: 'https://pokerstars.com/?ref=writer777',
        display_text: '立即加入 PokerStars',
        button_style: 'success',
        content_types: JSON.stringify(['casino_review', 'game_guide']),
        categories: JSON.stringify(['casino', 'poker']),
        target_countries: JSON.stringify(['US', 'CA', 'EU']),
        target_languages: JSON.stringify(['en', 'fr', 'de']),
        commission_rate: 30.00,
        commission_type: 'percentage',
        tracking_code: 'WR777PS',
        priority: 90
      },
      {
        name: '1xBet Sportsbook',
        description: '全球体育博彩领导者，覆盖所有主要体育项目',
        affiliate_url: 'https://1xbet.com/writer777',
        display_text: '开始投注',
        button_style: 'warning',
        content_types: JSON.stringify(['sports_betting', 'strategy_article']),
        categories: JSON.stringify(['sportsbook', 'esports']),
        target_countries: JSON.stringify(['BR', 'MX', 'AR']),
        target_languages: JSON.stringify(['pt', 'es']),
        commission_rate: 40.00,
        commission_type: 'percentage',
        tracking_code: 'WR777_1X',
        priority: 85
      },
      {
        name: 'Binance Exchange',
        description: '全球最大的加密货币交易平台',
        affiliate_url: 'https://binance.com/en/register?ref=WRITER777',
        display_text: '注册 Binance',
        button_style: 'secondary',
        content_types: JSON.stringify(['industry_news', 'regulatory_update']),
        categories: JSON.stringify(['crypto', 'exchange']),
        target_countries: JSON.stringify(['US', 'EU', 'AS']),
        target_languages: JSON.stringify(['en', 'zh', 'ja']),
        commission_rate: 20.00,
        commission_type: 'percentage',
        tracking_code: 'WR777BIN',
        priority: 75
      }
    ];

    for (const link of sampleAffiliateLinks) {
      await Database.run(`
        INSERT INTO affiliate_links (
          name, description, affiliate_url, display_text, button_style,
          content_types, categories, target_countries, target_languages,
          commission_rate, commission_type, tracking_code, tracking_params, priority
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        link.name, link.description, link.affiliate_url, link.display_text, link.button_style,
        link.content_types, link.categories, link.target_countries, link.target_languages,
        link.commission_rate, link.commission_type, link.tracking_code, 
        link.tracking_params || null, link.priority
      ]);
    }

    console.log('✅ 联盟营销系统数据库表创建完成！');
    console.log('📊 已添加 4 个示例联盟链接');
    
    // 显示创建的表信息
    const affiliateLinksCount = await Database.get('SELECT COUNT(*) as count FROM affiliate_links');
    console.log(`📈 联盟链接表记录数: ${affiliateLinksCount.count}`);

  } catch (error) {
    console.error('❌ 创建联盟营销系统失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  createAffiliateTables()
    .then(() => {
      console.log('🎉 联盟营销系统创建完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { createAffiliateTables };