const database = require('../config/database');
const { marked } = require('marked');

// Configure marked for HTML conversion
marked.setOptions({
  breaks: true,
  gfm: true,
  sanitize: false
});

// Helper function to convert markdown to HTML
function markdownToHtml(markdown) {
  if (!markdown) return '';
  
  try {
    return marked(markdown);
  } catch (error) {
    console.error('Error converting markdown to HTML:', error);
    return markdown; // Return original markdown if conversion fails
  }
}

// Helper function to detect if content is markdown
function isMarkdown(content) {
  if (!content) return false;
  
  // Check for common markdown patterns
  const markdownPatterns = [
    /^#+\s+/m,           // Headers
    /\*\*.*?\*\*/,       // Bold text
    /\*.*?\*/,           // Italic text
    /\[.*?\]\(.*?\)/,    // Links
    /```[\s\S]*?```/,    // Code blocks
    /`.*?`/,             // Inline code
    /^[\*\-]\s+/m,       // Unordered lists
    /^\d+\.\s+/m         // Ordered lists
  ];
  
  return markdownPatterns.some(pattern => pattern.test(content));
}

async function convertExistingArticles() {
  try {
    console.log('🔄 Converting existing markdown articles to HTML...');
    
    await database.connect();
    console.log('✅ Database connected');
    
    // Get all published articles
    const articles = await database.all(`
      SELECT id, title, content, slug
      FROM blog_posts 
      WHERE status = 'published'
      ORDER BY created_at DESC
    `);
    
    console.log(`📄 Found ${articles.length} published articles`);
    
    if (articles.length === 0) {
      console.log('❌ No published articles found');
      return;
    }
    
    let convertedCount = 0;
    let skippedCount = 0;
    
    for (const article of articles) {
      try {
        console.log(`\n📝 Processing: ${article.title}`);
        
        // Check if content appears to be markdown
        if (isMarkdown(article.content)) {
          console.log(`🔄 Converting markdown to HTML...`);
          
          // Convert to HTML
          const htmlContent = markdownToHtml(article.content);
          
          // Update the article
          await database.run(
            'UPDATE blog_posts SET content = ?, updated_at = ? WHERE id = ?',
            [htmlContent, new Date().toISOString(), article.id]
          );
          
          console.log(`✅ Converted: ${article.title}`);
          convertedCount++;
        } else {
          console.log(`⚠️  Already HTML or plain text: ${article.title}`);
          skippedCount++;
        }
        
      } catch (error) {
        console.error(`❌ Error processing article ${article.id}:`, error);
      }
    }
    
    console.log('\n🎉 === CONVERSION SUMMARY ===');
    console.log(`📊 Total articles processed: ${articles.length}`);
    console.log(`✅ Successfully converted: ${convertedCount}`);
    console.log(`⚠️  Skipped (already HTML): ${skippedCount}`);
    console.log(`❌ Errors: ${articles.length - convertedCount - skippedCount}`);
    
  } catch (error) {
    console.error('💥 Conversion failed:', error);
    throw error;
  } finally {
    await database.close();
    console.log('🔌 Database connection closed');
  }
}

// Run if called directly
if (require.main === module) {
  convertExistingArticles()
    .then(() => {
      console.log('\n🎯 Article conversion completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Article conversion failed:', error);
      process.exit(1);
    });
}

module.exports = { convertExistingArticles };
