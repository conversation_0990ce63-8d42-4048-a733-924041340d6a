#!/usr/bin/env node

console.log('=== RAILWAY STARTUP DEBUG ===');
console.log('Current time:', new Date().toISOString());
console.log('Process ID:', process.pid);
console.log('Node version:', process.version);
console.log('Current directory:', process.cwd());
console.log('Script directory:', __dirname);

// Check environment variables
console.log('\n=== ENVIRONMENT VARIABLES ===');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('PORT:', process.env.PORT);
console.log('DATABASE_URL exists:', !!process.env.DATABASE_URL);
console.log('JWT_SECRET exists:', !!process.env.JWT_SECRET);
console.log('GEMINI_API_KEY exists:', !!process.env.GEMINI_API_KEY);
console.log('GOOGLE_GEMINI_API_KEY exists:', !!process.env.GOOGLE_GEMINI_API_KEY);

// Check network interfaces
console.log('\n=== NETWORK INTERFACES ===');
const os = require('os');
const interfaces = os.networkInterfaces();
Object.keys(interfaces).forEach(name => {
  interfaces[name].forEach(iface => {
    if (iface.family === 'IPv4') {
      console.log(`${name}: ${iface.address}`);
    }
  });
});

// Test port binding
console.log('\n=== PORT BINDING TEST ===');
const http = require('http');
const PORT = process.env.PORT || 3001;

const testServer = http.createServer((req, res) => {
  res.writeHead(200);
  res.end('Test server OK');
});

testServer.on('error', (err) => {
  console.error('Port binding error:', err);
  process.exit(1);
});

testServer.listen(PORT, '0.0.0.0', () => {
  console.log(`Test server successfully bound to 0.0.0.0:${PORT}`);
  testServer.close(() => {
    console.log('Test server closed, port is available');
    console.log('\n=== STARTING MAIN SERVER ===');
    // Now start the actual server
    require('../server.js');
  });
});