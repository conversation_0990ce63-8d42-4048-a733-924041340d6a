#!/usr/bin/env node

/**
 * Database Migration Script - Add Language and Country Fields
 * 
 * This script adds target_language and target_country fields to the tasks table.
 * It's designed to be safe to run multiple times (won't fail if columns already exist).
 * 
 * Usage:
 *   node scripts/add-language-country-fields.js
 */

require('dotenv').config();
const database = require('../config/database');

async function addLanguageCountryFields() {
  console.log('🚀 Starting database migration to add language and country fields...');
  
  try {
    // Connect to the database without initializing tables (to avoid conflicts)
    console.log('📡 Connecting to database...');
    if (!database.pool || database.pool.ended) {
      const { Pool } = require('pg');
      database.pool = new Pool({
        connectionString: process.env.DATABASE_URL,
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
        max: 20,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
        query_timeout: 10000,
        statement_timeout: 10000
      });
    }
    
    // Test the connection
    const client = await database.pool.connect();
    client.release();
    console.log('Connected to PostgreSQL database');
    
    // Add target_language column
    console.log('➕ Adding target_language column...');
    try {
      await database.run(`
        ALTER TABLE tasks 
        ADD COLUMN target_language VARCHAR(10) DEFAULT 'en'
      `);
      console.log('✅ Successfully added target_language column');
    } catch (error) {
      if (error.message.includes('already exists') || error.message.includes('duplicate column')) {
        console.log('⚠️  target_language column already exists, skipping...');
      } else {
        throw error;
      }
    }
    
    // Add target_country column
    console.log('➕ Adding target_country column...');
    try {
      await database.run(`
        ALTER TABLE tasks 
        ADD COLUMN target_country VARCHAR(10) DEFAULT 'US'
      `);
      console.log('✅ Successfully added target_country column');
    } catch (error) {
      if (error.message.includes('already exists') || error.message.includes('duplicate column')) {
        console.log('⚠️  target_country column already exists, skipping...');
      } else {
        throw error;
      }
    }
    
    // Create indexes for the new columns
    console.log('📊 Creating indexes...');
    try {
      await database.run('CREATE INDEX IF NOT EXISTS idx_tasks_language ON tasks(target_language)');
      console.log('✅ Created index for target_language');
    } catch (error) {
      console.log('⚠️  Index for target_language might already exist:', error.message);
    }
    
    try {
      await database.run('CREATE INDEX IF NOT EXISTS idx_tasks_country ON tasks(target_country)');
      console.log('✅ Created index for target_country');
    } catch (error) {
      console.log('⚠️  Index for target_country might already exist:', error.message);
    }
    
    // Verify the changes
    console.log('🔍 Verifying migration...');
    
    const columns = await database.all(`
      SELECT column_name, data_type, column_default 
      FROM information_schema.columns 
      WHERE table_name = 'tasks' 
      AND column_name IN ('target_language', 'target_country')
      ORDER BY column_name
    `);
    
    console.log('📋 New columns in tasks table:');
    columns.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} (default: ${col.column_default})`);
    });
    
    // Count existing tasks to see what will be affected
    const taskCount = await database.get('SELECT COUNT(*) as count FROM tasks');
    console.log(`📊 Migration will affect ${taskCount.count} existing tasks`);
    
    console.log('✅ Database migration completed successfully!');
    console.log('🎉 Language and country fields have been added to the tasks table');
    
  } catch (error) {
    console.error('❌ Database migration failed:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    try {
      await database.close();
      console.log('🔌 Database connection closed.');
    } catch (closeError) {
      console.error('⚠️  Error closing database connection:', closeError);
    }
  }
}

// Run the migration
if (require.main === module) {
  addLanguageCountryFields()
    .then(() => {
      console.log('🎉 Migration script completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Fatal error during migration:', error);
      process.exit(1);
    });
}

module.exports = { addLanguageCountryFields };