const database = require('../config/database');

async function fixBlogPostsTable() {
  try {
    console.log('Checking and fixing blog_posts table...');
    
    // Connect to database
    await database.connect();
    console.log('Database connected');
    
    // Check if content_type column exists
    try {
      await database.get('SELECT content_type FROM blog_posts LIMIT 1');
      console.log('content_type column already exists');
    } catch (error) {
      if (error.message.includes('no such column: content_type')) {
        console.log('Adding missing content_type column...');
        await database.run('ALTER TABLE blog_posts ADD COLUMN content_type TEXT');
        console.log('✓ Added content_type column');
      } else {
        throw error;
      }
    }
    
    // Check if categories column exists
    try {
      await database.get('SELECT categories FROM blog_posts LIMIT 1');
      console.log('categories column already exists');
    } catch (error) {
      if (error.message.includes('no such column: categories')) {
        console.log('Adding missing categories column...');
        await database.run('ALTER TABLE blog_posts ADD COLUMN categories TEXT');
        console.log('✓ Added categories column');
      } else {
        throw error;
      }
    }
    
    // Check if tags column exists
    try {
      await database.get('SELECT tags FROM blog_posts LIMIT 1');
      console.log('tags column already exists');
    } catch (error) {
      if (error.message.includes('no such column: tags')) {
        console.log('Adding missing tags column...');
        await database.run('ALTER TABLE blog_posts ADD COLUMN tags TEXT');
        console.log('✓ Added tags column');
      } else {
        throw error;
      }
    }
    
    // Check if published_at column exists
    try {
      await database.get('SELECT published_at FROM blog_posts LIMIT 1');
      console.log('published_at column already exists');
    } catch (error) {
      if (error.message.includes('no such column: published_at')) {
        console.log('Adding missing published_at column...');
        await database.run('ALTER TABLE blog_posts ADD COLUMN published_at DATETIME');
        console.log('✓ Added published_at column');
      } else {
        throw error;
      }
    }
    
    // Show current table structure
    console.log('\n=== Current blog_posts table structure ===');
    const tableInfo = await database.all("PRAGMA table_info(blog_posts)");
    tableInfo.forEach(column => {
      console.log(`${column.name}: ${column.type} ${column.notnull ? 'NOT NULL' : ''} ${column.dflt_value ? `DEFAULT ${column.dflt_value}` : ''}`);
    });
    
    console.log('\n✓ blog_posts table is now ready for use');
    
  } catch (error) {
    console.error('Failed to fix blog_posts table:', error);
    throw error;
  } finally {
    await database.close();
    console.log('Database connection closed');
  }
}

// Run if called directly
if (require.main === module) {
  fixBlogPostsTable()
    .then(() => {
      console.log('blog_posts table fixed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Failed to fix blog_posts table:', error);
      process.exit(1);
    });
}

module.exports = { fixBlogPostsTable };
