const database = require('../config/database');

async function addKeywordFields() {
  try {
    console.log('🔄 Adding selected_keywords and secondary_keywords fields to tasks table...');
    
    // Connect to database
    await database.connect();
    
    // Check if fields already exist
    const checkColumns = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'tasks' 
      AND (column_name = 'selected_keywords' OR column_name = 'secondary_keywords')
    `;
    
    const existingColumns = await database.all(checkColumns);
    console.log('Existing keyword columns:', existingColumns);
    
    // Add selected_keywords column if it doesn't exist
    const hasSelectedKeywords = existingColumns.some(col => col.column_name === 'selected_keywords');
    if (!hasSelectedKeywords) {
      await database.run('ALTER TABLE tasks ADD COLUMN selected_keywords TEXT');
      console.log('✅ Added selected_keywords column');
    } else {
      console.log('ℹ️ selected_keywords column already exists');
    }
    
    // Add secondary_keywords column if it doesn't exist
    const hasSecondaryKeywords = existingColumns.some(col => col.column_name === 'secondary_keywords');
    if (!hasSecondaryKeywords) {
      await database.run('ALTER TABLE tasks ADD COLUMN secondary_keywords TEXT');
      console.log('✅ Added secondary_keywords column');
    } else {
      console.log('ℹ️ secondary_keywords column already exists');
    }
    
    console.log('🎉 Database migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await database.forceClose();
  }
}

// Run migration
if (require.main === module) {
  addKeywordFields();
}

module.exports = addKeywordFields;