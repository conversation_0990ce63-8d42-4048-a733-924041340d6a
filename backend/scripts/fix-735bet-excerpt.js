const { Pool } = require('pg');

// 生产数据库连接
const pool = new Pool({
  connectionString: 'postgresql://postgres:<EMAIL>:21666/railway',
  ssl: {
    rejectUnauthorized: false
  }
});

// 辅助函数：从内容中提取摘要
function extractExcerpt(content, maxLength = 200) {
  if (!content) return '';
  
  // 移除HTML标签获取纯文本
  const plainText = content.replace(/<[^>]*>/g, '');
  
  if (plainText.length <= maxLength) {
    return plainText;
  }
  
  // 查找最后一个完整句子
  const truncated = plainText.substring(0, maxLength);
  const lastSentence = truncated.lastIndexOf('.');
  
  if (lastSentence > maxLength * 0.7) {
    return truncated.substring(0, lastSentence + 1);
  }
  
  return truncated + '...';
}

async function fix735BetExcerpt() {
  try {
    console.log('修复735 Bet文章的excerpt字段...\n');
    
    // 获取文章的完整内容
    const selectQuery = `
      SELECT 
        id,
        title,
        slug,
        excerpt,
        content,
        game_info
      FROM blog_posts 
      WHERE slug LIKE '%735%bet%' OR title ILIKE '%735%bet%'
      ORDER BY created_at DESC 
      LIMIT 1
    `;
    
    const result = await pool.query(selectQuery);
    
    if (result.rows.length === 0) {
      console.log('未找到735 bet文章');
      return;
    }
    
    const article = result.rows[0];
    
    console.log('=== 当前文章信息 ===');
    console.log(`ID: ${article.id}`);
    console.log(`标题: "${article.title}"`);
    console.log(`当前excerpt: "${article.excerpt}"`);
    
    // 问题诊断
    console.log('\n=== 问题诊断 ===');
    if (article.excerpt && article.excerpt.startsWith('SEO Title:')) {
      console.log('❌ 发现问题：excerpt字段被错误设置为SEO标题格式');
    }
    
    // 尝试从多个来源生成正确的excerpt
    console.log('\n=== 生成正确的excerpt ===');
    
    let correctExcerpt = '';
    
    // 方法1：尝试从game_info中的SEO description获取
    if (article.game_info) {
      try {
        const gameInfo = JSON.parse(article.game_info);
        if (gameInfo.seo && gameInfo.seo.description) {
          correctExcerpt = gameInfo.seo.description;
          console.log('✓ 从game_info.seo.description获取excerpt');
          console.log(`新excerpt: "${correctExcerpt}"`);
        }
      } catch (e) {
        console.log('⚠️  无法解析game_info JSON');
      }
    }
    
    // 方法2：如果没有SEO description，从内容中提取
    if (!correctExcerpt) {
      console.log('从文章内容中提取excerpt...');
      
      // 跳过开头的SEO信息部分，找到实际内容
      let content = article.content;
      
      // 移除开头的SEO信息块
      content = content.replace(/^<p><strong>SEO Title:<\/strong>.*?<\/p>\s*/i, '');
      content = content.replace(/^<p><strong>Meta Description:<\/strong>.*?<\/p>\s*/i, '');
      content = content.replace(/^<p><strong>Focus Keywords:<\/strong>.*?<\/p>\s*/i, '');
      content = content.replace(/^<p><strong>Tags:<\/strong>.*?<\/p>\s*/i, '');
      
      // 移除CASINO_INFO块
      content = content.replace(/<!-- CASINO_INFO_START -->[\s\S]*?<!-- CASINO_INFO_END -->/g, '');
      
      // 移除FAQ块
      content = content.replace(/<!-- FAQ_START -->[\s\S]*?<!-- FAQ_END -->/g, '');
      
      correctExcerpt = extractExcerpt(content, 200);
      console.log('✓ 从清理后的内容中提取excerpt');
      console.log(`新excerpt: "${correctExcerpt}"`);
    }
    
    // 执行更新
    if (correctExcerpt && correctExcerpt !== article.excerpt) {
      console.log('\n=== 执行数据库更新 ===');
      
      const updateQuery = `
        UPDATE blog_posts 
        SET excerpt = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `;
      
      console.log('准备更新...');
      console.log(`原excerpt: "${article.excerpt}"`);
      console.log(`新excerpt: "${correctExcerpt}"`);
      
      const updateResult = await pool.query(updateQuery, [correctExcerpt, article.id]);
      console.log(`✓ 更新完成，影响行数: ${updateResult.rowCount}`);
      
      // 验证更新结果
      console.log('\n=== 验证更新结果 ===');
      const verifyResult = await pool.query('SELECT excerpt FROM blog_posts WHERE id = $1', [article.id]);
      const updatedExcerpt = verifyResult.rows[0].excerpt;
      console.log(`验证更新后的excerpt: "${updatedExcerpt}"`);
      
      if (updatedExcerpt === correctExcerpt) {
        console.log('✅ 更新成功！');
      } else {
        console.log('❌ 更新验证失败');
      }
      
    } else {
      console.log('\n=== 无需更新 ===');
      if (!correctExcerpt) {
        console.log('无法生成有效的excerpt');
      } else {
        console.log('excerpt已经是正确的');
      }
    }
    
  } catch (error) {
    console.error('修复失败:', error);
  } finally {
    await pool.end();
  }
}

// 执行修复
fix735BetExcerpt();