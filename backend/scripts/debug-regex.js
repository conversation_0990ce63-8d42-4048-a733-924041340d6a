/**
 * 调试正则表达式匹配问题
 */

const content = `
# 在线21点完整指南

## 关于作者

李明是一位资深赌场分析师，在iGaming行业拥有超过12年的经验。他专业于赌场评测、游戏分析和负责任博彩实践。
`;

console.log('调试内容:');
console.log(content);
console.log('\n---\n');

// 测试各种正则表达式
const regexes = [
  {
    name: '中文专用 v1',
    regex: /(?:关于作者|作者简介)[^]*?([\u4e00-\u9fff]{2,5})(?:是|为)/i
  },
  {
    name: '中文专用 v2',
    regex: /(?:关于作者|作者简介)[^]*?([\u4e00-\u9fff]{2,5})是/i
  },
  {
    name: '通用模式',
    regex: /(?:About the Author|Sobre o Autor)[^]*?([A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ]+(?:\s+[A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ]+)*)\s+(?:is|é)/i
  },
  {
    name: '作者:格式',
    regex: /(?:Author|By|Escrito por|作者)[:\s]+([A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+(?:\s+[A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+)*)/i
  }
];

for (const test of regexes) {
  console.log(`测试正则: ${test.name}`);
  console.log(`表达式: ${test.regex}`);
  
  const match = content.match(test.regex);
  if (match) {
    console.log(`完整匹配: "${match[0]}"`);
    console.log(`捕获组1: "${match[1]}"`);
    console.log(`所有捕获组: [${match.slice(1).map(g => `"${g}"`).join(', ')}]`);
  } else {
    console.log('没有匹配');
  }
  console.log('---');
}

// 测试"作者: 张伟"格式
const authorContent = `
# 游戏策略文章

作者: 张伟
`;

console.log('\n测试"作者: 张伟"格式:');
console.log(authorContent);

const authorRegex = /(?:Author|By|Escrito por|作者)[:\s]+([A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+(?:\s+[A-Za-záâãàéêíóôõúçÀÁÂÃÉÊÍÓÔÕÚÇ\u4e00-\u9fff]+)*)/i;
const authorMatch = authorContent.match(authorRegex);

if (authorMatch) {
  console.log(`匹配成功: "${authorMatch[1]}"`);
} else {
  console.log('匹配失败');
  
  // 测试更简单的格式
  const simpleAuthorRegex = /作者[:\s]+([^\n\r]+)/i;
  const simpleMatch = authorContent.match(simpleAuthorRegex);
  if (simpleMatch) {
    console.log(`简单匹配成功: "${simpleMatch[1]}"`);
  }
}

console.log('\n=== 调试完成 ===');