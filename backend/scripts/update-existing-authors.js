/**
 * 更新现有文章的作者信息脚本
 * 使用新的作者提取逻辑重新处理已发布的文章
 */

const pool = require('../config/database');
const { getProperAuthor, extractAuthorFromGameInfo, extractAuthorFromContent } = require('../utils/extractAuthorInfo');

async function updateExistingAuthors() {
  console.log('=== 更新现有文章作者信息 ===\n');
  
  try {
    // 1. 获取所有已发布的文章
    console.log('1. 获取所有已发布文章...');
    const articles = await pool.query(`
      SELECT id, title, author, content, game_info 
      FROM blog_posts 
      WHERE published = true
      ORDER BY created_at DESC
    `);
    
    if (articles.rows.length === 0) {
      console.log('❌ 没有找到已发布的文章');
      return;
    }
    
    console.log(`📄 找到 ${articles.rows.length} 篇已发布文章\n`);
    
    let updatedCount = 0;
    let skippedCount = 0;
    
    // 2. 逐个处理每篇文章
    for (const article of articles.rows) {
      console.log(`处理文章: "${article.title}" (ID: ${article.id})`);
      console.log(`  当前作者: "${article.author}"`);
      
      // 使用新的作者提取逻辑
      const newAuthor = getProperAuthor(article);
      console.log(`  提取的作者: "${newAuthor}"`);
      
      // 检查是否需要更新
      if (newAuthor !== article.author) {
        console.log(`  ✅ 需要更新: "${article.author}" -> "${newAuthor}"`);
        
        try {
          // 更新数据库
          await pool.query(
            'UPDATE blog_posts SET author = ?, updated_at = ? WHERE id = ?',
            [newAuthor, new Date().toISOString(), article.id]
          );
          
          console.log(`  ✅ 已更新到数据库`);
          updatedCount++;
        } catch (error) {
          console.log(`  ❌ 更新失败: ${error.message}`);
        }
      } else {
        console.log(`  ⏭️ 无需更新`);
        skippedCount++;
      }
      
      console.log('  ---');
    }
    
    // 3. 汇总结果
    console.log('\n=== 更新汇总 ===');
    console.log(`📊 总文章数: ${articles.rows.length}`);
    console.log(`✅ 已更新: ${updatedCount}`);
    console.log(`⏭️ 跳过: ${skippedCount}`);
    
    if (updatedCount > 0) {
      console.log('\n🎉 作者信息更新完成！');
      
      // 验证更新结果
      console.log('\n4. 验证更新结果...');
      const updatedArticles = await pool.query(`
        SELECT id, title, author 
        FROM blog_posts 
        WHERE published = true AND author != 'Writer 777 AI'
        ORDER BY updated_at DESC
        LIMIT 5
      `);
      
      if (updatedArticles.rows.length > 0) {
        console.log('✅ 更新验证 - 现在显示真实作者的文章:');
        for (const article of updatedArticles.rows) {
          console.log(`  📄 "${article.title}" - 作者: "${article.author}"`);
        }
      }
    } else {
      console.log('\n📝 所有文章的作者信息都已经是最新的。');
    }
    
  } catch (error) {
    console.error('❌ 更新过程中出错:', error);
  } finally {
    await pool.end();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  updateExistingAuthors()
    .then(() => {
      console.log('\n=== 脚本执行完成 ===');
      process.exit(0);
    })
    .catch(error => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { updateExistingAuthors };