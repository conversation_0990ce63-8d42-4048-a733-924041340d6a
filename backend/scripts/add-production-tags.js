const Database = require('../config/database');

/**
 * 为生产环境文章添加标签
 */
async function addProductionTags() {
  try {
    console.log('🏷️ 开始为生产环境文章添加标签...');
    
    await Database.connect();
    
    // 获取所有已发布的文章
    const articles = await Database.all(`
      SELECT id, title, content_type, content, language
      FROM blog_posts 
      WHERE status = 'published' 
      ORDER BY created_at DESC 
      LIMIT 20
    `);
    
    console.log(`找到 ${articles.length} 篇已发布文章`);
    
    // 定义标签映射
    const tagsByContentType = {
      'casino_review': ['casino online', 'análise de casino', 'jogos de casino', 'slots', 'bônus', 'apostas'],
      'game_guide': ['guia de jogo', 'slot machine', 'como jogar', 'dicas de jogo', 'estratégia', 'caça-níqueis'],
      'sports_betting': ['apostas esportivas', 'futebol', 'odds', 'dicas de apostas', 'análise esportiva'],
      'bonus_analysis': ['bônus de casino', 'bônus de boas-vindas', 'rodadas grátis', 'sem depósito', 'promoção'],
      'industry_news': ['notícias de jogos', 'indústria de casino', 'regulamentação', 'tendências de mercado'],
      'strategy_article': ['estratégia de jogo', 'dicas para ganhar', 'dicas de casino', 'estratégia de apostas'],
      'brand_copy': ['análise de casino', 'jogos online', 'plataforma de jogos']
    };
    
    const generalTags = ['jogos', 'casino', 'gaming online', 'entretenimento', 'apostas online'];
    
    // Adicionar tags baseadas no conteúdo
    const contentBasedTags = {
      'bet': ['apostas', 'betting', 'casa de apostas'],
      '777': ['777', 'slots', 'caça-níqueis'],
      'poker': ['poker', 'cartas', 'estratégia'],
      'blackjack': ['blackjack', '21', 'cartas'],
      'roleta': ['roleta', 'roulette', 'mesa'],
      'futebol': ['futebol', 'soccer', 'esportes'],
      'basquete': ['basquete', 'basketball', 'NBA'],
      'tênis': ['tênis', 'tennis', 'ATP']
    };
    
    let updatedCount = 0;
    
    // Para cada artigo, adicionar tags
    for (const article of articles) {
      const contentType = article.content_type || 'general';
      let articleTags = [];
      
      // Tags baseadas no tipo de conteúdo
      if (tagsByContentType[contentType]) {
        const relevantTags = tagsByContentType[contentType];
        const numTags = Math.floor(Math.random() * 3) + 2; // 2-4 tags
        
        for (let i = 0; i < numTags && i < relevantTags.length; i++) {
          const randomTag = relevantTags[Math.floor(Math.random() * relevantTags.length)];
          if (!articleTags.includes(randomTag)) {
            articleTags.push(randomTag);
          }
        }
      }
      
      // Tags baseadas no conteúdo do título e texto
      const titleLower = (article.title || '').toLowerCase();
      const contentLower = (article.content || '').toLowerCase();
      const searchText = titleLower + ' ' + contentLower;
      
      Object.entries(contentBasedTags).forEach(([keyword, tags]) => {
        if (searchText.includes(keyword)) {
          tags.forEach(tag => {
            if (!articleTags.includes(tag) && articleTags.length < 6) {
              articleTags.push(tag);
            }
          });
        }
      });
      
      // Adicionar 1-2 tags gerais
      const numGeneralTags = Math.floor(Math.random() * 2) + 1;
      for (let i = 0; i < numGeneralTags; i++) {
        const randomTag = generalTags[Math.floor(Math.random() * generalTags.length)];
        if (!articleTags.includes(randomTag) && articleTags.length < 6) {
          articleTags.push(randomTag);
        }
      }
      
      // Garantir pelo menos 2 tags
      if (articleTags.length < 2) {
        articleTags.push('casino', 'jogos');
      }
      
      // Remover duplicatas e limitar a 5 tags
      articleTags = [...new Set(articleTags)].slice(0, 5);
      
      // Atualizar artigo com tags
      const tagsJson = JSON.stringify(articleTags);
      await Database.run(
        'UPDATE blog_posts SET tags = ? WHERE id = ?',
        [tagsJson, article.id]
      );
      
      console.log(`✅ Atualizado "${article.title.substring(0, 50)}..." com tags:`, articleTags);
      updatedCount++;
    }
    
    console.log(`\n🎉 Sucesso! ${updatedCount} artigos atualizados com tags`);
    
    // Verificar o resultado
    const updatedArticles = await Database.all(`
      SELECT id, title, tags 
      FROM blog_posts 
      WHERE tags IS NOT NULL AND tags != '[]' 
      ORDER BY created_at DESC
      LIMIT 5
    `);
    
    console.log('\n📊 Primeiros 5 artigos com tags:');
    updatedArticles.forEach(article => {
      const tags = JSON.parse(article.tags || '[]');
      console.log(`- "${article.title.substring(0, 40)}...": [${tags.join(', ')}]`);
    });
    
    // Estatísticas das tags
    const allTags = {};
    const taggedArticles = await Database.all(`
      SELECT tags FROM blog_posts 
      WHERE tags IS NOT NULL AND tags != '[]'
    `);
    
    taggedArticles.forEach(article => {
      try {
        const tags = JSON.parse(article.tags || '[]');
        tags.forEach(tag => {
          allTags[tag] = (allTags[tag] || 0) + 1;
        });
      } catch (error) {
        console.log('Erro ao processar tags:', error);
      }
    });
    
    console.log('\n📈 Top 10 tags mais usadas:');
    Object.entries(allTags)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .forEach(([tag, count]) => {
        console.log(`- ${tag}: ${count} artigos`);
      });
    
  } catch (error) {
    console.error('❌ Erro ao adicionar tags:', error);
    throw error;
  }
}

// Se executado diretamente
if (require.main === module) {
  addProductionTags()
    .then(() => {
      console.log('✅ Tags de produção adicionadas com sucesso');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Falha ao adicionar tags:', error);
      process.exit(1);
    });
}

module.exports = { addProductionTags };