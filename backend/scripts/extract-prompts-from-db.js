#!/usr/bin/env node

/**
 * Extract Prompt Templates from Database Script
 * 
 * This script connects to the PostgreSQL database and extracts all prompt templates
 * to create a new code-based prompt configuration file.
 * 
 * Usage:
 *   node scripts/extract-prompts-from-db.js
 */

require('dotenv').config();
const database = require('../config/database');
const fs = require('fs');
const path = require('path');

async function extractPromptsFromDatabase() {
  console.log('🔍 正在从数据库中提取prompt模板...');
  
  try {
    // 连接数据库
    await database.connect();
    console.log('✅ 数据库连接成功');
    
    // 查询所有活跃的prompt模板
    const templates = await database.all(
      'SELECT * FROM prompt_templates WHERE is_active = TRUE ORDER BY content_type, created_at DESC'
    );
    
    console.log(`📊 找到 ${templates.length} 个活跃的prompt模板`);
    
    if (templates.length === 0) {
      console.log('⚠️  数据库中没有找到活跃的prompt模板');
      return;
    }
    
    // 转换模板数据为代码格式
    const extractedTemplates = templates.map(template => {
      let compliance_rules;
      try {
        compliance_rules = typeof template.compliance_rules === 'string' 
          ? JSON.parse(template.compliance_rules) 
          : template.compliance_rules;
      } catch (e) {
        compliance_rules = [];
      }
      
      return {
        content_type: template.content_type,
        template_name: template.template_name,
        template_version: template.template_version || '1.0',
        persona_description: template.persona_description || '',
        specialized_instructions: template.specialized_instructions || '',
        prompt_content: template.prompt_content || '',
        compliance_rules: Array.isArray(compliance_rules) ? compliance_rules : [],
        is_active: Boolean(template.is_active),
        // 添加数据库元信息用于参考
        _db_info: {
          id: template.id,
          created_at: template.created_at,
          updated_at: template.updated_at
        }
      };
    });
    
    // 生成新的配置文件内容
    const configContent = generatePromptConfigFile(extractedTemplates);
    
    // 写入新的配置文件
    const outputPath = path.join(__dirname, '../config/promptTemplates.js');
    fs.writeFileSync(outputPath, configContent, 'utf8');
    
    console.log('✅ 新的prompt配置文件已生成:', outputPath);
    
    // 显示提取的模板信息
    console.log('\n📋 提取的模板类型:');
    extractedTemplates.forEach(template => {
      console.log(`  - ${template.content_type}: ${template.template_name}`);
    });
    
    console.log('\n🎉 prompt模板提取完成!');
    console.log('💡 接下来需要更新promptTemplateService.js以使用新的配置文件');
    
  } catch (error) {
    console.error('❌ 提取prompt模板失败:', error);
    throw error;
  } finally {
    // 关闭数据库连接
    await database.close();
    console.log('🔌 数据库连接已关闭');
  }
}

function generatePromptConfigFile(templates) {
  return `// Prompt Templates Configuration
// 这个文件包含了从数据库提取的所有prompt模板
// 用于版本控制和代码管理

/**
 * 基础模板结构 - 所有模板的通用框架
 */
const BASE_TEMPLATE_STRUCTURE = \`You are {{PERSONA_DESCRIPTION}}. Your mission is to create ONE SINGLE, COHESIVE, and insightful article that is compliant, responsible, and provides genuine value to the reader. You must ANALYZE and INTERPRET all provided inputs to generate content that meets the highest standards of quality and safety.

**CRITICAL COMPLIANCE & SAFETY DIRECTIVES (ABSOLUTE PRIORITY):**

1.  **RESPONSIBLE GAMING FIRST:** You must proactively integrate responsible gaming messaging. Never use predatory language, guarantee wins, or promote gambling as a financial solution. Frame all activities as entertainment for adults with inherent risks.

2.  **JURISDICTIONAL COMPLIANCE:** You must strictly adhere to all provided compliance rules, content restrictions, and mandatory disclaimers for the target jurisdiction: {{JURISDICTION}}.

3.  **ACCURACY & SOURCE RELIANCE:** You must never fabricate information. All factual claims about game rules, odds, statistics, or regulations must be based on the provided reference sources.

**UNIVERSAL QUALITY & CONTENT RULES:**

1.  **THESIS-DRIVEN (if applicable):** For analytical content, develop one clear, non-obvious central argument. Challenge assumptions where appropriate.

2.  **ZERO DUPLICATION:** Every paragraph must introduce new value.

3.  **AUTHORIAL VOICE:** Fully embody the specified author's expertise and tone.

**INTERNAL THINKING (Mandatory First Step):**

Before generating output, perform this silent internal analysis (DO NOT include in final output):

1.  **Compliance Check:** Review the required disclaimers, jurisdiction, and safety directives. How will I ensure every rule is met?

2.  **Goal Alignment:** What is the core purpose of this specific content type? Review the specialized instructions and structure requirements to align the output.

3.  **Title Creativity & Tone Check:** Ensure the proposed SEO title is fresh, engaging, and follows the tonal balance and format guidelines.

4.  **Narrative Flow:** Create an outline of H2s that matches the required structure for this content type, incorporating the user-provided topics where they fit best.

5.  **Source Integration Plan:** How will each provided source contribute specific, valuable insights?

**ARTICLE INPUTS & DETAILS:**

{{article_inputs}}

**CRITICAL OUTPUT REQUIREMENTS (Strict Order - Use these exact labels):**

1.  **SEO Title:** [Generate one 55-65 character title. It must contain the Core Concept Keyword and be creative, avoiding cliché patterns like "Ultimate Guide", "Master", "Beyond", etc. Prioritize positive or neutral, benefit-driven formats.]

2.  **Meta Description:** [Generate one 150-160 character description.]

3.  **Focus Keywords:** [List up to 5 most important keywords.]

4.  **Tags:** [List up to 5 relevant tags, formatted as a single comma-separated string.]

**--------------------------------------------------------------------------** (Separator)

5.  **Full Article:** [Generate the complete article starting directly with the Introduction. DO NOT include an H1 tag (#) within the article body.]

6.  **About the Author Section:** [At the end of the article, generate a final H2 section titled "About the Author".]

7.  **Visual Aid Suggestions:** [After the "About the Author Section", generate a final H2 section titled "Visual Aid Suggestions".]

**ENHANCED WRITING INSTRUCTIONS:**

**SPECIALIZED INSTRUCTIONS & STRUCTURE FOR THIS CONTENT TYPE:**

{{SPECIALIZED_INSTRUCTIONS}}

**STYLISTIC GUIDELINES (Universal):**

* **Prioritize Clarity:** Ensure explanations are clear and accessible. Define complex terms if essential. Use analogies or examples.

* **Vary Sentence Structure:** Mix shorter, direct sentences with longer, analytical ones.

* **Avoid Filler Words & Meta-Commentary.**

* **Reduce Vague Intensifiers.**

* **Show, Don't Just Tell:** Instead of calling something "innovative," describe *why* it is innovative.

* **Favor Active Voice.**

**ADVANCED KEYWORD STRATEGY (Universal):**

1.  Identify the "Core Concept Keyword" for the H1 and intro.

2.  Theme H2s with other keywords.

3.  Lean heavily on semantic variations.

4.  **Golden Rule:** Readability is paramount over keyword density.

**QUALITY CONTROL CHECKPOINTS (Universal):**

Before finalizing, mentally verify: No fabricated claims, all compliance rules met, each paragraph adds value, the primary goal is clear, and the tone is authentic.

**Compliance Rules:**

{{compliance_rules}}

**FINAL INSTRUCTION:**

Generate the SEO Title, Meta Description, Focus Keywords, Tags, and complete article now. Follow ALL instructions with precision, prioritizing compliance, accuracy, and responsible communication.\`;

/**
 * 从数据库提取的Prompt模板配置
 * 生成时间: ${new Date().toISOString()}
 */
const PROMPT_TEMPLATES = {
${templates.map(template => `  ${template.content_type}: {
    template_name: ${JSON.stringify(template.template_name)},
    template_version: ${JSON.stringify(template.template_version)},
    persona_description: ${JSON.stringify(template.persona_description)},
    specialized_instructions: ${JSON.stringify(template.specialized_instructions)},
    prompt_content: ${template.prompt_content ? JSON.stringify(template.prompt_content) : 'BASE_TEMPLATE_STRUCTURE'},
    compliance_rules: ${JSON.stringify(template.compliance_rules, null, 2)},
    is_active: ${template.is_active}${template._db_info ? `,
    // 数据库信息 (仅供参考)
    _db_info: ${JSON.stringify(template._db_info, null, 2)}` : ''}
  }`).join(',\n')}
};

/**
 * 获取指定内容类型的模板
 * @param {string} contentType 内容类型
 * @returns {Object|null} 模板对象
 */
function getTemplateByContentType(contentType) {
  const template = PROMPT_TEMPLATES[contentType];
  if (!template) {
    // 如果没有找到指定类型，尝试使用generic模板
    return PROMPT_TEMPLATES.generic || null;
  }
  return template;
}

/**
 * 获取所有活跃的模板
 * @returns {Array} 活跃模板数组
 */
function getAllActiveTemplates() {
  return Object.entries(PROMPT_TEMPLATES)
    .filter(([, template]) => template.is_active)
    .map(([content_type, template]) => ({
      content_type,
      ...template
    }));
}

/**
 * 获取所有可用的内容类型
 * @returns {Array} 内容类型数组
 */
function getAvailableContentTypes() {
  return Object.keys(PROMPT_TEMPLATES).filter(type => PROMPT_TEMPLATES[type].is_active);
}

/**
 * 构建动态prompt
 * @param {string} contentType 内容类型
 * @param {Object} articleData 文章数据
 * @returns {string} 构建好的prompt
 */
function buildDynamicPrompt(contentType, articleData) {
  const template = getTemplateByContentType(contentType);
  if (!template) {
    throw new Error(\`No template found for content type: \${contentType}\`);
  }

  const {
    jurisdiction = 'international',
    compliance_rules = []
  } = articleData;

  // 使用基础模板结构或自定义prompt内容
  let promptContent = template.prompt_content === 'BASE_TEMPLATE_STRUCTURE' 
    ? BASE_TEMPLATE_STRUCTURE 
    : template.prompt_content;

  // 格式化合规规则
  const complianceRules = Array.isArray(template.compliance_rules)
    ? template.compliance_rules.concat(compliance_rules).join('\\n')
    : (template.compliance_rules || '');

  // 替换占位符
  promptContent = promptContent
    .replace(/{{PERSONA_DESCRIPTION}}/g, template.persona_description || '')
    .replace(/{{SPECIALIZED_INSTRUCTIONS}}/g, template.specialized_instructions || '')
    .replace(/{{JURISDICTION}}/g, jurisdiction)
    .replace(/{{compliance_rules}}/g, complianceRules);

  return promptContent;
}

module.exports = {
  PROMPT_TEMPLATES,
  BASE_TEMPLATE_STRUCTURE,
  getTemplateByContentType,
  getAllActiveTemplates,
  getAvailableContentTypes,
  buildDynamicPrompt
};
`;
}

// 运行脚本
if (require.main === module) {
  extractPromptsFromDatabase()
    .then(() => {
      console.log('🏁 脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { extractPromptsFromDatabase };