const database = require('../config/database');

// Helper function to generate slug from title
function generateSlug(title) {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim('-'); // Remove leading/trailing hyphens
}

// Helper function to extract title from article content
function extractTitle(content, fallbackName) {
  if (!content) return fallbackName;
  
  // Try to find H1 tag
  const h1Match = content.match(/<h1[^>]*>(.*?)<\/h1>/i);
  if (h1Match) {
    return h1Match[1].replace(/<[^>]*>/g, '').trim();
  }
  
  // Try to find first heading
  const headingMatch = content.match(/<h[1-6][^>]*>(.*?)<\/h[1-6]>/i);
  if (headingMatch) {
    return headingMatch[1].replace(/<[^>]*>/g, '').trim();
  }
  
  // Try to find first line that looks like a title
  const lines = content.split('\n');
  for (const line of lines) {
    const cleanLine = line.replace(/<[^>]*>/g, '').trim();
    if (cleanLine.length > 10 && cleanLine.length < 100 && !cleanLine.includes('.')) {
      return cleanLine;
    }
  }
  
  return fallbackName;
}

// Helper function to extract excerpt from content
function extractExcerpt(content, maxLength = 200) {
  if (!content) return '';
  
  // Remove HTML tags and get plain text
  const plainText = content.replace(/<[^>]*>/g, '');
  
  if (plainText.length <= maxLength) {
    return plainText;
  }
  
  // Find the last complete sentence within the limit
  const truncated = plainText.substring(0, maxLength);
  const lastSentence = truncated.lastIndexOf('.');
  
  if (lastSentence > maxLength * 0.7) {
    return truncated.substring(0, lastSentence + 1);
  }
  
  return truncated + '...';
}

// Helper function to ensure unique slug
async function ensureUniqueSlug(baseSlug) {
  let slug = baseSlug;
  let counter = 1;
  
  while (true) {
    const existing = await database.get(
      'SELECT id FROM blog_posts WHERE slug = ?',
      [slug]
    );
    
    if (!existing) {
      return slug;
    }
    
    slug = `${baseSlug}-${counter}`;
    counter++;
  }
}

// Main migration function
async function migrateArticles() {
  try {
    console.log('Starting article migration...');
    
    // Connect to database
    await database.connect();
    console.log('Database connected');
    
    // Get all completed tasks with generated articles
    const completedTasks = await database.all(`
      SELECT 
        id,
        name,
        content_type,
        generated_article,
        created_at,
        updated_at
      FROM tasks 
      WHERE status LIKE '%Completed%' 
        AND generated_article IS NOT NULL 
        AND generated_article != ''
      ORDER BY updated_at DESC
    `);
    
    console.log(`Found ${completedTasks.length} completed tasks with articles`);
    
    if (completedTasks.length === 0) {
      console.log('No articles to migrate');
      return;
    }
    
    let migratedCount = 0;
    let skippedCount = 0;
    
    for (const task of completedTasks) {
      try {
        // Extract title from article content or use task name
        const title = extractTitle(task.generated_article, task.name);
        
        // Generate slug
        const baseSlug = generateSlug(title);
        const uniqueSlug = await ensureUniqueSlug(baseSlug);
        
        // Extract excerpt
        const excerpt = extractExcerpt(task.generated_article);
        
        // Check if already migrated
        const existing = await database.get(
          'SELECT id FROM blog_posts WHERE slug = ?',
          [uniqueSlug]
        );
        
        if (existing) {
          console.log(`Skipping ${title} - already exists`);
          skippedCount++;
          continue;
        }
        
        // Insert into blog_posts
        await database.run(`
          INSERT INTO blog_posts (
            title,
            slug,
            content,
            excerpt,
            content_type,
            author,
            status,
            created_at,
            updated_at,
            published_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          title,
          uniqueSlug,
          task.generated_article,
          excerpt,
          task.content_type || 'article',
          'Writer 777 AI',
          'published',
          task.created_at,
          task.updated_at,
          task.updated_at // Use updated_at as published_at
        ]);
        
        console.log(`✓ Migrated: ${title} (${uniqueSlug})`);
        migratedCount++;
        
      } catch (error) {
        console.error(`Error migrating task ${task.id}:`, error);
      }
    }
    
    console.log('\n=== Migration Summary ===');
    console.log(`Total tasks found: ${completedTasks.length}`);
    console.log(`Successfully migrated: ${migratedCount}`);
    console.log(`Skipped (already exists): ${skippedCount}`);
    console.log(`Errors: ${completedTasks.length - migratedCount - skippedCount}`);
    
    // Show some sample migrated articles
    const sampleArticles = await database.all(`
      SELECT title, slug, content_type, published_at
      FROM blog_posts 
      WHERE status = 'published'
      ORDER BY published_at DESC
      LIMIT 5
    `);
    
    console.log('\n=== Sample Migrated Articles ===');
    sampleArticles.forEach(article => {
      console.log(`- ${article.title} (${article.content_type})`);
      console.log(`  Slug: ${article.slug}`);
      console.log(`  Published: ${article.published_at}`);
      console.log('');
    });
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    await database.close();
    console.log('Database connection closed');
  }
}

// Run migration if called directly
if (require.main === module) {
  migrateArticles()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateArticles };
