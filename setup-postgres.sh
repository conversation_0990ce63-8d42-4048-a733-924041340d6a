#!/bin/bash

# Writer777 本地PostgreSQL开发环境设置脚本

echo "=== Writer777 PostgreSQL本地开发环境设置 ==="

# 检查PostgreSQL是否运行
if ! pgrep -x "postgres" > /dev/null; then
    echo "PostgreSQL服务未运行，请先启动PostgreSQL服务"
    exit 1
fi

echo "PostgreSQL服务正在运行 ✓"

# 获取当前用户名
CURRENT_USER=$(whoami)
echo "当前用户: $CURRENT_USER"

# 数据库配置
DB_NAME="writer777_dev"
DB_USER="writer777"
DB_PASSWORD="writer777_dev"

echo ""
echo "=== 配置PostgreSQL用户和数据库 ==="
echo ""

# 创建SQL脚本来设置数据库
cat > /tmp/setup_writer777_db.sql << EOF
-- 创建数据库用户
CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';

-- 创建数据库
CREATE DATABASE $DB_NAME OWNER $DB_USER;

-- 给用户创建数据库的权限
ALTER USER $DB_USER CREATEDB;

-- 显示结果
\l
\du
EOF

echo "正在设置数据库用户和数据库..."
echo "请输入PostgreSQL超级用户(postgres)的密码:"

# 执行SQL脚本
sudo -u postgres psql -f /tmp/setup_writer777_db.sql

if [ $? -eq 0 ]; then
    echo ""
    echo "✓ 数据库设置完成！"
    echo ""
    echo "数据库信息:"
    echo "  数据库名: $DB_NAME"
    echo "  用户名: $DB_USER"
    echo "  密码: $DB_PASSWORD"
    echo "  连接字符串: postgresql://$DB_USER:$DB_PASSWORD@localhost:5432/$DB_NAME"
    
    # 测试连接
    echo ""
    echo "测试数据库连接..."
    PGPASSWORD=$DB_PASSWORD psql -U $DB_USER -h localhost -d $DB_NAME -c "SELECT 'Connection successful!' as status;"
    
    if [ $? -eq 0 ]; then
        echo "✓ 数据库连接测试成功！"
    else
        echo "✗ 数据库连接测试失败"
        exit 1
    fi
else
    echo "✗ 数据库设置失败"
    exit 1
fi

# 清理临时文件
rm -f /tmp/setup_writer777_db.sql

echo ""
echo "=== 下一步 ==="
echo "1. 设置环境变量文件 (.env)"
echo "2. 运行数据库初始化脚本"
echo "3. 启动开发服务器"
echo ""
echo "数据库连接字符串已准备好:"
echo "DATABASE_URL=postgresql://$DB_USER:$DB_PASSWORD@localhost:5432/$DB_NAME"