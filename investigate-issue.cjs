// 直接测试我们从数据库获取的实际JSON数据
const { Pool } = require('pg');

const pool = new Pool({
  connectionString: 'postgresql://postgres:<EMAIL>:21666/railway',
  ssl: { rejectUnauthorized: false }
});

async function investigateIssue() {
  try {
    // 1. 检查文章是否存在
    const articleResult = await pool.query(`
      SELECT id, title, slug, language, content_type
      FROM blog_posts 
      WHERE slug = 'viso-geral-e-fatos-chave-da-735-bet'
      LIMIT 1
    `);

    if (articleResult.rows.length === 0) {
      console.log('❌ 文章不存在！');
      return;
    }

    const article = articleResult.rows[0];
    console.log('✅ 文章存在:');
    console.log(`  - ID: ${article.id}`);
    console.log(`  - 标题: ${article.title}`);
    console.log(`  - Slug: ${article.slug}`);
    console.log(`  - 语言: ${article.language}`);
    console.log(`  - 内容类型: ${article.content_type}`);
    console.log('');

    // 2. 检查URL路由是否正确
    const expectedUrl = '/pt/articles/viso-geral-e-fatos-chave-da-735-bet';
    console.log(`✅ 预期URL: ${expectedUrl}`);
    console.log('');

    // 3. 检查前端是否能正确获取到这篇文章
    // 模拟前端API调用
    console.log('🔍 模拟前端API调用...');
    
    // 检查是否有其他相关文章
    const relatedResult = await pool.query(`
      SELECT id, title, slug, language 
      FROM blog_posts 
      WHERE title ILIKE '%735%' OR content ILIKE '%735%'
      ORDER BY created_at DESC
    `);

    console.log(`找到 ${relatedResult.rows.length} 篇相关文章:`);
    relatedResult.rows.forEach((row, index) => {
      console.log(`  ${index + 1}. [${row.language || 'N/A'}] ${row.title} (${row.slug})`);
    });
    console.log('');

    // 4. 检查layout_type是否正确
    const layoutResult = await pool.query(`
      SELECT content_type, COUNT(*) as count
      FROM blog_posts 
      GROUP BY content_type
    `);

    console.log('数据库中的content_type分布:');
    layoutResult.rows.forEach(row => {
      console.log(`  - ${row.content_type}: ${row.count}篇`);
    });
    console.log('');

    // 5. 检查前端路由配置
    console.log('🔍 可能的问题:');
    console.log('1. 前端路由配置问题');
    console.log('2. layout_type字段不匹配');
    console.log('3. 语言路由问题');
    console.log('4. 文章状态问题');
    
    // 6. 检查文章状态
    const statusResult = await pool.query(`
      SELECT status, COUNT(*) as count
      FROM blog_posts 
      GROUP BY status
    `);

    console.log('\n文章状态分布:');
    statusResult.rows.forEach(row => {
      console.log(`  - ${row.status}: ${row.count}篇`);
    });

    // 7. 检查具体文章状态
    const detailResult = await pool.query(`
      SELECT status, content_type, published_at
      FROM blog_posts 
      WHERE slug = 'viso-geral-e-fatos-chave-da-735-bet'
    `);

    if (detailResult.rows.length > 0) {
      const detail = detailResult.rows[0];
      console.log('\n📋 文章详细信息:');
      console.log(`  - 状态: ${detail.status}`);
      console.log(`  - 内容类型: ${detail.content_type}`);
      console.log(`  - 发布时间: ${detail.published_at}`);
      
      if (detail.status !== 'published') {
        console.log('⚠️  文章可能未发布！');
      }
      
      if (detail.content_type !== 'casino_review') {
        console.log('⚠️  内容类型可能不匹配！');
      }
    }

  } catch (error) {
    console.error('调查错误:', error);
  } finally {
    await pool.end();
  }
}

investigateIssue();