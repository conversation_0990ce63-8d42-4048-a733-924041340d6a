const testKeywordResearch = async () => {
  try {
    const response = await fetch('http://localhost:3001/api/ideation/keyword-research', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-KEY': 'aag_a0f9a0342d98977ce9ddd48f0a38b789072c3c79109251556ad125a0d11e67fd'
      },
      body: JSON.stringify({
        keyword: '333bet',
        language: 'pt',
        country: 'br'
      })
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('Success:', JSON.stringify(data, null, 2));
    } else {
      console.error('Error:', data);
    }
  } catch (error) {
    console.error('Error:', error.message);
  }
};

testKeywordResearch();