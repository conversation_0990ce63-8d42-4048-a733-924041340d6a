# Vercel Deployment Fix

## Problem
V<PERSON><PERSON> is trying to run an old cached build command:
```bash
cd ai-article-generator && rm -rf node_modules package-lock.json && npm install
```

But the frontend files are now in the root directory.

## Solution Options

### Option 1: Fix Vercel Dashboard Settings (Recommended)

1. **Go to Vercel Dashboard**
   - Visit [vercel.com/dashboard](https://vercel.com/dashboard)
   - Select your project

2. **Update Build Settings**
   - Go to **Settings** → **General**
   - Scroll to **Build & Output Settings**
   - Set the following:
     - **Framework Preset**: `Vite`
     - **Build Command**: `npm run build` (or leave empty)
     - **Output Directory**: `dist` (or leave empty)
     - **Install Command**: `npm install` (or leave empty)
     - **Root Directory**: Leave empty

3. **Save and Redeploy**
   - Click **Save**
   - Go to **Deployments** tab
   - Click **Redeploy** on the latest deployment

### Option 2: Delete and Recreate Project

1. **Delete Current Project**
   - In Vercel dashboard, go to Settings → Advanced
   - Delete the project

2. **Create New Project**
   - Click "New Project"
   - Import from GitHub
   - Select your repository
   - Vercel should auto-detect it as a Vite project

### Option 3: Use Git to Clean Repository

If you have access to git, run these commands:

```bash
# Remove the old directory
git rm -rf ai-article-generator

# Commit the change
git add .
git commit -m "Remove old frontend directory, frontend now in root"

# Push to trigger new deployment
git push origin master
```

## Current Repository Structure

The repository now has the frontend files in the root:

```
/
├── package.json          # Frontend package.json (Vite project)
├── vite.config.js        # Vite configuration
├── index.html            # Main HTML file
├── src/                  # React source code
├── tailwind.config.js    # Tailwind CSS config
├── postcss.config.js     # PostCSS config
├── vercel.json           # Vercel configuration
└── backend/              # Backend code (for Railway)
```

## Expected Build Process

After fixing, Vercel should:

1. **Detect**: Auto-detect as Vite project
2. **Install**: Run `npm install` in root
3. **Build**: Run `npm run build` (executes `vite build`)
4. **Output**: Serve files from `dist/` directory

## Verification

After deployment succeeds, test:

1. **Frontend loads**: Visit your Vercel URL
2. **Routing works**: Navigate between pages
3. **API calls work**: Test with your Railway backend

## Troubleshooting

If it still fails:

1. **Check build logs** in Vercel dashboard
2. **Verify package.json** has correct scripts
3. **Check vercel.json** is properly formatted
4. **Try manual redeploy** from dashboard
5. **Contact Vercel support** if needed

## Files to Check

Make sure these files exist in root:

- ✅ `package.json` (frontend version)
- ✅ `vite.config.js`
- ✅ `index.html`
- ✅ `src/` directory
- ✅ `vercel.json`

## Next Steps

1. Fix Vercel settings using Option 1
2. Test deployment
3. Update frontend API config to use Railway backend URL
4. Test full integration
