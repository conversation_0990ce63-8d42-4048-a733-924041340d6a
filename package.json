{"name": "writer-777", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "start": "cd backend && npm start", "preview": "vite preview", "backend:install": "cd backend && npm install --production"}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^5.4.10"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@tailwindcss/typography": "^0.5.16", "@vitejs/plugin-react": "^4.5.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.3", "react-router-dom": "^6.28.0"}}