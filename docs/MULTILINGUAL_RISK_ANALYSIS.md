# Multilingual Risk Analysis System

## Overview

The Writer 777 application now includes a comprehensive multilingual risk analysis system that can detect dangerous gambling-related language across multiple languages. This system combines deterministic keyword matching with AI-powered contextual analysis to ensure responsible gaming content across all supported languages.

## Supported Languages

### 🌍 **Complete Language Support (7 Languages)**

| Language | Code | Native Name | Coverage |
|----------|------|-------------|----------|
| English | `en` | English | ✅ Complete |
| Spanish | `es` | Español | ✅ Complete |
| German | `de` | Deutsch | ✅ Complete |
| French | `fr` | Français | ✅ Complete |
| Portuguese | `pt` | Português | ✅ Complete |
| Italian | `it` | Italiano | ✅ Complete |
| Dutch | `nl` | Nederlands | ✅ Complete |

## Risk Categories

Each language includes professionally translated keywords across all risk categories:

### 🚨 **High-Risk Categories**
1. **Predatory Language** - Terms that make unrealistic promises
   - Examples: "guaranteed win", "ganancia garantizada", "garantierter gewinn"
   
2. **Addiction Triggers** - Language that encourages compulsive behavior
   - Examples: "one more bet", "una apuesta más", "noch eine wette"
   
3. **Financial Pressure** - Terms encouraging dangerous financial behavior
   - Examples: "bet your savings", "apuesta tus ahorros", "setze deine ersparnisse"
   
4. **Age-Inappropriate Content** - Language targeting minors
   - Examples: "family fun", "diversión familiar", "familienspaß"

### ⚠️ **Medium-Risk Categories**
5. **Promotional Terms** - Marketing language requiring disclaimers
   - Examples: "hot streak", "racha caliente", "glückssträhne"
   
6. **Probability Misrepresentation** - Misleading probability explanations
   - Examples: "due to win", "debido a ganar", "fällig zu gewinnen"

## Technical Implementation

### Hybrid Analysis Approach

1. **Fast Keyword Analysis**
   - Deterministic matching against translated keyword lists
   - Instant results for critical blacklisted terms
   - Language-specific keyword databases

2. **AI-Powered Contextual Analysis**
   - Language-agnostic understanding of context and nuance
   - Cultural adaptation for different markets
   - Advanced pattern recognition beyond simple keywords

3. **Combined Results**
   - Comprehensive risk assessment
   - Detailed explanations and recommendations
   - Multilingual safe alternatives

### API Endpoints

#### Get Supported Languages
```
GET /api/risk-analysis/supported-languages
```

#### Analyze Text (Multilingual)
```
POST /api/risk-analysis/analyze-text
{
  "text": "content to analyze",
  "languageCode": "es",
  "contentType": "casino_review",
  "jurisdiction": "spain",
  "useAI": true
}
```

#### Check Keywords (Multilingual)
```
POST /api/risk-analysis/check-keywords
{
  "keywords": ["palabra clave", "another keyword"],
  "languageCode": "es",
  "useAI": false
}
```

#### Get Safe Alternatives (Multilingual)
```
POST /api/risk-analysis/safe-alternatives
{
  "riskyTerm": "ganancia garantizada",
  "languageCode": "es"
}
```

## Integration Points

### Article Generation Workflow
- Pre-generation risk assessment of topics and keywords
- Language-aware content analysis during generation
- Post-generation compliance checking

### User Interface Components
- `LanguageSelector` component for language selection
- `RiskKeywordWarning` component with multilingual support
- Real-time risk notifications in user's selected language

### Content Management
- Multilingual keyword research with risk analysis
- Language-specific topic ideation with safety checks
- Cross-language content review capabilities

## Usage Examples

### JavaScript (Frontend)
```javascript
// Analyze Spanish content
const response = await fetch('/api/risk-analysis/analyze-text', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    text: 'Este sistema garantiza ganancias seguras',
    languageCode: 'es',
    useAI: true
  })
});

const analysis = await response.json();
console.log(analysis.analysis.isCompliant); // false
console.log(analysis.analysis.detectedRisks); // Array of risks found
```

### Node.js (Backend)
```javascript
const riskKeywordService = require('./services/riskKeywordService');

// Analyze German content with AI
const analysis = await riskKeywordService.analyzeText(
  'Dieses System garantiert sichere Gewinne',
  'casino_review',
  'germany',
  'de'
);

console.log(analysis.isCompliant); // false
console.log(analysis.aiAnalysis.detectedViolations); // AI-detected issues
```

## Safe Alternatives System

The system provides culturally appropriate safe alternatives for risky terms in each language:

### English → Spanish → German Examples
- "guaranteed win" → "ganancia garantizada" → "garantierter gewinn"
- Safe alternatives: "potential success" → "éxito potencial" → "potenzieller erfolg"

### Multilingual Default Alternatives
- **English**: "play responsibly", "understand the risks"
- **Spanish**: "jugar responsablemente", "entender los riesgos"
- **German**: "verantwortlich spielen", "risiken verstehen"
- **French**: "jouer de manière responsable", "comprendre les risques"
- **Portuguese**: "jogar responsavelmente", "entender os riscos"
- **Italian**: "giocare responsabilmente", "capire i rischi"
- **Dutch**: "verantwoord spelen", "risico's begrijpen"

## Configuration

### Environment Variables
```bash
# AI Analysis (optional but recommended)
GEMINI_API_KEY=your_gemini_api_key

# Default language (optional)
DEFAULT_LANGUAGE=en
```

### Frontend Configuration
```javascript
// Language selector usage
<LanguageSelector
  selectedLanguage="es"
  onLanguageChange={(code) => setLanguage(code)}
  disabled={false}
/>

// Risk warning with language support
<RiskKeywordWarning
  keywords={['ganancia garantizada', 'dinero fácil']}
  languageCode="es"
  useAI={true}
  contentType="casino_review"
  onDismiss={() => setShowWarning(false)}
/>
```

## Performance Considerations

### Optimization Strategies
1. **Keyword Analysis First**: Fast deterministic checks before AI analysis
2. **Caching**: Language-specific keyword databases cached in memory
3. **Fallback Support**: Graceful degradation to English if language unsupported
4. **Async Processing**: Non-blocking AI analysis with immediate keyword results

### Response Times
- **Keyword Analysis**: < 10ms per request
- **AI Analysis**: 1-3 seconds per request
- **Combined Analysis**: 1-3 seconds (parallel processing)

## Compliance & Regulatory Support

### Jurisdiction-Specific Features
- Language-aware compliance checking
- Regional regulatory requirement integration
- Culturally appropriate risk messaging
- Local gambling authority guideline adherence

### Supported Jurisdictions
- International (default)
- European Union (multilingual)
- Spain (Spanish)
- Germany (German)
- France (French)
- Portugal (Portuguese)
- Italy (Italian)
- Netherlands (Dutch)

## Future Expansion

The system is designed for easy expansion to additional languages:

### Adding New Languages
1. Translate keyword lists in `riskKeywordService.js`
2. Add language mapping in AI analysis method
3. Update safe alternatives database
4. Add language info to frontend components
5. Test with native speakers for cultural accuracy

### Planned Languages
- Swedish (sv)
- Norwegian (no)
- Danish (da)
- Finnish (fi)
- Polish (pl)
- Czech (cs)

## Monitoring & Analytics

### Risk Detection Metrics
- Language-specific risk detection rates
- AI vs keyword analysis accuracy comparison
- False positive/negative tracking per language
- User compliance improvement metrics

### Logging
All risk analysis requests are logged with:
- Language code
- Analysis type (keyword/AI/hybrid)
- Risk level detected
- Processing time
- User action taken

This comprehensive multilingual system ensures that Writer 777 can safely generate responsible gambling content across major European languages while maintaining the highest standards of player protection and regulatory compliance.
