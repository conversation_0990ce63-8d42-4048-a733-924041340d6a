# 视频优化指南

## 当前情况
- 原始文件：777.mp4 (14MB)
- 对于网页背景视频来说偏大，建议优化到 2-5MB

## 推荐的视频优化参数

### 使用 FFmpeg 压缩命令：
```bash
# 高质量压缩 (推荐)
ffmpeg -i 777.mp4 -c:v libx264 -crf 28 -preset medium -vf "scale=1280:720" -c:a aac -b:a 128k -movflags +faststart 777-optimized.mp4

# 更激进的压缩
ffmpeg -i 777.mp4 -c:v libx264 -crf 32 -preset fast -vf "scale=1024:576" -c:a aac -b:a 96k -movflags +faststart 777-small.mp4

# 为移动端创建更小版本
ffmpeg -i 777.mp4 -c:v libx264 -crf 35 -preset fast -vf "scale=640:360" -c:a aac -b:a 64k -movflags +faststart 777-mobile.mp4
```

### 参数说明：
- `-crf 28-32`: 质量控制 (数值越高文件越小，但质量越低)
- `-preset medium/fast`: 编码速度与质量平衡
- `-vf "scale=1280:720"`: 调整分辨率
- `-movflags +faststart`: 优化流媒体播放
- `-c:a aac -b:a 128k`: 音频编码优化

### 其他在线工具：
1. **HandBrake** (免费桌面应用)
2. **Clipchamp** (在线压缩)
3. **CloudConvert** (在线转换)

## 实施步骤：
1. 压缩原始视频文件
2. 创建多个版本（桌面端/移动端）
3. 替换 public/777.mp4 文件
4. 测试加载性能

## 当前实现的性能优化：
- ✅ 移动端自动使用静态图片
- ✅ 慢网络连接检测
- ✅ 用户偏好检测（减少动画）
- ✅ 错误处理和回退机制
- ✅ 预加载优化
- ✅ 渐进式加载效果