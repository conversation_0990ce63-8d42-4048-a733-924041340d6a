// Admin Panel JavaScript
class AdminPanel {
    constructor() {
        this.token = localStorage.getItem('adminToken');
        this.user = JSON.parse(localStorage.getItem('adminUser') || 'null');
        this.baseURL = window.location.origin;
        this.templates = [];
        this.currentTemplate = null;

        this.init();
    }

    init() {
        if (this.token && this.user) {
            this.showDashboard();
            this.loadUserInfo();
            this.loadTemplates();
            this.loadSettings();
        } else {
            this.showLogin();
        }
    }

    showLogin() {
        document.getElementById('loginForm').style.display = 'block';
        document.getElementById('adminDashboard').style.display = 'none';
    }

    showDashboard() {
        document.getElementById('loginForm').style.display = 'none';
        document.getElementById('adminDashboard').style.display = 'block';
    }

    async makeRequest(endpoint, options = {}) {
        const url = `${this.baseURL}/api/admin${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`,
                'X-Admin-Email': this.user?.email || ''
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                if (response.status === 401) {
                    // Token expired or invalid, logout
                    this.logout();
                    throw new Error('Session expired. Please login again.');
                }
                throw new Error(data.error || 'Request failed');
            }

            return data;
        } catch (error) {
            console.error('API Request failed:', error);
            throw error;
        }
    }

    async loadUserInfo() {
        try {
            const response = await this.makeRequest('/me');
            this.user = response.data.user;
            localStorage.setItem('adminUser', JSON.stringify(this.user));

            // Update header
            const userElement = document.getElementById('currentUser');
            const roleIcon = this.user.role === 'super_admin' ? 'fas fa-crown' : 'fas fa-user-shield';
            const roleText = this.user.role === 'super_admin' ? 'Super Admin' : 'Admin';

            userElement.innerHTML = `
                <span class="badge bg-gold text-dark">
                    <i class="${roleIcon} me-1"></i>
                    ${this.user.name || this.user.email} (${roleText})
                </span>
            `;
        } catch (error) {
            console.error('Failed to load user info:', error);
        }
    }

    async login(email, password) {
        try {
            const response = await fetch(`${this.baseURL}/api/admin/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: email,
                    password: password
                })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Login failed');
            }

            // Store token and user info
            this.token = data.data.token;
            this.user = data.data.user;
            localStorage.setItem('adminToken', this.token);
            localStorage.setItem('adminUser', JSON.stringify(this.user));

            this.showAlert(data.message || 'Login successful');
            this.showDashboard();
            this.loadUserInfo();
            this.loadTemplates();
            this.loadSettings();

            return true;
        } catch (error) {
            this.showAlert(error.message, 'danger');
            return false;
        }
    }

    logout() {
        localStorage.removeItem('adminToken');
        localStorage.removeItem('adminUser');
        this.token = null;
        this.user = null;
        location.reload();
    }

    showAlert(message, type = 'success') {
        const alertContainer = document.getElementById('alertContainer');
        const alertId = 'alert-' + Date.now();
        
        const alertHTML = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        alertContainer.insertAdjacentHTML('beforeend', alertHTML);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            const alert = document.getElementById(alertId);
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    async loadTemplates() {
        try {
            const response = await this.makeRequest('/templates');
            this.templates = response.data;
            this.renderTemplates();
        } catch (error) {
            this.showAlert('Failed to load templates: ' + error.message, 'danger');
        }
    }

    renderTemplates() {
        const grid = document.getElementById('templatesGrid');
        
        if (this.templates.length === 0) {
            grid.innerHTML = `
                <div class="col-12">
                    <div class="admin-card p-4 text-center">
                        <i class="fas fa-file-alt fa-3x mb-3 text-muted"></i>
                        <h5>No templates found</h5>
                        <p class="text-muted">Create your first prompt template to get started.</p>
                        <button class="btn btn-gold" onclick="adminPanel.showCreateTemplateModal()">
                            <i class="fas fa-plus me-2"></i>Create Template
                        </button>
                    </div>
                </div>
            `;
            return;
        }

        grid.innerHTML = this.templates.map(template => `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="admin-card template-card p-3 h-100" onclick="adminPanel.editTemplate(${template.id})">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="mb-0">${template.template_name}</h6>
                        <span class="badge ${template.is_active ? 'bg-success' : 'bg-secondary'} status-badge">
                            ${template.is_active ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                    <p class="text-muted small mb-2">
                        <i class="fas fa-tag me-1"></i>${template.content_type}
                        <span class="ms-2">
                            <i class="fas fa-code-branch me-1"></i>v${template.template_version}
                        </span>
                    </p>
                    <p class="small mb-3">${template.persona_description?.substring(0, 100) || 'No description'}...</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            ${new Date(template.updated_at || template.created_at).toLocaleDateString()}
                        </small>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-light btn-sm" onclick="event.stopPropagation(); adminPanel.editTemplate(${template.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="event.stopPropagation(); adminPanel.deleteTemplate(${template.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    async loadSettings() {
        try {
            const response = await this.makeRequest('/settings');
            const settings = response.data;
            
            // Update AI model select
            document.getElementById('aiModel').value = settings.ai_model;
            
            // Update system status
            const statusHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="d-flex justify-content-between">
                            <span>API Key:</span>
                            <span class="badge ${settings.api_key_configured ? 'bg-success' : 'bg-danger'}">
                                ${settings.api_key_configured ? 'Configured' : 'Missing'}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex justify-content-between">
                            <span>Environment:</span>
                            <span class="badge bg-info">${settings.environment}</span>
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('systemStatus').innerHTML = statusHTML;
        } catch (error) {
            this.showAlert('Failed to load settings: ' + error.message, 'danger');
        }
    }

    showCreateTemplateModal() {
        this.currentTemplate = null;
        document.getElementById('templateModalTitle').textContent = 'Create New Template';
        document.getElementById('templateForm').reset();
        document.getElementById('templateId').value = '';
        
        const modal = new bootstrap.Modal(document.getElementById('templateModal'));
        modal.show();
    }

    async editTemplate(templateId) {
        try {
            const response = await this.makeRequest(`/templates/${templateId}`);
            this.currentTemplate = response.data;
            
            // Populate form
            document.getElementById('templateModalTitle').textContent = 'Edit Template';
            document.getElementById('templateId').value = this.currentTemplate.id;
            document.getElementById('contentType').value = this.currentTemplate.content_type;
            document.getElementById('templateName').value = this.currentTemplate.template_name;
            document.getElementById('templateVersion').value = this.currentTemplate.template_version;
            document.getElementById('isActive').value = this.currentTemplate.is_active.toString();
            document.getElementById('personaDescription').value = this.currentTemplate.persona_description || '';
            document.getElementById('specializedInstructions').value = this.currentTemplate.specialized_instructions || '';
            document.getElementById('promptContent').value = this.currentTemplate.prompt_content || '';
            
            // Handle compliance rules
            const complianceRules = Array.isArray(this.currentTemplate.compliance_rules) 
                ? this.currentTemplate.compliance_rules.join('\n')
                : '';
            document.getElementById('complianceRules').value = complianceRules;
            
            const modal = new bootstrap.Modal(document.getElementById('templateModal'));
            modal.show();
        } catch (error) {
            this.showAlert('Failed to load template: ' + error.message, 'danger');
        }
    }

    async deleteTemplate(templateId) {
        if (!confirm('Are you sure you want to deactivate this template?')) {
            return;
        }

        try {
            await this.makeRequest(`/templates/${templateId}`, {
                method: 'DELETE'
            });
            
            this.showAlert('Template deactivated successfully');
            this.loadTemplates();
        } catch (error) {
            this.showAlert('Failed to delete template: ' + error.message, 'danger');
        }
    }

    async previewTemplate() {
        const formData = this.getFormData();
        
        const sampleData = {
            topics: ['Sample Topic 1', 'Sample Topic 2'],
            primaryKeywords: ['keyword1', 'keyword2'],
            secondaryKeywords: ['secondary1', 'secondary2'],
            sources: [{ title: 'Sample Source', content: 'Sample content' }],
            tonality: 'informative',
            length: 'medium_article',
            format: 'markdown',
            targetAudience: 'general',
            articleGoal: 'inform and engage',
            jurisdiction: 'international'
        };

        try {
            const response = await this.makeRequest('/templates/preview', {
                method: 'POST',
                body: JSON.stringify({
                    template: formData,
                    sampleData: sampleData
                })
            });

            document.getElementById('previewContent').textContent = response.data.preview;
            
            // Trigger Prism highlighting
            if (window.Prism) {
                Prism.highlightAll();
            }
            
            const modal = new bootstrap.Modal(document.getElementById('previewModal'));
            modal.show();
        } catch (error) {
            this.showAlert('Failed to generate preview: ' + error.message, 'danger');
        }
    }

    getFormData() {
        const complianceRulesText = document.getElementById('complianceRules').value;
        const complianceRules = complianceRulesText
            .split('\n')
            .map(rule => rule.trim())
            .filter(rule => rule.length > 0);

        return {
            content_type: document.getElementById('contentType').value,
            template_name: document.getElementById('templateName').value,
            template_version: document.getElementById('templateVersion').value,
            prompt_content: document.getElementById('promptContent').value,
            persona_description: document.getElementById('personaDescription').value,
            specialized_instructions: document.getElementById('specializedInstructions').value,
            compliance_rules: complianceRules,
            is_active: document.getElementById('isActive').value === 'true'
        };
    }

    async exportTemplates() {
        try {
            const response = await fetch(`${this.baseURL}/api/admin/templates/export`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'X-Admin-Email': this.user?.email || ''
                }
            });

            if (!response.ok) {
                throw new Error('Export failed');
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `writer777-templates-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            this.showAlert('Templates exported successfully');
        } catch (error) {
            this.showAlert('Failed to export templates: ' + error.message, 'danger');
        }
    }

    async importTemplates() {
        const fileInput = document.getElementById('importFile');
        const overwrite = document.getElementById('overwriteExisting').checked;

        if (!fileInput.files[0]) {
            this.showAlert('Please select a file to import', 'danger');
            return;
        }

        try {
            const file = fileInput.files[0];
            const text = await file.text();
            const data = JSON.parse(text);

            if (!data.templates || !Array.isArray(data.templates)) {
                throw new Error('Invalid file format');
            }

            const response = await this.makeRequest('/templates/import', {
                method: 'POST',
                body: JSON.stringify({
                    templates: data.templates,
                    overwrite: overwrite
                })
            });

            this.showAlert(`Import completed: ${response.data.imported} imported, ${response.data.skipped} skipped`);
            this.loadTemplates();
            
            // Clear file input
            fileInput.value = '';
            document.getElementById('overwriteExisting').checked = false;
        } catch (error) {
            this.showAlert('Failed to import templates: ' + error.message, 'danger');
        }
    }
}

// Global functions for HTML event handlers
let adminPanel;

async function login(event) {
    event.preventDefault();
    const email = document.getElementById('adminEmail').value;
    const password = document.getElementById('adminPassword').value;

    if (!email || !password) {
        adminPanel.showAlert('Please enter both email and password', 'danger');
        return;
    }

    // Show loading state
    const submitBtn = event.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Authenticating...';
    submitBtn.disabled = true;

    try {
        await adminPanel.login(email, password);
    } finally {
        // Restore button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

function logout() {
    adminPanel.logout();
}

async function updateSettings(event) {
    event.preventDefault();
    
    const aiModel = document.getElementById('aiModel').value;
    
    try {
        await adminPanel.makeRequest('/settings', {
            method: 'PUT',
            body: JSON.stringify({ ai_model: aiModel })
        });
        
        adminPanel.showAlert('Settings updated successfully. Restart server for changes to take effect.');
    } catch (error) {
        adminPanel.showAlert('Failed to update settings: ' + error.message, 'danger');
    }
}

// Template form submission
document.addEventListener('DOMContentLoaded', function() {
    adminPanel = new AdminPanel();
    
    // Template form handler
    document.getElementById('templateForm').addEventListener('submit', async function(event) {
        event.preventDefault();
        
        const formData = adminPanel.getFormData();
        const templateId = document.getElementById('templateId').value;
        
        try {
            if (templateId) {
                // Update existing template
                await adminPanel.makeRequest(`/templates/${templateId}`, {
                    method: 'PUT',
                    body: JSON.stringify(formData)
                });
                adminPanel.showAlert('Template updated successfully');
            } else {
                // Create new template
                await adminPanel.makeRequest('/templates', {
                    method: 'POST',
                    body: JSON.stringify(formData)
                });
                adminPanel.showAlert('Template created successfully');
            }
            
            // Close modal and reload templates
            bootstrap.Modal.getInstance(document.getElementById('templateModal')).hide();
            adminPanel.loadTemplates();
        } catch (error) {
            adminPanel.showAlert('Failed to save template: ' + error.message, 'danger');
        }
    });
});

// Export functions to global scope
window.login = login;
window.logout = logout;
window.updateSettings = updateSettings;
window.exportTemplates = () => adminPanel.exportTemplates();
window.importTemplates = () => adminPanel.importTemplates();
window.previewTemplate = () => adminPanel.previewTemplate();
window.showCreateTemplateModal = () => adminPanel.showCreateTemplateModal();
