<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Writer 777 - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-gold: #FFD700;
            --secondary-gold: #FFA500;
            --dark-bg: #1a1a1a;
            --card-bg: #2d2d2d;
            --text-light: #f8f9fa;
            --border-gold: #B8860B;
        }

        body {
            background: linear-gradient(135deg, var(--dark-bg) 0%, #2c2c2c 100%);
            color: var(--text-light);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .admin-header {
            background: linear-gradient(135deg, var(--primary-gold) 0%, var(--secondary-gold) 100%);
            color: #000;
            padding: 1rem 0;
            box-shadow: 0 4px 20px rgba(255, 215, 0, 0.3);
        }

        .admin-card {
            background: var(--card-bg);
            border: 1px solid var(--border-gold);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .admin-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(255, 215, 0, 0.2);
        }

        .btn-gold {
            background: linear-gradient(135deg, var(--primary-gold) 0%, var(--secondary-gold) 100%);
            border: none;
            color: #000;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-gold:hover {
            background: linear-gradient(135deg, var(--secondary-gold) 0%, var(--primary-gold) 100%);
            color: #000;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
        }

        .nav-tabs .nav-link {
            background: transparent;
            border: 1px solid var(--border-gold);
            color: var(--text-light);
            margin-right: 5px;
        }

        .nav-tabs .nav-link.active {
            background: var(--primary-gold);
            color: #000;
            border-color: var(--primary-gold);
        }

        .table-dark {
            background: var(--card-bg);
        }

        .form-control, .form-select {
            background: var(--card-bg);
            border: 1px solid var(--border-gold);
            color: var(--text-light);
        }

        .form-control:focus, .form-select:focus {
            background: var(--card-bg);
            border-color: var(--primary-gold);
            color: var(--text-light);
            box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
        }

        .modal-content {
            background: var(--card-bg);
            border: 1px solid var(--border-gold);
        }

        .modal-header {
            border-bottom: 1px solid var(--border-gold);
        }

        .modal-footer {
            border-top: 1px solid var(--border-gold);
        }

        .alert-success {
            background: rgba(40, 167, 69, 0.2);
            border-color: #28a745;
            color: #d4edda;
        }

        .alert-danger {
            background: rgba(220, 53, 69, 0.2);
            border-color: #dc3545;
            color: #f8d7da;
        }

        .code-preview {
            background: #1e1e1e;
            border: 1px solid var(--border-gold);
            border-radius: 8px;
            max-height: 400px;
            overflow-y: auto;
        }

        .template-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .template-card:hover {
            border-color: var(--primary-gold);
            box-shadow: 0 4px 20px rgba(255, 215, 0, 0.2);
        }

        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        .loading-spinner {
            display: none;
        }

        .loading .loading-spinner {
            display: inline-block;
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="fas fa-crown me-2"></i>
                        Writer 777 Admin Panel
                    </h1>
                </div>
                <div class="col-md-6 text-end">
                    <span id="currentUser" class="me-3">
                        <!-- User info will be loaded here -->
                    </span>
                    <button class="btn btn-dark" onclick="logout()">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Alert Container -->
        <div id="alertContainer"></div>

        <!-- Admin Login Form -->
        <div id="loginForm" class="row justify-content-center">
            <div class="col-md-6">
                <div class="admin-card p-4">
                    <h3 class="text-center mb-4">
                        <i class="fas fa-crown me-2"></i>Writer 777 Admin Access
                    </h3>
                    <form onsubmit="login(event)">
                        <div class="mb-3">
                            <label for="adminEmail" class="form-label">Email</label>
                            <input type="email" class="form-control" id="adminEmail" placeholder="Enter your email" required>
                            <div class="form-text">Super Admin: <EMAIL></div>
                        </div>
                        <div class="mb-3">
                            <label for="adminPassword" class="form-label">Password</label>
                            <input type="password" class="form-control" id="adminPassword" placeholder="Enter your password" required>
                        </div>
                        <button type="submit" class="btn btn-gold w-100">
                            <i class="fas fa-sign-in-alt me-2"></i>Login to Admin Panel
                        </button>
                    </form>

                    <div class="mt-4 p-3 bg-dark rounded">
                        <h6><i class="fas fa-info-circle me-2"></i>Access Levels</h6>
                        <small class="text-muted">
                            <strong>Super Admin (<EMAIL>):</strong> Full access to all features<br>
                            <strong>Admin Users:</strong> Template management and basic settings<br>
                            <strong>Note:</strong> Use your regular Writer 777 account credentials
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Admin Dashboard -->
        <div id="adminDashboard" style="display: none;">
            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs mb-4" id="adminTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="templates-tab" data-bs-toggle="tab" data-bs-target="#templates" type="button" role="tab">
                        <i class="fas fa-file-alt me-2"></i>Prompt Templates
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab">
                        <i class="fas fa-cog me-2"></i>System Settings
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="import-export-tab" data-bs-toggle="tab" data-bs-target="#import-export" type="button" role="tab">
                        <i class="fas fa-exchange-alt me-2"></i>Import/Export
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="adminTabContent">
                <!-- Templates Tab -->
                <div class="tab-pane fade show active" id="templates" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3><i class="fas fa-file-alt me-2"></i>Prompt Templates</h3>
                        <button class="btn btn-gold" onclick="showCreateTemplateModal()">
                            <i class="fas fa-plus me-2"></i>Create New Template
                        </button>
                    </div>

                    <!-- Templates Grid -->
                    <div id="templatesGrid" class="row">
                        <!-- Templates will be loaded here -->
                    </div>
                </div>

                <!-- Settings Tab -->
                <div class="tab-pane fade" id="settings" role="tabpanel">
                    <h3><i class="fas fa-cog me-2"></i>System Settings</h3>
                    
                    <div class="admin-card p-4 mt-4">
                        <h5>AI Model Configuration</h5>
                        <form onsubmit="updateSettings(event)">
                            <div class="mb-3">
                                <label for="aiModel" class="form-label">AI Model</label>
                                <select class="form-select" id="aiModel">
                                    <option value="gemini-2.5-pro-preview-05-06">Gemini 2.5 Pro Preview (May 2024)</option>
                                    <option value="gemini-2.5-pro-preview-06-05">Gemini 2.5 Pro Preview (June 2024)</option>
                                    <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                                    <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-gold">
                                <i class="fas fa-save me-2"></i>Update Settings
                            </button>
                        </form>

                        <div class="mt-4">
                            <h6>System Status</h6>
                            <div id="systemStatus">
                                <!-- Status will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Import/Export Tab -->
                <div class="tab-pane fade" id="import-export" role="tabpanel">
                    <h3><i class="fas fa-exchange-alt me-2"></i>Import/Export Templates</h3>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="admin-card p-4">
                                <h5><i class="fas fa-download me-2"></i>Export Templates</h5>
                                <p>Download all active templates as JSON file.</p>
                                <button class="btn btn-gold" onclick="exportTemplates()">
                                    <i class="fas fa-file-export me-2"></i>Export All Templates
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="admin-card p-4">
                                <h5><i class="fas fa-upload me-2"></i>Import Templates</h5>
                                <p>Upload JSON file to import templates.</p>
                                <input type="file" class="form-control mb-3" id="importFile" accept=".json">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="overwriteExisting">
                                    <label class="form-check-label" for="overwriteExisting">
                                        Overwrite existing templates
                                    </label>
                                </div>
                                <button class="btn btn-gold" onclick="importTemplates()">
                                    <i class="fas fa-file-import me-2"></i>Import Templates
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Template Edit Modal -->
    <div class="modal fade" id="templateModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="templateModalTitle">Edit Template</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="templateForm">
                        <input type="hidden" id="templateId">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contentType" class="form-label">Content Type</label>
                                    <select class="form-select" id="contentType" required>
                                        <option value="casino_review">Casino Review</option>
                                        <option value="game_guide">Game Guide</option>
                                        <option value="strategy_article">Strategy Article</option>
                                        <option value="brand_copy">Brand Copy</option>
                                        <option value="industry_news">Industry News</option>
                                        <option value="sports_betting">Sports Betting</option>
                                        <option value="generic">Generic</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="templateName" class="form-label">Template Name</label>
                                    <input type="text" class="form-control" id="templateName" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="templateVersion" class="form-label">Version</label>
                                    <input type="text" class="form-control" id="templateVersion" value="1.0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="isActive" class="form-label">Status</label>
                                    <select class="form-select" id="isActive">
                                        <option value="true">Active</option>
                                        <option value="false">Inactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="personaDescription" class="form-label">Persona Description</label>
                            <textarea class="form-control" id="personaDescription" rows="3"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="specializedInstructions" class="form-label">Specialized Instructions</label>
                            <textarea class="form-control" id="specializedInstructions" rows="3"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="promptContent" class="form-label">Prompt Content</label>
                            <textarea class="form-control" id="promptContent" rows="10" required></textarea>
                            <div class="form-text">Use placeholders like {{TOPICS_SECTION}}, {{KEYWORDS_SECTION}}, etc.</div>
                        </div>

                        <div class="mb-3">
                            <label for="complianceRules" class="form-label">Compliance Rules (one per line)</label>
                            <textarea class="form-control" id="complianceRules" rows="4"></textarea>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-secondary" onclick="previewTemplate()">
                                <i class="fas fa-eye me-2"></i>Preview
                            </button>
                            <button type="submit" class="btn btn-gold">
                                <i class="fas fa-save me-2"></i>Save Template
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Preview Modal -->
    <div class="modal fade" id="previewModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Template Preview</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="code-preview">
                        <pre><code id="previewContent" class="language-markdown"></code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="js/admin.js"></script>
</body>
</html>
