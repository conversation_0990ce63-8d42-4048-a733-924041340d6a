# Writer777 本地开发环境设置指南

## 概述
本指南将帮助你在本地设置Writer777的完整开发环境，使用PostgreSQL数据库。

## 前提条件
- Node.js (v18或更高版本)
- PostgreSQL (已安装并运行)
- npm 或 yarn

## 步骤1: 检查PostgreSQL服务

首先确认PostgreSQL服务正在运行：

```bash
# 检查PostgreSQL进程
ps aux | grep postgres

# 如果没有运行，启动服务
sudo service postgresql start
# 或者
sudo systemctl start postgresql
```

## 步骤2: 创建数据库和用户

### 方法1: 使用提供的SQL脚本（推荐）

```bash
# 在项目根目录，以postgres用户身份运行SQL脚本
sudo -u postgres psql -f create-local-db.sql
```

### 方法2: 手动创建

```bash
# 切换到postgres用户并打开psql
sudo -u postgres psql

# 在psql中执行以下命令:
CREATE USER writer777 WITH PASSWORD 'writer777_dev';
CREATE DATABASE writer777_dev OWNER writer777;
ALTER USER writer777 CREATEDB;
GRANT ALL PRIVILEGES ON DATABASE writer777_dev TO writer777;

# 退出psql
\q
```

### 方法3: 如果postgres用户需要密码

如果你的PostgreSQL设置了密码，可以这样操作：

```bash
# 使用密码连接
psql -U postgres -h localhost -W

# 然后执行上面的SQL命令
```

## 步骤3: 验证数据库连接

```bash
# 测试连接
PGPASSWORD=writer777_dev psql -U writer777 -h localhost -d writer777_dev -c "SELECT 'Connection successful!' as status;"
```

应该看到：
```
    status     
---------------
 Connection successful!
```

## 步骤4: 配置环境变量

环境变量文件已经创建在 `backend/.env`，检查内容：

```bash
cat backend/.env
```

确保以下变量设置正确：
```
DATABASE_URL=postgresql://writer777:writer777_dev@localhost:5432/writer777_dev
NODE_ENV=development
GEMINI_API_KEY=your_gemini_api_key_here  # 需要真实的API密钥
JWT_SECRET=writer777_local_development_secret_key_32_chars_minimum
```

## 步骤5: 安装依赖

```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ..
npm install
```

## 步骤6: 初始化数据库

```bash
cd backend

# 测试PostgreSQL连接
npm run test-postgres

# 初始化数据库表结构
npm run init-db

# 创建超级管理员账户
node scripts/create-super-admin.js
```

## 步骤7: 启动开发服务器

```bash
# 启动后端服务器 (在 backend 目录)
cd backend
npm run dev

# 在新的终端窗口启动前端服务器 (在根目录)
cd ..
npm run dev
```

## 访问应用

- **前端**: http://localhost:5173
- **后端API**: http://localhost:3001
- **健康检查**: http://localhost:3001/health

## 默认管理员账户

- **邮箱**: <EMAIL>
- **密码**: admin123

## 常见问题解决

### 1. PostgreSQL连接失败

**错误**: `password authentication failed for user "writer777"`

**解决方案**:
- 确保用户和数据库已正确创建
- 检查密码是否正确
- 验证PostgreSQL服务是否运行

### 2. 权限错误

**错误**: `permission denied for database`

**解决方案**:
```bash
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE writer777_dev TO writer777;"
sudo -u postgres psql -d writer777_dev -c "GRANT ALL PRIVILEGES ON SCHEMA public TO writer777;"
```

### 3. 端口占用

**错误**: `Port 3001 is already in use`

**解决方案**:
```bash
# 查找占用端口的进程
lsof -i :3001

# 杀死进程
kill -9 <PID>
```

### 4. API密钥配置

为了完整功能，你需要配置真实的API密钥：

1. **Google Gemini API密钥**:
   - 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
   - 创建API密钥
   - 在 `backend/.env` 中设置 `GEMINI_API_KEY`

2. **Serper API密钥** (可选):
   - 访问 [Serper.dev](https://serper.dev)
   - 获取API密钥
   - 在 `backend/.env` 中设置 `SERPER_API_KEY`

## 数据库管理

### 查看数据库数据
```bash
cd backend
node scripts/check-data.js
```

### 添加示例文章
```bash
cd backend
node scripts/add-sample-articles.js
```

### 重置超级管理员密码
```bash
cd backend
node scripts/reset-super-admin-password.js
```

## 开发工作流

1. 修改代码
2. 后端会自动重启 (nodemon)
3. 前端会自动刷新 (Vite HMR)
4. 使用浏览器开发者工具调试

## 生产部署注意事项

本地开发环境的配置仅用于开发目的。生产环境请使用Railway等云平台，并配置适当的安全设置。

---

如果遇到其他问题，请检查：
1. PostgreSQL服务状态
2. 环境变量配置
3. 网络连接
4. 日志输出中的详细错误信息