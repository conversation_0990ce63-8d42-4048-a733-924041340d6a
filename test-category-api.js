const fetch = require('node-fetch');

// 测试category API
async function testCategoryAPI() {
  try {
    // 1. 先测试登录
    console.log('=== 测试登录 ===');
    const loginResponse = await fetch('http://localhost:3008/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>', // 假设这是默认管理员邮箱
        password: 'admin123'
      })
    });

    if (!loginResponse.ok) {
      console.log('登录失败，尝试注册测试用户...');
      const registerResponse = await fetch('http://localhost:3008/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'test123',
          confirmPassword: 'test123',
          fullName: 'Test User'
        })
      });
      
      if (registerResponse.ok) {
        const registerData = await registerResponse.json();
        console.log('注册成功:', registerData);
        token = registerData.token;
      } else {
        console.error('注册失败:', await registerResponse.text());
        return;
      }
    } else {
      const loginData = await loginResponse.json();
      console.log('登录成功:', loginData);
      token = loginData.token;
    }

    // 2. 测试获取所有任务
    console.log('\n=== 测试获取所有任务 ===');
    const tasksResponse = await fetch('http://localhost:3008/api/tasks', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (tasksResponse.ok) {
      const tasksData = await tasksResponse.json();
      console.log('任务列表:', tasksData);
    } else {
      console.error('获取任务失败:', await tasksResponse.text());
    }

    // 3. 测试获取casino_review类型的任务
    console.log('\n=== 测试获取casino_review类型任务 ===');
    const categoryResponse = await fetch('http://localhost:3008/api/content/category/casino_review', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (categoryResponse.ok) {
      const categoryData = await categoryResponse.json();
      console.log('Casino Review任务:', categoryData);
    } else {
      console.error('获取casino_review任务失败:', await categoryResponse.text());
    }

    // 4. 测试获取所有content categories
    console.log('\n=== 测试获取所有content categories ===');
    const categoriesResponse = await fetch('http://localhost:3008/api/content/categories', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (categoriesResponse.ok) {
      const categoriesData = await categoriesResponse.json();
      console.log('所有分类:', categoriesData);
    } else {
      console.error('获取分类失败:', await categoriesResponse.text());
    }

  } catch (error) {
    console.error('测试失败:', error);
  }
}

testCategoryAPI();