// Image service for managing dynamic website images
import { API_CONFIG } from '../config/api';

// Default fallback images (used when no custom images are set)
const DEFAULT_IMAGES = {
  hero: {
    main: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=1200&h=600&fit=crop&crop=center',
    tech: 'https://images.unsplash.com/photo-1518186285589-2f7649de83e0?w=1200&h=600&fit=crop&crop=center',
    business: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=1200&h=600&fit=crop&crop=center',
    gaming: 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=1200&h=600&fit=crop&crop=center'
  },
  contentTypes: {
    'bonus_analysis': 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=800&h=400&fit=crop&crop=center',
    'brand_copy': 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=400&fit=crop&crop=center',
    'casino_review': 'https://images.unsplash.com/photo-1596838132731-3301c3fd4317?w=800&h=400&fit=crop&crop=center',
    'game_guide': 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=800&h=400&fit=crop&crop=center',
    'industry_news': 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=800&h=400&fit=crop&crop=center',
    'regulatory_update': 'https://images.unsplash.com/photo-1589391886645-d51941baf7fb?w=800&h=400&fit=crop&crop=center',
    'sports_betting': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=400&fit=crop&crop=center',
    'strategy_article': 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop&crop=center'
  }
};

// In-memory cache for website images
let websiteImagesCache = null;
let lastFetchTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

class ImageService {
  // Fetch website images from API
  async fetchWebsiteImages() {
    try {
      const now = Date.now();
      
      // Return cached data if still valid
      if (websiteImagesCache && (now - lastFetchTime) < CACHE_DURATION) {
        return websiteImagesCache;
      }

      const token = localStorage.getItem('token');
      if (!token) {
        // No token, use defaults
        return DEFAULT_IMAGES;
      }

      const response = await fetch(`${API_CONFIG.BASE_URL}/api/admin/website-images`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          // Merge with defaults to ensure all required images exist
          websiteImagesCache = this.mergeWithDefaults(result.data);
          lastFetchTime = now;
          return websiteImagesCache;
        }
      }
    } catch (error) {
      console.warn('Failed to fetch website images, using defaults:', error.message);
    }

    // Fallback to defaults
    return DEFAULT_IMAGES;
  }

  // Merge custom images with defaults
  mergeWithDefaults(customImages) {
    return {
      hero: {
        ...DEFAULT_IMAGES.hero,
        ...(customImages.hero || {})
      },
      contentTypes: {
        ...DEFAULT_IMAGES.contentTypes,
        ...(customImages.contentTypes || {})
      }
    };
  }

  // Get hero image for specific theme
  async getHeroImage(theme = 'main') {
    const images = await this.fetchWebsiteImages();
    return images.hero[theme] || images.hero.main || DEFAULT_IMAGES.hero.main;
  }

  // Get content type background image
  async getContentTypeImage(contentType, imageType = 'background') {
    const images = await this.fetchWebsiteImages();
    
    // For content types, we use the same image for both background and thumbnail
    const customImage = images.contentTypes[contentType];
    if (customImage) {
      return customImage;
    }

    // Fallback to default
    return DEFAULT_IMAGES.contentTypes[contentType] || DEFAULT_IMAGES.contentTypes['strategy_article'];
  }

  // Get background style for content type cards
  async getContentTypeCardBackground(contentType) {
    const image = await this.getContentTypeImage(contentType);
    return {
      backgroundImage: `linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.4)), url(${image})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat'
    };
  }

  // Clear cache (useful when images are updated)
  clearCache() {
    websiteImagesCache = null;
    lastFetchTime = 0;
  }

  // Preload images for better performance
  async preloadImages() {
    try {
      const images = await this.fetchWebsiteImages();
      
      // Preload hero images
      Object.values(images.hero).forEach(url => {
        if (url) {
          const img = new Image();
          img.src = url;
        }
      });

      // Preload content type images
      Object.values(images.contentTypes).forEach(url => {
        if (url) {
          const img = new Image();
          img.src = url;
        }
      });
    } catch (error) {
      console.warn('Failed to preload images:', error.message);
    }
  }
}

// Export singleton instance
export const imageService = new ImageService();

// Export for backward compatibility and direct access
export const getHeroImage = (theme = 'main') => imageService.getHeroImage(theme);
export const getContentTypeImage = (contentType) => imageService.getContentTypeImage(contentType);
export const getContentTypeCardBackground = (contentType) => imageService.getContentTypeCardBackground(contentType);

export default imageService;