import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { TagIcon, MagnifyingGlassIcon, HashtagIcon } from '@heroicons/react/24/outline';
import { buildApiUrl, API_CONFIG } from '../config/api';
import MainNavigation from '../components/layout/MainNavigation';

/**
 * Tags列表页面 - 显示所有可用标签
 * 类似于 https://www.ai-pomo.com/tags 的设计
 */
const TagsPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [tags, setTags] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredTags, setFilteredTags] = useState([]);
  const [sortBy, setSortBy] = useState('count'); // count, name
  const [error, setError] = useState(null);

  // 获取所有标签
  useEffect(() => {
    const fetchTags = async () => {
      try {
        setLoading(true);
        const response = await fetch(buildApiUrl(`${API_CONFIG.ENDPOINTS.TAGS.ALL}?limit=200&sort=${sortBy}`));
        const data = await response.json();
        
        if (data.success) {
          setTags(data.data);
          setFilteredTags(data.data);
        } else {
          setError(data.message || 'Failed to load tags');
        }
      } catch (error) {
        console.error('Error fetching tags:', error);
        setError('Failed to load tags');
      } finally {
        setLoading(false);
      }
    };

    fetchTags();
  }, [sortBy]);

  // 搜索过滤
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredTags(tags);
    } else {
      const filtered = tags.filter(tag =>
        tag.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredTags(filtered);
    }
  }, [searchTerm, tags]);

  const handleTagClick = (tag) => {
    navigate(`/pt/tags/${tag.urlName}`);
  };

  const getTagSize = (count) => {
    // 根据使用频率决定标签大小
    if (count >= 10) return 'text-2xl';
    if (count >= 5) return 'text-xl';
    if (count >= 3) return 'text-lg';
    return 'text-base';
  };

  const getTagColor = (count) => {
    // 根据使用频率决定颜色强度
    if (count >= 10) return 'bg-blue-600 hover:bg-blue-700 text-white';
    if (count >= 5) return 'bg-blue-500 hover:bg-blue-600 text-white';
    if (count >= 3) return 'bg-blue-400 hover:bg-blue-500 text-white';
    return 'bg-blue-300 hover:bg-blue-400 text-blue-900';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-indigo-900">
        <MainNavigation />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto"></div>
            <p className="text-slate-300 mt-4">{t('common.loading')}</p>
          </div>
        </div>
        
        {/* Footer */}
        <footer className="bg-slate-900/50 border-t border-blue-500/30 mt-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="text-center text-slate-400">
              <p>&copy; 2025 Writer 777. {t('footer.subtitle', 'Professional Gaming Content Platform')}.</p>
            </div>
          </div>
        </footer>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-indigo-900">
      <MainNavigation />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        
        {/* 页面标题 */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center space-x-3 px-6 py-3 rounded-full text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg mb-6">
            <HashtagIcon className="w-6 h-6" />
            <span>{t('tags.allTags')}</span>
          </div>
          <h1 className="text-4xl lg:text-6xl font-bold text-white mb-4 leading-tight">
            {t('tags.title')}
          </h1>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto">
            {t('tags.exploreDescription', 'Explore articles by topic. Click on any tag to discover related content.')}
          </p>
        </div>

        {/* 搜索和筛选 */}
        <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            
            {/* 搜索框 */}
            <div className="flex-1 relative">
              <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
              <input
                type="text"
                placeholder={t('tags.searchPlaceholder', 'Search tags...')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* 排序选择 */}
            <div className="flex items-center space-x-3">
              <span className="text-slate-300 text-sm">{t('tags.sortBy', 'Sort by:')}:</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="count">{t('tags.popularity', 'Popularity')}</option>
                <option value="name">{t('tags.name', 'Name')}</option>
              </select>
            </div>
          </div>

          {/* 统计信息 */}
          <div className="mt-4 text-slate-300 text-sm">
            {t('tags.showingResults', { 
              showing: filteredTags.length, 
              total: tags.length 
            }, `Showing ${filteredTags.length} of ${tags.length} tags`)}
          </div>
        </div>

        {/* 错误状态 */}
        {error && (
          <div className="bg-red-500/20 border border-red-500/30 rounded-xl p-6 text-center mb-8">
            <p className="text-red-300">{error}</p>
          </div>
        )}

        {/* 标签云 */}
        {filteredTags.length > 0 ? (
          <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-8">
            <div className="flex flex-wrap gap-3 justify-center">
              {filteredTags.map((tag, index) => (
                <button
                  key={index}
                  onClick={() => handleTagClick(tag)}
                  className={`
                    px-4 py-2 rounded-full transition-all duration-300 hover:scale-110 
                    ${getTagSize(tag.count)} ${getTagColor(tag.count)}
                    font-semibold shadow-lg hover:shadow-xl
                  `}
                  title={`${tag.count} articles tagged with "${tag.name}"`}
                >
                  #{tag.name}
                  <span className="ml-2 text-xs opacity-75">({tag.count})</span>
                </button>
              ))}
            </div>
          </div>
        ) : (
          !loading && (
            <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-8 text-center">
              <TagIcon className="w-16 h-16 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-300 text-lg">
                {searchTerm ? 
                  t('tags.noTagsFoundSearch', 'No tags found matching your search.') : 
                  t('tags.noTagsAvailable', 'No tags available yet.')
                }
              </p>
            </div>
          )
        )}

        {/* 热门标签部分 */}
        {tags.length > 0 && !searchTerm && (
          <div className="mt-12">
            <h2 className="text-2xl font-bold text-white mb-6 text-center">
              {t('tags.popularTags')}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {tags.slice(0, 6).map((tag, index) => (
                <div
                  key={index}
                  onClick={() => handleTagClick(tag)}
                  className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-6 hover:bg-white/20 transition-all duration-300 cursor-pointer group"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold text-white group-hover:text-blue-300 transition-colors">
                        #{tag.name}
                      </h3>
                      <p className="text-slate-400 text-sm">
                        {tag.count} {tag.count === 1 ? 
                          t('common.article', 'article') : 
                          t('common.articles', 'articles')
                        }
                      </p>
                    </div>
                    <div className="bg-blue-500/20 rounded-full p-3">
                      <TagIcon className="w-6 h-6 text-blue-400" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

      </div>
      
      {/* Footer */}
      <footer className="bg-slate-900/50 border-t border-blue-500/30 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-slate-400">
            <p>&copy; 2025 Writer 777. {t('footer.subtitle', 'Professional Gaming Content Platform')}.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default TagsPage;