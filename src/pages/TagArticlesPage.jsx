import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  TagIcon, 
  ArrowLeftIcon, 
  CalendarIcon,
  ClockIcon,
  HashtagIcon 
} from '@heroicons/react/24/outline';
import TagsDisplay from '../components/common/TagsDisplay';
import { buildApiUrl, API_CONFIG } from '../config/api';
import MainNavigation from '../components/layout/MainNavigation';

/**
 * 单个标签的文章列表页面
 * URL: /tags/[tagName]
 */
const TagArticlesPage = () => {
  const { tagName } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [articles, setArticles] = useState([]);
  const [tagInfo, setTagInfo] = useState(null);
  const [relatedTags, setRelatedTags] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({ page: 1, totalPages: 1 });

  // 获取标签下的文章
  useEffect(() => {
    const fetchTagArticles = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // 获取文章列表
        const articlesResponse = await fetch(buildApiUrl(`${API_CONFIG.ENDPOINTS.TAGS.ARTICLES(tagName)}?page=${pagination.page}&limit=12`));
        const articlesData = await articlesResponse.json();
        
        if (articlesData.success) {
          setArticles(articlesData.data);
          setTagInfo(articlesData.tag);
          setPagination(articlesData.pagination);
        } else {
          setError(articlesData.message || 'Failed to load articles');
        }

        // 获取相关标签
        try {
          const relatedResponse = await fetch(buildApiUrl(`${API_CONFIG.ENDPOINTS.TAGS.RELATED(tagName)}?limit=8`));
          const relatedData = await relatedResponse.json();
          if (relatedData.success) {
            setRelatedTags(relatedData.data);
          }
        } catch (relatedError) {
          // Silently ignore related tags loading errors
        }

      } catch (error) {
        console.error('Error fetching tag articles:', error);
        setError('Failed to load articles');
      } finally {
        setLoading(false);
      }
    };

    if (tagName) {
      fetchTagArticles();
    }
  }, [tagName, pagination.page]);

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getContentTypeIcon = (contentType) => {
    const icons = {
      'casino_review': '🎰',
      'game_guide': '🎮',
      'sports_betting': '⚽',
      'bonus_analysis': '🎁',
      'industry_news': '📰',
      'strategy_article': '🎯',
      'brand_copy': '📝'
    };
    return icons[contentType] || '📄';
  };

  const getContentTypeLabel = (contentType) => {
    return t(`contentTypes.${contentType}`, contentType?.replace('_', ' ') || 'Article');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-indigo-900">
        <MainNavigation />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto"></div>
            <p className="text-slate-300 mt-4">{t('common.loading')}</p>
          </div>
        </div>
        
        {/* Footer */}
        <footer className="bg-slate-900/50 border-t border-blue-500/30 mt-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="text-center text-slate-400">
              <p>&copy; 2025 Writer 777. {t('footer.subtitle', 'Professional Gaming Content Platform')}.</p>
            </div>
          </div>
        </footer>
      </div>
    );
  }

  if (error || !tagInfo) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-indigo-900">
        <MainNavigation />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <TagIcon className="w-16 h-16 text-slate-400 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-white mb-4">{t('tags.tagNotFound', 'Tag Not Found')}</h1>
            <p className="text-slate-300 mb-8">{error || t('tags.tagNotExist', 'The requested tag does not exist.')}</p>
            <Link
              to="/pt/tags"
              className="inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-xl transition-colors"
            >
              <ArrowLeftIcon className="w-5 h-5" />
              <span>{t('tags.viewAllTags')}</span>
            </Link>
          </div>
        </div>
        
        {/* Footer */}
        <footer className="bg-slate-900/50 border-t border-blue-500/30 mt-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="text-center text-slate-400">
              <p>&copy; 2025 Writer 777. {t('footer.subtitle', 'Professional Gaming Content Platform')}.</p>
            </div>
          </div>
        </footer>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-indigo-900">
      <MainNavigation />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        
        {/* 面包屑导航 */}
        <div className="mb-8">
          <nav className="flex items-center space-x-2 text-sm">
            <Link to="/" className="text-slate-400 hover:text-white transition-colors">
              Home
            </Link>
            <span className="text-slate-600">/</span>
            <Link to="/pt/tags" className="text-slate-400 hover:text-white transition-colors">
              Tags
            </Link>
            <span className="text-slate-600">/</span>
            <span className="text-white">{tagInfo.displayName}</span>
          </nav>
        </div>

        {/* 标签标题部分 */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center space-x-3 px-6 py-3 rounded-full text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg mb-6">
            <HashtagIcon className="w-6 h-6" />
            <span>{tagInfo.displayName}</span>
          </div>
          <h1 className="text-4xl lg:text-6xl font-bold text-white mb-4 leading-tight">
            {t('tags.articlesWithTag', { tag: tagInfo.displayName })}
          </h1>
          <p className="text-xl text-slate-300">
            {pagination.total} {pagination.total === 1 ? 
              t('common.article', 'article') : 
              t('common.articles', 'articles')
            } {t('tags.taggedWith', 'tagged with')} "{tagInfo.displayName}"
          </p>
          
          {/* 返回按钮 */}
          <div className="mt-6">
            <Link
              to="/pt/tags"
              className="inline-flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 border border-white/20 text-white rounded-xl transition-all duration-200"
            >
              <ArrowLeftIcon className="w-4 h-4" />
              <span>{t('tags.viewAllTags')}</span>
            </Link>
          </div>
        </div>

        {/* 文章网格 */}
        {articles.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {articles.map((article, index) => (
              <Link
                key={index}
                to={`/article/${article.slug}`}
                className="group bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl overflow-hidden hover:bg-white/20 transition-all duration-300 hover:scale-105"
              >
                {/* 文章图片 */}
                {article.featured_image && (
                  <div className="aspect-video bg-slate-800 overflow-hidden">
                    <img
                      src={article.featured_image}
                      alt={article.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                      loading="lazy"
                    />
                  </div>
                )}
                
                {/* 文章内容 */}
                <div className="p-6">
                  {/* 内容类型标签 */}
                  <div className="mb-3">
                    <span className="inline-flex items-center space-x-2 px-3 py-1 bg-blue-500/20 text-blue-300 text-sm rounded-full">
                      <span>{getContentTypeIcon(article.content_type)}</span>
                      <span>{getContentTypeLabel(article.content_type)}</span>
                    </span>
                  </div>

                  {/* 标题 */}
                  <h3 className="text-xl font-bold text-white mb-3 group-hover:text-blue-300 transition-colors line-clamp-2">
                    {article.title}
                  </h3>

                  {/* 摘要 */}
                  {article.excerpt && (
                    <p className="text-slate-300 mb-4 line-clamp-3">
                      {article.excerpt}
                    </p>
                  )}

                  {/* 元数据 */}
                  <div className="flex items-center justify-between text-sm text-slate-400 mb-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <CalendarIcon className="w-4 h-4" />
                        <span>{formatDate(article.created_at)}</span>
                      </div>
                      {article.reading_time && (
                        <div className="flex items-center space-x-1">
                          <ClockIcon className="w-4 h-4" />
                          <span>{article.reading_time} min read</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 其他标签 */}
                  {article.tags && article.tags.length > 1 && (
                    <TagsDisplay 
                      tags={article.tags.filter(tag => 
                        tag.toLowerCase() !== tagInfo.name.toLowerCase()
                      )}
                      variant="minimal"
                      showIcon={false}
                    />
                  )}
                </div>
              </Link>
            ))}
          </div>
        ) : (
          <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-8 text-center mb-12">
            <TagIcon className="w-16 h-16 text-slate-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-white mb-4">
              {t('tags.noArticlesFound')}
            </h2>
            <p className="text-slate-300">
              No articles have been tagged with "{tagInfo.displayName}" yet.
            </p>
          </div>
        )}

        {/* 分页 */}
        {pagination.totalPages > 1 && (
          <div className="flex justify-center items-center space-x-4 mb-12">
            <button
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page === 1}
              className="px-4 py-2 bg-white/10 border border-white/20 text-white rounded-xl hover:bg-white/20 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {t('pagination.previous')}
            </button>
            
            <span className="text-slate-300">
              {t('pagination.page', { current: pagination.page, total: pagination.totalPages })}
            </span>
            
            <button
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page === pagination.totalPages}
              className="px-4 py-2 bg-white/10 border border-white/20 text-white rounded-xl hover:bg-white/20 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {t('pagination.next')}
            </button>
          </div>
        )}

        {/* 相关标签 */}
        {relatedTags.length > 0 && (
          <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-8">
            <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
              <TagIcon className="w-6 h-6 mr-3 text-blue-400" />
              {t('tags.relatedTags')}
            </h2>
            <div className="flex flex-wrap gap-3">
              {relatedTags.map((tag, index) => (
                <button
                  key={index}
                  onClick={() => navigate(`/pt/tags/${tag.urlName}`)}
                  className="px-4 py-2 bg-blue-500/20 text-blue-300 text-sm rounded-full border border-blue-500/30 hover:border-blue-500/50 transition-all duration-200 hover:bg-blue-500/30 hover:scale-105"
                >
                  #{tag.name}
                  <span className="ml-2 text-xs opacity-75">({tag.count})</span>
                </button>
              ))}
            </div>
          </div>
        )}

      </div>
      
      {/* Footer */}
      <footer className="bg-slate-900/50 border-t border-blue-500/30 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-slate-400">
            <p>&copy; 2025 Writer 777. {t('footer.subtitle', 'Professional Gaming Content Platform')}.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default TagArticlesPage;