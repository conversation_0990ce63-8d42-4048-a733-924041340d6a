import { useTranslation } from 'react-i18next';

/**
 * Hook for handling multilingual content
 * Maps current language to appropriate content language codes
 */
export const useLanguageContent = () => {
  const { i18n } = useTranslation();
  
  // Map frontend languages to content language codes
  const getContentLanguage = () => {
    const langMap = {
      'en': 'en', // English
      'pt': 'pt', // Portuguese 
      'es': 'es'  // Spanish
    };
    
    return langMap[i18n.language] || 'en'; // Default to English
  };

  // Get localized content type labels
  const getLocalizedContentType = (contentType) => {
    const { t } = useTranslation();
    return t(`contentTypes.${contentType}`, contentType);
  };

  // Format date according to current language
  const formatLocalizedDate = (dateString) => {
    if (!dateString) return '';
    
    const localeMap = {
      'en': 'en-US',
      'pt': 'pt-BR', 
      'es': 'es-ES'
    };
    
    const locale = localeMap[i18n.language] || 'en-US';
    
    try {
      return new Date(dateString).toLocaleDateString(locale, {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  // Get reading time with localized text
  const getLocalizedReadingTime = (content, wordsPerMinute = 200) => {
    const { t } = useTranslation();
    
    if (!content) return t('article.readingTime', { time: 0 });
    
    // Remove HTML tags and get plain text
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;
    const text = tempDiv.textContent || tempDiv.innerText || '';
    
    // Count words
    const wordCount = text.trim().split(/\s+/).filter(word => word.length > 0).length;
    
    // Calculate reading time
    const readTime = Math.ceil(wordCount / wordsPerMinute);
    const finalTime = Math.max(1, readTime); // Minimum 1 minute
    
    return t('article.readingTime', { time: finalTime });
  };

  return {
    currentLanguage: i18n.language,
    contentLanguage: getContentLanguage(),
    getLocalizedContentType,
    formatLocalizedDate,
    getLocalizedReadingTime,
    changeLanguage: i18n.changeLanguage
  };
};