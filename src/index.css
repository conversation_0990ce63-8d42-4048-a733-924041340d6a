@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&family=Cinzel:wght@400;500;600;700;800;900&display=swap');
@import './styles/glassmorphism.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  /* Disable double-tap zoom on mobile while preserving other touch functionality */
  touch-action: manipulation;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #3B4A6B;
  min-height: 100vh;
}

/* Casino-themed background patterns */
.casino-bg {
  background-image: 
    radial-gradient(circle at 25% 25%, #f59e0b15 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, #dc262615 0%, transparent 50%),
    linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
}

/* Improved text contrast classes */
.text-high-contrast {
  color: #ffffff;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.text-gold-bright {
  color: #fbbf24;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.text-gold-light {
  color: #fde68a;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
}

.casino-card {
  background: linear-gradient(145deg, #1e293b, #334155);
  border: 1px solid #f59e0b66;
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.4),
    0 0 25px rgba(245, 158, 11, 0.15),
    inset 0 1px 0 rgba(245, 158, 11, 0.15);
}

.casino-card:hover {
  box-shadow: 
    0 10px 15px -3px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(245, 158, 11, 0.2),
    inset 0 1px 0 rgba(245, 158, 11, 0.2);
  transform: translateY(-2px);
}

.gold-gradient {
  background: linear-gradient(135deg, #f59e0b, #d97706, #b45309);
}

.red-gradient {
  background: linear-gradient(135deg, #ef4444, #dc2626, #b91c1c);
}

.green-gradient {
  background: linear-gradient(135deg, #22c55e, #16a34a, #15803d);
}

.casino-button {
  background: linear-gradient(145deg, #f59e0b, #d97706);
  border: 2px solid #b45309;
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(245, 158, 11, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.casino-button:hover {
  background: linear-gradient(145deg, #d97706, #b45309);
  box-shadow: 
    0 6px 8px -1px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(245, 158, 11, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.neon-glow {
  text-shadow:
    0 0 3px #f59e0b,
    0 0 6px #f59e0b,
    1px 1px 2px rgba(0, 0, 0, 0.6);
}

.chip-shadow {
  box-shadow: 
    0 8px 16px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(245, 158, 11, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

/* Enhanced Editor Styles */
.article-editor {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  line-height: 1.6;
}

.article-editor:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Enhanced Preview Styles */
.article-preview {
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.7;
}

.article-preview h1 {
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.article-preview h2 {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.25rem;
}

.article-preview blockquote {
  border-left: 4px solid #3b82f6;
  background-color: #f8fafc;
  padding: 1rem;
  margin: 1rem 0;
  font-style: italic;
}

.article-preview code {
  background-color: #f1f5f9;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.article-preview pre {
  background-color: #1e293b;
  color: #f1f5f9;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
}

/* Enhanced Article Content Styles */
.article-content {
  font-family: 'Inter', system-ui, sans-serif;
}

.article-content h1,
.article-content h2,
.article-content h3,
.article-content h4,
.article-content h5,
.article-content h6 {
  scroll-margin-top: 6rem;
  position: relative;
}

.article-content h1 {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 3rem 0 1.5rem 0;
  border-bottom: 3px solid rgba(251, 191, 36, 0.3);
  padding-bottom: 1rem;
}

.article-content h2 {
  font-size: 2rem;
  font-weight: 700;
  margin: 2.5rem 0 1.25rem 0;
  border-bottom: 2px solid rgba(251, 191, 36, 0.2);
  padding-bottom: 0.75rem;
}

.article-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 2rem 0 1rem 0;
}

.article-content h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1.5rem 0 0.75rem 0;
}

.article-content p {
  margin: 1.5rem 0;
  line-height: 1.8;
}

.article-content blockquote {
  border-left: 4px solid rgba(251, 191, 36, 0.6);
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(251, 191, 36, 0.05));
  padding: 1.5rem;
  margin: 2rem 0;
  border-radius: 0.5rem;
  font-style: italic;
  position: relative;
}

.article-content blockquote::before {
  content: '"';
  font-size: 4rem;
  color: rgba(251, 191, 36, 0.3);
  position: absolute;
  top: -0.5rem;
  left: 1rem;
  font-family: serif;
}

.article-content code {
  background: rgba(15, 23, 42, 0.8);
  color: rgb(251, 191, 36);
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.9em;
  border: 1px solid rgba(251, 191, 36, 0.2);
}

.article-content pre {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.95));
  border: 1px solid rgba(251, 191, 36, 0.2);
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin: 2rem 0;
  overflow-x: auto;
  position: relative;
}

.article-content pre code {
  background: none;
  border: none;
  padding: 0;
  color: rgba(251, 191, 36, 0.9);
}

.article-content ul,
.article-content ol {
  margin: 1.5rem 0;
  padding-left: 2rem;
}

.article-content li {
  margin: 0.5rem 0;
  line-height: 1.7;
}

.article-content ul li::marker {
  color: rgba(251, 191, 36, 0.7);
}

.article-content ol li::marker {
  color: rgba(251, 191, 36, 0.7);
  font-weight: 600;
}

.article-content table {
  width: 100%;
  margin: 2rem 0;
  border-collapse: collapse;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.article-content th,
.article-content td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid rgba(251, 191, 36, 0.2);
}

.article-content th {
  background: rgba(251, 191, 36, 0.1);
  font-weight: 600;
}

.article-content a {
  color: rgb(251, 191, 36);
  text-decoration: underline;
  text-decoration-color: rgba(251, 191, 36, 0.4);
  text-underline-offset: 0.25rem;
  transition: all 0.2s ease;
}

.article-content a:hover {
  text-decoration-color: rgb(251, 191, 36);
  color: rgb(254, 215, 170);
}

.article-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.75rem;
  margin: 2rem 0;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.article-content hr {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(251, 191, 36, 0.5), transparent);
  margin: 3rem 0;
}

/* Enhanced editor and preview sections */
.editor-section {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(229, 231, 235, 0.8);
  backdrop-filter: blur(10px);
}

.preview-section {
  background: rgba(249, 250, 251, 0.95);
  border: 1px solid rgba(229, 231, 235, 0.8);
  backdrop-filter: blur(10px);
}

/* Fullscreen mode */
.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background: rgba(255, 255, 255, 0.98);
}

/* Smooth transitions */
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.5);
}

::-webkit-scrollbar-thumb {
  background: rgba(245, 158, 11, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(245, 158, 11, 0.7);
}

/* Print Styles */
@media print {
  .casino-bg {
    background: white !important;
  }

  .casino-card {
    background: white !important;
    border: 1px solid #ccc !important;
    box-shadow: none !important;
  }

  .text-casino-gold-400,
  .text-casino-gold-200,
  .text-casino-gold-100,
  .text-casino-gold-300 {
    color: #000 !important;
  }

  .article-content {
    color: #000 !important;
  }

  .article-content h1,
  .article-content h2,
  .article-content h3,
  .article-content h4,
  .article-content h5,
  .article-content h6 {
    color: #000 !important;
    border-color: #ccc !important;
  }

  .article-content code {
    background: #f5f5f5 !important;
    color: #000 !important;
    border: 1px solid #ccc !important;
  }

  .article-content pre {
    background: #f5f5f5 !important;
    border: 1px solid #ccc !important;
  }

  .article-content pre code {
    color: #000 !important;
  }

  .article-content blockquote {
    background: #f9f9f9 !important;
    border-left: 4px solid #ccc !important;
  }

  .article-content a {
    color: #0066cc !important;
  }

  /* Hide interactive elements when printing */
  button,
  .sticky,
  .fixed,
  nav,
  aside {
    display: none !important;
  }
}

/* Mobile Responsive Improvements */
@media (max-width: 768px) {
  .article-content h1 {
    font-size: 2rem;
    margin: 2rem 0 1rem 0;
  }

  .article-content h2 {
    font-size: 1.5rem;
    margin: 1.5rem 0 0.75rem 0;
  }

  .article-content h3 {
    font-size: 1.25rem;
    margin: 1.25rem 0 0.5rem 0;
  }

  .article-content p {
    margin: 1rem 0;
    font-size: 1rem;
  }

  .article-content blockquote {
    margin: 1.5rem 0;
    padding: 1rem;
  }

  .article-content pre {
    margin: 1.5rem 0;
    padding: 1rem;
    font-size: 0.875rem;
  }

  .article-content ul,
  .article-content ol {
    padding-left: 1.5rem;
  }
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Stabilize article card images */
.article-image {
  backface-visibility: hidden;
  transform: translateZ(0);
  image-rendering: auto;
  image-rendering: -webkit-optimize-contrast;
  display: block;
  position: relative;
}

/* Prevent any transform animations on images */
.article-image,
.article-image:hover,
.group:hover .article-image {
  transform: translateZ(0) !important;
  transition: none !important;
}

/* Disable hover transforms on article cards to prevent jitter */
.glass-interactive.group:hover {
  transform: none !important;
}

/* Layout-specific styles */
.casino-review-content .article-content blockquote {
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.1), rgba(219, 39, 119, 0.1));
  border-left-color: rgba(147, 51, 234, 0.6);
}

.game-guide-content .article-content h2,
.game-guide-content .article-content h3 {
  position: relative;
  padding-left: 2rem;
}

.game-guide-content .article-content h2::before {
  content: counter(step-counter);
  counter-increment: step-counter;
  position: absolute;
  left: 0;
  top: 0;
  width: 1.5rem;
  height: 1.5rem;
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: bold;
}

.game-guide-content .article-content {
  counter-reset: step-counter;
}

.strategy-article-content .article-content table {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1));
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.strategy-article-content .article-content th {
  background: rgba(34, 197, 94, 0.2);
}

.brand-copy-content .article-content {
  text-align: center;
}

.brand-copy-content .article-content h1,
.brand-copy-content .article-content h2,
.brand-copy-content .article-content h3 {
  text-align: center;
  background: linear-gradient(135deg, #f59e0b, #f97316);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-copy-content .article-content p {
  font-size: 1.25rem;
  line-height: 1.9;
  margin: 2rem auto;
  max-width: 42rem;
}

.industry-news-content .article-content h2::before {
  content: "📰";
  margin-right: 0.5rem;
}

.industry-news-content .article-content blockquote {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(236, 72, 153, 0.1));
  border-left-color: rgba(239, 68, 68, 0.6);
  position: relative;
}

.industry-news-content .article-content blockquote::after {
  content: "Breaking";
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: linear-gradient(135deg, #ef4444, #ec4899);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: bold;
}

.sports-betting-content .article-content h2::before {
  content: "🏆";
  margin-right: 0.5rem;
}

.sports-betting-content .article-content table {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  border: 1px solid rgba(99, 102, 241, 0.3);
}

.sports-betting-content .article-content th {
  background: rgba(99, 102, 241, 0.2);
}

/* Content type specific animations */
.casino-review-content {
  animation: fadeInUp 0.6s ease-out;
}

.game-guide-content {
  animation: slideInRight 0.6s ease-out;
}

.strategy-article-content {
  animation: fadeInScale 0.6s ease-out;
}

.brand-copy-content {
  animation: fadeInDown 0.6s ease-out;
}

.industry-news-content {
  animation: slideInLeft 0.6s ease-out;
}

.sports-betting-content {
  animation: bounceIn 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
