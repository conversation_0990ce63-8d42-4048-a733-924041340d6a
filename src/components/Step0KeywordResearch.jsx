import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { apiCall, API_CONFIG } from '../config/api';
import KeywordResearchPanel from './KeywordResearchPanel';
import SelectionManager from './SelectionManager';

const Step0KeywordResearch = ({ data, updateData, onNext, isGeneratingTopics, setIsGeneratingTopics }) => {
  const { t } = useTranslation();
  const [keywordInput, setKeywordInput] = useState('');
  const [isResearching, setIsResearching] = useState(false);
  const [currentResearchKeyword, setCurrentResearchKeyword] = useState('');

  // Function to get recommended language and country based on jurisdiction from Step 1
  const getRecommendedSettings = (jurisdiction) => {
    const jurisdictionMap = {
      // International
      'international': { language: 'en', country: 'us' },

      // Europe
      'united_kingdom': { language: 'en', country: 'gb' },
      'france': { language: 'fr', country: 'fr' },
      'spain': { language: 'es', country: 'es' },
      'italy': { language: 'it', country: 'it' },
      'germany': { language: 'de', country: 'de' },
      'portugal': { language: 'pt', country: 'pt' },
      'russia': { language: 'en', country: 'ru' },
      'netherlands': { language: 'en', country: 'nl' },
      'poland': { language: 'en', country: 'pl' },
      'sweden': { language: 'en', country: 'se' },
      'norway': { language: 'en', country: 'no' },
      'finland': { language: 'en', country: 'fi' },

      // Americas
      'united_states': { language: 'en', country: 'us' },
      'canada': { language: 'en', country: 'ca' },
      'brazil': { language: 'pt', country: 'br' },
      'mexico': { language: 'es', country: 'mx' },
      'argentina': { language: 'es', country: 'ar' },
      'chile': { language: 'es', country: 'cl' },
      'colombia': { language: 'es', country: 'co' },

      // Asia
      'japan': { language: 'ja', country: 'jp' },
      'south_korea': { language: 'en', country: 'kr' },
      'china': { language: 'zh', country: 'cn' },
      'india': { language: 'en', country: 'in' },
      'singapore': { language: 'en', country: 'sg' },
      'thailand': { language: 'en', country: 'th' },
      'vietnam': { language: 'en', country: 'vn' },
      'philippines': { language: 'en', country: 'ph' },
      'indonesia': { language: 'en', country: 'id' },
      'malaysia': { language: 'en', country: 'my' },

      // Oceania
      'australia': { language: 'en', country: 'au' },
      'new_zealand': { language: 'en', country: 'nz' },

      // Africa
      'south_africa': { language: 'en', country: 'za' },
      'egypt': { language: 'en', country: 'eg' },
      'nigeria': { language: 'en', country: 'ng' },
      'kenya': { language: 'en', country: 'ke' },
      'morocco': { language: 'en', country: 'ma' }
    };

    return jurisdictionMap[jurisdiction] || { language: 'en', country: 'us' };
  };

  // Auto-set language and country based on jurisdiction from Step 1
  const recommendedSettings = getRecommendedSettings(data?.jurisdiction || 'international');
  const [selectedLanguage, setSelectedLanguage] = useState(data?.selectedLanguage || recommendedSettings.language);
  const [selectedCountry, setSelectedCountry] = useState(data?.selectedCountry || recommendedSettings.country);
  const [userHasManuallySelected, setUserHasManuallySelected] = useState(false);

  // Update language and country when jurisdiction changes, but only if user hasn't manually selected
  useEffect(() => {
    if (!userHasManuallySelected && !data?.selectedLanguage && !data?.selectedCountry) {
      const newSettings = getRecommendedSettings(data?.jurisdiction || 'international');
      setSelectedLanguage(newSettings.language);
      setSelectedCountry(newSettings.country);
    } else if (data?.selectedLanguage && data?.selectedCountry) {
      // Restore saved selections
      setSelectedLanguage(data.selectedLanguage);
      setSelectedCountry(data.selectedCountry);
      setUserHasManuallySelected(true);
    }
  }, [data?.jurisdiction, data?.selectedLanguage, data?.selectedCountry]);

  // Helper function to get jurisdiction display name
  const getJurisdictionDisplayName = (jurisdiction) => {
    const jurisdictionNames = {
      'international': 'International',
      'united_kingdom': 'United Kingdom',
      'united_states': 'United States',
      'south_korea': 'South Korea',
      'new_zealand': 'New Zealand',
      'south_africa': 'South Africa'
    };

    // Convert underscore to space and capitalize each word for others
    const displayName = jurisdictionNames[jurisdiction] ||
      jurisdiction.split('_').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1)
      ).join(' ');

    return displayName;
  };

  // Use persistent data from main app state
  const keywordResearchData = data.keywordResearchData;
  const hasResearched = !!keywordResearchData;
  
  // Local state to track selections for immediate UI updates
  const [localSelectedKeywords, setLocalSelectedKeywords] = useState(data.selectedKeywords || []);
  const [localSecondaryKeywords, setLocalSecondaryKeywords] = useState(data.secondaryKeywords || []);
  
  // Sync local state with data props when they change
  useEffect(() => {
    setLocalSelectedKeywords(data.selectedKeywords || []);
    setLocalSecondaryKeywords(data.secondaryKeywords || []);
  }, [data.selectedKeywords, data.secondaryKeywords]);
  
  // Use local state for UI display
  const selectedKeywords = localSelectedKeywords;
  const secondaryKeywords = localSecondaryKeywords;

  const addKeyword = () => {
    if (keywordInput.trim() && !data.keywords.includes(keywordInput.trim())) {
      updateData({
        keywords: [...data.keywords, keywordInput.trim()]
      });
      setKeywordInput('');
    }
  };

  const performKeywordResearch = async (keyword) => {
    if (!keyword || keyword.trim().length === 0) {
      alert('Please enter a keyword for research.');
      return;
    }

    setIsResearching(true);
    setCurrentResearchKeyword(keyword.trim());
    try {
      const researchData = await apiCall(API_CONFIG.ENDPOINTS.IDEATION_KEYWORD_RESEARCH, {
        method: 'POST',
        body: JSON.stringify({ 
          keyword: keyword.trim(),
          language: selectedLanguage,
          country: selectedCountry
        }),
      });

      // Clear local selections and save research data to persistent state
      setLocalSelectedKeywords([]);
      setLocalSecondaryKeywords([]);
      updateData({
        keywordResearchData: researchData,
        selectedKeywords: [], // Clear selections on new research
        secondaryKeywords: [], // Clear secondary keywords too
        selectedCountry: selectedCountry, // Save country for compliance settings
        selectedLanguage: selectedLanguage // Save language for reference
      });
    } catch (error) {
      console.error('Error performing keyword research:', error);
      alert('Failed to perform keyword research. Please try again.');
    } finally {
      setIsResearching(false);
    }
  };

  const handleKeywordFromResearch = (keyword) => {
    // This function might not be needed anymore with direct selection handling
    // If it's for adding to a general list, it can stay.
    if (keyword.trim() && !data.keywords.includes(keyword.trim())) {
      updateData({
        keywords: [...data.keywords, keyword.trim()]
      });
    }
  };

  const handleTopicFromResearch = (topic) => {
    updateData({ selectedTopic: topic });
  };

  // Simplified selection functions
  const toggleKeywordSelection = (keyword, isSecondary = false) => {
    let newSelectedKeywords = [...localSelectedKeywords];
    let newSecondaryKeywords = [...localSecondaryKeywords];

    if (isSecondary) {
      if (newSecondaryKeywords.includes(keyword)) {
        newSecondaryKeywords = newSecondaryKeywords.filter(k => k !== keyword);
      } else {
        newSecondaryKeywords = [...newSecondaryKeywords, keyword];
      }
    } else {
      if (newSelectedKeywords.includes(keyword)) {
        newSelectedKeywords = newSelectedKeywords.filter(k => k !== keyword);
      } else {
        newSelectedKeywords = [...newSelectedKeywords, keyword];
      }
    }


    // 立即更新本地状态，确保UI反应灵敏
    setLocalSelectedKeywords(newSelectedKeywords);
    setLocalSecondaryKeywords(newSecondaryKeywords);

    // 同时更新父组件状态
    updateData({
      selectedKeywords: newSelectedKeywords,
      secondaryKeywords: newSecondaryKeywords
    });
  };

  const clearAllSelectedKeywords = () => {
    setLocalSelectedKeywords([]);
    setLocalSecondaryKeywords([]);
    updateData({ selectedKeywords: [], secondaryKeywords: [] });
  };

  const addCustomKeywordToSelection = (customKeyword) => {
    if (customKeyword.trim() && !selectedKeywords.includes(customKeyword.trim())) {
      const newSelectedKeywords = [...selectedKeywords, customKeyword.trim()];
      setLocalSelectedKeywords(newSelectedKeywords);
      updateData({
        selectedKeywords: newSelectedKeywords
      });
    }
  };

  // Renamed and updated function to use new selectedKeywords state
  const generateTopicsFromSelectedKeywords = async () => {
    const keywords = selectedKeywords;
    const uniqueKeywords = [...new Set([...data.keywords, ...keywords])];

    if (keywords.length === 0) {
      alert('Please select some keywords first.');
      return;
    }

    // Set loading state and navigate to next step immediately
    setIsGeneratingTopics(true);

    // Clear any existing topic selections to fix the persistence issue
    updateData({
      selectedTopics: [], // Clear topic selections
      selectedTopic: '', // Clear legacy topic selection
      keywords: uniqueKeywords, // Set keywords immediately
    });

    // Navigate to next step immediately so user sees loading in Step 2
    onNext();

    try {
      // Generate article topics using AI - prioritize saved data over state
      const targetLanguage = data.selectedLanguage || selectedLanguage || 'en';
      const targetCountry = data.selectedCountry || selectedCountry || 'us';
      
      // Map language codes to readable names for the prompt
      const languageNames = {
        'en': 'English',
        'zh': 'Chinese (Simplified)',
        'pt': 'Portuguese',
        'es': 'Spanish', 
        'de': 'German',
        'fr': 'French',
        'it': 'Italian',
        'ja': 'Japanese'
      };
      
      const countryNames = {
        'us': 'United States',
        'br': 'Brazil',
        'gb': 'United Kingdom',
        'ca': 'Canada',
        'au': 'Australia',
        'de': 'Germany',
        'fr': 'France',
        'es': 'Spain',
        'it': 'Italy',
        'jp': 'Japan',
        'cn': 'China'
      };

      
      const targetLanguageName = languageNames[targetLanguage] || targetLanguage;
      const targetCountryName = countryNames[targetCountry] || targetCountry;
      
      const prompt = `You are an expert AI Content Strategist and Ideation Assistant. Your mission is to transform a list of user-selected keywords into a rich array of compelling, relevant, and actionable article topics, exploring all sensible possibilities and organizing them into strategic clusters.

CRITICAL LANGUAGE REQUIREMENT: The target audience speaks ${targetLanguageName} and is located in ${targetCountryName}. You MUST generate ALL topic suggestions in ${targetLanguageName}.

IMPORTANT: All topic suggestions should be relevant to ${targetCountryName} market and cultural context. Consider local regulations, cultural preferences, and market specifics for ${targetCountryName}.

A user has completed a detailed keyword research phase, analyzing Google Autocomplete, "People Also Ask" (PAA), Related Keywords, and Key Terms extracted from top search results. They have carefully selected the following keywords as highly relevant and representative of their content goals and audience's interests.

Target Market: ${targetCountryName} (Language: ${targetLanguageName})
User-Selected Keywords:
[
${keywords.map(keyword => `  "${keyword}"`).join(',\n')}
]

Your task is to generate a comprehensive and diverse list of distinct article topics based *only* on these keywords. Please follow these guidelines meticulously for maximum impact:

1. **Goal & Quality:** Generate a *wide variety* of unique and high-quality article topics. Aim for a substantial list, but *never* sacrifice relevance, clarity, or value for sheer quantity.

2. **Strict Relevance:** Every topic *must* directly stem from one or more of the provided keywords. Do *not* introduce external concepts.

3. **Strategic Combination (High Priority):** **Actively seek and prioritize opportunities to intelligently combine multiple keywords** from the list. This is crucial for creating richer, more specific, in-depth, and targeted ideas.

4. **Strategic Grouping:** Your primary goal is to group the generated topics into logical, strategic clusters based on user intent and content format. This helps the user see the relationships between ideas and build a coherent content plan.

5. **User Intent Focus (Inspiration):** Consider the *likely user intent* behind these keywords to spark ideas for your clusters:
    * **Answering Questions:** Addressing PAA or implied queries.
    * **Providing Instructions:** How-To guides, tutorials.
    * **Solving Problems:** Addressing pain points.
    * **Offering Comparisons:** "X vs Y" or "Best X".
    * **Informing & Explaining:** Deep dives, "What is," "Why X matters."
    * **Curating Lists:** Listicles, resource roundups.

6. **Diverse Angles & Creativity (Explore Freely):** **Think expansively!** Explore different formats and perspectives. Ensure some topics allow an author to showcase **Experience, Expertise, and build Trust/Authority (E-E-A-T).** Be creative and suggest unique angles that will capture attention. Examples include:
    * **Trend Analyses & Predictions**
    * **Opinion Pieces & Thought Leadership**
    * **Myth-Busting & Debunking Articles**
    * **Historical Deep Dives & Evolution Pieces**
    * **Case Studies & Success Stories**

7. **Clarity & Actionability:** Topics should be phrased as compelling, clear titles that immediately suggest the article's value.

8. **Output Format:** **CRITICAL: You MUST use this exact Markdown format.** Use H3 headings (###) followed by numbered lists. Do not add any introductory text, explanations, or concluding remarks. Start directly with the first cluster.

### Pillar Content & Broad Guides
1. [Your comprehensive topic idea here]
2. [Your comprehensive topic idea here]
3. [Your comprehensive topic idea here]

### Question-Based Topics
1. [Your question-answering topic here]
2. [Your question-answering topic here]
3. [Your question-answering topic here]

### Niche Angles & Thought Leadership
1. [Your unique/opinionated topic here]
2. [Your unique/opinionated topic here]
3. [Your unique/opinionated topic here]

### How-To's & Actionable Guides
1. [Your tutorial/guide topic here]
2. [Your tutorial/guide topic here]
3. [Your tutorial/guide topic here]

### Comparison & List-Based Topics
1. [Your comparison/list topic here]
2. [Your comparison/list topic here]
3. [Your comparison/list topic here]

**IMPORTANT:** Follow this format exactly. Each cluster must start with "### " followed by the cluster name, then numbered list items starting with "1. ". Generate 3-5 topics per cluster.

Think like an innovative and seasoned content manager aiming to build a robust, diverse, and audience-focused content plan. Generate the clustered list now.`;

      const response = await apiCall(API_CONFIG.ENDPOINTS.IDEATION_SUGGESTIONS, {
        method: 'POST',
        body: JSON.stringify({
          prompt: prompt,
          keywords: keywords,
          language: targetLanguage,
          country: targetCountry
        }),
      });

      // Parse the response to extract clustered topic suggestions
      let topicSuggestions = [];
      let clusteredTopics = {};


      if (response && response.suggestions) {
        topicSuggestions = response.suggestions;

        // Check if the response also has clustered data
        if (response.clustered) {
          clusteredTopics = response.clustered;
        }
      } else if (response && typeof response === 'string') {

        // Parse clustered Markdown response
        const lines = response.split('\n').filter(line => line.trim());
        let currentCluster = null;


        for (const line of lines) {
          const trimmedLine = line.trim();

          // Check for H3 headers (cluster names) - be more flexible
          if (trimmedLine.startsWith('### ') || trimmedLine.startsWith('## ') || trimmedLine.match(/^#{1,3}\s+/)) {
            currentCluster = trimmedLine.replace(/^#{1,3}\s+/, '').trim();
            clusteredTopics[currentCluster] = [];
          }
          // Check for numbered list items
          else if (/^\d+\./.test(trimmedLine) && currentCluster) {
            const topic = trimmedLine.replace(/^\d+\.\s*/, '').trim();
            if (topic.length > 0) {
              clusteredTopics[currentCluster].push(topic);
              topicSuggestions.push(topic); // Also add to flat list for backward compatibility
            }
          }
          // Also check for bullet points as fallback
          else if (/^[-*]\s+/.test(trimmedLine) && currentCluster) {
            const topic = trimmedLine.replace(/^[-*]\s+/, '').trim();
            if (topic.length > 0) {
              clusteredTopics[currentCluster].push(topic);
              topicSuggestions.push(topic);
            }
          }
        }


        // If no clusters found, fall back to simple numbered list parsing
        if (Object.keys(clusteredTopics).length === 0) {
          topicSuggestions = lines
            .filter(line => /^\d+\./.test(line.trim()))
            .map(line => line.replace(/^\d+\.\s*/, '').trim())
            .filter(topic => topic.length > 0);
        }
      }

      // Update data with generated topics (both clustered and flat)
      // Keep keywordResearchSelections for article generation later
      updateData({
        topicSuggestions: {
          aiGenerated: topicSuggestions,
          clustered: clusteredTopics,
          generatedFromKeywords: keywords,
          hasClusters: Object.keys(clusteredTopics).length > 0
        }
        // Don't clear keywordResearchSelections - we need them for article generation
      });

    } catch (error) {
      console.error('Error generating article topics:', error);

      // Parse error response for better user messaging
      let errorMessage = 'AI service is temporarily unavailable. Please try again later.';
      if (error.message && error.message.includes('AI service unavailable')) {
        errorMessage = 'Unable to connect to AI service. Please check your internet connection and try again.';
      }

      // Update data with error state
      updateData({
        topicSuggestions: {
          error: true,
          errorMessage: errorMessage
        }
        // Keep keywordResearchSelections even on error
      });
    } finally {
      setIsGeneratingTopics(false);
    }
  };



  // addCustomKeyword is now addCustomKeywordToSelection, defined earlier
  // This specific block can be removed if addCustomKeywordToSelection covers its functionality.

  return (
    <div className="flex">
      {/* Main Content Area */}
      <div className={`${hasResearched ? 'flex-1 pr-6' : 'w-full'} space-y-8`}>
        {/* Header Section */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Keyword Research</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            <span className="font-semibold text-blue-600">Step 1 of 6:</span> Discover content opportunities by researching keywords.
            Enter a topic to find trending angles, popular questions, and content ideas
            that will form the foundation of your article.
          </p>
        </div>

        {/* Main Research Section */}
        <div className="bg-white border border-gray-200 rounded-xl p-8 shadow-sm">
          <div className="max-w-2xl mx-auto">
            <h3 className="text-xl font-semibold text-gray-900 mb-6 text-center">
              🔍 Keyword Research & Topic Discovery
            </h3>

            {/* Language and Country Selection */}
            <div className="mb-6">
              {/* Recommendation Notice */}
              {data?.jurisdiction && data.jurisdiction !== 'international' && (
                <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-start">
                    <div className="text-blue-600 mr-3 mt-0.5">💡</div>
                    <div className="text-sm text-blue-700">
                      <strong className="text-blue-900">Recommended for {getJurisdictionDisplayName(data.jurisdiction)}:</strong>
                      <div className="mt-1">
                        Language and country have been auto-set based on your jurisdiction selection.
                        You can change them if needed.
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div className="flex gap-4">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-900 mb-2">
                    Language
                    {data?.jurisdiction && data.jurisdiction !== 'international' && (
                      <span className="ml-1 text-xs text-blue-600 font-normal">(recommended)</span>
                    )}
                  </label>
                  <select
                    value={selectedLanguage}
                    onChange={(e) => {
                      const newLanguage = e.target.value;
                      setSelectedLanguage(newLanguage);
                      setUserHasManuallySelected(true);
                      // 统一使用updateData来更新状态，它会触发TaskEditor的保存逻辑
                      updateData({ 
                        selectedLanguage: newLanguage,
                        targetLanguage: newLanguage // 确保字段名一致
                      });
                    }}
                    className="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                  >
                    <option value="en">English</option>
                    <option value="zh">Chinese (中文)</option>
                    <option value="pt">Portuguese (Português)</option>
                    <option value="es">Spanish (Español)</option>
                    <option value="de">German (Deutsch)</option>
                    <option value="fr">French (Français)</option>
                    <option value="it">Italian (Italiano)</option>
                    <option value="ja">Japanese (日本語)</option>
                  </select>
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-900 mb-2">
                    Country
                    {data?.jurisdiction && data.jurisdiction !== 'international' && (
                      <span className="ml-1 text-xs text-gray-600 font-normal">(recommended)</span>
                    )}
                  </label>
                  <select
                    value={selectedCountry}
                    onChange={(e) => {
                      const newCountry = e.target.value;
                      setSelectedCountry(newCountry);
                      setUserHasManuallySelected(true);
                      // 统一使用updateData来更新状态，它会触发TaskEditor的保存逻辑
                      updateData({ 
                        selectedCountry: newCountry,
                        targetCountry: newCountry // 确保字段名一致
                      });
                    }}
                    className="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                  >
                    <option value="us">United States</option>
                    <option value="gb">United Kingdom</option>
                    <option value="ca">Canada</option>
                    <option value="au">Australia</option>
                    <option value="de">Germany</option>
                    <option value="fr">France</option>
                    <option value="es">Spain</option>
                    <option value="it">Italy</option>
                    <option value="br">Brazil</option>
                    <option value="mx">Mexico</option>
                    <option value="jp">Japan</option>
                    <option value="kr">South Korea</option>
                    <option value="cn">China</option>
                    <option value="in">India</option>
                    <option value="ru">Russia</option>
                    <option value="ar">Argentina</option>
                    <option value="cl">Chile</option>
                    <option value="co">Colombia</option>
                    <option value="sg">Singapore</option>
                    <option value="th">Thailand</option>
                    <option value="vn">Vietnam</option>
                    <option value="ph">Philippines</option>
                    <option value="id">Indonesia</option>
                    <option value="my">Malaysia</option>
                    <option value="nz">New Zealand</option>
                    <option value="za">South Africa</option>
                    <option value="eg">Egypt</option>
                    <option value="ng">Nigeria</option>
                    <option value="ke">Kenya</option>
                    <option value="ma">Morocco</option>
                    <option value="nl">Netherlands</option>
                    <option value="pl">Poland</option>
                    <option value="se">Sweden</option>
                    <option value="no">Norway</option>
                    <option value="fi">Finland</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Primary Input */}
            <div className="flex gap-4 mb-6">
              <input
                type="text"
                value={keywordInput}
                onChange={(e) => setKeywordInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && keywordInput.trim()) {
                    addKeyword();
                    performKeywordResearch(keywordInput.trim());
                  }
                }}
                placeholder="Enter a keyword to research (e.g., 'casino games', 'sports betting', 'poker strategy')"
                className="flex-1 px-6 py-4 bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 text-lg placeholder-gray-400 shadow-inner"
              />
              <button
                onClick={() => {
                  if (keywordInput.trim()) {
                    addKeyword();
                    performKeywordResearch(keywordInput.trim());
                  }
                }}
                disabled={!keywordInput.trim() || isResearching}
                className="casino-button px-8 py-4 text-black font-bold rounded-xl disabled:opacity-50 disabled:cursor-not-allowed text-lg flex items-center shadow-lg hover:shadow-xl transition-all duration-200"
              >
                {isResearching ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-black mr-2"></div>
                    Researching...
                  </>
                ) : (
                  <>
                    <MagnifyingGlassIcon className="w-5 h-5 mr-2" />
                    Research
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Keyword Research Results */}
        {keywordResearchData && (
          <div className="bg-white border border-gray-200 shadow-sm rounded-xl">
            <div className="p-6 border-b border-gray-300 bg-gray-50">
              <h3 className="text-2xl font-bold text-gray-900 mb-2 ">
                🎯 Research Results for "{keywordResearchData.keyword}"
              </h3>
              <p className="text-gray-600">
                Discover trending topics, popular questions, and content opportunities based on real search data.
              </p>
            </div>
            <KeywordResearchPanel
              researchData={keywordResearchData}
              onKeywordSelect={handleKeywordFromResearch} // Keep if used for other purposes
              onTopicSelect={handleTopicFromResearch}
              // Pass simplified selection handlers and data
              selectedKeywords={selectedKeywords}
              onToggleKeyword={toggleKeywordSelection}
              // Limits can be managed directly in KeywordResearchPanel or passed if needed
              // For simplicity, let's assume KeywordResearchPanel handles its display based on selectedKeywords.length
            />
          </div>
        )}
      </div>

      {/* Right Sidebar - Selection Manager - Only show after research */}
      {hasResearched && (
        <div className="w-80 flex-shrink-0">
          <div className="sticky top-6">
            <SelectionManager
              selectedKeywords={selectedKeywords}
              onToggleKeyword={toggleKeywordSelection} // For removing individual keywords
              onClearAll={clearAllSelectedKeywords}
              onUseAsKeywords={generateTopicsFromSelectedKeywords} // Fixed prop name
              currentKeyword={currentResearchKeyword}
              onAddCustomKeyword={addCustomKeywordToSelection}
              isGeneratingTopics={isGeneratingTopics}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Step0KeywordResearch;
