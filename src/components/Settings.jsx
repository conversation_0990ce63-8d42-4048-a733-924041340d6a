import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import { 
  ArrowLeftIcon, 
  UserIcon, 
  BellIcon, 
  ShieldCheckIcon,
  KeyIcon,
  GlobeAltIcon,
  CogIcon
} from '@heroicons/react/24/outline';
import AuthenticatedLayout from './layout/AuthenticatedLayout';

const Settings = () => {
  const { t } = useTranslation();
  const { user, logout } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [settings, setSettings] = useState({
    profile: {
      fullName: user?.fullName || '',
      email: user?.email || '',
      timezone: 'UTC',
      language: 'en'
    },
    notifications: {
      emailNotifications: true,
      taskUpdates: true,
      systemAlerts: false,
      weeklyReports: true
    },
    security: {
      twoFactorEnabled: false,
      sessionTimeout: 30,
      loginAlerts: true
    },
    preferences: {
      defaultContentType: 'casino_review',
      autoSave: true,
      darkMode: false,
      compactView: false
    }
  });

  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });

  // Check if user is admin or super admin
  const isSuperAdmin = user?.role === 'admin' || user?.role === 'super_admin';

  const tabs = [
    { id: 'profile', name: 'Profile', icon: UserIcon },
    { id: 'notifications', name: 'Notifications', icon: BellIcon },
    { id: 'security', name: 'Security', icon: ShieldCheckIcon },
    { id: 'preferences', name: 'Preferences', icon: CogIcon },
    ...(isSuperAdmin ? [{ id: 'admin', name: 'Admin Panel', icon: KeyIcon }] : [])
  ];

  const handleSettingChange = (category, key, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
  };

  const handleSave = async (category) => {
    setIsLoading(true);
    setMessage({ type: '', text: '' });

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setMessage({ 
        type: 'success', 
        text: `${category.charAt(0).toUpperCase() + category.slice(1)} settings saved successfully!` 
      });
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: 'Failed to save settings. Please try again.' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  const renderProfileSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-casino-gold-300 mb-2">
          Full Name
        </label>
        <input
          type="text"
          value={settings.profile.fullName}
          onChange={(e) => handleSettingChange('profile', 'fullName', e.target.value)}
          className="w-full px-4 py-3 bg-casino-dark-600 border border-casino-gold-500/30 rounded-xl text-white placeholder-casino-gold-400 focus:outline-none focus:ring-2 focus:ring-casino-gold-500 focus:border-transparent"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-casino-gold-300 mb-2">
          Email Address
        </label>
        <input
          type="email"
          value={settings.profile.email}
          disabled
          className="w-full px-4 py-3 bg-casino-dark-700 border border-casino-gold-500/20 rounded-xl text-casino-gold-400 cursor-not-allowed"
        />
        <p className="text-xs text-casino-gold-400 mt-1">Email cannot be changed</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-casino-gold-300 mb-2">
            Timezone
          </label>
          <select
            value={settings.profile.timezone}
            onChange={(e) => handleSettingChange('profile', 'timezone', e.target.value)}
            className="w-full px-4 py-3 bg-casino-dark-600 border border-casino-gold-500/30 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-500"
          >
            <option value="UTC">UTC</option>
            <option value="America/New_York">Eastern Time</option>
            <option value="America/Chicago">Central Time</option>
            <option value="America/Denver">Mountain Time</option>
            <option value="America/Los_Angeles">Pacific Time</option>
            <option value="Europe/London">London</option>
            <option value="Europe/Paris">Paris</option>
            <option value="Asia/Tokyo">Tokyo</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-casino-gold-300 mb-2">
            Language
          </label>
          <select
            value={settings.profile.language}
            onChange={(e) => handleSettingChange('profile', 'language', e.target.value)}
            className="w-full px-4 py-3 bg-casino-dark-600 border border-casino-gold-500/30 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-500"
          >
            <option value="en">English</option>
            <option value="es">Spanish</option>
            <option value="de">German</option>
            <option value="fr">French</option>
            <option value="it">Italian</option>
          </select>
        </div>
      </div>

      <button
        onClick={() => handleSave('profile')}
        disabled={isLoading}
        className="px-6 py-3 bg-gradient-to-r from-casino-gold-500 to-casino-gold-600 text-casino-dark-900 rounded-xl font-semibold hover:from-casino-gold-400 hover:to-casino-gold-500 transition-all duration-200 disabled:opacity-50"
      >
        {isLoading ? 'Saving...' : 'Save Profile'}
      </button>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      {Object.entries(settings.notifications).map(([key, value]) => (
        <div key={key} className="flex items-center justify-between p-4 bg-casino-dark-600 rounded-xl border border-casino-gold-500/20">
          <div>
            <h4 className="text-casino-gold-300 font-medium">
              {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </h4>
            <p className="text-sm text-casino-gold-400">
              {key === 'emailNotifications' && 'Receive email notifications for important updates'}
              {key === 'taskUpdates' && 'Get notified when your tasks are completed'}
              {key === 'systemAlerts' && 'Receive system maintenance and security alerts'}
              {key === 'weeklyReports' && 'Get weekly summary reports of your activity'}
            </p>
          </div>
          <button
            onClick={() => handleSettingChange('notifications', key, !value)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              value ? 'bg-casino-gold-500' : 'bg-casino-dark-700'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                value ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
      ))}

      <button
        onClick={() => handleSave('notifications')}
        disabled={isLoading}
        className="px-6 py-3 bg-gradient-to-r from-casino-gold-500 to-casino-gold-600 text-casino-dark-900 rounded-xl font-semibold hover:from-casino-gold-400 hover:to-casino-gold-500 transition-all duration-200 disabled:opacity-50"
      >
        {isLoading ? 'Saving...' : 'Save Notifications'}
      </button>
    </div>
  );

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div className="p-4 bg-casino-dark-600 rounded-xl border border-casino-gold-500/20">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-casino-gold-300 font-medium">Two-Factor Authentication</h4>
            <p className="text-sm text-casino-gold-400">Add an extra layer of security to your account</p>
          </div>
          <button
            onClick={() => handleSettingChange('security', 'twoFactorEnabled', !settings.security.twoFactorEnabled)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              settings.security.twoFactorEnabled ? 'bg-casino-gold-500' : 'bg-casino-dark-700'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                settings.security.twoFactorEnabled ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-casino-gold-300 mb-2">
          Session Timeout (minutes)
        </label>
        <select
          value={settings.security.sessionTimeout}
          onChange={(e) => handleSettingChange('security', 'sessionTimeout', parseInt(e.target.value))}
          className="w-full px-4 py-3 bg-casino-dark-600 border border-casino-gold-500/30 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-500"
        >
          <option value={15}>15 minutes</option>
          <option value={30}>30 minutes</option>
          <option value={60}>1 hour</option>
          <option value={120}>2 hours</option>
          <option value={480}>8 hours</option>
        </select>
      </div>

      <div className="p-4 bg-casino-dark-600 rounded-xl border border-casino-gold-500/20">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-casino-gold-300 font-medium">Login Alerts</h4>
            <p className="text-sm text-casino-gold-400">Get notified of new login attempts</p>
          </div>
          <button
            onClick={() => handleSettingChange('security', 'loginAlerts', !settings.security.loginAlerts)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              settings.security.loginAlerts ? 'bg-casino-gold-500' : 'bg-casino-dark-700'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                settings.security.loginAlerts ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
      </div>

      <button
        onClick={() => handleSave('security')}
        disabled={isLoading}
        className="px-6 py-3 bg-gradient-to-r from-casino-gold-500 to-casino-gold-600 text-casino-dark-900 rounded-xl font-semibold hover:from-casino-gold-400 hover:to-casino-gold-500 transition-all duration-200 disabled:opacity-50"
      >
        {isLoading ? 'Saving...' : 'Save Security'}
      </button>
    </div>
  );

  const renderPreferencesSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-casino-gold-300 mb-2">
          Default Content Type
        </label>
        <select
          value={settings.preferences.defaultContentType}
          onChange={(e) => handleSettingChange('preferences', 'defaultContentType', e.target.value)}
          className="w-full px-4 py-3 bg-casino-dark-600 border border-casino-gold-500/30 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-500"
        >
          <option value="casino_review">{t('contentTypes.casino_review')}</option>
          <option value="game_guide">{t('contentTypes.game_guide')}</option>
          <option value="strategy_article">{t('contentTypes.strategy_article')}</option>
          <option value="brand_copy">{t('contentTypes.brand_copy')}</option>
          <option value="industry_news">{t('contentTypes.industry_news')}</option>
          <option value="sports_betting">{t('contentTypes.sports_betting')}</option>
        </select>
      </div>

      {Object.entries(settings.preferences).filter(([key]) => key !== 'defaultContentType').map(([key, value]) => (
        <div key={key} className="flex items-center justify-between p-4 bg-casino-dark-600 rounded-xl border border-casino-gold-500/20">
          <div>
            <h4 className="text-casino-gold-300 font-medium">
              {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </h4>
            <p className="text-sm text-casino-gold-400">
              {key === 'autoSave' && 'Automatically save your work as you type'}
              {key === 'darkMode' && 'Use dark theme throughout the application'}
              {key === 'compactView' && 'Show more content in less space'}
            </p>
          </div>
          <button
            onClick={() => handleSettingChange('preferences', key, !value)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              value ? 'bg-casino-gold-500' : 'bg-casino-dark-700'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                value ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
      ))}

      <button
        onClick={() => handleSave('preferences')}
        disabled={isLoading}
        className="px-6 py-3 bg-gradient-to-r from-casino-gold-500 to-casino-gold-600 text-casino-dark-900 rounded-xl font-semibold hover:from-casino-gold-400 hover:to-casino-gold-500 transition-all duration-200 disabled:opacity-50"
      >
        {isLoading ? 'Saving...' : 'Save Preferences'}
      </button>
    </div>
  );

  const renderAdminSettings = () => (
    <div className="space-y-6">
      <div className="p-6 bg-gradient-to-r from-casino-gold-500/10 to-casino-gold-600/10 rounded-xl border border-casino-gold-500/30">
        <div className="flex items-center space-x-3 mb-4">
          <div className="p-2 bg-casino-gold-500 rounded-lg">
            <KeyIcon className="w-6 h-6 text-casino-dark-900" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-casino-gold-100">Super Admin Access</h3>
            <p className="text-casino-gold-300">Manage system settings and prompt templates</p>
          </div>
        </div>

        <div className="space-y-4">
          <div className="p-4 bg-casino-dark-600 rounded-lg border border-casino-gold-500/20">
            <h4 className="text-casino-gold-200 font-medium mb-2">Prompt Template Management</h4>
            <p className="text-casino-gold-400 text-sm mb-4">
              Create, edit, and manage AI prompt templates for different content types.
              Configure AI model settings and system parameters.
            </p>
            <Link
              to="/admin"
              className="px-6 py-3 bg-gradient-to-r from-casino-gold-500 to-casino-gold-600 text-casino-dark-900 rounded-xl font-semibold hover:from-casino-gold-400 hover:to-casino-gold-500 transition-all duration-200 flex items-center space-x-2 inline-flex"
            >
              <KeyIcon className="w-5 h-5" />
              <span>Open Admin Panel</span>
            </Link>
          </div>

          <div className="p-4 bg-casino-dark-600 rounded-lg border border-casino-gold-500/20">
            <h4 className="text-casino-gold-200 font-medium mb-2">System Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-casino-gold-400">User Role:</span>
                <span className="text-casino-gold-200 ml-2 font-medium">Super Administrator</span>
              </div>
              <div>
                <span className="text-casino-gold-400">Access Level:</span>
                <span className="text-casino-gold-200 ml-2 font-medium">Full System Access</span>
              </div>
              <div>
                <span className="text-casino-gold-400">Email:</span>
                <span className="text-casino-gold-200 ml-2 font-medium">{user?.email}</span>
              </div>
              <div>
                <span className="text-casino-gold-400">Account Type:</span>
                <span className="text-casino-gold-200 ml-2 font-medium">Administrator</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-casino-dark-800 via-casino-dark-900 to-black flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-casino-gold-500 mx-auto"></div>
          <p className="mt-4 text-casino-gold-200">Loading settings...</p>
        </div>
      </div>
    );
  }

  return (
    <AuthenticatedLayout>
      <div className="min-h-screen bg-gradient-to-br from-casino-dark-800 via-casino-dark-900 to-black">
        {/* Page Header */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center space-x-4 mb-8">
            <Link
              to="/dashboard"
              className="flex items-center space-x-2 text-casino-gold-300 hover:text-casino-gold-100 transition-colors"
            >
                <ArrowLeftIcon className="w-5 h-5" />
                <span>Back to Dashboard</span>
              </Link>
              <div className="h-6 w-px bg-casino-gold-500/30"></div>
              <h1 className="text-xl font-bold text-casino-gold-100">Settings</h1>
            </div>
          </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all duration-200 ${
                      activeTab === tab.id
                        ? 'bg-casino-gold-500 text-casino-dark-900 font-semibold'
                        : 'text-casino-gold-300 hover:bg-casino-dark-600 hover:text-casino-gold-100'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{tab.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="casino-card p-6">
              {message.text && (
                <div className={`mb-6 p-4 rounded-xl ${
                  message.type === 'success' 
                    ? 'bg-green-500/20 border border-green-500/30 text-green-300' 
                    : 'bg-red-500/20 border border-red-500/30 text-red-300'
                }`}>
                  {message.text}
                </div>
              )}

              <div className="mb-6">
                <h2 className="text-2xl font-bold text-casino-gold-100 mb-2">
                  {tabs.find(tab => tab.id === activeTab)?.name}
                </h2>
                <p className="text-casino-gold-400">
                  {activeTab === 'profile' && 'Manage your account information and preferences'}
                  {activeTab === 'notifications' && 'Control how and when you receive notifications'}
                  {activeTab === 'security' && 'Secure your account with additional protection'}
                  {activeTab === 'preferences' && 'Customize your Writer 777 experience'}
                  {activeTab === 'admin' && 'Access administrative functions and system management'}
                </p>
              </div>

              {activeTab === 'profile' && renderProfileSettings()}
              {activeTab === 'notifications' && renderNotificationSettings()}
              {activeTab === 'security' && renderSecuritySettings()}
              {activeTab === 'preferences' && renderPreferencesSettings()}
              {activeTab === 'admin' && isSuperAdmin && renderAdminSettings()}
            </div>
          </div>
        </div>
        </div>
      </div>
    </AuthenticatedLayout>
  );
};

export default Settings;
