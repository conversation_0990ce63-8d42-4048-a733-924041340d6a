import React, { useState } from 'react';
import { XMarkIcon, ChevronDownIcon, ChevronUpIcon, TagIcon, QuestionMarkCircleIcon, CloudIcon, MagnifyingGlassIcon, PlusIcon } from '@heroicons/react/24/outline';

const SelectionManager = ({ selectedKeywords = [], onToggleKeyword, onClearAll, onUseAsKeywords, currentKeyword, isGeneratingTopics }) => {
  const [expandedSections, setExpandedSections] = useState({
    autocomplete: true,
    custom: true,
    related: true,
    keyTerms: true,
    paa: true
  });

  const [customKeyword, setCustomKeyword] = useState('');

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Simplified keyword count (all keywords are now of the same 'type')
  const totalSelectedCount = selectedKeywords.length;
  // Define a general limit if needed, or remove if not applicable with the new simplified logic
  const keywordLimit = 15; // Example: Max 15 keywords total

  const handleAddCustomKeyword = () => {
    if (customKeyword.trim()) {
      if (totalSelectedCount >= keywordLimit && !selectedKeywords.includes(customKeyword.trim())) {
        alert(`You can select a maximum of ${keywordLimit} keywords.`);
        return;
      }
      onToggleKeyword(customKeyword.trim()); // Use onToggleKeyword for custom keywords
      setCustomKeyword('');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleAddCustomKeyword();
    }
  };

  // No need to group by type anymore, all selected keywords are in selectedKeywords array
  const totalCount = selectedKeywords.length;

  // Show empty state only if no keyword and no selections
  if (totalCount === 0 && !currentKeyword) {
    return (
      <div className="w-80 bg-white rounded-lg shadow-lg border border-gray-200 p-4">
        <div className="text-center text-gray-500">
          <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
            <TagIcon className="w-6 h-6 text-gray-400" />
          </div>
          <h3 className="font-medium text-gray-900 mb-1">No Items Selected</h3>
          <p className="text-sm text-gray-500">
            Research a keyword and click "+" buttons to add items here.
          </p>
        </div>
      </div>
    );
  }

  // Show the manager if there's a keyword or selections
  if (!currentKeyword && totalCount === 0) {
    return null;
  }

  return (
    <div className="w-80 bg-white rounded-xl shadow-xl border border-gray-200 max-h-[calc(100vh-120px)] overflow-hidden flex flex-col">
      {/* Current Keyword */}
      {currentKeyword && (
        <div className="p-5 border-b border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50">
          <h3 className="font-bold text-green-900 flex items-center text-lg">
            <MagnifyingGlassIcon className="w-5 h-5 mr-2 text-green-600" />
            Research: "{currentKeyword}"
          </h3>
        </div>
      )}

      {/* Header */}
      <div className="p-5 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-bold text-gray-900 flex items-center text-lg">
            <TagIcon className="w-5 h-5 mr-2 text-blue-600" />
            Selected Keywords ({totalCount})
          </h3>
          {totalCount > 0 && (
            <button
              onClick={onClearAll}
              className="text-sm text-red-600 hover:text-red-800 font-medium px-2 py-1 rounded hover:bg-red-50 transition-colors duration-200"
            >
              Clear All
            </button>
          )}
        </div>
        {/* Simplified Limits Display */}
        <div className="flex items-center justify-between text-xs">
          <div className={`px-2 py-1 rounded ${
            totalCount >= keywordLimit
              ? 'bg-red-100 text-red-800'
              : totalCount >= keywordLimit - 2 // Example: warning when 2 slots left
              ? 'bg-orange-100 text-orange-800'
              : 'bg-green-100 text-green-800'
          }`}>
            Selected: {totalCount}/{keywordLimit}
          </div>
        </div>
      </div>

      {/* Content - Scrollable */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4 space-y-4">
          {/* Show message when keyword exists but no selections */}
          {currentKeyword && totalCount === 0 && (
            <div className="text-center text-gray-500 py-8">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <TagIcon className="w-6 h-6 text-blue-400" />
              </div>
              <h4 className="font-medium text-gray-900 mb-2">Ready to Collect Items</h4>
              <p className="text-sm text-gray-500">
                Click the "+" buttons next to keywords, terms, or questions to add them here.
              </p>
            </div>
          )}
          {/* Display all selected keywords in a single list */}
          {selectedKeywords.length > 0 && (
            <div className="border border-gray-200 rounded-lg">
              <button
                onClick={() => toggleSection('allKeywords')}
                className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50"
              >
                <h4 className="font-medium text-gray-900 flex items-center text-sm">
                  <TagIcon className="w-4 h-4 mr-2" />
                  All Selected Keywords ({selectedKeywords.length})
                </h4>
                {expandedSections.allKeywords ? (
                  <ChevronUpIcon className="w-4 h-4 text-gray-500" />
                ) : (
                  <ChevronDownIcon className="w-4 h-4 text-gray-500" />
                )}
              </button>

              {expandedSections.allKeywords && (
                <div className="px-3 pb-3 space-y-2">
                  {selectedKeywords.map((keyword, index) => (
                    <div
                      key={`selected-${index}`}
                      className="flex items-center justify-between p-2 bg-gray-50 rounded border border-gray-200"
                    >
                      <span className="text-sm text-gray-900 flex-1 truncate">{keyword}</span>
                      <button
                        onClick={() => onToggleKeyword(keyword)} // Use onToggleKeyword
                        className="ml-2 text-red-600 hover:text-red-800"
                      >
                        <XMarkIcon className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Add Custom Keyword Section */}
          <div className="border border-green-200 rounded-xl bg-gradient-to-r from-green-50 to-emerald-50 shadow-sm">
            <div className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-bold text-green-900 flex items-center text-sm">
                  <PlusIcon className="w-4 h-4 mr-2" />
                  Add Custom Keyword
                </h4>
                {totalSelectedCount >= keywordLimit && (
                  <span className="text-xs text-red-700 bg-red-100 px-3 py-1 rounded-full font-medium">
                    Keyword limit reached
                  </span>
                )}
              </div>
              <div className="flex space-x-3">
                <input
                  type="text"
                  value={customKeyword}
                  onChange={(e) => setCustomKeyword(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Enter your keyword..."
                  disabled={totalSelectedCount >= keywordLimit && !selectedKeywords.includes(customKeyword.trim())}
                  className={`flex-1 px-4 py-2 border rounded-lg text-sm focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 ${
                    (totalSelectedCount >= keywordLimit && !selectedKeywords.includes(customKeyword.trim()))
                      ? 'border-gray-300 bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'border-green-300 focus:ring-green-500 focus:border-green-500'
                  }`} 
                />
                <button
                  onClick={handleAddCustomKeyword}
                  disabled={!customKeyword.trim() || (totalSelectedCount >= keywordLimit && !selectedKeywords.includes(customKeyword.trim()))}
                  className={`px-4 py-2 rounded-lg text-sm font-bold transition-all duration-200 ${
                    customKeyword.trim() && !(totalSelectedCount >= keywordLimit && !selectedKeywords.includes(customKeyword.trim()))
                      ? 'bg-green-600 text-white hover:bg-green-700 shadow-md hover:shadow-lg'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  <PlusIcon className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      {totalCount > 0 && (
        <div className="p-5 border-t border-gray-200 bg-gradient-to-r from-gray-50 to-slate-50 space-y-3">
          {/* Requirements Info */}
          <div className="text-xs text-gray-700 mb-3">
            <div className="flex justify-between items-center">
              {/* Simplified requirement, e.g., at least 5 keywords total */}
              <span className="font-medium">Minimum: 5 keywords</span> 
              <span className={`px-2 py-1 rounded-full font-bold text-xs ${
                totalSelectedCount >= 5
                  ? 'text-green-700 bg-green-100' : 'text-orange-700 bg-orange-100'
              }`}>
                {totalSelectedCount >= 5 ? '✓ Ready' : '⚠ Need more'}
              </span>
            </div>
          </div>

          <button
            onClick={() => onUseAsKeywords(selectedKeywords)} // Pass selectedKeywords directly
            disabled={isGeneratingTopics || totalSelectedCount < 5} // Simplified condition
            className={`w-full px-4 py-3 rounded-xl font-bold text-sm flex items-center justify-center transition-all duration-200 ${
              isGeneratingTopics
                ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                : totalSelectedCount < 5 
                ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700 shadow-lg hover:shadow-xl'
            }`}
          >
            {isGeneratingTopics ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                Generating Topics...
              </>
            ) : (
              `Generate Article Topics (${selectedKeywords.length})`
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default SelectionManager;
