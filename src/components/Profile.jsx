import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { 
  ArrowLeftIcon, 
  UserIcon, 
  EnvelopeIcon,
  CalendarIcon,
  ShieldCheckIcon,
  CogIcon
} from '@heroicons/react/24/outline';

const Profile = () => {
  const { user, logout } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    fullName: user?.fullName || '',
    email: user?.email || '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    setMessage({ type: '', text: '' });

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setMessage({ 
        type: 'success', 
        text: 'Profile updated successfully!' 
      });
      setIsEditing(false);
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: 'Failed to update profile. Please try again.' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      fullName: user?.fullName || '',
      email: user?.email || '',
    });
    setIsEditing(false);
    setMessage({ type: '', text: '' });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-casino-dark-800 via-casino-dark-900 to-black">
      {/* Header */}
      <header className="casino-card border-b border-casino-gold-500/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link
                to="/dashboard"
                className="flex items-center space-x-2 text-casino-gold-300 hover:text-casino-gold-100 transition-colors"
              >
                <ArrowLeftIcon className="w-5 h-5" />
                <span>Back to Dashboard</span>
              </Link>
              <div className="h-6 w-px bg-casino-gold-500/30"></div>
              <h1 className="text-xl font-bold text-casino-gold-100">Profile</h1>
            </div>
            
            <Link
              to="/settings"
              className="flex items-center space-x-2 px-3 py-2 rounded-lg text-casino-gold-200 hover:bg-casino-gold-500/20 hover:text-casino-gold-100 transition-all duration-200 border border-casino-gold-500/30"
            >
              <CogIcon className="w-5 h-5" />
              <span className="hidden sm:block">Settings</span>
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Profile Card */}
          <div className="lg:col-span-1">
            <div className="casino-card p-6 text-center">
              <div className="w-24 h-24 bg-gradient-to-br from-casino-gold-500 to-casino-gold-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <UserIcon className="w-12 h-12 text-casino-dark-900" />
              </div>
              
              <h2 className="text-xl font-bold text-casino-gold-100 mb-2">
                {user?.fullName || 'User'}
              </h2>
              
              <p className="text-casino-gold-400 mb-4">
                {user?.email}
              </p>
              
              <div className="flex items-center justify-center space-x-2 text-sm text-casino-gold-300 mb-4">
                <CalendarIcon className="w-4 h-4" />
                <span>Member since {new Date().getFullYear()}</span>
              </div>
              
              <div className="flex items-center justify-center space-x-2 text-sm text-casino-gold-300">
                <ShieldCheckIcon className="w-4 h-4" />
                <span>V1 Default Access</span>
              </div>
            </div>
          </div>

          {/* Profile Details */}
          <div className="lg:col-span-2">
            <div className="casino-card p-6">
              {message.text && (
                <div className={`mb-6 p-4 rounded-xl ${
                  message.type === 'success' 
                    ? 'bg-green-500/20 border border-green-500/30 text-green-300' 
                    : 'bg-red-500/20 border border-red-500/30 text-red-300'
                }`}>
                  {message.text}
                </div>
              )}

              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-casino-gold-100">Account Information</h3>
                {!isEditing && (
                  <button
                    onClick={() => setIsEditing(true)}
                    className="px-4 py-2 bg-casino-gold-500 text-casino-dark-900 rounded-lg font-medium hover:bg-casino-gold-400 transition-colors"
                  >
                    Edit Profile
                  </button>
                )}
              </div>

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-casino-gold-300 mb-2">
                    <UserIcon className="w-4 h-4 inline mr-2" />
                    Full Name
                  </label>
                  {isEditing ? (
                    <input
                      type="text"
                      name="fullName"
                      value={formData.fullName}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 bg-casino-dark-600 border border-casino-gold-500/30 rounded-xl text-white placeholder-casino-gold-400 focus:outline-none focus:ring-2 focus:ring-casino-gold-500 focus:border-transparent"
                      placeholder="Enter your full name"
                    />
                  ) : (
                    <div className="w-full px-4 py-3 bg-casino-dark-700 border border-casino-gold-500/20 rounded-xl text-casino-gold-200">
                      {user?.fullName || 'Not set'}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-casino-gold-300 mb-2">
                    <EnvelopeIcon className="w-4 h-4 inline mr-2" />
                    Email Address
                  </label>
                  <div className="w-full px-4 py-3 bg-casino-dark-700 border border-casino-gold-500/20 rounded-xl text-casino-gold-400 cursor-not-allowed">
                    {user?.email}
                  </div>
                  <p className="text-xs text-casino-gold-400 mt-1">Email cannot be changed</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-casino-gold-300 mb-2">
                    <ShieldCheckIcon className="w-4 h-4 inline mr-2" />
                    Plan Type
                  </label>
                  <div className="w-full px-4 py-3 bg-casino-dark-700 border border-casino-gold-500/20 rounded-xl text-casino-gold-200">
                    {user?.planType || 'V1_DEFAULT_ACCESS'}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-casino-gold-300 mb-2">
                    <CalendarIcon className="w-4 h-4 inline mr-2" />
                    Account Status
                  </label>
                  <div className="flex items-center space-x-2">
                    <div className="w-full px-4 py-3 bg-casino-dark-700 border border-casino-gold-500/20 rounded-xl text-casino-gold-200">
                      Active
                    </div>
                    <div className="flex items-center space-x-2 px-3 py-2 bg-green-500/20 border border-green-500/30 rounded-lg">
                      <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                      <span className="text-green-300 text-sm">Verified</span>
                    </div>
                  </div>
                </div>

                {isEditing && (
                  <div className="flex items-center space-x-4 pt-4">
                    <button
                      onClick={handleSave}
                      disabled={isLoading}
                      className="px-6 py-3 bg-gradient-to-r from-casino-gold-500 to-casino-gold-600 text-casino-dark-900 rounded-xl font-semibold hover:from-casino-gold-400 hover:to-casino-gold-500 transition-all duration-200 disabled:opacity-50"
                    >
                      {isLoading ? 'Saving...' : 'Save Changes'}
                    </button>
                    <button
                      onClick={handleCancel}
                      className="px-6 py-3 bg-casino-dark-600 text-casino-gold-300 rounded-xl font-medium hover:bg-casino-dark-500 transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Account Actions */}
            <div className="casino-card p-6 mt-6">
              <h3 className="text-xl font-bold text-casino-gold-100 mb-4">Account Actions</h3>
              
              <div className="space-y-4">
                <Link
                  to="/settings"
                  className="flex items-center justify-between p-4 bg-casino-dark-600 rounded-xl border border-casino-gold-500/20 hover:bg-casino-dark-500 transition-colors group"
                >
                  <div className="flex items-center space-x-3">
                    <CogIcon className="w-5 h-5 text-casino-gold-400" />
                    <div>
                      <h4 className="text-casino-gold-300 font-medium">Account Settings</h4>
                      <p className="text-sm text-casino-gold-400">Manage notifications, security, and preferences</p>
                    </div>
                  </div>
                  <ArrowLeftIcon className="w-5 h-5 text-casino-gold-400 rotate-180 group-hover:translate-x-1 transition-transform" />
                </Link>

                <button
                  onClick={logout}
                  className="w-full flex items-center justify-center space-x-2 p-4 bg-red-500/20 border border-red-500/30 rounded-xl text-red-300 hover:bg-red-500/30 transition-colors"
                >
                  <span>Sign Out</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
