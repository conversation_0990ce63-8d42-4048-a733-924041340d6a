import React from 'react';

const WriterLogo = ({ variant = 'full', className = '', animated = true }) => {
  // Full logo with text
  if (variant === 'full') {
    return (
      <svg 
        className={className} 
        viewBox="0 0 200 80" 
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <linearGradient id="blueGrad" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style={{ stopColor: '#2563EB', stopOpacity: 1 }} />
            <stop offset="50%" style={{ stopColor: '#3B82F6', stopOpacity: 1 }} />
            <stop offset="100%" style={{ stopColor: '#1E40AF', stopOpacity: 1 }} />
          </linearGradient>
          
          <linearGradient id="goldAccent" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style={{ stopColor: '#FCD34D', stopOpacity: 1 }} />
            <stop offset="100%" style={{ stopColor: '#F59E0B', stopOpacity: 1 }} />
          </linearGradient>
        </defs>
        
        <g transform="translate(40, 40)">
          <path 
            d="M -25 -15 L -15 15 L -5 -5 L 5 15 L 15 -5 L 25 15" 
            stroke="url(#blueGrad)" 
            strokeWidth="6" 
            fill="none" 
            strokeLinecap="round" 
            strokeLinejoin="round"
          />
          
          <g transform="translate(0, -20)">
            <path d="M -10 0 Q 0 -3 10 0" stroke="url(#goldAccent)" strokeWidth="3" fill="none"/>
            <path d="M -8 -5 Q 0 -8 8 -5" stroke="url(#goldAccent)" strokeWidth="2.5" fill="none" opacity="0.8"/>
            <path d="M -6 -10 Q 0 -13 6 -10" stroke="url(#goldAccent)" strokeWidth="2" fill="none" opacity="0.6"/>
          </g>
          
          {animated && (
            <circle cx="0" cy="-25" r="2" fill="url(#goldAccent)">
              <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite"/>
            </circle>
          )}
        </g>
        
        <g transform="translate(100, 40)">
          <text x="0" y="0" fontFamily="'Segoe UI', Arial, sans-serif" fontSize="28" fontWeight="700" fill="url(#blueGrad)">
            Writer
          </text>
          <text x="70" y="0" fontFamily="'Segoe UI', Arial, sans-serif" fontSize="28" fontWeight="300" fill="#1F2937">
            777
          </text>
          <text x="0" y="18" fontFamily="'Segoe UI', Arial, sans-serif" fontSize="10" fontWeight="400" fill="#6B7280" letterSpacing="1">
            AI ARTICLE GENERATOR
          </text>
        </g>
      </svg>
    );
  }

  // Icon only version
  if (variant === 'icon') {
    return (
      <svg 
        className={className} 
        viewBox="0 0 100 100" 
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <linearGradient id="mainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style={{ stopColor: '#3B82F6', stopOpacity: 1 }} />
            <stop offset="100%" style={{ stopColor: '#1E40AF', stopOpacity: 1 }} />
          </linearGradient>
          
          <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style={{ stopColor: '#FCD34D', stopOpacity: 1 }} />
            <stop offset="100%" style={{ stopColor: '#F59E0B', stopOpacity: 1 }} />
          </linearGradient>
          
          <filter id="glow">
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
        
        <rect width="100" height="100" rx="20" fill="url(#mainGradient)"/>
        
        <g transform="translate(50, 50)">
          <path 
            d="M -30 -20 L -18 20 L -9 -5 L 0 20 L 9 -5 L 18 20 L 30 -20" 
            stroke="white" 
            strokeWidth="5" 
            fill="none" 
            strokeLinecap="round" 
            strokeLinejoin="round"
            filter="url(#glow)"
          />
          
          <g transform="translate(20, -10)">
            <circle cx="0" cy="0" r="2.5" fill="url(#accentGradient)"/>
            <circle cx="5" cy="0" r="2.5" fill="url(#accentGradient)"/>
            <circle cx="10" cy="0" r="2.5" fill="url(#accentGradient)"/>
            <circle cx="10" cy="5" r="2" fill="url(#accentGradient)" opacity="0.8"/>
            <circle cx="10" cy="10" r="2" fill="url(#accentGradient)" opacity="0.6"/>
            <circle cx="10" cy="15" r="2" fill="url(#accentGradient)" opacity="0.4"/>
          </g>
          
          {animated && (
            <circle cx="0" cy="-28" r="3" fill="white">
              <animate attributeName="opacity" values="0.4;1;0.4" dur="2s" repeatCount="indefinite"/>
            </circle>
          )}
        </g>
      </svg>
    );
  }

  // Simple text version
  return (
    <div className={`flex items-center ${className}`}>
      <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
        Writer
      </span>
      <span className="text-2xl font-light text-gray-800 ml-1">777</span>
    </div>
  );
};

export default WriterLogo;