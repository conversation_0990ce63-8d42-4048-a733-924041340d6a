import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  UserIcon,
  CalendarIcon,
  ClockIcon,
  StarIcon,
  InformationCircleIcon,
  ExclamationTriangleIcon,
  PlayIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { formatDate, addHeadingIds } from '../../utils/articleUtils';
import { extractGameInfo, removeGameInfoFromContent, getDefaultGameInfo } from '../../utils/gameInfoParser';
import { getContentTypeImage, getContentTypeCardBackground } from '../../utils/imageUtils';

/**
 * Professional Game Guide Layout - Casino.org style, but SAFE
 */
const SimpleGameGuideLayout = ({ 
  article, 
  tableOfContents = [],
  estimatedReadTime = 5,
  contentRef,
  scrollToHeading
}) => {
  const { t } = useTranslation();
  const [heroBackgroundImage, setHeroBackgroundImage] = useState('');
  const [gameHeroImage, setGameHeroImage] = useState('');
  const [isMobileTocOpen, setIsMobileTocOpen] = useState(false);

  // Load dynamic images
  useEffect(() => {
    const loadImages = async () => {
      try {
        // Load game guide background image
        const backgroundImage = await getContentTypeImage('game_guide', 'background');
        setHeroBackgroundImage(backgroundImage);

        // Use article featured_image directly
        setGameHeroImage(article.featured_image);
      } catch (error) {
        console.warn('Failed to load dynamic images:', error);
      }
    };

    loadImages();
  }, [article]);

  // If no article, show loading
  if (!article) {
    return <div className="text-center py-10">{t('article.loading')}</div>;
  }

  // Extract safe values with defaults
  const title = article.title || 'Game Guide';
  const publishedAt = article.published_at || new Date().toISOString();
  const rawContent = article.content || '<p>No content available</p>';
  const excerpt = article.meta_description || article.excerpt || '';
  const contentTypeInfo = article.contentTypeInfo || { icon: '🎮', label: 'Game Guide', color: 'from-blue-500 to-cyan-500' };

  // Extract author from About the Author section if available, otherwise use default
  const extractAuthorFromContent = (content) => {
    // Try to extract author name from About the Author section
    const authorSectionMatch = content.match(/<h2[^>]*>About the Author<\/h2>([\s\S]*?)(?=<h2|$)/i);
    if (authorSectionMatch) {
      // Look for patterns like "I'm [Name]" or "My name is [Name]" or just extract first strong name
      const authorContent = authorSectionMatch[1];
      const nameMatches = [
        authorContent.match(/I'm\s+([^,\.\n<]+)/i),
        authorContent.match(/My name is\s+([^,\.\n<]+)/i),
        authorContent.match(/<strong[^>]*>([^<]+)<\/strong>/i),
        authorContent.match(/\*\*([^*]+)\*\*/),
      ];
      
      for (const match of nameMatches) {
        if (match && match[1] && match[1].trim()) {
          return match[1].trim();
        }
      }
    }
    return null;
  };

  const extractedAuthor = extractAuthorFromContent(rawContent);
  const author = extractedAuthor || article.author || 'Writer 777 AI';

  // Function to normalize and replace author content with consistent information
  const normalizeAuthorContent = (content, authorName) => {
    // Find About the Author section
    const authorSectionRegex = /<h2[^>]*>About the Author<\/h2>([\s\S]*?)(?=<h2|$)/i;
    const match = content.match(authorSectionRegex);
    
    if (match) {
      // Create standardized author section with avatar
      const standardAuthorSection = `
        <h2>About the Author</h2>
        <div style="display: flex; align-items: flex-start; gap: 1rem; margin: 2rem 0;">
          <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face" 
               alt="${authorName}" 
               style="width: 80px; height: 80px; border-radius: 50%; object-fit: cover; border: 3px solid #f59e0b;" />
          <div>
            <h3 style="margin: 0 0 0.5rem 0; color: #f1f5f9; font-size: 1.25rem;">${authorName}</h3>
            <p style="margin: 0; color: #cbd5e1; line-height: 1.6;">
              ${authorName} is a seasoned gaming expert with years of experience in the iGaming industry. 
              Specializing in slot games and casino reviews, ${authorName.split(' ')[0]} provides in-depth analysis 
              and strategic insights to help players make informed decisions. With a passion for responsible gaming 
              and player education, ${authorName.split(' ')[0]} delivers comprehensive guides that combine expert 
              knowledge with practical gaming advice.
            </p>
          </div>
        </div>
      `;
      
      // Replace the existing author section
      return content.replace(authorSectionRegex, standardAuthorSection);
    }
    
    return content;
  };

  // Extract game info from content or use stored game_info
  let gameInfo = null;
  let cleanContent = rawContent;

  // Try to get game info from article.game_info first (if stored in database)
  if (article.game_info) {
    try {
      gameInfo = typeof article.game_info === 'string' ? JSON.parse(article.game_info) : article.game_info;
    } catch (error) {
      console.error('Error parsing stored game_info:', error);
    }
  }

  // If no stored game info, try to extract from content
  if (!gameInfo) {
    gameInfo = extractGameInfo(rawContent);
    if (gameInfo) {
      // Remove the JSON block from content for display
      cleanContent = removeGameInfoFromContent(rawContent);
    }
  }

  // If still no game info, use defaults
  if (!gameInfo) {
    gameInfo = getDefaultGameInfo(title);
  }

  // Function to remove SEO information from content
  const removeSEOInfo = (content) => {
    // Remove SEO title, meta description, keywords, and tags sections
    const seoPatterns = [
      /## SEO Title[\s\S]*?(?=\n##|$)/gi,
      /## Meta Description[\s\S]*?(?=\n##|$)/gi,
      /## Focus Keywords[\s\S]*?(?=\n##|$)/gi,
      /## Tags[\s\S]*?(?=\n##|$)/gi,
      /<h2[^>]*>SEO Title<\/h2>[\s\S]*?(?=<h2|$)/gi,
      /<h2[^>]*>Meta Description<\/h2>[\s\S]*?(?=<h2|$)/gi,
      /<h2[^>]*>Focus Keywords<\/h2>[\s\S]*?(?=<h2|$)/gi,
      /<h2[^>]*>Tags<\/h2>[\s\S]*?(?=<h2|$)/gi,
      /\*\*SEO Title:\*\*[\s\S]*?(?=\n|$)/gi,
      /\*\*Meta Description:\*\*[\s\S]*?(?=\n|$)/gi,
      /\*\*Focus Keywords:\*\*[\s\S]*?(?=\n|$)/gi,
      /\*\*Tags:\*\*[\s\S]*?(?=\n|$)/gi
    ];
    
    let cleanedContent = content;
    seoPatterns.forEach(pattern => {
      cleanedContent = cleanedContent.replace(pattern, '');
    });
    
    // Remove any multiple consecutive line breaks
    cleanedContent = cleanedContent.replace(/\n{3,}/g, '\n\n');
    
    return cleanedContent.trim();
  };

  // Add IDs to headings for table of contents navigation and remove SEO info
  let processedContent = removeSEOInfo(cleanContent);
  // Normalize author information to be consistent
  processedContent = normalizeAuthorContent(processedContent, author);
  const content = tableOfContents && tableOfContents.length > 0 
    ? addHeadingIds(processedContent, tableOfContents)
    : processedContent;

  // Simple star rendering
  const renderStars = (rating = gameInfo?.rating || 4.2) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    
    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(<StarIconSolid key={i} className="w-5 h-5 text-yellow-400" />);
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <div key={i} className="relative w-5 h-5">
            <StarIcon className="w-5 h-5 text-gray-600 absolute" />
            <StarIconSolid className="w-5 h-5 text-yellow-400 absolute" style={{ clipPath: 'inset(0 50% 0 0)' }} />
          </div>
        );
      } else {
        stars.push(<StarIcon key={i} className="w-5 h-5 text-gray-600" />);
      }
    }
    return stars;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-indigo-900 overflow-x-hidden">
      {/* Header Section */}
      <div className="relative bg-gradient-to-r from-slate-900 via-slate-800 to-slate-900 text-white overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <img 
            src={heroBackgroundImage} 
            alt="Game guide background"
            className="w-full h-full object-cover opacity-60"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-slate-900/60 via-slate-800/50 to-slate-900/60"></div>
        </div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-12 py-8 sm:py-12 w-full">

          {/* Breadcrumb */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3 text-sm text-slate-300">
              <span className="hover:text-white transition-colors cursor-pointer">{t('nav.home')}</span>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span className="text-white font-medium">{t('contentTypes.game_guide')}</span>
            </div>
            
            {/* Mobile TOC Button */}
            <button
              onClick={() => setIsMobileTocOpen(true)}
              className="lg:hidden bg-white/10 hover:bg-white/20 text-white border border-white/20 hover:border-white/30 rounded-lg px-3 py-2 flex items-center space-x-2 text-sm font-medium transition-all duration-200"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
              <span>{t('article.tableOfContents')}</span>
            </button>
          </div>

          <div className="grid lg:grid-cols-4 gap-8 lg:gap-12 w-full">
            {/* Main Title Area */}
            <div className="lg:col-span-3 w-full max-w-full overflow-hidden">
              {/* Title */}
              <div className="mb-6">
                <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white leading-tight bg-gradient-to-r from-white to-slate-200 bg-clip-text text-transparent mb-4">
                  {title}
                </h1>
              </div>

              {/* Meta Info with Badge */}
              <div className="flex flex-wrap items-center gap-3 sm:gap-6 text-slate-300 text-sm sm:text-base mb-6">
                <div className={`inline-flex items-center space-x-2 px-4 py-2 rounded-full text-xs font-semibold bg-gradient-to-r ${contentTypeInfo.color} text-white shadow-lg backdrop-blur-sm`}>
                  <span className="text-sm">{contentTypeInfo.icon}</span>
                  <span className="uppercase tracking-wide hidden sm:inline">{contentTypeInfo.label}</span>
                  <span className="uppercase tracking-wide sm:hidden">GUIDE</span>
                </div>
                <div className="flex items-center space-x-2 bg-white/10 px-3 py-2 rounded-lg backdrop-blur-sm">
                  <UserIcon className="w-4 h-4" />
                  <span className="font-medium">By {author}</span>
                </div>
                <div className="flex items-center space-x-2 bg-white/10 px-3 py-2 rounded-lg backdrop-blur-sm">
                  <CalendarIcon className="w-4 h-4" />
                  <span className="hidden sm:inline">{formatDate(publishedAt)}</span>
                  <span className="sm:hidden">{new Date(publishedAt).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center space-x-2 bg-white/10 px-3 py-2 rounded-lg backdrop-blur-sm">
                  <ClockIcon className="w-4 h-4" />
                  <span>{estimatedReadTime} min read</span>
                </div>
              </div>

              {/* Rating */}
              <div className="flex items-center space-x-4 mb-4">
                <div className="flex items-center space-x-3 bg-white/10 px-4 py-3 rounded-xl backdrop-blur-sm">
                  <span className="text-2xl sm:text-3xl font-bold text-white">{gameInfo?.rating || 4.2}</span>
                  <div className="flex space-x-1">
                    {renderStars(gameInfo?.rating || 4.2)}
                  </div>
                </div>
                <span className="text-slate-300 text-base sm:text-lg font-medium">{t('gameGuide.overallRating')}</span>
              </div>

              {/* Excerpt */}
              {excerpt && (
                <div className="bg-white/10 border border-white/20 rounded-2xl p-4 sm:p-6 backdrop-blur-sm shadow-xl mb-4">
                  <p className="text-white text-base sm:text-lg leading-relaxed font-light">
                    {excerpt}
                  </p>
                </div>
              )}

              {/* Featured Game Image */}
              <div className="relative rounded-2xl overflow-hidden shadow-2xl group border border-white/10">
                <img 
                  src={gameHeroImage} 
                  alt={title}
                  className="w-full h-[250px] sm:h-[320px] lg:h-[400px] object-cover transition-transform duration-700 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent"></div>
                <div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6 lg:p-8">
                  <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-white mb-2 drop-shadow-2xl">{t('gameGuide.gameOverview')}</h2>
                  <p className="text-slate-200 text-sm sm:text-base lg:text-lg font-light drop-shadow-xl">{t('gameGuide.gameExperience')}</p>
                </div>
              </div>
            </div>

            {/* Game Info Card */}
            <div className="lg:col-span-1 w-full max-w-full">
              <div className="bg-white/15 border border-white/20 rounded-2xl p-4 sm:p-6 lg:p-8 backdrop-blur-md shadow-2xl sticky top-8 w-full max-w-full mx-auto sm:max-w-md lg:max-w-none">
                <h3 className="text-xl font-bold text-white mb-6 flex items-center">
                  <InformationCircleIcon className="w-6 h-6 mr-3" />
                  {t('gameGuide.gameStatistics')}
                </h3>

                {/* Key Stats */}
                <div className="space-y-5">
                  <div className="flex justify-between items-center py-2">
                    <span className="text-slate-300 text-sm font-medium">{t('gameGuide.provider')}</span>
                    <span className="text-white font-semibold">{gameInfo?.provider || t('gameGuide.unknownProvider')}</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-t border-white/10">
                    <span className="text-slate-300 text-sm font-medium">{t('gameGuide.rtp')}</span>
                    <span className="text-green-400 font-bold">{gameInfo?.rtp || '96.50%'}</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-t border-white/10">
                    <span className="text-slate-300 text-sm font-medium">{t('gameGuide.volatility')}</span>
                    <span className={`font-bold ${
                      gameInfo?.volatility === 'Low' ? 'text-green-400' :
                      gameInfo?.volatility === 'Medium' ? 'text-yellow-400' :
                      gameInfo?.volatility === 'High' ? 'text-orange-400' :
                      gameInfo?.volatility === 'Very High' ? 'text-red-400' :
                      'text-orange-400'
                    }`}>{gameInfo?.volatility || t('gameGuide.volatilityHigh')}</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-t border-white/10">
                    <span className="text-slate-300 text-sm font-medium">{t('gameGuide.betRange')}</span>
                    <span className="text-white font-semibold">{gameInfo?.minBet || '$0.20'} - {gameInfo?.maxBet || '$100'}</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-t border-white/10">
                    <span className="text-slate-300 text-sm font-medium">{t('gameGuide.maxWin')}</span>
                    <span className="text-yellow-400 font-bold text-lg">{gameInfo?.maxWin || '21,100x'}</span>
                  </div>
                  {gameInfo?.reels && gameInfo?.rows && (
                    <div className="flex justify-between items-center py-2 border-t border-white/10">
                      <span className="text-slate-300 text-sm font-medium">{t('gameGuide.grid')}</span>
                      <span className="text-white font-semibold">{gameInfo.reels}x{gameInfo.rows}</span>
                    </div>
                  )}
                  {gameInfo?.paylines && (
                    <div className="flex justify-between items-center py-2 border-t border-white/10">
                      <span className="text-slate-300 text-sm font-medium">{t('gameGuide.paylines')}</span>
                      <span className="text-white font-semibold">{gameInfo.paylines}</span>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="mt-8 space-y-4">
                  <button className="w-full bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center space-x-3 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                    <PlayIcon className="w-5 h-5" />
                    <span>{t('gameGuide.playDemo')}</span>
                  </button>
                  <button className="w-full bg-gradient-to-r from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                    {t('gameGuide.playForReal')}
                  </button>
                </div>

                {/* Responsible Gambling Notice */}
                <div className="mt-8 p-4 bg-orange-500/20 border border-orange-400/30 rounded-xl backdrop-blur-sm">
                  <div className="flex items-start space-x-3">
                    <ExclamationTriangleIcon className="w-5 h-5 text-orange-400 mt-0.5 flex-shrink-0" />
                    <div className="text-xs text-orange-200">
                      <strong className="block mb-2 text-sm">{t('gameGuide.playResponsibly')}</strong>
                      <p className="leading-relaxed">{t('gameGuide.responsibleGamblingNotice')}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-12 py-8 lg:py-12 w-full">
        <div className="grid lg:grid-cols-4 gap-8 lg:gap-12 w-full">
          {/* Left Column - Main Content */}
          <div className="lg:col-span-3 space-y-8 lg:space-y-16 w-full max-w-full">
            {/* Main Article Content */}
            <div className="glass-card-dark rounded-3xl px-4 py-8 sm:px-6 sm:py-10 lg:px-12 lg:py-16 shadow-2xl w-full max-w-full mx-auto">
              <div 
                ref={contentRef}
                className="article-content-wrapper prose prose-lg prose-slate prose-invert max-w-full w-full mx-auto"
                style={{
                  fontSize: '16px',
                  lineHeight: '1.8',
                  color: '#cbd5e1'
                }}
                dangerouslySetInnerHTML={{ 
                  __html: content || '<p>No content available</p>'
                }}
              />

              <style jsx global>{`
                .article-content-wrapper {
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif !important;
                  line-height: 1.8 !important;
                  color: #cbd5e1 !important;
                  max-width: none !important;
                }

                .article-content-wrapper :global(h1) {
                  font-size: 2.25rem;
                  font-weight: 700;
                  color: #f1f5f9;
                  margin: 4rem 0 2.5rem 0;
                  line-height: 1.2;
                }

                .article-content-wrapper :global(h2) {
                  font-size: 1.875rem;
                  font-weight: 600;
                  color: #f1f5f9;
                  margin: 4rem 0 2rem 0;
                  line-height: 1.3;
                  border-bottom: 2px solid #475569;
                  padding-bottom: 0.5rem;
                }

                .article-content-wrapper :global(h3) {
                  font-size: 1.5rem;
                  font-weight: 600;
                  color: #f1f5f9;
                  margin: 3.5rem 0 1.5rem 0;
                  line-height: 1.4;
                }

                .article-content-wrapper :global(h4) {
                  font-size: 1.25rem;
                  font-weight: 600;
                  color: #cbd5e1;
                  margin: 3rem 0 1.25rem 0;
                  line-height: 1.4;
                }

                .article-content-wrapper p {
                  font-size: 1rem !important;
                  line-height: 1.75 !important;
                  color: #cbd5e1 !important;
                  margin: 0 0 1.75rem 0 !important;
                  text-align: left !important;
                }

                .article-content-wrapper :global(ul),
                .article-content-wrapper :global(ol) {
                  margin: 1.75rem 0 !important;
                  padding-left: 2rem !important;
                  color: #cbd5e1 !important;
                }

                .article-content-wrapper :global(ul li) {
                  position: relative !important;
                  margin: 0.75rem 0 !important;
                  padding-left: 0.5rem !important;
                  color: #cbd5e1 !important;
                  list-style-type: disc !important;
                  display: list-item !important;
                }

                .article-content-wrapper :global(ol li) {
                  position: relative !important;
                  margin: 0.75rem 0 !important;
                  padding-left: 0.5rem !important;
                  color: #cbd5e1 !important;
                  list-style-type: decimal !important;
                  display: list-item !important;
                }

                .article-content-wrapper :global(ol) {
                  list-style-type: decimal !important;
                  list-style-position: outside !important;
                }

                .article-content-wrapper :global(ul) {
                  list-style-type: disc !important;
                  list-style-position: outside !important;
                }

                .article-content-wrapper h1 {
                  font-size: 2.25rem !important;
                  font-weight: 700 !important;
                  color: #f1f5f9 !important;
                  margin: 3rem 0 1.5rem 0 !important;
                  line-height: 1.25 !important;
                }

                .article-content-wrapper h2 {
                  font-size: 1.875rem !important;
                  font-weight: 600 !important;
                  color: #f1f5f9 !important;
                  margin: 3rem 0 1.25rem 0 !important;
                  line-height: 1.3 !important;
                  border-bottom: 2px solid #475569 !important;
                  padding-bottom: 0.5rem !important;
                }

                .article-content-wrapper h3 {
                  font-size: 1.5rem !important;
                  font-weight: 600 !important;
                  color: #f1f5f9 !important;
                  margin: 2.5rem 0 1rem 0 !important;
                  line-height: 1.4 !important;
                }

                .article-content-wrapper h4 {
                  font-size: 1.25rem !important;
                  font-weight: 600 !important;
                  color: #cbd5e1 !important;
                  margin: 2rem 0 0.75rem 0 !important;
                  line-height: 1.4 !important;
                }

                .article-content-wrapper p:first-child {
                  margin-top: 0 !important;
                }

                .article-content-wrapper h1:first-child,
                .article-content-wrapper h2:first-child,
                .article-content-wrapper h3:first-child,
                .article-content-wrapper h4:first-child {
                  margin-top: 0 !important;
                }

                .article-content-wrapper :global(strong) {
                  font-weight: 600;
                  color: #f1f5f9;
                }

                .article-content-wrapper :global(a) {
                  color: #60a5fa;
                  text-decoration: underline;
                  text-decoration-thickness: 1px;
                  text-underline-offset: 2px;
                }

                .article-content-wrapper :global(a:hover) {
                  color: #93c5fd;
                }

                .article-content-wrapper :global(ul),
                .article-content-wrapper :global(ol) {
                  margin: 2rem 0 2.5rem 0;
                  padding-left: 1.5rem;
                }

                .article-content-wrapper :global(li) {
                  font-size: 1rem;
                  line-height: 1.7;
                  color: #cbd5e1;
                  margin: 1rem 0;
                }

                .article-content-wrapper :global(blockquote) {
                  font-size: 1rem;
                  font-style: italic;
                  color: #94a3b8;
                  border-left: 4px solid #475569;
                  padding: 1rem 1.5rem;
                  margin: 3rem 0;
                  background: rgba(51, 65, 85, 0.3);
                  border-radius: 0 0.5rem 0.5rem 0;
                }

                .article-content-wrapper :global(table) {
                  margin: 3rem 0;
                  font-size: 0.875rem;
                  width: 100%;
                  border-collapse: collapse;
                  background: rgba(51, 65, 85, 0.3);
                  border-radius: 0.5rem;
                  overflow: hidden;
                }

                .article-content-wrapper :global(th) {
                  background: rgba(71, 85, 105, 0.5);
                  padding: 0.75rem;
                  font-weight: 600;
                  color: #f1f5f9;
                  text-align: left;
                  border-bottom: 1px solid #475569;
                }

                .article-content-wrapper :global(td) {
                  padding: 0.75rem;
                  color: #cbd5e1;
                  border-bottom: 1px solid #374151;
                }

                .article-content-wrapper :global(img) {
                  border-radius: 0.5rem;
                  margin: 3rem 0;
                  width: 100%;
                  height: auto;
                  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                }

                .article-content-wrapper :global(code) {
                  background-color: rgba(51, 65, 85, 0.5);
                  color: #fbbf24;
                  padding: 0.125rem 0.375rem;
                  border-radius: 0.25rem;
                  font-family: 'SF Mono', Monaco, Consolas, monospace;
                  font-size: 0.875em;
                }

                .article-content-wrapper :global(hr) {
                  border: none;
                  border-top: 1px solid #475569;
                  margin: 2rem 0;
                }
              `}</style>

              {/* Game Screenshots */}
              {gameInfo?.gameImages && gameInfo.gameImages.length > 0 && (
                <div className="mt-24 space-y-12">
                  <h3 className="text-4xl font-bold text-white mb-12 text-center">{t('gameGuide.gameScreenshots')}</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 md:gap-8 lg:gap-12 w-full max-w-full px-2 sm:px-0">
                    {gameInfo.gameImages.slice(0, 4).map((imageUrl, index) => (
                      <div key={index} className="relative rounded-2xl overflow-hidden shadow-xl group w-full max-w-full">
                        <img 
                          src={imageUrl} 
                          alt={`Game screenshot ${index + 1}`}
                          className="w-full h-60 sm:h-72 md:h-80 object-cover transition-transform duration-500 group-hover:scale-110"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <div className="absolute bottom-0 left-0 right-0 p-6 transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300">
                          <p className="text-white text-lg font-semibold">Screenshot {index + 1}</p>
                          <p className="text-slate-200 text-sm mt-2">Game interface preview</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

            </div>

            {/* Game Statistics Table */}
            <div className="glass-card-dark rounded-3xl p-4 sm:p-6 lg:p-12 shadow-2xl w-full max-w-full mx-auto">
              <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-white mb-6 lg:mb-10 text-center">{t('gameGuide.gameStatistics')}</h2>
              <div className="overflow-x-auto overflow-hidden rounded-2xl border border-slate-200">
                <table className="w-full min-w-full">
                  <thead>
                    <tr className="bg-gradient-to-r from-slate-700 to-slate-600">
                      <th className="px-3 sm:px-6 lg:px-8 py-3 sm:py-4 lg:py-5 text-left font-bold text-white text-sm sm:text-base lg:text-lg">Feature</th>
                      <th className="px-3 sm:px-6 lg:px-8 py-3 sm:py-4 lg:py-5 text-left font-bold text-white text-sm sm:text-base lg:text-lg">Details</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-slate-600">
                    <tr className="hover:bg-slate-700/50 transition-colors duration-200">
                      <td className="px-3 sm:px-6 lg:px-8 py-4 sm:py-5 font-semibold text-slate-300 text-sm sm:text-base lg:text-lg">Provider</td>
                      <td className="px-3 sm:px-6 lg:px-8 py-4 sm:py-5 text-white font-medium text-sm sm:text-base lg:text-lg">{gameInfo?.provider || 'Unknown Provider'}</td>
                    </tr>
                    <tr className="hover:bg-slate-700/50 transition-colors duration-200">
                      <td className="px-3 sm:px-6 lg:px-8 py-4 sm:py-5 font-semibold text-slate-300 text-sm sm:text-base lg:text-lg">RTP</td>
                      <td className="px-3 sm:px-6 lg:px-8 py-4 sm:py-5 text-emerald-400 font-bold text-sm sm:text-base lg:text-lg">{gameInfo?.rtp || '96.50%'}</td>
                    </tr>
                    <tr className="hover:bg-slate-700/50 transition-colors duration-200">
                      <td className="px-3 sm:px-6 lg:px-8 py-4 sm:py-5 font-semibold text-slate-300 text-sm sm:text-base lg:text-lg">Volatility</td>
                      <td className={`px-3 sm:px-6 lg:px-8 py-4 sm:py-5 font-bold text-sm sm:text-base lg:text-lg ${
                        gameInfo?.volatility === 'Low' ? 'text-green-400' :
                        gameInfo?.volatility === 'Medium' ? 'text-yellow-400' :
                        gameInfo?.volatility === 'High' ? 'text-orange-400' :
                        gameInfo?.volatility === 'Very High' ? 'text-red-400' :
                        'text-orange-400'
                      }`}>{gameInfo?.volatility || 'High'}</td>
                    </tr>
                    <tr className="hover:bg-slate-700/50 transition-colors duration-200">
                      <td className="px-3 sm:px-6 lg:px-8 py-4 sm:py-5 font-semibold text-slate-300 text-sm sm:text-base lg:text-lg">Min Bet</td>
                      <td className="px-3 sm:px-6 lg:px-8 py-4 sm:py-5 text-white font-medium text-sm sm:text-base lg:text-lg">{gameInfo?.minBet || '$0.20'}</td>
                    </tr>
                    <tr className="hover:bg-slate-700/50 transition-colors duration-200">
                      <td className="px-3 sm:px-6 lg:px-8 py-4 sm:py-5 font-semibold text-slate-300 text-sm sm:text-base lg:text-lg">Max Bet</td>
                      <td className="px-3 sm:px-6 lg:px-8 py-4 sm:py-5 text-white font-medium text-sm sm:text-base lg:text-lg">{gameInfo?.maxBet || '$100'}</td>
                    </tr>
                    <tr className="hover:bg-slate-700/50 transition-colors duration-200">
                      <td className="px-3 sm:px-6 lg:px-8 py-4 sm:py-5 font-semibold text-slate-300 text-sm sm:text-base lg:text-lg">Max Win</td>
                      <td className="px-3 sm:px-6 lg:px-8 py-4 sm:py-5 text-amber-400 font-bold text-base sm:text-lg lg:text-xl">{gameInfo?.maxWin || '21,100x'}</td>
                    </tr>
                    {gameInfo?.reels && (
                      <tr className="hover:bg-slate-700/50 transition-colors duration-200">
                        <td className="px-3 sm:px-6 lg:px-8 py-4 sm:py-5 font-semibold text-slate-300 text-sm sm:text-base lg:text-lg">Reels</td>
                        <td className="px-3 sm:px-6 lg:px-8 py-4 sm:py-5 text-white font-medium text-sm sm:text-base lg:text-lg">{gameInfo.reels}</td>
                      </tr>
                    )}
                    {gameInfo?.rows && (
                      <tr className="hover:bg-slate-700/50 transition-colors duration-200">
                        <td className="px-3 sm:px-6 lg:px-8 py-4 sm:py-5 font-semibold text-slate-300 text-sm sm:text-base lg:text-lg">Rows</td>
                        <td className="px-3 sm:px-6 lg:px-8 py-4 sm:py-5 text-white font-medium text-sm sm:text-base lg:text-lg">{gameInfo.rows}</td>
                      </tr>
                    )}
                    {gameInfo?.paylines && (
                      <tr className="hover:bg-slate-700/50 transition-colors duration-200">
                        <td className="px-3 sm:px-6 lg:px-8 py-4 sm:py-5 font-semibold text-slate-300 text-sm sm:text-base lg:text-lg">Paylines</td>
                        <td className="px-3 sm:px-6 lg:px-8 py-4 sm:py-5 text-white font-medium text-sm sm:text-base lg:text-lg">{gameInfo.paylines}</td>
                      </tr>
                    )}
                    {gameInfo?.bonusFeatures && gameInfo.bonusFeatures.length > 0 && (
                      <tr className="hover:bg-slate-700/50 transition-colors duration-200">
                        <td className="px-3 sm:px-6 lg:px-8 py-4 sm:py-5 font-semibold text-slate-300 text-sm sm:text-base lg:text-lg border-b-0">Bonus Features</td>
                        <td className="px-3 sm:px-6 lg:px-8 py-4 sm:py-5 text-white font-medium text-sm sm:text-base lg:text-lg border-b-0">{gameInfo.bonusFeatures.join(', ')}</td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Pros and Cons Section */}
            <div className="grid md:grid-cols-2 gap-6 md:gap-10 w-full max-w-full mx-auto px-2 sm:px-0">
              <div className="bg-gradient-to-br from-emerald-900/30 to-green-900/30 border border-emerald-500/30 rounded-3xl p-6 sm:p-8 md:p-10 shadow-xl backdrop-blur-sm w-full max-w-full">
                <h3 className="text-2xl font-bold text-emerald-400 mb-8 flex items-center">
                  <div className="p-3 bg-emerald-600 rounded-full mr-4">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  Pros
                </h3>
                <ul className="space-y-4">
                  {(gameInfo?.pros && gameInfo.pros.length > 0 ? gameInfo.pros : [
                    `High RTP of ${gameInfo?.rtp || '96.50%'}`,
                    'Exciting bonus features',
                    'Mobile optimized gameplay',
                    'Wide betting range'
                  ]).map((pro, index) => (
                    <li key={index} className="flex items-start group">
                      <div className="flex-shrink-0 w-8 h-8 bg-emerald-600 rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-200">
                        <span className="text-white font-bold text-sm">✓</span>
                      </div>
                      <span className="text-slate-200 text-lg font-medium">{pro}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="bg-gradient-to-br from-red-900/30 to-rose-900/30 border border-red-500/30 rounded-3xl p-6 sm:p-8 md:p-10 shadow-xl backdrop-blur-sm w-full max-w-full">
                <h3 className="text-2xl font-bold text-red-400 mb-8 flex items-center">
                  <div className="p-3 bg-red-600 rounded-full mr-4">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  Cons
                </h3>
                <ul className="space-y-4">
                  {(gameInfo?.cons && gameInfo.cons.length > 0 ? gameInfo.cons : [
                    `${gameInfo?.volatility || 'High'} volatility`,
                    'No progressive jackpot',
                    'Limited bonus buy options'
                  ]).map((con, index) => (
                    <li key={index} className="flex items-start group">
                      <div className="flex-shrink-0 w-8 h-8 bg-red-600 rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-200">
                        <span className="text-white font-bold text-sm">✗</span>
                      </div>
                      <span className="text-slate-200 text-lg font-medium">{con}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Where to Play Section */}
            <div className="glass-card-dark rounded-3xl p-4 sm:p-6 lg:p-12 shadow-2xl w-full max-w-full mx-auto">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
                {(gameInfo?.recommendedCasinos || getDefaultGameInfo().recommendedCasinos).slice(0, 3).map((casino, index) => {
                  const colors = [
                    'from-orange-500 to-red-600',
                    'from-blue-600 to-indigo-700', 
                    'from-emerald-600 to-green-700'
                  ];
                  const acronyms = casino.name.split(' ').map(word => word[0]).join('').substring(0, 2);
                  
                  return (
                    <div key={index} className="glass-card-dark rounded-2xl p-4 sm:p-6 lg:p-8 hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 group w-full max-w-full">
                      <div className="flex items-center mb-4 sm:mb-6">
                        <div className={`w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-br ${colors[index]} rounded-2xl flex items-center justify-center mr-3 sm:mr-4 group-hover:scale-110 transition-transform duration-300 flex-shrink-0`}>
                          <span className="text-white font-bold text-base sm:text-lg lg:text-xl">{acronyms}</span>
                        </div>
                        <div className="min-w-0 flex-1">
                          <h4 className="font-bold text-white text-sm sm:text-base lg:text-lg truncate">{casino.name}</h4>
                          <div className="flex items-center mt-1">
                            <span className="text-yellow-400 text-sm sm:text-base lg:text-lg">
                              {'★'.repeat(Math.floor(casino.rating || 4.5))}
                              {'☆'.repeat(5 - Math.floor(casino.rating || 4.5))}
                            </span>
                            <span className="text-xs sm:text-sm text-slate-300 ml-2 font-medium">{casino.rating || 4.5}/5</span>
                          </div>
                        </div>
                      </div>
                      <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-emerald-900/30 rounded-xl border border-emerald-500/30">
                        <p className="text-xs sm:text-sm text-emerald-400 font-semibold mb-2">{t('gameGuide.welcomeBonus')}</p>
                        <p className="text-lg sm:text-xl lg:text-2xl font-bold text-emerald-400">{casino.bonus || '100% up to $500'}</p>
                      </div>
                      <a href={casino.playUrl || '#'} className="block w-full bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white text-center py-3 sm:py-4 px-4 sm:px-6 rounded-xl font-bold text-sm sm:text-base lg:text-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                        {t('gameGuide.playNow')}
                      </a>
                    </div>
                  );
                })}
              </div>

            </div>

            {/* Editor's Review */}
            {gameInfo?.editorReview && (gameInfo.editorReview.authorName || gameInfo.editorReview.reviewText) && (
              <div className="bg-gradient-to-br from-indigo-900/30 to-blue-900/30 border border-indigo-500/30 rounded-3xl p-6 sm:p-8 lg:p-12 shadow-2xl backdrop-blur-sm w-full max-w-full mx-auto">
                <h3 className="text-3xl font-bold text-indigo-400 mb-10 text-center">{t('gameGuide.editorReview')}</h3>
                <div className="flex items-start mb-8">
                  <div className="relative mr-6">
                    <img 
                      src={gameInfo.editorReview.authorImage} 
                      alt="Editor" 
                      className="w-20 h-20 rounded-full border-4 border-white shadow-lg object-cover"
                    />
                    <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center border-2 border-white">
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-bold text-white text-xl mb-2">
                      {gameInfo.editorReview.authorName || 'John Smith'}
                    </h4>
                    <p className="text-indigo-400 font-semibold mb-3">
                      {gameInfo.editorReview.authorTitle || 'Senior Game Analyst'}
                    </p>
                    <div className="flex items-center mb-4">
                      <div className="flex items-center bg-white px-4 py-2 rounded-full border border-indigo-200">
                        <span className="text-yellow-400 text-xl">
                          {'★'.repeat(Math.floor(gameInfo.editorReview.rating || 4.2))}
                          {'☆'.repeat(5 - Math.floor(gameInfo.editorReview.rating || 4.2))}
                        </span>
                        <span className="text-slate-200 ml-3 font-bold text-lg">
                          {gameInfo.editorReview.rating || 4.2}/5
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-slate-800/70 rounded-2xl p-8 border border-indigo-500/30">
                  <blockquote className="text-slate-200 leading-relaxed text-lg italic">
                    {gameInfo.editorReview.reviewText || 
                    `"This game offers an exceptional gaming experience with its vibrant graphics and engaging gameplay. 
                    The high RTP and exciting bonus features make it a must-try for both beginners and experienced players. 
                    While the volatility might be challenging for some, the potential rewards make it worthwhile."`}
                  </blockquote>
                </div>
              </div>
            )}

            {/* FAQ Section */}
            <div className="glass-card-dark rounded-3xl p-6 sm:p-8 lg:p-12 shadow-2xl w-full max-w-full mx-auto">
              <h2 className="text-3xl font-bold text-white mb-10 text-center">{t('gameGuide.faq')}</h2>
              <div className="space-y-6">
                {(gameInfo?.faqItems && gameInfo.faqItems.length > 0 ? gameInfo.faqItems : [
                  {
                    question: "What is the RTP of this game?",
                    answer: `The game has an RTP (Return to Player) of ${gameInfo?.rtp || '96.50%'}, which is above the industry average and offers great value for players.`
                  },
                  {
                    question: "Can I play this game on mobile?",
                    answer: `${gameInfo?.mobileOptimized ? 'Yes, the game is fully optimized for mobile devices and can be played seamlessly on both iOS and Android platforms.' : 'This game may have limited mobile compatibility. Please check with your preferred casino for mobile support.'}`
                  },
                  {
                    question: "What is the minimum bet?",
                    answer: `The minimum bet starts at ${gameInfo?.minBet || '$0.20'}, making it accessible for players with different budgets and betting preferences.`
                  }
                ]).map((faq, index) => (
                  <details key={index} className="group glass-card-dark rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
                    <summary className="flex justify-between items-center cursor-pointer p-8 hover:bg-slate-700/50 transition-colors duration-200">
                      <span className="font-bold text-white text-lg">{faq.question}</span>
                      <div className="p-2 bg-slate-100 rounded-full group-open:bg-blue-100 transition-colors duration-200">
                        <svg className="w-5 h-5 text-slate-400 group-open:text-blue-400 group-open:rotate-180 transition-all duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </summary>
                    <div className="px-8 pb-8 pt-4 text-slate-200 text-lg leading-relaxed border-t border-slate-600">
                      {faq.answer}
                    </div>
                  </details>
                ))}
              </div>
            </div>
          </div>

          {/* Right Sidebar - Hidden on mobile */}
          <div className="lg:col-span-1 space-y-8 hidden lg:block">
            {/* Table of Contents */}
            <div className="glass-card-dark rounded-2xl p-6 sticky top-8 shadow-xl">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center">
                <div className="p-1.5 bg-slate-100 rounded-lg mr-2">
                  <svg className="w-4 h-4 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                  </svg>
                </div>
                {t('article.tableOfContents')}
              </h3>
              <nav className="space-y-1">
                {tableOfContents && tableOfContents.length > 0 ? (
                  tableOfContents.map((item, index) => (
                    <button
                      key={index}
                      onClick={() => scrollToHeading && scrollToHeading(item.id)}
                      className="flex items-center text-sm text-slate-300 hover:text-blue-400 hover:bg-blue-900/30 py-2 px-3 rounded-lg transition-all duration-200 group w-full text-left"
                    >
                      <span className="w-5 h-5 bg-slate-700 group-hover:bg-blue-500 text-slate-300 group-hover:text-white rounded-md mr-2.5 transition-all duration-200 flex items-center justify-center text-xs font-semibold">
                        {index + 1}
                      </span>
                      {item.text}
                    </button>
                  ))
                ) : (
                  <>
                    <a href="#overview" className="flex items-center text-sm text-slate-300 hover:text-blue-400 hover:bg-blue-900/30 py-2 px-3 rounded-lg transition-all duration-200 group">
                      <span className="w-5 h-5 bg-slate-700 group-hover:bg-blue-500 text-slate-300 group-hover:text-white rounded-md mr-2.5 transition-all duration-200 flex items-center justify-center text-xs font-semibold">
                        1
                      </span>
                      {t('gameGuide.gameOverview')}
                    </a>
                    <a href="#features" className="flex items-center text-sm text-slate-300 hover:text-blue-400 hover:bg-blue-900/30 py-2 px-3 rounded-lg transition-all duration-200 group">
                      <span className="w-5 h-5 bg-slate-700 group-hover:bg-blue-500 text-slate-300 group-hover:text-white rounded-md mr-2.5 transition-all duration-200 flex items-center justify-center text-xs font-semibold">
                        2
                      </span>
                      {t('gameGuide.keyFeatures')}
                    </a>
                    <a href="#how-to-play" className="flex items-center text-sm text-slate-300 hover:text-blue-400 hover:bg-blue-900/30 py-2 px-3 rounded-lg transition-all duration-200 group">
                      <span className="w-5 h-5 bg-slate-700 group-hover:bg-blue-500 text-slate-300 group-hover:text-white rounded-md mr-2.5 transition-all duration-200 flex items-center justify-center text-xs font-semibold">
                        3
                      </span>
                      {t('gameGuide.howToPlay')}
                    </a>
                    <a href="#where-to-play" className="flex items-center text-sm text-slate-300 hover:text-blue-400 hover:bg-blue-900/30 py-2 px-3 rounded-lg transition-all duration-200 group">
                      <span className="w-5 h-5 bg-slate-700 group-hover:bg-blue-500 text-slate-300 group-hover:text-white rounded-md mr-2.5 transition-all duration-200 flex items-center justify-center text-xs font-semibold">
                        4
                      </span>
                      {t('gameGuide.whereToPlay')}
                    </a>
                    <a href="#pros-cons" className="flex items-center text-sm text-slate-300 hover:text-blue-400 hover:bg-blue-900/30 py-2 px-3 rounded-lg transition-all duration-200 group">
                      <span className="w-5 h-5 bg-slate-700 group-hover:bg-blue-500 text-slate-300 group-hover:text-white rounded-md mr-2.5 transition-all duration-200 flex items-center justify-center text-xs font-semibold">
                        5
                      </span>
                      {t('gameGuide.prosAndCons')}
                    </a>
                    <a href="#faq" className="flex items-center text-sm text-slate-300 hover:text-blue-400 hover:bg-blue-900/30 py-2 px-3 rounded-lg transition-all duration-200 group">
                      <span className="w-5 h-5 bg-slate-700 group-hover:bg-blue-500 text-slate-300 group-hover:text-white rounded-md mr-2.5 transition-all duration-200 flex items-center justify-center text-xs font-semibold">
                        6
                      </span>
                      {t('gameGuide.faq')}
                    </a>
                  </>
                )}
              </nav>
            </div>
          </div>

        </div>
      </div>

      {/* Mobile TOC Sidebar */}
      {isMobileTocOpen && (
        <div className="lg:hidden fixed inset-0 z-50">
          {/* Backdrop */}
          <div 
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={() => setIsMobileTocOpen(false)}
          />
          
          {/* Sidebar */}
          <div className="absolute right-0 top-0 h-full w-80 max-w-[85vw] bg-slate-900 border-l border-slate-700 shadow-2xl transform transition-transform duration-300">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-slate-700">
              <h3 className="text-lg font-bold text-white flex items-center">
                <svg className="w-5 h-5 text-slate-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                </svg>
                {t('article.tableOfContents')}
              </h3>
              <button
                onClick={() => setIsMobileTocOpen(false)}
                className="p-2 hover:bg-slate-800 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            {/* TOC Content */}
            <div className="p-4 overflow-y-auto h-full pb-20">
              <nav className="space-y-1">
                {tableOfContents && tableOfContents.length > 0 ? (
                  tableOfContents.map((item, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        scrollToHeading && scrollToHeading(item.id);
                        setIsMobileTocOpen(false);
                      }}
                      className="flex items-center text-sm text-slate-300 hover:text-blue-400 hover:bg-blue-900/30 py-3 px-3 rounded-lg transition-all duration-200 group w-full text-left"
                    >
                      <span className="w-6 h-6 bg-slate-700 group-hover:bg-blue-500 text-slate-300 group-hover:text-white rounded-md mr-3 transition-all duration-200 flex items-center justify-center text-xs font-semibold flex-shrink-0">
                        {index + 1}
                      </span>
                      <span className="line-clamp-2">{item.text}</span>
                    </button>
                  ))
                ) : (
                  <>
                    <a 
                      href="#overview" 
                      onClick={() => setIsMobileTocOpen(false)}
                      className="flex items-center text-sm text-slate-300 hover:text-blue-400 hover:bg-blue-900/30 py-3 px-3 rounded-lg transition-all duration-200 group"
                    >
                      <span className="w-6 h-6 bg-slate-700 group-hover:bg-blue-500 text-slate-300 group-hover:text-white rounded-md mr-3 transition-all duration-200 flex items-center justify-center text-xs font-semibold">
                        1
                      </span>
                      {t('gameGuide.gameOverview')}
                    </a>
                    <a 
                      href="#features" 
                      onClick={() => setIsMobileTocOpen(false)}
                      className="flex items-center text-sm text-slate-300 hover:text-blue-400 hover:bg-blue-900/30 py-3 px-3 rounded-lg transition-all duration-200 group"
                    >
                      <span className="w-6 h-6 bg-slate-700 group-hover:bg-blue-500 text-slate-300 group-hover:text-white rounded-md mr-3 transition-all duration-200 flex items-center justify-center text-xs font-semibold">
                        2
                      </span>
                      {t('gameGuide.keyFeatures')}
                    </a>
                    <a 
                      href="#how-to-play" 
                      onClick={() => setIsMobileTocOpen(false)}
                      className="flex items-center text-sm text-slate-300 hover:text-blue-400 hover:bg-blue-900/30 py-3 px-3 rounded-lg transition-all duration-200 group"
                    >
                      <span className="w-6 h-6 bg-slate-700 group-hover:bg-blue-500 text-slate-300 group-hover:text-white rounded-md mr-3 transition-all duration-200 flex items-center justify-center text-xs font-semibold">
                        3
                      </span>
                      {t('gameGuide.howToPlay')}
                    </a>
                    <a 
                      href="#tips" 
                      onClick={() => setIsMobileTocOpen(false)}
                      className="flex items-center text-sm text-slate-300 hover:text-blue-400 hover:bg-blue-900/30 py-3 px-3 rounded-lg transition-all duration-200 group"
                    >
                      <span className="w-6 h-6 bg-slate-700 group-hover:bg-blue-500 text-slate-300 group-hover:text-white rounded-md mr-3 transition-all duration-200 flex items-center justify-center text-xs font-semibold">
                        4
                      </span>
                      {t('gameGuide.tipsAndStrategies')}
                    </a>
                    <a 
                      href="#pros-cons" 
                      onClick={() => setIsMobileTocOpen(false)}
                      className="flex items-center text-sm text-slate-300 hover:text-blue-400 hover:bg-blue-900/30 py-3 px-3 rounded-lg transition-all duration-200 group"
                    >
                      <span className="w-6 h-6 bg-slate-700 group-hover:bg-blue-500 text-slate-300 group-hover:text-white rounded-md mr-3 transition-all duration-200 flex items-center justify-center text-xs font-semibold">
                        5
                      </span>
                      {t('gameGuide.prosAndCons')}
                    </a>
                    <a 
                      href="#faq" 
                      onClick={() => setIsMobileTocOpen(false)}
                      className="flex items-center text-sm text-slate-300 hover:text-blue-400 hover:bg-blue-900/30 py-3 px-3 rounded-lg transition-all duration-200 group"
                    >
                      <span className="w-6 h-6 bg-slate-700 group-hover:bg-blue-500 text-slate-300 group-hover:text-white rounded-md mr-3 transition-all duration-200 flex items-center justify-center text-xs font-semibold">
                        6
                      </span>
                      {t('gameGuide.faq')}
                    </a>
                  </>
                )}
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleGameGuideLayout;