import React from 'react';
import {
  UserIcon,
  CalendarIcon,
  ClockIcon,
  TagIcon,
  ListBulletIcon,
  StarIcon,
  InformationCircleIcon,
  ExclamationTriangleIcon,
  PlayIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { formatDate, addHeadingIds } from '../../utils/articleUtils';
import TagsDisplay from '../common/TagsDisplay';

/**
 * Default Article Layout - Base layout for all article types
 */
const DefaultArticleLayout = ({ 
  article, 
  tableOfContents, 
  estimatedReadTime, 
  contentRef,
  scrollToHeading,
  showTableOfContents 
}) => {
  // Guard against undefined article
  if (!article) {
    console.error('DefaultArticleLayout - Article is undefined');
    return (
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <p className="text-cyan-400">Loading article...</p>
        </div>
      </div>
    );
  }

  // Check if this is a game guide for special styling
  const isGameGuide = article.content_type === 'game_guide';

  // Simple star rendering for game guides
  const renderStars = (rating = 4.2) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    
    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(<StarIconSolid key={i} className="w-5 h-5 text-yellow-400" />);
      } else {
        stars.push(<StarIcon key={i} className="w-5 h-5 text-gray-600" />);
      }
    }
    return stars;
  };

  // Game Guide Special Layout
  if (isGameGuide) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Game Guide Header */}
        <div className="mb-8">
          {/* Breadcrumb */}
          <div className="flex items-center space-x-2 text-sm text-slate-400 mb-4">
            <span>Home</span>
            <span>/</span>
            <span>Game Guides</span>
            <span>/</span>
            <span className="text-white truncate">{article.title}</span>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Title Area */}
            <div className="lg:col-span-2">
              {/* Content Type Badge */}
              <div className="mb-4">
                <div className={`inline-flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r ${article.contentTypeInfo?.color || 'from-blue-500 to-cyan-500'} text-white`}>
                  <span>{article.contentTypeInfo?.icon || '🎮'}</span>
                  <span>{article.contentTypeInfo?.label || 'Game Guide'}</span>
                </div>
              </div>

              {/* Title */}
              <h1 className="text-3xl lg:text-4xl font-bold text-white mb-4 leading-tight">
                {article.title}
              </h1>

              {/* Meta Info */}
              <div className="flex flex-wrap items-center gap-6 text-slate-300 text-sm mb-6">
                <div className="flex items-center space-x-1">
                  <UserIcon className="w-4 h-4" />
                  <span>By {article.author}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <CalendarIcon className="w-4 h-4" />
                  <span>{formatDate(article.published_at)}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <ClockIcon className="w-4 h-4" />
                  <span>{estimatedReadTime} min read</span>
                </div>
              </div>

              {/* Rating */}
              <div className="flex items-center space-x-4 mb-6">
                <div className="flex items-center space-x-2">
                  <span className="text-2xl font-bold text-white">4.2</span>
                  <div className="flex space-x-1">
                    {renderStars(4.2)}
                  </div>
                </div>
                <span className="text-slate-400 text-sm">Overall Rating</span>
              </div>

              {/* Excerpt */}
              {article.excerpt && (
                <div className="glass-card-dark rounded-lg p-4">
                  <p className="text-slate-200 leading-relaxed">
                    {article.excerpt}
                  </p>
                </div>
              )}
            </div>

            {/* Game Info Card */}
            <div className="lg:col-span-1">
              <div className="glass-card-dark rounded-xl p-6 sticky top-6">
                <h3 className="text-lg font-bold text-white mb-4 flex items-center">
                  <InformationCircleIcon className="w-5 h-5 mr-2" />
                  Game Information
                </h3>

                {/* Key Stats */}
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-slate-400 text-sm">Provider</span>
                    <span className="text-white font-semibold">Pragmatic Play</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-400 text-sm">RTP</span>
                    <span className="text-green-400 font-semibold">96.50%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-400 text-sm">Volatility</span>
                    <span className="text-red-400 font-semibold">High</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-400 text-sm">Bet Range</span>
                    <span className="text-white font-semibold">$0.20 - $100</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-400 text-sm">Max Win</span>
                    <span className="text-slate-300 font-bold">21,100x</span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="mt-6 space-y-3">
                  <button className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-bold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2">
                    <PlayIcon className="w-5 h-5" />
                    <span>Play Demo</span>
                  </button>
                  <button className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-bold py-3 px-4 rounded-lg transition-all duration-200">
                    Play for Real Money
                  </button>
                </div>

                {/* Responsible Gambling Notice */}
                <div className="mt-6 p-3 bg-orange-900/30 border border-orange-500/50 rounded-lg">
                  <div className="flex items-start space-x-2">
                    <ExclamationTriangleIcon className="w-4 h-4 text-orange-400 mt-0.5 flex-shrink-0" />
                    <div className="text-xs text-orange-200">
                      <strong className="block mb-1">Play Responsibly</strong>
                      <p>18+ only. Gambling can be addictive. Please play responsibly.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="grid lg:grid-cols-4 gap-8">
          {/* Article Content */}
          <div className="lg:col-span-3">
            <article className="glass-card-dark rounded-xl p-8">
              <div 
                ref={contentRef}
                className="prose prose-lg prose-invert max-w-none 
                  prose-headings:text-slate-400 
                  prose-p:text-slate-200 prose-p:leading-relaxed
                  prose-a:text-slate-300 prose-a:hover:text-purple-100
                  prose-strong:text-purple-100
                  prose-ul:text-slate-200 
                  prose-ol:text-slate-200
                  prose-li:text-slate-200
                  prose-blockquote:text-slate-300 prose-blockquote:border-slate-500
                  prose-code:text-purple-100 prose-code:bg-slate-800
                  prose-table:text-slate-200"
                dangerouslySetInnerHTML={{ __html: addHeadingIds(article.content || '') }}
              />
            </article>

            {/* Tags */}
            <div className="mt-8">
              <TagsDisplay 
                tags={article.tags}
                title="Related Topics"
                variant="default"
                className="glass-card-dark rounded-xl"
              />
            </div>
          </div>

          {/* Table of Contents Sidebar */}
          {showTableOfContents && tableOfContents && Array.isArray(tableOfContents) && tableOfContents.length > 0 && (
            <div className="lg:col-span-1">
              <div className="glass-card-dark rounded-xl p-6 sticky top-6">
                <h3 className="text-lg font-bold text-white mb-4 flex items-center">
                  <ListBulletIcon className="w-5 h-5 mr-2" />
                  Table of Contents
                </h3>
                <nav className="space-y-1">
                  {tableOfContents.map((item, index) => (
                    <button
                      key={index}
                      onClick={() => scrollToHeading && item.id && scrollToHeading(item.id)}
                      className={`block w-full text-left py-2 px-3 rounded-lg transition-colors text-sm hover:bg-cyan-500/20 hover:text-white ${
                        (item.level || 1) === 1 ? 'font-semibold text-slate-200' :
                        (item.level || 1) === 2 ? 'ml-3 text-slate-300' :
                        'ml-6 text-slate-400'
                      }`}
                    >
                      {item.text || 'Section'}
                    </button>
                  ))}
                </nav>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Default Layout for other content types
  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Mobile Table of Contents */}
      {showTableOfContents && tableOfContents && Array.isArray(tableOfContents) && tableOfContents.length > 0 && (
        <div className="lg:hidden mb-8">
          <div className="glass-card-dark rounded-xl p-6">
            <h3 className="text-lg font-semibold text-slate-400 mb-4 flex items-center">
              <ListBulletIcon className="w-5 h-5 mr-2" />
              Table of Contents
            </h3>
            <nav className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {tableOfContents.map((item, index) => (
                <button
                  key={index}
                  onClick={() => scrollToHeading(item.id)}
                  className={`text-left text-sm text-slate-300 hover:text-slate-200 transition-colors py-2 px-3 rounded-lg hover:bg-cyan-500/10 ${
                    item.level > 2 ? 'pl-6' : ''
                  } ${item.level > 3 ? 'pl-9' : ''}`}
                >
                  {item.text}
                </button>
              ))}
            </nav>
          </div>
        </div>
      )}

      <div className="flex gap-8">
        {/* Desktop Table of Contents Sidebar */}
        {showTableOfContents && tableOfContents && Array.isArray(tableOfContents) && tableOfContents.length > 0 && (
          <aside className="hidden lg:block w-64 flex-shrink-0">
            <div className="sticky top-24 glass-card-dark rounded-xl p-6">
              <h3 className="text-lg font-semibold text-slate-400 mb-4 flex items-center">
                <ListBulletIcon className="w-5 h-5 mr-2" />
                Table of Contents
              </h3>
              <nav className="space-y-2">
                {tableOfContents.map((item, index) => (
                  <button
                    key={index}
                    onClick={() => scrollToHeading(item.id)}
                    className={`block w-full text-left text-sm text-slate-300 hover:text-slate-200 transition-colors py-1 px-2 rounded hover:bg-cyan-500/10 ${
                      item.level > 2 ? 'pl-6' : ''
                    } ${item.level > 3 ? 'pl-10' : ''}`}
                  >
                    {item.text}
                  </button>
                ))}
              </nav>
            </div>
          </aside>
        )}

        {/* Main Article Content */}
        <article className="flex-1 min-w-0">
          {/* Article Header */}
          <header className="mb-12">
            {/* Content Type Badge */}
            <div className="mb-6">
              <div className={`inline-flex items-center space-x-3 px-6 py-3 rounded-full text-base font-semibold bg-gradient-to-r ${article.contentTypeInfo?.color || 'from-blue-500 to-cyan-600'} text-white shadow-lg`}>
                <span className="text-xl">{article.contentTypeInfo?.icon || '📄'}</span>
                <span>{article.contentTypeInfo?.label || 'Article'}</span>
              </div>
            </div>

            {/* Title */}
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-slate-400 mb-8 leading-tight">
              {article.title}
            </h1>

            {/* Meta Information */}
            <div className="flex flex-wrap items-center gap-8 text-slate-300 text-base mb-6">
              <div className="flex items-center space-x-2">
                <UserIcon className="w-5 h-5" />
                <span className="font-medium">{article.author}</span>
              </div>
              <div className="flex items-center space-x-2">
                <CalendarIcon className="w-5 h-5" />
                <span>{formatDate(article.published_at)}</span>
              </div>
              <div className="flex items-center space-x-2">
                <ClockIcon className="w-5 h-5" />
                <span>{estimatedReadTime} min read</span>
              </div>
            </div>

            {/* Excerpt */}
            {article.excerpt && (
              <div className="glass-card-dark rounded-xl p-6">
                <p className="text-xl text-slate-200 leading-relaxed font-medium">
                  {article.excerpt}
                </p>
              </div>
            )}
          </header>

          {/* Article Content */}
          <div className="glass-card-dark rounded-xl p-10">
            <div 
              ref={contentRef}
              className="prose prose-xl max-w-none text-purple-100 article-content"
              dangerouslySetInnerHTML={{ 
                __html: addHeadingIds(article.content || '', tableOfContents)
              }}
              style={{
                '--tw-prose-body': 'rgb(196 181 253 / 0.9)',
                '--tw-prose-headings': 'rgb(196 181 253)',
                '--tw-prose-links': 'rgb(196 181 253)',
                '--tw-prose-bold': 'rgb(196 181 253)',
                '--tw-prose-counters': 'rgb(196 181 253 / 0.7)',
                '--tw-prose-bullets': 'rgb(196 181 253 / 0.7)',
                '--tw-prose-hr': 'rgb(196 181 253 / 0.4)',
                '--tw-prose-quotes': 'rgb(196 181 253 / 0.9)',
                '--tw-prose-quote-borders': 'rgb(196 181 253 / 0.5)',
                '--tw-prose-captions': 'rgb(196 181 253 / 0.7)',
                '--tw-prose-code': 'rgb(196 181 253)',
                '--tw-prose-pre-code': 'rgb(196 181 253 / 0.9)',
                '--tw-prose-pre-bg': 'rgb(15 23 42 / 0.8)',
                '--tw-prose-th-borders': 'rgb(196 181 253 / 0.4)',
                '--tw-prose-td-borders': 'rgb(196 181 253 / 0.3)',
                lineHeight: '1.8',
                fontSize: '1.125rem'
              }}
            />
          </div>

          {/* Tags Section */}
          {article.tags && Array.isArray(article.tags) && article.tags.length > 0 && (
            <div className="mt-12">
              <div className="glass-card-dark rounded-xl p-6">
                <div className="flex items-center space-x-3 mb-6">
                  <TagIcon className="w-6 h-6 text-slate-400" />
                  <span className="text-slate-400 font-semibold text-lg">Article Tags</span>
                </div>
                <div className="flex flex-wrap gap-3">
                  {article.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-4 py-2 bg-gradient-to-r from-purple-500/20 to-purple-400/20 text-slate-200 text-base rounded-full border border-slate-500/40 hover:border-purple-400/60 transition-colors font-medium"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}
        </article>
      </div>
    </div>
  );
};

export default DefaultArticleLayout;
