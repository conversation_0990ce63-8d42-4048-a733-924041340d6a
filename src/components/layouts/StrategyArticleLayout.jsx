import React, { useState, useEffect } from 'react';
import {
  UserIcon,
  CalendarIcon,
  ClockIcon,
  TagIcon,
  ListBulletIcon,
  ChartBarIcon,
  CalculatorIcon,
  LightBulbIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { formatDate, addHeadingIds } from '../../utils/articleUtils';

/**
 * Strategy Article Layout - Specialized layout for strategy articles with data focus
 */
const StrategyArticleLayout = ({ 
  article, 
  tableOfContents, 
  estimatedReadTime, 
  contentRef,
  scrollToHeading,
  showTableOfContents 
}) => {
  const [keyMetrics, setKeyMetrics] = useState([]);
  const [strategyLevel, setStrategyLevel] = useState('Intermediate');

  useEffect(() => {
    // Extract key metrics and strategy level from content
    if (article?.content) {
      const metrics = extractKeyMetrics(article.content);
      setKeyMetrics(metrics);
      
      const level = extractStrategyLevel(article.content);
      setStrategyLevel(level);
    }
  }, [article?.content]);

  // Guard against undefined article
  if (!article) {
    console.error('StrategyArticleLayout - Article is undefined');
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <p className="text-casino-gold-400">Loading strategy article...</p>
        </div>
      </div>
    );
  }

  const extractKeyMetrics = (content) => {
    // Extract numerical data and percentages from content
    const percentageMatches = content.match(/(\d+(?:\.\d+)?%)/g) || [];
    const ratioMatches = content.match(/(\d+(?:\.\d+)?:\d+(?:\.\d+)?)/g) || [];
    const oddsMatches = content.match(/(\d+(?:\.\d+)?\/\d+(?:\.\d+)?)/g) || [];
    
    const metrics = [];
    
    // Add some default metrics for demonstration
    if (percentageMatches.length > 0) {
      metrics.push({ label: 'Win Rate', value: percentageMatches[0], type: 'percentage' });
    }
    if (ratioMatches.length > 0) {
      metrics.push({ label: 'Risk Ratio', value: ratioMatches[0], type: 'ratio' });
    }
    if (oddsMatches.length > 0) {
      metrics.push({ label: 'Odds', value: oddsMatches[0], type: 'odds' });
    }
    
    // Default metrics if none found
    if (metrics.length === 0) {
      metrics.push(
        { label: 'Expected Value', value: '+2.3%', type: 'percentage' },
        { label: 'House Edge', value: '1.4%', type: 'percentage' },
        { label: 'Volatility', value: 'Medium', type: 'text' },
        { label: 'Bankroll Req.', value: '100x', type: 'multiplier' }
      );
    }
    
    return metrics;
  };

  const extractStrategyLevel = (content) => {
    const lowerContent = content.toLowerCase();
    if (lowerContent.includes('advanced') || lowerContent.includes('expert') || lowerContent.includes('professional')) {
      return 'Advanced';
    }
    if (lowerContent.includes('beginner') || lowerContent.includes('basic') || lowerContent.includes('simple')) {
      return 'Beginner';
    }
    return 'Intermediate';
  };

  const getStrategyColor = (level) => {
    const colors = {
      'Beginner': 'from-green-500 to-emerald-500',
      'Intermediate': 'from-yellow-500 to-orange-500',
      'Advanced': 'from-red-500 to-pink-500'
    };
    return colors[level] || colors['Intermediate'];
  };

  const getMetricIcon = (type) => {
    switch (type) {
      case 'percentage': return '📊';
      case 'ratio': return '⚖️';
      case 'odds': return '🎯';
      case 'multiplier': return '💰';
      default: return '📈';
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Strategy Article Header */}
      <div className="mb-12">
        <div className="grid lg:grid-cols-4 gap-8">
          {/* Main Header Info */}
          <div className="lg:col-span-3">
            {/* Content Type Badge */}
            <div className="mb-6">
              <div className={`inline-flex items-center space-x-3 px-6 py-3 rounded-full text-base font-semibold bg-gradient-to-r ${article.contentTypeInfo?.color || 'from-orange-500 to-red-500'} text-white shadow-lg`}>
                <span className="text-xl">{article.contentTypeInfo?.icon || '♠️'}</span>
                <span>{article.contentTypeInfo?.label || 'Strategy Guide'}</span>
              </div>
            </div>

            {/* Title */}
            <h1 className="text-4xl md:text-5xl font-bold text-casino-gold-400 mb-6 leading-tight">
              {article.title}
            </h1>

            {/* Meta Information */}
            <div className="flex flex-wrap items-center gap-6 text-casino-gold-300 text-base mb-6">
              <div className="flex items-center space-x-2">
                <UserIcon className="w-5 h-5" />
                <span className="font-medium">{article.author}</span>
              </div>
              <div className="flex items-center space-x-2">
                <CalendarIcon className="w-5 h-5" />
                <span>{formatDate(article.published_at)}</span>
              </div>
              <div className="flex items-center space-x-2">
                <ClockIcon className="w-5 h-5" />
                <span>{estimatedReadTime} min read</span>
              </div>
            </div>

            {/* Excerpt */}
            {article.excerpt && (
              <div className="casino-card rounded-xl p-6 bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-500/30">
                <p className="text-xl text-casino-gold-200 leading-relaxed font-medium">
                  {article.excerpt}
                </p>
              </div>
            )}
          </div>

          {/* Strategy Metrics Card */}
          <div className="lg:col-span-1">
            <div className="casino-card rounded-xl p-6 chip-shadow bg-gradient-to-br from-green-900/20 to-emerald-900/20 border border-green-500/30 sticky top-24">
              <div className="text-center mb-6">
                <div className="text-4xl mb-3">📊</div>
                <h3 className="text-xl font-bold text-casino-gold-400 mb-2">Strategy Analysis</h3>
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-gradient-to-r ${getStrategyColor(strategyLevel)} text-white`}>
                  {strategyLevel} Level
                </div>
              </div>

              {/* Key Metrics */}
              <div className="space-y-4 mb-6">
                <h4 className="text-lg font-semibold text-casino-gold-400 mb-3 flex items-center">
                  <ChartBarIcon className="w-5 h-5 mr-2" />
                  Key Metrics
                </h4>
                {keyMetrics.map((metric, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-casino-dark-700/50 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{getMetricIcon(metric.type)}</span>
                      <span className="text-sm text-casino-gold-300">{metric.label}</span>
                    </div>
                    <span className="text-sm font-bold text-casino-gold-200">
                      {metric.value}
                    </span>
                  </div>
                ))}
              </div>

              {/* Strategy Tips */}
              <div className="pt-6 border-t border-casino-gold-500/30">
                <h4 className="text-lg font-semibold text-casino-gold-400 mb-3 flex items-center">
                  <LightBulbIcon className="w-5 h-5 mr-2" />
                  Key Points
                </h4>
                <div className="space-y-2 text-sm text-casino-gold-300">
                  <p>• Mathematical approach required</p>
                  <p>• Bankroll management essential</p>
                  <p>• Practice before real money</p>
                  <p>• Understand variance impact</p>
                </div>
              </div>

              {/* Risk Warning */}
              <div className="mt-6 pt-6 border-t border-casino-gold-500/30">
                <div className="flex items-center space-x-2 mb-3">
                  <ExclamationTriangleIcon className="w-5 h-5 text-yellow-400" />
                  <h4 className="text-lg font-semibold text-yellow-400">Risk Notice</h4>
                </div>
                <p className="text-xs text-casino-gold-300">
                  All strategies carry risk. Past performance doesn't guarantee future results. 
                  Only gamble what you can afford to lose.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex gap-8">
        {/* Desktop Table of Contents Sidebar */}
        {showTableOfContents && tableOfContents.length > 0 && (
          <aside className="hidden lg:block w-64 flex-shrink-0">
            <div className="sticky top-24 casino-card rounded-xl p-6 chip-shadow">
              <h3 className="text-lg font-semibold text-casino-gold-400 mb-4 flex items-center">
                <ListBulletIcon className="w-5 h-5 mr-2" />
                Strategy Sections
              </h3>
              <nav className="space-y-2">
                {tableOfContents.map((item, index) => (
                  <button
                    key={index}
                    onClick={() => scrollToHeading(item.id)}
                    className={`block w-full text-left text-sm text-casino-gold-300 hover:text-casino-gold-200 transition-colors py-1 px-2 rounded hover:bg-casino-gold-500/10 ${
                      item.level > 2 ? 'pl-6' : ''
                    } ${item.level > 3 ? 'pl-10' : ''}`}
                  >
                    {item.text}
                  </button>
                ))}
              </nav>
            </div>
          </aside>
        )}

        {/* Article Content */}
        <article className="flex-1 min-w-0">
          <div className="casino-card rounded-xl p-10 chip-shadow strategy-article-content">
            <div 
              ref={contentRef}
              className="prose prose-xl max-w-none text-casino-gold-100 article-content"
              dangerouslySetInnerHTML={{ 
                __html: addHeadingIds(article.content || '', tableOfContents)
              }}
              style={{
                '--tw-prose-body': 'rgb(251 191 36 / 0.9)',
                '--tw-prose-headings': 'rgb(251 191 36)',
                '--tw-prose-links': 'rgb(251 191 36)',
                '--tw-prose-bold': 'rgb(251 191 36)',
                '--tw-prose-counters': 'rgb(251 191 36 / 0.7)',
                '--tw-prose-bullets': 'rgb(251 191 36 / 0.7)',
                '--tw-prose-hr': 'rgb(251 191 36 / 0.4)',
                '--tw-prose-quotes': 'rgb(251 191 36 / 0.9)',
                '--tw-prose-quote-borders': 'rgb(251 191 36 / 0.5)',
                '--tw-prose-captions': 'rgb(251 191 36 / 0.7)',
                '--tw-prose-code': 'rgb(251 191 36)',
                '--tw-prose-pre-code': 'rgb(251 191 36 / 0.9)',
                '--tw-prose-pre-bg': 'rgb(15 23 42 / 0.8)',
                '--tw-prose-th-borders': 'rgb(251 191 36 / 0.4)',
                '--tw-prose-td-borders': 'rgb(251 191 36 / 0.3)',
                lineHeight: '1.8',
                fontSize: '1.125rem'
              }}
            />
          </div>

          {/* Tags Section */}
          {article.tags && article.tags.length > 0 && (
            <div className="mt-12">
              <div className="casino-card rounded-xl p-6 chip-shadow">
                <div className="flex items-center space-x-3 mb-6">
                  <TagIcon className="w-6 h-6 text-casino-gold-400" />
                  <span className="text-casino-gold-400 font-semibold text-lg">Strategy Tags</span>
                </div>
                <div className="flex flex-wrap gap-3">
                  {article.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-4 py-2 bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-casino-gold-200 text-base rounded-full border border-green-500/40 hover:border-green-400/60 transition-colors font-medium"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}
        </article>
      </div>
    </div>
  );
};

export default StrategyArticleLayout;
