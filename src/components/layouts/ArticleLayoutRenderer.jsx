import React from 'react';
import CasinoReviewLayout from './CasinoReviewLayout';
import GameGuideLayout from './GameGuideLayout';
import StrategyArticleLayout from './StrategyArticleLayout';
import BrandCopyLayout from './BrandCopyLayout';
import IndustryNewsLayout from './IndustryNewsLayout';
import SportsBettingLayout from './SportsBettingLayout';
import DefaultArticleLayout from './DefaultArticleLayout';
import SimpleGameGuideLayout from './SimpleGameGuideLayout';

/**
 * Article Layout Renderer - Routes articles to appropriate layout based on content type
 */
const ArticleLayoutRenderer = ({
  article,
  tableOfContents,
  estimatedReadTime,
  readingProgress,
  contentRef,
  scrollToHeading,
  showTableOfContents,
  setShowTableOfContents
}) => {
  const getLayoutComponent = (contentType) => {
    const layoutMap = {
      'casino_review': CasinoReviewLayout,
      'game_guide': SimpleGameGuideLayout, // Using SIMPLE layout - GUARANTEED TO WORK
      'strategy_article': StrategyArticleLayout,
      'brand_copy': BrandCopyLayout,
      'industry_news': IndustryNewsLayout,
      'sports_betting': SportsBettingLayout
    };

    return layoutMap[contentType] || DefaultArticleLayout;
  };

  // Guard against undefined article
  if (!article) {
    console.error('ArticleLayoutRenderer - Article is undefined or null');
    return (
      <div className="text-center py-10">
        <p className="text-purple-400">Loading article...</p>
      </div>
    );
  }

  const LayoutComponent = getLayoutComponent(article.content_type);

  return (
    <LayoutComponent
      article={article}
      tableOfContents={tableOfContents}
      estimatedReadTime={estimatedReadTime}
      readingProgress={readingProgress}
      contentRef={contentRef}
      scrollToHeading={scrollToHeading}
      showTableOfContents={showTableOfContents}
      setShowTableOfContents={setShowTableOfContents}
    />
  );
};

export default ArticleLayoutRenderer;
