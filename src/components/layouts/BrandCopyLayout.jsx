import React from 'react';
import {
  UserIcon,
  CalendarIcon,
  ClockIcon,
  TagIcon,
  ListBulletIcon,
  SparklesIcon,
  MegaphoneIcon,
  HeartIcon
} from '@heroicons/react/24/outline';
import { formatDate, addHeadingIds } from '../../utils/articleUtils';

/**
 * Brand Copy Layout - Specialized layout for marketing and brand content
 */
const BrandCopyLayout = ({ 
  article, 
  tableOfContents, 
  estimatedReadTime, 
  contentRef,
  scrollToHeading,
  showTableOfContents 
}) => {
  // Guard against undefined article
  if (!article) {
    console.error('BrandCopyLayout - Article is undefined');
    return (
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <p className="text-casino-gold-400">Loading brand copy...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Brand Copy Header - Full Width */}
      <div className="mb-12">
        {/* Content Type Badge */}
        <div className="text-center mb-8">
          <div className={`inline-flex items-center space-x-3 px-8 py-4 rounded-full text-lg font-bold bg-gradient-to-r ${article.contentTypeInfo?.color || 'from-blue-500 to-cyan-500'} text-white shadow-xl`}>
            <span className="text-2xl">{article.contentTypeInfo?.icon || '🏢'}</span>
            <span>{article.contentTypeInfo?.label || 'Brand Copy'}</span>
          </div>
        </div>

        {/* Title - Centered and Large */}
        <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-casino-gold-400 mb-8 leading-tight text-center">
          {article.title}
        </h1>

        {/* Meta Information - Centered */}
        <div className="flex flex-wrap items-center justify-center gap-8 text-casino-gold-300 text-base mb-8">
          <div className="flex items-center space-x-2">
            <UserIcon className="w-5 h-5" />
            <span className="font-medium">{article.author}</span>
          </div>
          <div className="flex items-center space-x-2">
            <CalendarIcon className="w-5 h-5" />
            <span>{formatDate(article.published_at)}</span>
          </div>
          <div className="flex items-center space-x-2">
            <ClockIcon className="w-5 h-5" />
            <span>{estimatedReadTime} min read</span>
          </div>
        </div>

        {/* Excerpt - Highlighted and Centered */}
        {article.excerpt && (
          <div className="max-w-4xl mx-auto">
            <div className="casino-card rounded-2xl p-8 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/40 text-center">
              <p className="text-2xl text-casino-gold-200 leading-relaxed font-medium">
                {article.excerpt}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Mobile Table of Contents */}
      {showTableOfContents && tableOfContents.length > 0 && (
        <div className="lg:hidden mb-8">
          <div className="casino-card rounded-xl p-6 chip-shadow">
            <h3 className="text-lg font-semibold text-casino-gold-400 mb-4 flex items-center justify-center">
              <ListBulletIcon className="w-5 h-5 mr-2" />
              Content Sections
            </h3>
            <nav className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {tableOfContents.map((item, index) => (
                <button
                  key={index}
                  onClick={() => scrollToHeading(item.id)}
                  className={`text-center text-sm text-casino-gold-300 hover:text-casino-gold-200 transition-colors py-3 px-4 rounded-lg hover:bg-casino-gold-500/10 border border-yellow-500/30 ${
                    item.level > 2 ? 'text-xs' : ''
                  }`}
                >
                  {item.text}
                </button>
              ))}
            </nav>
          </div>
        </div>
      )}

      <div className="flex gap-8">
        {/* Desktop Table of Contents Sidebar */}
        {showTableOfContents && tableOfContents.length > 0 && (
          <aside className="hidden lg:block w-64 flex-shrink-0">
            <div className="sticky top-24 casino-card rounded-xl p-6 chip-shadow bg-gradient-to-br from-yellow-900/20 to-orange-900/20 border border-yellow-500/30">
              <h3 className="text-lg font-semibold text-casino-gold-400 mb-4 flex items-center">
                <ListBulletIcon className="w-5 h-5 mr-2" />
                Content Sections
              </h3>
              <nav className="space-y-2">
                {tableOfContents.map((item, index) => (
                  <button
                    key={index}
                    onClick={() => scrollToHeading(item.id)}
                    className={`block w-full text-left text-sm text-casino-gold-300 hover:text-casino-gold-200 transition-colors py-2 px-3 rounded hover:bg-yellow-500/10 border border-transparent hover:border-yellow-500/30 ${
                      item.level > 2 ? 'pl-6' : ''
                    } ${item.level > 3 ? 'pl-10' : ''}`}
                  >
                    {item.text}
                  </button>
                ))}
              </nav>

              {/* Brand Elements */}
              <div className="mt-8 pt-6 border-t border-casino-gold-500/30">
                <h4 className="text-lg font-semibold text-casino-gold-400 mb-4 flex items-center">
                  <SparklesIcon className="w-5 h-5 mr-2" />
                  Brand Elements
                </h4>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2 text-sm">
                    <MegaphoneIcon className="w-4 h-4 text-yellow-400" />
                    <span className="text-casino-gold-300">Engaging Copy</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <HeartIcon className="w-4 h-4 text-red-400" />
                    <span className="text-casino-gold-300">Emotional Appeal</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <SparklesIcon className="w-4 h-4 text-cyan-400" />
                    <span className="text-casino-gold-300">Brand Voice</span>
                  </div>
                </div>
              </div>
            </div>
          </aside>
        )}

        {/* Article Content */}
        <article className="flex-1 min-w-0">
          <div className="casino-card rounded-2xl p-12 chip-shadow brand-copy-content bg-gradient-to-br from-yellow-900/10 to-orange-900/10 border border-yellow-500/20">
            <div 
              ref={contentRef}
              className="prose prose-xl max-w-none text-casino-gold-100 article-content brand-content"
              dangerouslySetInnerHTML={{ 
                __html: addHeadingIds(article.content || '', tableOfContents)
              }}
              style={{
                '--tw-prose-body': 'rgb(251 191 36 / 0.95)',
                '--tw-prose-headings': 'rgb(251 191 36)',
                '--tw-prose-links': 'rgb(251 191 36)',
                '--tw-prose-bold': 'rgb(251 191 36)',
                '--tw-prose-counters': 'rgb(251 191 36 / 0.8)',
                '--tw-prose-bullets': 'rgb(251 191 36 / 0.8)',
                '--tw-prose-hr': 'rgb(251 191 36 / 0.5)',
                '--tw-prose-quotes': 'rgb(251 191 36 / 0.95)',
                '--tw-prose-quote-borders': 'rgb(251 191 36 / 0.6)',
                '--tw-prose-captions': 'rgb(251 191 36 / 0.8)',
                '--tw-prose-code': 'rgb(251 191 36)',
                '--tw-prose-pre-code': 'rgb(251 191 36 / 0.9)',
                '--tw-prose-pre-bg': 'rgb(15 23 42 / 0.8)',
                '--tw-prose-th-borders': 'rgb(251 191 36 / 0.5)',
                '--tw-prose-td-borders': 'rgb(251 191 36 / 0.4)',
                lineHeight: '1.9',
                fontSize: '1.2rem',
                textAlign: 'center'
              }}
            />
          </div>

          {/* Tags Section */}
          {article.tags && article.tags.length > 0 && (
            <div className="mt-12">
              <div className="casino-card rounded-xl p-8 chip-shadow text-center">
                <div className="flex items-center justify-center space-x-3 mb-6">
                  <TagIcon className="w-6 h-6 text-casino-gold-400" />
                  <span className="text-casino-gold-400 font-semibold text-xl">Brand Tags</span>
                </div>
                <div className="flex flex-wrap justify-center gap-4">
                  {article.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-6 py-3 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 text-casino-gold-200 text-lg rounded-full border border-yellow-500/40 hover:border-yellow-400/60 transition-colors font-medium shadow-lg"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}
        </article>
      </div>
    </div>
  );
};

export default BrandCopyLayout;
