import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  UserIcon,
  CalendarIcon,
  ClockIcon,
  TagIcon,
  ListBulletIcon,
  StarIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  DevicePhoneMobileIcon,
  ChatBubbleLeftRightIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  PlayIcon,
  CreditCardIcon,
  GlobeAltIcon,
  InformationCircleIcon,
  QuestionMarkCircleIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  CameraIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { formatDate, addHeadingIds } from '../../utils/articleUtils';
import { extractSEOData, cleanArticleContent } from '../../utils/extractArticleData';
import { getDefaultFAQ, getDefaultPros, getDefaultCons } from '../../utils/defaultCasinoData';
import { AffiliateButtonGroup } from '../affiliate/AffiliateButton';
import ArticleAffiliateDisplay, { ArticleAffiliateGroup } from '../affiliate/ArticleAffiliateDisplay';
import SmartAffiliateButton, { SmartAffiliateButtonGroup } from '../affiliate/SmartAffiliateButton';
import EnhancedAffiliateButton, { EnhancedAffiliateButtonGroup } from '../affiliate/EnhancedAffiliateButton';
import TagsDisplay from '../common/TagsDisplay';

/**
 * Professional Casino Review Layout - Comprehensive casino review design
 * Based on industry standards from AskGamblers, Casino.guru, etc.
 */
const CasinoReviewLayout = ({ 
  article, 
  tableOfContents, 
  estimatedReadTime, 
  contentRef,
  scrollToHeading,
  showTableOfContents 
}) => {
  const { t } = useTranslation();
  const [extractedData, setExtractedData] = useState(null);
  const [activeSection, setActiveSection] = useState('overview');
  const [seoData, setSeoData] = useState(null);
  const [cleanedContent, setCleanedContent] = useState('');
  const [expandedFAQ, setExpandedFAQ] = useState({});
  const [selectedImage, setSelectedImage] = useState(null);

  useEffect(() => {
    if (article?.content) {
      // Use originalContent if available (contains JSON data), otherwise fallback to content
      const contentForExtraction = article.originalContent || article.content;
      
      // Extract casino data from original content BEFORE cleaning
      const data = extractCasinoData(contentForExtraction);
      setExtractedData(data);
      
      // Extract SEO data and clean content
      const seo = extractSEOData(contentForExtraction);
      setSeoData(seo);
      
      const cleaned = cleanArticleContent(contentForExtraction);
      setCleanedContent(cleaned);
    }
  }, [article?.content, article?.originalContent]);

  // Note: SEO data (title, meta description, keywords) is now handled by PublicArticleDetail
  // This component focuses only on layout and content rendering

  // Guard against undefined article
  if (!article) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <p className="text-casino-gold-400">{t('casinoReview.loadingReview')}</p>
        </div>
      </div>
    );
  }

  // Extract comprehensive casino data from content
  const extractCasinoData = (content) => {
    // Detect article language from content or article properties
    const detectLanguage = () => {
      if (article?.language) return article.language;
      
      // Simple language detection based on common words
      const contentLower = content.toLowerCase();
      if (contentLower.includes('perguntas frequentes') || contentLower.includes('jogos disponíveis')) return 'pt';
      if (contentLower.includes('preguntas frecuentes') || contentLower.includes('juegos disponibles')) return 'es';
      if (contentLower.includes('foire aux questions') || contentLower.includes('jeux disponibles')) return 'fr';
      if (contentLower.includes('häufig gestellte fragen') || contentLower.includes('verfügbare spiele')) return 'de';
      if (contentLower.includes('domande frequenti') || contentLower.includes('giochi disponibili')) return 'it';
      if (contentLower.includes('常见问题') || contentLower.includes('可用游戏')) return 'zh';
      if (contentLower.includes('よくある質問') || contentLower.includes('利用可能なゲーム')) return 'ja';
      return 'en'; // default to English
    };
    
    const detectedLanguage = detectLanguage();
    // Extract ratings
    const ratingPatterns = {
      overall: /overall.*?rating.*?(\d+(?:\.\d+)?)/i,
      games: /games.*?rating.*?(\d+(?:\.\d+)?)/i,
      bonuses: /bonus.*?rating.*?(\d+(?:\.\d+)?)/i,
      support: /support.*?rating.*?(\d+(?:\.\d+)?)/i,
      security: /security.*?rating.*?(\d+(?:\.\d+)?)/i,
      mobile: /mobile.*?rating.*?(\d+(?:\.\d+)?)/i,
      payments: /payment.*?rating.*?(\d+(?:\.\d+)?)/i,
      usability: /usability.*?rating.*?(\d+(?:\.\d+)?)/i
    };

    const ratings = {};
    Object.entries(ratingPatterns).forEach(([key, pattern]) => {
      const match = content.match(pattern);
      if (match) {
        ratings[key] = parseFloat(match[1]);
      }
    });

    // Extract casino info JSON block first
    const casinoInfoMatch = content.match(/<!-- CASINO_INFO_START -->([\s\S]*?)<!-- CASINO_INFO_END -->/i);
    let casinoInfoFromContent = null;
    
    if (casinoInfoMatch) {
      
      // 强力HTML清理和JSON修复
      let rawJson = casinoInfoMatch[1].trim();
      
      // Step 1: Remove HTML tags completely
      rawJson = rawJson
        .replace(/<p>/g, '')
        .replace(/<\/p>/g, '')
        .replace(/<br>/g, '')
        .replace(/<br\/>/g, '')
        .replace(/<br \/>/g, '');
      
      // Step 2: Decode HTML entities
      rawJson = rawJson
        .replace(/&quot;/g, '"')
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>');
      
      
      // Step 3: Fix broken URLs with <a href> tags
      rawJson = rawJson.replace(
        /"url":\s*"<a href="([^"]*)"[^>]*>([^<]*)<\/a>"/g,
        '"url": "$1"'
      );
      
      // Step 4: Fix any remaining broken URLs
      rawJson = rawJson.replace(
        /"url":\s*"([^"]*\.jpg),/g,
        '"url": "$1",'
      );
      
      
      try {
        casinoInfoFromContent = JSON.parse(rawJson);
        // JSON parsed successfully
      } catch (error) {
        console.error('JSON parse failed:', error.message);
        
        // Last resort: try to fix the specific error around position 3313
        if (error.message.includes('position 33')) {
          const problemStart = Math.max(0, 3300);
          const problemEnd = Math.min(rawJson.length, 3350);
          const problemArea = rawJson.substring(problemStart, problemEnd);
          
          // Fix common issues in FAQ section
          let fixedJson = rawJson
            // Fix unescaped quotes in FAQ answers
            .replace(/"answer":\s*"([^"]*)"([^"]*)"([^"]*)",/g, '"answer": "$1\\"$2\\"$3",')
            // Fix missing quotes after URLs
            .replace(/https:\/\/[^",\s]*[^",\s]/g, (match) => {
              if (!match.endsWith('"')) {
                return match + '"';
              }
              return match;
            })
            // Fix broken question/answer pairs
            .replace(/"question":\s*"([^"]*)",\s*"answer":\s*"([^"]*)",/g, 
              (match, question, answer) => {
                // Escape internal quotes in the answer
                const escapedAnswer = answer.replace(/"/g, '\\"');
                return `"question": "${question}", "answer": "${escapedAnswer}",`;
              });
          
          
          try {
            casinoInfoFromContent = JSON.parse(fixedJson);
            // JSON fixed and parsed successfully
          } catch (finalError) {
            console.error('Final fix failed:', finalError.message);
          }
        }
      }
    } else {
      // No CASINO_INFO tags found, will use defaults
    }

    // Extract pros and cons from content or JSON
    let pros = [];
    let cons = [];
    
    if (casinoInfoFromContent?.pros) {
      pros = casinoInfoFromContent.pros;
    } else {
      const prosMatch = content.match(/(?:pros?|advantages?)[:\s]*\n?((?:[-*•]\s*.*?\n?)+)/i);
      pros = prosMatch ? 
        prosMatch[1].split(/[-*•]\s*/).filter(item => item.trim()).map(item => item.trim()) : 
        [];
    }
    
    if (casinoInfoFromContent?.cons) {
      cons = casinoInfoFromContent.cons;
    } else {
      const consMatch = content.match(/(?:cons?|disadvantages?)[:\s]*\n?((?:[-*•]\s*.*?\n?)+)/i);
      cons = consMatch ? 
        consMatch[1].split(/[-*•]\s*/).filter(item => item.trim()).map(item => item.trim()) : 
        [];
    }

    // Extract key facts - avoid HTML tags in matches
    const licenseMatch = content.match(/licens[e|ed].*?by[:\s]*([^<\n\.]+?)(?:\n|\.)/i);
    const establishedMatch = content.match(/established[:\s]*(\d{4})/i);
    const ownerMatch = content.match(/owner[:\s]*([^<\n\.]+?)(?:\n|\.)/i);

    // Use JSON data if available, otherwise fallback to extracted or default values
    return {
      ratings: casinoInfoFromContent?.ratings || (Object.keys(ratings).length > 0 ? ratings : {
        overall: 4.2,
        games: 4.5,
        bonuses: 4.0,
        support: 3.8,
        security: 4.7,
        mobile: 4.1,
        payments: 4.3,
        usability: 4.4
      }),
      pros: pros.length > 0 ? pros : getDefaultPros(detectedLanguage),
      cons: cons.length > 0 ? cons : getDefaultCons(detectedLanguage),
      license: casinoInfoFromContent?.license || (licenseMatch ? licenseMatch[1].trim() : 'Curacao Gaming License'),
      established: casinoInfoFromContent?.established || (establishedMatch ? establishedMatch[1] : '2020'),
      owner: casinoInfoFromContent?.owner || (ownerMatch ? ownerMatch[1].trim() : 'Unknown'),
      welcome_bonus: casinoInfoFromContent?.welcome_bonus || '100% up to $500 + Free Spins',
      total_games: casinoInfoFromContent?.total_games || '2000+',
      support_hours: casinoInfoFromContent?.support_hours || '24/7',
      min_deposit: casinoInfoFromContent?.min_deposit || '$10',
      withdrawal_time: casinoInfoFromContent?.withdrawal_time || '1-3 business days',
      screenshots: casinoInfoFromContent?.screenshots || [],
      faq: casinoInfoFromContent?.faq || getDefaultFAQ(detectedLanguage)
    };
  };

  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(<StarIconSolid key={i} className="w-5 h-5 text-yellow-400" />);
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <div key={i} className="relative w-5 h-5">
            <StarIcon className="w-5 h-5 text-gray-300 absolute" />
            <div className="overflow-hidden w-1/2">
              <StarIconSolid className="w-5 h-5 text-yellow-400" />
            </div>
          </div>
        );
      } else {
        stars.push(<StarIcon key={i} className="w-5 h-5 text-gray-300" />);
      }
    }
    return stars;
  };

  const ratingCategories = [
    { key: 'games', label: t('casinoReview.gameLibrary'), icon: '🎮', description: t('casinoReview.qualityAndVarietyOfGames') },
    { key: 'bonuses', label: t('casinoReview.bonuses'), icon: '🎁', description: t('casinoReview.welcomeOffersAndPromotions') },
    { key: 'payments', label: t('casinoReview.banking'), icon: '💳', description: t('casinoReview.depositAndWithdrawalOptions') },
    { key: 'support', label: t('casinoReview.support'), icon: '💬', description: t('casinoReview.customerServiceQuality') },
    { key: 'security', label: t('casinoReview.security'), icon: '🛡️', description: t('casinoReview.safetyAndLicensing') },
    { key: 'mobile', label: t('casinoReview.mobile'), icon: '📱', description: t('casinoReview.mobileGamingExperience') },
    { key: 'usability', label: t('casinoReview.usability'), icon: '🎯', description: t('casinoReview.userInterfaceAndNavigation') }
  ];

  const quickFacts = [
    { label: t('casinoReview.established'), value: extractedData?.established || '2020', icon: '📅' },
    { 
      label: t('casinoReview.license'), 
      value: (() => {
        const license = extractedData?.license || 'Curacao';
        // Clean up license text - remove "Gaming License" and long descriptions
        return license
          .replace(/Gaming License/gi, '')
          .replace(/publicamente.*$/gi, '')
          .replace(/não divulgada.*$/gi, '')
          .replace(/não.*$/gi, '')
          .trim() || 'Curacao';
      })(), 
      icon: '🛡️' 
    },
    { 
      label: t('casinoReview.owner'), 
      value: (() => {
        const owner = extractedData?.owner || 'Gaming Corp';
        // Clean up owner text - limit length and remove extra info
        return owner
          .replace(/não divulgada.*$/gi, 'N/A')
          .replace(/Informação.*$/gi, 'N/A')
          .substring(0, 20) + (owner.length > 20 ? '...' : '');
      })(), 
      icon: '🏢' 
    },
    { label: t('casinoReview.games'), value: extractedData?.total_games || '500+', icon: '🎮' },
    { label: t('casinoReview.minDeposit'), value: extractedData?.min_deposit || 'R$ 20', icon: '💰' },
    { 
      label: t('casinoReview.support'), 
      value: (() => {
        const support = extractedData?.support_hours || '24/7';
        // Clean up support text
        return support
          .replace(/Não especificado.*$/gi, 'N/A')
          .replace(/não garantido.*$/gi, '')
          .trim() || 'N/A';
      })(), 
      icon: '💬' 
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-indigo-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Casino Hero Section */}
        <div className="mb-12">
          <div 
            className="bg-gradient-to-r from-slate-800/50 to-slate-700/50 backdrop-blur-xl border border-white/10 rounded-3xl p-8 lg:p-12 shadow-2xl relative overflow-hidden"
            style={{
              backgroundImage: article.featured_image 
                ? `linear-gradient(rgba(30, 41, 59, 0.8), rgba(51, 65, 85, 0.8)), url(${article.featured_image})`
                : undefined,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat'
            }}
          >
            <div className="grid lg:grid-cols-3 gap-8 items-center">
              
              {/* Casino Logo & Basic Info */}
              <div className="lg:col-span-2">
                {/* Content Type Badge */}
                <div className="mb-6">
                  <div className="inline-flex items-center space-x-3 px-6 py-3 rounded-full text-base font-semibold bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg">
                    <span className="text-xl">🎰</span>
                    <span>{t('casinoReview.casinoReviewTitle')}</span>
                  </div>
                </div>

                {/* Casino Name */}
                <h1 className="text-4xl lg:text-6xl font-bold text-white mb-4 leading-tight">
                  {article.title?.replace(' Review', '').replace(' Casino Review', '') || 'Casino Name'}
                </h1>

                {/* Meta Info */}
                <div className="flex flex-wrap items-center gap-6 text-slate-300 text-sm mb-6">
                  <div className="flex items-center space-x-2">
                    <UserIcon className="w-4 h-4" />
                    <span>By {article.author || 'Casino Expert'}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CalendarIcon className="w-4 h-4" />
                    <span>{formatDate(article.published_at)}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <ClockIcon className="w-4 h-4" />
                    <span>{estimatedReadTime} min read</span>
                  </div>
                </div>

                {/* Enhanced Affiliate Buttons - Keep original UI, enhance with affiliate links */}
                <div className="mt-8">
                  <EnhancedAffiliateButtonGroup
                    article={article}
                    positions={['primary_cta', 'secondary_cta']}
                    layout="horizontal"
                    size="large"
                    className=""
                    trackingData={{
                      section: 'hero',
                      content_type: 'casino_review'
                    }}
                  />
                </div>
              </div>

              {/* Overall Rating Card */}
              <div className="lg:col-span-1">
                <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6 text-center">
                  <h3 className="text-xl font-bold text-white mb-4">{t('casinoReview.expertRating')}</h3>
                  <div className="text-6xl font-bold text-yellow-400 mb-3">
                    {extractedData?.ratings?.overall || '4.2'}
                  </div>
                  <div className="flex justify-center space-x-1 mb-4">
                    {renderStars(extractedData?.ratings?.overall || 4.2)}
                  </div>
                  <p className="text-slate-300 text-sm">
                    {t('casinoReview.basedOnCategories', { count: ratingCategories.length })}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Facts & Ratings Grid */}
        <div className="grid lg:grid-cols-4 gap-6 mb-12">
          
          {/* Quick Facts Card */}
          <div className="lg:col-span-1">
            <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6 h-full">
              <h3 className="text-xl font-bold text-white mb-6 flex items-center">
                <InformationCircleIcon className="w-6 h-6 mr-2 text-blue-400" />
                {t('casinoReview.quickFacts')}
              </h3>
              <div className="space-y-4">
                {quickFacts.map((fact, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{fact.icon}</span>
                      <span className="text-slate-300 text-sm">{fact.label}</span>
                    </div>
                    <span className="text-white font-semibold text-sm">{fact.value}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Detailed Ratings */}
          <div className="lg:col-span-3">
            <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6">
              <h3 className="text-xl font-bold text-white mb-6">{t('casinoReview.detailedRatings')}</h3>
              <div className="grid md:grid-cols-2 gap-4">
                {ratingCategories.map((category) => (
                  <div key={category.key} className="bg-white/5 rounded-xl p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{category.icon}</span>
                        <span className="text-white font-medium">{category.label}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="flex space-x-1">
                          {renderStars(extractedData?.ratings?.[category.key] || 4.0)}
                        </div>
                        <span className="text-yellow-400 font-bold text-lg min-w-[2rem]">
                          {extractedData?.ratings?.[category.key] || '4.0'}
                        </span>
                      </div>
                    </div>
                    <p className="text-slate-400 text-xs">{category.description}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Pros & Cons Section */}
        <div className="grid md:grid-cols-2 gap-6 mb-12">
          {/* Pros */}
          <div className="bg-emerald-500/10 border border-emerald-500/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-emerald-400 mb-6 flex items-center">
              <CheckCircleIcon className="w-6 h-6 mr-2" />
              {t('casinoReview.pros')}
            </h3>
            <div className="space-y-3">
              {(extractedData?.pros || []).map((pro, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <CheckCircleIcon className="w-5 h-5 text-emerald-400 mt-0.5 flex-shrink-0" />
                  <span className="text-slate-200">{pro}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Cons */}
          <div className="bg-red-500/10 border border-red-500/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-red-400 mb-6 flex items-center">
              <XCircleIcon className="w-6 h-6 mr-2" />
              {t('casinoReview.cons')}
            </h3>
            <div className="space-y-3">
              {(extractedData?.cons || []).map((con, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <XCircleIcon className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  <span className="text-slate-200">{con}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="grid lg:grid-cols-4 gap-8">

          {/* Article Content */}
          <article className="lg:col-span-3">
            <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-8 lg:p-12">
              <div 
                ref={contentRef}
                className="prose prose-xl prose-invert max-w-none"
                dangerouslySetInnerHTML={{ 
                  __html: (() => {
                    let content = cleanedContent || article.content || '';
                    
                    // Add IDs manually by replacing h1, h2, h3 tags
                    let headingIndex = 0;
                    content = content.replace(/<h([123])([^>]*)>(.*?)<\/h[123]>/gi, (match, level, attrs, text) => {
                      const id = `heading-${headingIndex}`;
                      headingIndex++;
                      // Remove any existing id attribute and add our own
                      const cleanAttrs = attrs.replace(/id\s*=\s*["'][^"']*["']/gi, '').trim();
                      return `<h${level}${cleanAttrs ? ' ' + cleanAttrs : ''} id="${id}">${text}</h${level}>`;
                    });
                    
                    return content;
                  })()
                }}
                style={{
                  '--tw-prose-body': '#cbd5e1',
                  '--tw-prose-headings': '#f1f5f9',
                  '--tw-prose-links': '#60a5fa',
                  '--tw-prose-bold': '#f1f5f9',
                  '--tw-prose-counters': '#94a3b8',
                  '--tw-prose-bullets': '#94a3b8',
                  '--tw-prose-hr': '#475569',
                  '--tw-prose-quotes': '#94a3b8',
                  '--tw-prose-quote-borders': '#475569',
                  '--tw-prose-captions': '#94a3b8',
                  '--tw-prose-code': '#fbbf24',
                  '--tw-prose-pre-code': '#e5e7eb',
                  '--tw-prose-pre-bg': '#1f2937',
                  '--tw-prose-th-borders': '#475569',
                  '--tw-prose-td-borders': '#374151',
                  lineHeight: '1.8'
                }}
              />
            </div>

            {/* Final Verdict Section */}
            <div className="mt-8 bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-white mb-4 flex items-center">
                <StarIcon className="w-7 h-7 mr-3 text-yellow-400" />
                {t('casinoReview.expertVerdict')}
              </h3>
              <div className="flex items-center space-x-6 mb-4">
                <div className="text-4xl font-bold text-yellow-400">
                  {extractedData?.ratings?.overall || '4.2'}/5
                </div>
                <div className="flex space-x-1">
                  {renderStars(extractedData?.ratings?.overall || 4.2)}
                </div>
              </div>
              <p className="text-slate-200 text-lg leading-relaxed mb-6">
                {(() => {
                  // Filter out SEO-related content from excerpt
                  let excerpt = article.excerpt || '';
                  
                  // Remove SEO prefixes and technical content
                  excerpt = excerpt.replace(/^(SEO Title:|Meta Description:|Keywords:).*$/gm, '').trim();
                  excerpt = excerpt.replace(/^(A \d+.*?é Confiável\?)/, '').trim();
                  
                  // If no valid excerpt after cleaning, try to extract from content
                  if (!excerpt && article.content) {
                    // Try to extract first meaningful paragraph from content
                    const contentMatch = article.content.match(/<p[^>]*>([^<]+)<\/p>/);
                    if (contentMatch) {
                      excerpt = contentMatch[1].trim();
                    }
                  }
                  
                  // Use language-appropriate default only if absolutely no content
                  if (!excerpt) {
                    const isPortuguese = article.language === 'pt' || article.title?.includes('Bet') || article.content?.includes('jogos');
                    excerpt = isPortuguese 
                      ? 'Este cassino oferece uma experiência de jogo sólida com uma boa seleção de jogos e suporte ao cliente confiável. Embora existam áreas para melhorias, oferece bom valor tanto para jogadores novos quanto experientes.'
                      : 'This casino offers a solid gaming experience with a good selection of games and reliable customer support. While there are areas for improvement, it provides good value for both new and experienced players.';
                  }
                  
                  return excerpt;
                })()}
              </p>
              <div className="space-y-4">
                <EnhancedAffiliateButton
                  article={article}
                  position="primary_cta"
                  size="large"
                  className="justify-center"
                  trackingData={{
                    section: 'verdict',
                    content_type: 'casino_review'
                  }}
                />
              </div>
            </div>

            {/* Tags Section */}
            <div className="mt-8">
              <TagsDisplay 
                tags={article.tags}
                title={t('casinoReview.reviewTags')}
                variant="default"
              />
            </div>

            {/* Screenshots Gallery Section */}
            {extractedData?.screenshots && extractedData.screenshots.length > 0 && (
              <div className="mt-8 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-8">
                <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
                  <CameraIcon className="w-7 h-7 mr-3 text-green-400" />
                  {t('casinoReview.casinoScreenshots')}
                </h2>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {extractedData.screenshots.map((screenshot, index) => (
                    <div 
                      key={index}
                      className="aspect-video bg-white/5 rounded-lg overflow-hidden cursor-pointer hover:bg-white/10 transition-all duration-300 border border-white/10 hover:border-white/30"
                      onClick={() => setSelectedImage(screenshot)}
                    >
                      <img
                        src={screenshot.url || screenshot}
                        alt={screenshot.caption || `Casino Screenshot ${index + 1}`}
                        className="w-full h-full object-cover"
                        loading="lazy"
                      />
                    </div>
                  ))}
                </div>
                {selectedImage && (
                  <div 
                    className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4"
                    onClick={() => setSelectedImage(null)}
                  >
                    <div className="max-w-4xl max-h-full">
                      <img
                        src={selectedImage.url || selectedImage}
                        alt={selectedImage.caption || 'Casino Screenshot'}
                        className="max-w-full max-h-full object-contain rounded-lg"
                      />
                      {selectedImage.caption && (
                        <p className="text-white text-center mt-4 text-lg">
                          {selectedImage.caption}
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* FAQ Section */}
            {extractedData?.faq && extractedData.faq.length > 0 && (
              <div className="mt-8 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-8">
                <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
                  <QuestionMarkCircleIcon className="w-7 h-7 mr-3 text-purple-400" />
                  {t('casinoReview.frequentlyAskedQuestions')}
                </h2>
                <div className="space-y-4">
                  {extractedData.faq.map((item, index) => (
                    <div 
                      key={index}
                      className="bg-white/5 rounded-xl overflow-hidden transition-all duration-300"
                    >
                      <button
                        onClick={() => setExpandedFAQ({...expandedFAQ, [index]: !expandedFAQ[index]})}
                        className="w-full px-6 py-4 flex items-center justify-between text-left hover:bg-white/10 transition-colors"
                      >
                        <span className="text-white font-medium text-lg pr-4">{item.question}</span>
                        {expandedFAQ[index] ? (
                          <ChevronUpIcon className="w-5 h-5 text-purple-400 flex-shrink-0" />
                        ) : (
                          <ChevronDownIcon className="w-5 h-5 text-purple-400 flex-shrink-0" />
                        )}
                      </button>
                      {expandedFAQ[index] && (
                        <div className="px-6 pb-4">
                          <p className="text-slate-300 leading-relaxed">{item.answer}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </article>

          {/* Table of Contents Sidebar */}
          {tableOfContents && tableOfContents.length > 0 && (
            <aside className="lg:col-span-1">
              <div className="sticky top-8">
                {/* Desktop version - always visible */}
                <div className="hidden lg:block bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6">
                  <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                    <ListBulletIcon className="w-5 h-5 mr-2 text-purple-400" />
                    {article.language === 'pt' ? 'Índice' : 'Contents'}
                  </h3>
                  <nav className="space-y-1">
                    {(() => {
                      let h1Count = 0, h2Count = 0, h3Count = 0;
                      return tableOfContents.map((item, index) => {
                        // Count heading numbers properly
                        if ((item.level || 1) === 1) {
                          h1Count++;
                          h2Count = 0;
                          h3Count = 0;
                        } else if ((item.level || 1) === 2) {
                          h2Count++;
                          h3Count = 0;
                        } else if ((item.level || 1) === 3) {
                          h3Count++;
                        }
                        
                        const number = (item.level || 1) === 1 ? `${h1Count}.` :
                                      (item.level || 1) === 2 ? `${h1Count}.${h2Count}` :
                                      `${h1Count}.${h2Count}.${h3Count}`;
                        
                        return (
                          <button
                            key={index}
                            onClick={() => {
                              if (item.id) {
                                scrollToHeading(item.id);
                              }
                            }}
                            className={`block w-full text-left py-2 px-3 rounded-lg transition-colors text-sm hover:bg-white/10 hover:text-white ${
                              (item.level || 1) === 1 ? 'font-semibold text-slate-200' :
                              (item.level || 1) === 2 ? 'ml-3 text-slate-300' :
                              'ml-6 text-slate-400'
                            }`}
                          >
                            <span className="text-purple-400 mr-2">
                              •
                            </span>
                            {item.text}
                          </button>
                        );
                      });
                    })()}
                  </nav>
                </div>

                {/* Mobile version - collapsible */}
                <div className="lg:hidden">
                  <button
                    onClick={() => setShowTableOfContents(!showTableOfContents)}
                    className="w-full bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-4 flex items-center justify-between text-white font-semibold"
                  >
                    <div className="flex items-center">
                      <ListBulletIcon className="w-5 h-5 mr-2 text-purple-400" />
                      {article.language === 'pt' ? 'Índice' : 'Contents'}
                    </div>
                    <div className={`transform transition-transform ${showTableOfContents ? 'rotate-180' : ''}`}>
                      ↓
                    </div>
                  </button>
                  
                  {showTableOfContents && (
                    <div className="mt-2 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-4">
                      <nav className="space-y-1">
                        {(() => {
                          let h1Count = 0, h2Count = 0, h3Count = 0;
                          return tableOfContents.map((item, index) => {
                            // Count heading numbers properly
                            if ((item.level || 1) === 1) {
                              h1Count++;
                              h2Count = 0;
                              h3Count = 0;
                            } else if ((item.level || 1) === 2) {
                              h2Count++;
                              h3Count = 0;
                            } else if ((item.level || 1) === 3) {
                              h3Count++;
                            }
                            
                            const number = (item.level || 1) === 1 ? `${h1Count}.` :
                                          (item.level || 1) === 2 ? `${h1Count}.${h2Count}` :
                                          `${h1Count}.${h2Count}.${h3Count}`;
                            
                            return (
                              <button
                                key={index}
                                onClick={() => {
                                  if (item.id) {
                                    scrollToHeading(item.id);
                                    setShowTableOfContents(false); // Auto-hide on mobile
                                  }
                                }}
                                className={`block w-full text-left py-2 px-3 rounded-lg transition-colors text-sm hover:bg-white/10 hover:text-white ${
                                  (item.level || 1) === 1 ? 'font-semibold text-slate-200' :
                                  (item.level || 1) === 2 ? 'ml-3 text-slate-300' :
                                  'ml-6 text-slate-400'
                                }`}
                              >
                                <span className="text-purple-400 mr-2">
                                  •
                                </span>
                                {item.text}
                              </button>
                            );
                          });
                        })()}
                      </nav>
                    </div>
                  )}
                </div>
              </div>
            </aside>
          )}
        </div>
      </div>
    </div>
  );
};

export default CasinoReviewLayout;