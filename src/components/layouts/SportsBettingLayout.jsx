import React, { useState, useEffect } from 'react';
import {
  UserIcon,
  CalendarIcon,
  ClockIcon,
  TagIcon,
  ListBulletIcon,
  TrophyIcon,
  ChartBarIcon,
  FireIcon,
  ExclamationTriangleIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  BanknotesIcon,
  PlayIcon,
  ChatBubbleLeftRightIcon,
  CheckIcon,
  XMarkIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import { StarIcon } from '@heroicons/react/24/solid';
import { formatDate, addHeadingIds } from '../../utils/articleUtils';

/**
 * Professional Sports Betting Layout - Based on Gazeta Esportiva and Dimers
 */
const SportsBettingLayout = ({ 
  article, 
  tableOfContents, 
  estimatedReadTime, 
  contentRef,
  scrollToHeading,
  showTableOfContents 
}) => {
  const [sportType, setSportType] = useState('General');
  const [bettingType, setBettingType] = useState('Analysis');
  const [matchInfo, setMatchInfo] = useState(null);
  const [predictions, setPredictions] = useState([]);
  const [playerProps, setPlayerProps] = useState([]);
  const [expertPicks, setExpertPicks] = useState([]);
  const [statistics, setStatistics] = useState(null);

  useEffect(() => {
    // Extract comprehensive sports betting information from content
    if (article?.content || article?.title) {
      const content = (article.title || '') + ' ' + (article.content || '');
      
      const sport = extractSportType(content);
      setSportType(sport);
      
      const type = extractBettingType(content);
      setBettingType(type);
      
      const match = extractMatchInfo(article.content || '');
      setMatchInfo(match);
      
      const predictionData = extractPredictions(article.content || '');
      setPredictions(predictionData);
      
      const props = extractPlayerProps(article.content || '');
      setPlayerProps(props);
      
      const picks = extractExpertPicks(article.content || '');
      setExpertPicks(picks);
      
      const stats = extractStatistics(article.content || '');
      setStatistics(stats);
    }
  }, [article?.content, article?.title]);

  // Guard against undefined article
  if (!article) {
    console.error('SportsBettingLayout - Article is undefined');
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <p className="text-casino-gold-400">Loading sports betting article...</p>
        </div>
      </div>
    );
  }

  const extractSportType = (content) => {
    const lowerContent = content.toLowerCase();
    if (lowerContent.includes('football') || lowerContent.includes('nfl')) return 'Football';
    if (lowerContent.includes('basketball') || lowerContent.includes('nba')) return 'Basketball';
    if (lowerContent.includes('baseball') || lowerContent.includes('mlb')) return 'Baseball';
    if (lowerContent.includes('soccer') || lowerContent.includes('fifa')) return 'Soccer';
    if (lowerContent.includes('tennis')) return 'Tennis';
    if (lowerContent.includes('hockey') || lowerContent.includes('nhl')) return 'Hockey';
    if (lowerContent.includes('boxing') || lowerContent.includes('mma')) return 'Combat Sports';
    return 'General';
  };

  const extractBettingType = (content) => {
    const lowerContent = content.toLowerCase();
    if (lowerContent.includes('prediction') || lowerContent.includes('pick')) return 'Predictions';
    if (lowerContent.includes('strategy') || lowerContent.includes('system')) return 'Strategy';
    if (lowerContent.includes('odds') || lowerContent.includes('line')) return 'Odds Analysis';
    if (lowerContent.includes('preview') || lowerContent.includes('matchup')) return 'Preview';
    return 'Analysis';
  };

  // Extract match/game information from structured content
  const extractMatchInfo = (content) => {
    try {
      const matchInfoRegex = /MATCH_INFO_START([\s\S]*?)MATCH_INFO_END/;
      const match = content.match(matchInfoRegex);
      if (match) {
        return JSON.parse(match[1]);
      }
    } catch (error) {
      console.warn('Failed to parse match info:', error);
    }
    
    // Return default match info
    return {
      homeTeam: 'Home Team',
      awayTeam: 'Away Team',
      date: new Date().toISOString(),
      time: '20:00',
      venue: 'Stadium',
      league: sportType,
      odds: {
        home: { odds: '+150', percentage: 45 },
        draw: { odds: '+220', percentage: 25 },
        away: { odds: '+180', percentage: 30 }
      },
      status: 'upcoming'
    };
  };

  // Extract betting predictions from content
  const extractPredictions = (content) => {
    try {
      const predictionsRegex = /PREDICTIONS_START([\s\S]*?)PREDICTIONS_END/;
      const match = content.match(predictionsRegex);
      if (match) {
        return JSON.parse(match[1]);
      }
    } catch (error) {
      console.warn('Failed to parse predictions:', error);
    }
    
    // Return default predictions
    return [
      {
        type: 'Match Result',
        prediction: 'Home Win',
        confidence: 75,
        reasoning: 'Strong home record and recent form'
      },
      {
        type: 'Total Goals',
        prediction: 'Over 2.5',
        confidence: 68,
        reasoning: 'Both teams average high scoring games'
      },
      {
        type: 'Both Teams to Score',
        prediction: 'Yes',
        confidence: 82,
        reasoning: 'Defensive vulnerabilities on both sides'
      }
    ];
  };

  // Extract player props from content
  const extractPlayerProps = (content) => {
    try {
      const propsRegex = /PLAYER_PROPS_START([\s\S]*?)PLAYER_PROPS_END/;
      const match = content.match(propsRegex);
      if (match) {
        return JSON.parse(match[1]);
      }
    } catch (error) {
      console.warn('Failed to parse player props:', error);
    }
    
    // Return default player props
    return [
      {
        player: 'Star Player',
        prop: 'Anytime Goalscorer',
        odds: '+180',
        recommendation: 'YES',
        confidence: 72
      },
      {
        player: 'Midfielder',
        prop: 'Shots on Target',
        line: '2.5',
        odds: '+110',
        recommendation: 'OVER',
        confidence: 65
      }
    ];
  };

  // Extract expert picks from content
  const extractExpertPicks = (content) => {
    try {
      const picksRegex = /EXPERT_PICKS_START([\s\S]*?)EXPERT_PICKS_END/;
      const match = content.match(picksRegex);
      if (match) {
        return JSON.parse(match[1]);
      }
    } catch (error) {
      console.warn('Failed to parse expert picks:', error);
    }
    
    // Return default expert picks
    return [
      {
        expert: 'Sports Analyst',
        pick: 'Home Team -1.5',
        odds: '+125',
        units: 2,
        reasoning: 'Superior squad depth and tactical advantage'
      }
    ];
  };

  // Extract statistics from content
  const extractStatistics = (content) => {
    try {
      const statsRegex = /STATISTICS_START([\s\S]*?)STATISTICS_END/;
      const match = content.match(statsRegex);
      if (match) {
        return JSON.parse(match[1]);
      }
    } catch (error) {
      console.warn('Failed to parse statistics:', error);
    }
    
    // Return default statistics
    return {
      homeStats: {
        wins: 8,
        draws: 2,
        losses: 0,
        goalsFor: 24,
        goalsAgainst: 8,
        form: ['W', 'W', 'W', 'D', 'W']
      },
      awayStats: {
        wins: 6,
        draws: 3,
        losses: 1,
        goalsFor: 18,
        goalsAgainst: 12,
        form: ['W', 'L', 'W', 'W', 'D']
      },
      headToHead: {
        totalMeetings: 10,
        homeWins: 6,
        draws: 2,
        awayWins: 2
      }
    };
  };

  const getSportIcon = (sport) => {
    const icons = {
      'Football': '🏈',
      'Basketball': '🏀',
      'Baseball': '⚾',
      'Soccer': '⚽',
      'Tennis': '🎾',
      'Hockey': '🏒',
      'Combat Sports': '🥊',
      'General': '🏆'
    };
    return icons[sport] || icons['General'];
  };

  const getSportColor = (sport) => {
    const colors = {
      'Football': 'from-orange-500 to-red-500',
      'Basketball': 'from-orange-600 to-yellow-500',
      'Baseball': 'from-blue-500 to-green-500',
      'Soccer': 'from-green-500 to-blue-500',
      'Tennis': 'from-yellow-500 to-green-500',
      'Hockey': 'from-blue-600 to-cyan-500',
      'Combat Sports': 'from-red-600 to-orange-500',
      'General': 'from-indigo-500 to-blue-500'
    };
    return colors[sport] || colors['General'];
  };

  const getBettingTypeColor = (type) => {
    const colors = {
      'Predictions': 'from-green-500 to-emerald-500',
      'Strategy': 'from-blue-500 to-cyan-500',
      'Odds Analysis': 'from-yellow-500 to-orange-500',
      'Preview': 'from-blue-500 to-cyan-500',
      'Analysis': 'from-indigo-500 to-blue-500'
    };
    return colors[type] || colors['Analysis'];
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Sports Betting Header */}
      <div className="mb-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Header Info */}
          <div className="lg:col-span-2">
            {/* Sport and Betting Type Badges */}
            <div className="flex flex-wrap items-center gap-4 mb-6">
              <div className={`inline-flex items-center space-x-3 px-6 py-3 rounded-full text-base font-semibold bg-gradient-to-r ${article.contentTypeInfo?.color || 'from-green-500 to-blue-500'} text-white shadow-lg`}>
                <span className="text-xl">{article.contentTypeInfo?.icon || '🏈'}</span>
                <span>{article.contentTypeInfo?.label || 'Sports Betting'}</span>
              </div>
              
              <div className={`inline-flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r ${getSportColor(sportType)} text-white shadow-lg`}>
                <span className="text-lg">{getSportIcon(sportType)}</span>
                <span>{sportType}</span>
              </div>
              
              <div className={`inline-flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r ${getBettingTypeColor(bettingType)} text-white shadow-lg`}>
                <ChartBarIcon className="w-4 h-4" />
                <span>{bettingType}</span>
              </div>
            </div>

            {/* Title */}
            <h1 className="text-4xl md:text-5xl font-bold text-casino-gold-400 mb-6 leading-tight">
              {article.title}
            </h1>

            {/* Meta Information */}
            <div className="flex flex-wrap items-center gap-6 text-casino-gold-300 text-base mb-6">
              <div className="flex items-center space-x-2">
                <UserIcon className="w-5 h-5" />
                <span className="font-medium">{article.author}</span>
              </div>
              <div className="flex items-center space-x-2">
                <CalendarIcon className="w-5 h-5" />
                <span>{formatDate(article.published_at)}</span>
              </div>
              <div className="flex items-center space-x-2">
                <ClockIcon className="w-5 h-5" />
                <span>{estimatedReadTime} min read</span>
              </div>
            </div>

            {/* Excerpt */}
            {article.excerpt && (
              <div className="casino-card rounded-xl p-6 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-slate-500/30">
                <p className="text-xl text-casino-gold-200 leading-relaxed font-medium">
                  {article.excerpt}
                </p>
              </div>
            )}
          </div>

          {/* Quick Match Info */}
          <div className="lg:col-span-1">
            {matchInfo && (
              <div className="casino-card rounded-xl p-6 chip-shadow bg-gradient-to-br from-green-900/20 to-blue-900/20 border border-slate-500/30 sticky top-24">
                <div className="text-center mb-6">
                  <div className="flex items-center justify-center space-x-2 mb-4">
                    <span className="text-2xl">{getSportIcon(sportType)}</span>
                    <span className="text-lg font-bold text-casino-gold-400">{matchInfo.league}</span>
                  </div>
                  <div className="space-y-2">
                    <div className="text-lg font-semibold text-casino-gold-200">{matchInfo.homeTeam}</div>
                    <div className="text-sm text-casino-gold-400">vs</div>
                    <div className="text-lg font-semibold text-casino-gold-200">{matchInfo.awayTeam}</div>
                  </div>
                  <div className="mt-4 text-sm text-casino-gold-300">
                    <div>{new Date(matchInfo.date).toLocaleDateString()}</div>
                    <div>{matchInfo.time} at {matchInfo.venue}</div>
                  </div>
                </div>

                {/* Live Odds */}
                <div className="space-y-3 mb-6">
                  <h4 className="text-sm font-semibold text-casino-gold-400 flex items-center">
                    <FireIcon className="w-4 h-4 mr-2" />
                    Live Odds
                  </h4>
                  <div className="grid grid-cols-3 gap-2 text-xs">
                    <div className="bg-casino-dark-700/50 rounded p-2 text-center">
                      <div className="text-casino-gold-300">Home</div>
                      <div className="text-green-400 font-bold">{matchInfo.odds.home.odds}</div>
                      <div className="text-casino-gold-400">{matchInfo.odds.home.percentage}%</div>
                    </div>
                    <div className="bg-casino-dark-700/50 rounded p-2 text-center">
                      <div className="text-casino-gold-300">Draw</div>
                      <div className="text-yellow-400 font-bold">{matchInfo.odds.draw.odds}</div>
                      <div className="text-casino-gold-400">{matchInfo.odds.draw.percentage}%</div>
                    </div>
                    <div className="bg-casino-dark-700/50 rounded p-2 text-center">
                      <div className="text-casino-gold-300">Away</div>
                      <div className="text-red-400 font-bold">{matchInfo.odds.away.odds}</div>
                      <div className="text-casino-gold-400">{matchInfo.odds.away.percentage}%</div>
                    </div>
                  </div>
                </div>

                {/* Responsible Gambling */}
                <div className="pt-4 border-t border-casino-gold-500/30">
                  <div className="flex items-center space-x-2 mb-2">
                    <ExclamationTriangleIcon className="w-4 h-4 text-yellow-400" />
                    <h4 className="text-sm font-semibold text-yellow-400">Bet Responsibly</h4>
                  </div>
                  <p className="text-xs text-casino-gold-300">
                    Gambling can be addictive. Please bet responsibly and within your means.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Professional Sports Betting Content Sections */}
      
      {/* Betting Predictions & Analysis */}
      <div className="grid lg:grid-cols-3 gap-8 mb-8">
        
        {/* Main Predictions */}
        <div className="lg:col-span-2">
          <div className="casino-card rounded-xl p-8 chip-shadow bg-gradient-to-br from-blue-900/20 to-green-900/20 border border-slate-500/30">
            <div className="flex items-center space-x-3 mb-6">
              <TrophyIcon className="w-6 h-6 text-casino-gold-400" />
              <h2 className="text-2xl font-bold text-casino-gold-400">Expert Predictions</h2>
            </div>
            
            <div className="grid md:grid-cols-2 gap-6">
              {predictions.map((prediction, index) => (
                <div key={index} className="bg-casino-dark-700/50 rounded-xl p-6 border border-slate-600/30">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-casino-gold-300">{prediction.type}</h3>
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${
                        prediction.confidence >= 80 ? 'bg-green-400' :
                        prediction.confidence >= 65 ? 'bg-yellow-400' : 'bg-red-400'
                      }`}></div>
                      <span className="text-sm font-bold text-casino-gold-200">{prediction.confidence}%</span>
                    </div>
                  </div>
                  
                  <div className="mb-4">
                    <div className="text-xl font-bold text-white mb-2">{prediction.prediction}</div>
                    <p className="text-sm text-casino-gold-300">{prediction.reasoning}</p>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-casino-gold-400">Confidence</span>
                    <div className="w-24 bg-casino-dark-800 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          prediction.confidence >= 80 ? 'bg-green-400' :
                          prediction.confidence >= 65 ? 'bg-yellow-400' : 'bg-red-400'
                        }`}
                        style={{ width: `${prediction.confidence}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {/* Team Statistics Comparison */}
        <div className="lg:col-span-1">
          {statistics && (
            <div className="casino-card rounded-xl p-6 chip-shadow bg-gradient-to-br from-purple-900/20 to-blue-900/20 border border-slate-500/30">
              <div className="flex items-center space-x-2 mb-6">
                <ChartBarIcon className="w-5 h-5 text-casino-gold-400" />
                <h3 className="text-lg font-bold text-casino-gold-400">Team Stats</h3>
              </div>
              
              {/* Home Team Stats */}
              <div className="mb-6">
                <h4 className="text-sm font-semibold text-casino-gold-300 mb-3">{matchInfo?.homeTeam || 'Home Team'}</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-casino-gold-400">Record:</span>
                    <span className="text-casino-gold-200">{statistics.homeStats.wins}W-{statistics.homeStats.draws}D-{statistics.homeStats.losses}L</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-casino-gold-400">Goals:</span>
                    <span className="text-casino-gold-200">{statistics.homeStats.goalsFor}:{statistics.homeStats.goalsAgainst}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-casino-gold-400">Form:</span>
                    <div className="flex space-x-1">
                      {statistics.homeStats.form.map((result, i) => (
                        <span key={i} className={`w-5 h-5 rounded-full text-xs flex items-center justify-center font-bold ${
                          result === 'W' ? 'bg-green-500 text-white' :
                          result === 'D' ? 'bg-yellow-500 text-black' : 'bg-red-500 text-white'
                        }`}>
                          {result}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Away Team Stats */}
              <div className="mb-6">
                <h4 className="text-sm font-semibold text-casino-gold-300 mb-3">{matchInfo?.awayTeam || 'Away Team'}</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-casino-gold-400">Record:</span>
                    <span className="text-casino-gold-200">{statistics.awayStats.wins}W-{statistics.awayStats.draws}D-{statistics.awayStats.losses}L</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-casino-gold-400">Goals:</span>
                    <span className="text-casino-gold-200">{statistics.awayStats.goalsFor}:{statistics.awayStats.goalsAgainst}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-casino-gold-400">Form:</span>
                    <div className="flex space-x-1">
                      {statistics.awayStats.form.map((result, i) => (
                        <span key={i} className={`w-5 h-5 rounded-full text-xs flex items-center justify-center font-bold ${
                          result === 'W' ? 'bg-green-500 text-white' :
                          result === 'D' ? 'bg-yellow-500 text-black' : 'bg-red-500 text-white'
                        }`}>
                          {result}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Head to Head */}
              <div className="pt-4 border-t border-casino-gold-500/30">
                <h4 className="text-sm font-semibold text-casino-gold-300 mb-3">Head to Head</h4>
                <div className="text-xs text-casino-gold-400">
                  Last {statistics.headToHead.totalMeetings} meetings: 
                  <span className="text-green-400 ml-1">{statistics.headToHead.homeWins}H</span> - 
                  <span className="text-yellow-400">{statistics.headToHead.draws}D</span> - 
                  <span className="text-red-400">{statistics.headToHead.awayWins}A</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Player Props & Expert Picks */}
      <div className="grid lg:grid-cols-2 gap-8 mb-8">
        
        {/* Player Props */}
        <div className="casino-card rounded-xl p-8 chip-shadow bg-gradient-to-br from-orange-900/20 to-red-900/20 border border-slate-500/30">
          <div className="flex items-center space-x-3 mb-6">
            <PlayIcon className="w-6 h-6 text-casino-gold-400" />
            <h2 className="text-2xl font-bold text-casino-gold-400">Player Props</h2>
          </div>
          
          <div className="space-y-4">
            {playerProps.map((prop, index) => (
              <div key={index} className="bg-casino-dark-700/50 rounded-xl p-4 border border-slate-600/30">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h3 className="text-lg font-semibold text-casino-gold-200">{prop.player}</h3>
                    <p className="text-sm text-casino-gold-400">{prop.prop} {prop.line && `(${prop.line})`}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-casino-gold-100">{prop.odds}</div>
                    <div className={`text-sm font-bold ${
                      prop.recommendation === 'YES' || prop.recommendation === 'OVER' ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {prop.recommendation}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-xs text-casino-gold-400">Confidence: {prop.confidence}%</span>
                  <div className="w-20 bg-casino-dark-800 rounded-full h-1.5">
                    <div 
                      className="h-1.5 rounded-full bg-blue-400"
                      style={{ width: `${prop.confidence}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Expert Picks */}
        <div className="casino-card rounded-xl p-8 chip-shadow bg-gradient-to-br from-cyan-900/20 to-blue-900/20 border border-slate-500/30">
          <div className="flex items-center space-x-3 mb-6">
            <StarIcon className="w-6 h-6 text-casino-gold-400" />
            <h2 className="text-2xl font-bold text-casino-gold-400">Expert Picks</h2>
          </div>
          
          <div className="space-y-6">
            {expertPicks.map((pick, index) => (
              <div key={index} className="bg-casino-dark-700/50 rounded-xl p-6 border border-slate-600/30">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-casino-gold-200">{pick.expert}</h3>
                  <div className="flex items-center space-x-2">
                    <BanknotesIcon className="w-4 h-4 text-green-400" />
                    <span className="text-sm font-bold text-green-400">{pick.units} Units</span>
                  </div>
                </div>
                
                <div className="mb-4">
                  <div className="text-xl font-bold text-white mb-2">{pick.pick}</div>
                  <div className="text-lg text-casino-gold-300">{pick.odds}</div>
                </div>
                
                <p className="text-sm text-casino-gold-400">{pick.reasoning}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* Main Article Content */}
      <div className="flex gap-8">
        {/* Desktop Table of Contents Sidebar */}
        {showTableOfContents && tableOfContents.length > 0 && (
          <aside className="hidden lg:block w-64 flex-shrink-0">
            <div className="sticky top-24 casino-card rounded-xl p-6 chip-shadow">
              <h3 className="text-lg font-semibold text-casino-gold-400 mb-4 flex items-center">
                <ListBulletIcon className="w-5 h-5 mr-2" />
                Analysis Sections
              </h3>
              <nav className="space-y-2">
                {tableOfContents.map((item, index) => (
                  <button
                    key={index}
                    onClick={() => scrollToHeading(item.id)}
                    className={`block w-full text-left text-sm text-casino-gold-300 hover:text-casino-gold-200 transition-colors py-1 px-2 rounded hover:bg-casino-gold-500/10 ${
                      item.level > 2 ? 'pl-6' : ''
                    } ${item.level > 3 ? 'pl-10' : ''}`}
                  >
                    {item.text}
                  </button>
                ))}
              </nav>
            </div>
          </aside>
        )}

        {/* Article Content */}
        <article className="flex-1 min-w-0">
          <div className="casino-card rounded-xl p-10 chip-shadow sports-betting-content">
            <div 
              ref={contentRef}
              className="prose prose-xl max-w-none text-casino-gold-100 article-content"
              dangerouslySetInnerHTML={{ 
                __html: addHeadingIds(article.content || '', tableOfContents)
              }}
              style={{
                '--tw-prose-body': 'rgb(251 191 36 / 0.9)',
                '--tw-prose-headings': 'rgb(251 191 36)',
                '--tw-prose-links': 'rgb(251 191 36)',
                '--tw-prose-bold': 'rgb(251 191 36)',
                '--tw-prose-counters': 'rgb(251 191 36 / 0.7)',
                '--tw-prose-bullets': 'rgb(251 191 36 / 0.7)',
                '--tw-prose-hr': 'rgb(251 191 36 / 0.4)',
                '--tw-prose-quotes': 'rgb(251 191 36 / 0.9)',
                '--tw-prose-quote-borders': 'rgb(251 191 36 / 0.5)',
                '--tw-prose-captions': 'rgb(251 191 36 / 0.7)',
                '--tw-prose-code': 'rgb(251 191 36)',
                '--tw-prose-pre-code': 'rgb(251 191 36 / 0.9)',
                '--tw-prose-pre-bg': 'rgb(15 23 42 / 0.8)',
                '--tw-prose-th-borders': 'rgb(251 191 36 / 0.4)',
                '--tw-prose-td-borders': 'rgb(251 191 36 / 0.3)',
                lineHeight: '1.8',
                fontSize: '1.125rem'
              }}
            />
          </div>

          {/* Tags Section */}
          {article.tags && article.tags.length > 0 && (
            <div className="mt-12">
              <div className="casino-card rounded-xl p-6 chip-shadow">
                <div className="flex items-center space-x-3 mb-6">
                  <TagIcon className="w-6 h-6 text-casino-gold-400" />
                  <span className="text-casino-gold-400 font-semibold text-lg">Betting Tags</span>
                </div>
                <div className="flex flex-wrap gap-3">
                  {article.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-4 py-2 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 text-casino-gold-200 text-base rounded-full border border-slate-500/40 hover:border-slate-600/60 transition-colors font-medium"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}
        </article>
      </div>
    </div>
  );
};

export default SportsBettingLayout;
