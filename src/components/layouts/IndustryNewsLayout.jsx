import React, { useState, useEffect } from 'react';
import {
  UserIcon,
  CalendarIcon,
  ClockIcon,
  TagIcon,
  ListBulletIcon,
  NewspaperIcon,
  GlobeAltIcon,
  ArrowTrendingUpIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline';
import { formatDate, addHeadingIds, formatRelativeTime } from '../../utils/articleUtils';

/**
 * Industry News Layout - Specialized layout for news articles
 */
const IndustryNewsLayout = ({ 
  article, 
  tableOfContents, 
  estimatedReadTime, 
  contentRef,
  scrollToHeading,
  showTableOfContents 
}) => {
  const [newsCategory, setNewsCategory] = useState('General');
  const [urgencyLevel, setUrgencyLevel] = useState('Normal');

  useEffect(() => {
    // Extract news category and urgency from content
    if (article?.content || article?.title) {
      const category = extractNewsCategory((article.title || '') + ' ' + (article.content || ''));
      setNewsCategory(category);
      
      const urgency = extractUrgencyLevel((article.title || '') + ' ' + (article.content || ''));
      setUrgencyLevel(urgency);
    }
  }, [article?.content, article?.title]);

  // Guard against undefined article
  if (!article) {
    console.error('IndustryNewsLayout - Article is undefined');
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <p className="text-casino-gold-400">Loading industry news...</p>
        </div>
      </div>
    );
  }

  const extractNewsCategory = (content) => {
    const lowerContent = content.toLowerCase();
    if (lowerContent.includes('regulation') || lowerContent.includes('legal') || lowerContent.includes('law')) {
      return 'Regulation';
    }
    if (lowerContent.includes('merger') || lowerContent.includes('acquisition') || lowerContent.includes('partnership')) {
      return 'Business';
    }
    if (lowerContent.includes('technology') || lowerContent.includes('innovation') || lowerContent.includes('digital')) {
      return 'Technology';
    }
    if (lowerContent.includes('market') || lowerContent.includes('revenue') || lowerContent.includes('earnings')) {
      return 'Market';
    }
    return 'General';
  };

  const extractUrgencyLevel = (content) => {
    const lowerContent = content.toLowerCase();
    if (lowerContent.includes('breaking') || lowerContent.includes('urgent') || lowerContent.includes('alert')) {
      return 'Breaking';
    }
    if (lowerContent.includes('important') || lowerContent.includes('significant') || lowerContent.includes('major')) {
      return 'Important';
    }
    return 'Normal';
  };

  const getCategoryColor = (category) => {
    const colors = {
      'Regulation': 'from-red-500 to-pink-500',
      'Business': 'from-blue-500 to-cyan-500',
      'Technology': 'from-blue-500 to-cyan-500',
      'Market': 'from-green-500 to-emerald-500',
      'General': 'from-gray-500 to-slate-500'
    };
    return colors[category] || colors['General'];
  };

  const getUrgencyColor = (urgency) => {
    const colors = {
      'Breaking': 'from-red-600 to-red-500',
      'Important': 'from-orange-600 to-orange-500',
      'Normal': 'from-blue-600 to-blue-500'
    };
    return colors[urgency] || colors['Normal'];
  };

  const getCategoryIcon = (category) => {
    const icons = {
      'Regulation': '⚖️',
      'Business': '💼',
      'Technology': '💻',
      'Market': '📈',
      'General': '📰'
    };
    return icons[category] || icons['General'];
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* News Header */}
      <div className="mb-12">
        <div className="grid lg:grid-cols-4 gap-8">
          {/* Main Header Info */}
          <div className="lg:col-span-3">
            {/* News Badges */}
            <div className="flex flex-wrap items-center gap-4 mb-6">
              <div className={`inline-flex items-center space-x-3 px-6 py-3 rounded-full text-base font-semibold bg-gradient-to-r ${article.contentTypeInfo?.color || 'from-blue-500 to-indigo-500'} text-white shadow-lg`}>
                <span className="text-xl">{article.contentTypeInfo?.icon || '📰'}</span>
                <span>{article.contentTypeInfo?.label || 'Industry News'}</span>
              </div>
              
              {urgencyLevel !== 'Normal' && (
                <div className={`inline-flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-bold bg-gradient-to-r ${getUrgencyColor(urgencyLevel)} text-white shadow-lg animate-pulse`}>
                  <ExclamationCircleIcon className="w-4 h-4" />
                  <span>{urgencyLevel}</span>
                </div>
              )}
              
              <div className={`inline-flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r ${getCategoryColor(newsCategory)} text-white shadow-lg`}>
                <span>{getCategoryIcon(newsCategory)}</span>
                <span>{newsCategory}</span>
              </div>
            </div>

            {/* Title */}
            <h1 className="text-4xl md:text-5xl font-bold text-casino-gold-400 mb-6 leading-tight">
              {article.title}
            </h1>

            {/* Enhanced Meta Information */}
            <div className="flex flex-wrap items-center gap-6 text-casino-gold-300 text-base mb-6">
              <div className="flex items-center space-x-2">
                <UserIcon className="w-5 h-5" />
                <span className="font-medium">{article.author}</span>
              </div>
              <div className="flex items-center space-x-2">
                <CalendarIcon className="w-5 h-5" />
                <span>{formatDate(article.published_at)}</span>
              </div>
              <div className="flex items-center space-x-2">
                <ClockIcon className="w-5 h-5" />
                <span>{estimatedReadTime} min read</span>
              </div>
              <div className="flex items-center space-x-2">
                <GlobeAltIcon className="w-5 h-5" />
                <span>{formatRelativeTime(article.published_at)}</span>
              </div>
            </div>

            {/* Excerpt */}
            {article.excerpt && (
              <div className="casino-card rounded-xl p-6 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-slate-500/30">
                <p className="text-xl text-casino-gold-200 leading-relaxed font-medium">
                  {article.excerpt}
                </p>
              </div>
            )}
          </div>

          {/* News Info Card */}
          <div className="lg:col-span-1">
            <div className="casino-card rounded-xl p-6 chip-shadow bg-gradient-to-br from-blue-900/20 to-cyan-900/20 border border-slate-500/30 sticky top-24">
              <div className="text-center mb-6">
                <div className="text-4xl mb-3">📰</div>
                <h3 className="text-xl font-bold text-casino-gold-400 mb-2">News Brief</h3>
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-gradient-to-r ${getCategoryColor(newsCategory)} text-white`}>
                  {newsCategory} News
                </div>
              </div>

              {/* News Stats */}
              <div className="space-y-4 mb-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <NewspaperIcon className="w-5 h-5 text-red-400" />
                    <span className="text-sm text-casino-gold-300">Category</span>
                  </div>
                  <span className="text-sm font-semibold text-casino-gold-200">
                    {newsCategory}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <ClockIcon className="w-5 h-5 text-green-400" />
                    <span className="text-sm text-casino-gold-300">Published</span>
                  </div>
                  <span className="text-sm font-semibold text-casino-gold-200">
                    {formatRelativeTime(article.published_at)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <ArrowTrendingUpIcon className="w-5 h-5 text-yellow-400" />
                    <span className="text-sm text-casino-gold-300">Priority</span>
                  </div>
                  <span className="text-sm font-semibold text-casino-gold-200">
                    {urgencyLevel}
                  </span>
                </div>
              </div>

              {/* Key Points */}
              <div className="pt-6 border-t border-casino-gold-500/30">
                <h4 className="text-lg font-semibold text-casino-gold-400 mb-3">Key Points</h4>
                <div className="space-y-2 text-sm text-casino-gold-300">
                  <p>• Industry impact analysis</p>
                  <p>• Market implications</p>
                  <p>• Regulatory considerations</p>
                  <p>• Future outlook</p>
                </div>
              </div>

              {/* Related Topics */}
              <div className="mt-6 pt-6 border-t border-casino-gold-500/30">
                <h4 className="text-lg font-semibold text-casino-gold-400 mb-3">Related Topics</h4>
                <div className="flex flex-wrap gap-2">
                  <span className="px-2 py-1 bg-red-500/20 text-red-300 text-xs rounded-full border border-red-500/40">
                    Industry
                  </span>
                  <span className="px-2 py-1 bg-blue-500/20 text-blue-300 text-xs rounded-full border border-blue-500/40">
                    Market
                  </span>
                  <span className="px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded-full border border-green-500/40">
                    Analysis
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex gap-8">
        {/* Desktop Table of Contents Sidebar */}
        {showTableOfContents && tableOfContents.length > 0 && (
          <aside className="hidden lg:block w-64 flex-shrink-0">
            <div className="sticky top-24 casino-card rounded-xl p-6 chip-shadow">
              <h3 className="text-lg font-semibold text-casino-gold-400 mb-4 flex items-center">
                <ListBulletIcon className="w-5 h-5 mr-2" />
                Article Sections
              </h3>
              <nav className="space-y-2">
                {tableOfContents.map((item, index) => (
                  <button
                    key={index}
                    onClick={() => scrollToHeading(item.id)}
                    className={`block w-full text-left text-sm text-casino-gold-300 hover:text-casino-gold-200 transition-colors py-1 px-2 rounded hover:bg-casino-gold-500/10 ${
                      item.level > 2 ? 'pl-6' : ''
                    } ${item.level > 3 ? 'pl-10' : ''}`}
                  >
                    {item.text}
                  </button>
                ))}
              </nav>
            </div>
          </aside>
        )}

        {/* Article Content */}
        <article className="flex-1 min-w-0">
          <div className="casino-card rounded-xl p-10 chip-shadow industry-news-content">
            <div 
              ref={contentRef}
              className="prose prose-xl max-w-none text-casino-gold-100 article-content"
              dangerouslySetInnerHTML={{ 
                __html: addHeadingIds(article.content || '', tableOfContents)
              }}
              style={{
                '--tw-prose-body': 'rgb(251 191 36 / 0.9)',
                '--tw-prose-headings': 'rgb(251 191 36)',
                '--tw-prose-links': 'rgb(251 191 36)',
                '--tw-prose-bold': 'rgb(251 191 36)',
                '--tw-prose-counters': 'rgb(251 191 36 / 0.7)',
                '--tw-prose-bullets': 'rgb(251 191 36 / 0.7)',
                '--tw-prose-hr': 'rgb(251 191 36 / 0.4)',
                '--tw-prose-quotes': 'rgb(251 191 36 / 0.9)',
                '--tw-prose-quote-borders': 'rgb(251 191 36 / 0.5)',
                '--tw-prose-captions': 'rgb(251 191 36 / 0.7)',
                '--tw-prose-code': 'rgb(251 191 36)',
                '--tw-prose-pre-code': 'rgb(251 191 36 / 0.9)',
                '--tw-prose-pre-bg': 'rgb(15 23 42 / 0.8)',
                '--tw-prose-th-borders': 'rgb(251 191 36 / 0.4)',
                '--tw-prose-td-borders': 'rgb(251 191 36 / 0.3)',
                lineHeight: '1.8',
                fontSize: '1.125rem'
              }}
            />
          </div>

          {/* Tags Section */}
          {article.tags && article.tags.length > 0 && (
            <div className="mt-12">
              <div className="casino-card rounded-xl p-6 chip-shadow">
                <div className="flex items-center space-x-3 mb-6">
                  <TagIcon className="w-6 h-6 text-casino-gold-400" />
                  <span className="text-casino-gold-400 font-semibold text-lg">News Tags</span>
                </div>
                <div className="flex flex-wrap gap-3">
                  {article.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-4 py-2 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 text-casino-gold-200 text-base rounded-full border border-slate-500/40 hover:border-slate-600/60 transition-colors font-medium"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}
        </article>
      </div>
    </div>
  );
};

export default IndustryNewsLayout;
