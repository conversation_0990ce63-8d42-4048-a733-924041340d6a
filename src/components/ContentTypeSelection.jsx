import React, { useState, useEffect } from 'react';
import { ChevronRightIcon } from '@heroicons/react/24/outline';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import { API_CONFIG } from '../config/api';

const CONTENT_TYPES = {
  CASINO_REVIEW: {
    id: 'casino_review',
    title: 'Casino Review',
    description: 'Comprehensive casino platform reviews with games, bonuses, and user experience analysis',
    icon: '🎰',
    prompts: {
      structure: 'casino review format with sections for games, bonuses, payment methods, and overall rating',
      focus: 'objective evaluation of casino features, user experience, and trustworthiness',
      compliance: 'include responsible gambling disclaimers and age verification requirements'
    }
  },
  GAME_GUIDE: {
    id: 'game_guide',
    title: 'Game Guide',
    description: 'Strategic guides for casino games including rules, strategies, and tips',
    icon: '🃏',
    prompts: {
      structure: 'educational guide format with rules explanation, strategy sections, and practical tips',
      focus: 'game mechanics, optimal strategies, and beginner-friendly explanations',
      compliance: 'emphasize entertainment value and responsible gaming practices'
    }
  },
  BONUS_ANALYSIS: {
    id: 'bonus_analysis',
    title: 'Bonus Analysis',
    description: 'Detailed analysis of casino bonuses, promotions, and their terms',
    icon: '🎁',
    prompts: {
      structure: 'analytical format covering bonus types, wagering requirements, and value assessment',
      focus: 'bonus terms transparency, real value calculation, and player benefits',
      compliance: 'clearly explain wagering requirements and terms & conditions'
    }
  },
  INDUSTRY_NEWS: {
    id: 'industry_news',
    title: 'Industry News',
    description: 'Latest developments in the gambling and casino industry',
    icon: '📰',
    prompts: {
      structure: 'news article format with headline, summary, detailed analysis, and implications',
      focus: 'factual reporting, industry impact analysis, and future implications',
      compliance: 'maintain journalistic integrity and cite reliable sources'
    }
  },
  SPORTS_BETTING: {
    id: 'sports_betting',
    title: 'Sports Betting',
    description: 'Sports betting guides, predictions, and platform reviews',
    icon: '⚽',
    prompts: {
      structure: 'betting guide format with analysis, odds explanation, and strategic advice',
      focus: 'statistical analysis, betting strategies, and risk management',
      compliance: 'promote responsible betting and include addiction resources'
    }
  },
  REGULATORY_UPDATE: {
    id: 'regulatory_update',
    title: 'Regulatory Update',
    description: 'Updates on gambling laws, regulations, and compliance requirements',
    icon: '⚖️',
    prompts: {
      structure: 'regulatory analysis format with law changes, compliance requirements, and industry impact',
      focus: 'legal accuracy, compliance implications, and operator responsibilities',
      compliance: 'ensure legal accuracy and recommend professional legal consultation'
    }
  }
};

const ContentTypeSelection = ({ data, updateData, onNext }) => {
  const { t } = useTranslation();
  const { authenticatedFetch } = useAuth();
  const [selectedType, setSelectedType] = useState(data?.contentType || '');
  const [selectedJurisdiction, setSelectedJurisdiction] = useState(data?.jurisdiction || 'international');
  const [selectedRegion, setSelectedRegion] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [contentTypes, setContentTypes] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchContentTypes();
  }, []);

  const fetchContentTypes = async () => {
    try {
      const response = await authenticatedFetch(`/api/public/content-types`);
      if (response.ok) {
        const data = await response.json();
        setContentTypes(data.contentTypes);
      }
    } catch (error) {
      console.error('Error fetching content types:', error);
      // Fallback to hardcoded types if API fails
      setContentTypes(Object.values(CONTENT_TYPES));
    } finally {
      setLoading(false);
    }
  };

  const getContentTypeInfo = (contentType) => {
    const typeMap = {
      'casino_review': { icon: '🎰', title: t('contentTypes.casino_review'), description: 'Comprehensive casino platform reviews with games, bonuses, and user experience analysis' },
      'game_guide': { icon: '🎮', title: t('contentTypes.game_guide'), description: 'Strategic guides for casino games including rules, strategies, and tips' },
      'strategy_article': { icon: '📊', title: t('contentTypes.strategy_article', 'Strategy Article'), description: 'Mathematical analysis and evidence-based gaming strategies' },
      'brand_copy': { icon: '✨', title: t('contentTypes.brand_copy', 'Brand Copy'), description: 'Compelling marketing content and brand messaging' },
      'industry_news': { icon: '📰', title: t('contentTypes.industry_news'), description: 'Latest developments in the gambling and casino industry' },
      'sports_betting': { icon: '⚽', title: t('contentTypes.sports_betting'), description: 'Sports betting guides, predictions, and platform reviews' },
      'bonus_analysis': { icon: '🎁', title: t('contentTypes.bonus_analysis'), description: 'Detailed analysis of casino bonuses and promotions' },
      'regulatory_update': { icon: '⚖️', title: t('contentTypes.regulatory_update'), description: 'Updates on gambling laws and regulations' },
      'generic': { icon: '📝', title: t('contentTypes.general'), description: 'General purpose content' }
    };

    return typeMap[contentType] || { 
      icon: '📄', 
      title: contentType ? contentType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'Unknown Type', 
      description: 'Custom content type' 
    };
  };

  const handleTypeSelect = (typeId) => {
    setSelectedType(typeId);
    const typeInfo = getContentTypeInfo(typeId);
    updateData({
      contentType: typeId,
      contentTypeConfig: typeInfo
    });
  };

  const handleJurisdictionChange = (jurisdiction) => {
    setSelectedJurisdiction(jurisdiction);
    updateData({ jurisdiction });
  };

  const handleRegionChange = (regionKey) => {
    setSelectedRegion(regionKey);
    setSelectedJurisdiction(''); // Reset country selection when region changes
    setSearchQuery(''); // Clear search when region is selected
    setShowSearchResults(false);
  };

  // Search functionality
  const handleSearchChange = (query) => {
    setSearchQuery(query);
    setShowSearchResults(query.length > 0);
    if (query.length === 0) {
      setSelectedRegion('');
      setSelectedJurisdiction('');
    }
  };

  const getSearchResults = () => {
    if (searchQuery.length < 2) return [];
    
    const results = [];
    Object.entries(COMPLIANCE_REGIONS).forEach(([regionKey, region]) => {
      Object.entries(region.countries).forEach(([countryKey, country]) => {
        if (country.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            country.code.toLowerCase().includes(searchQuery.toLowerCase())) {
          results.push({
            countryKey,
            country,
            regionKey,
            regionName: region.name
          });
        }
      });
    });
    return results.slice(0, 10); // Limit to 10 results
  };

  const handleSearchResultSelect = (result) => {
    setSelectedRegion(result.regionKey);
    setSelectedJurisdiction(result.countryKey);
    setSearchQuery(result.country.name);
    setShowSearchResults(false);
  };

  const handleContinue = () => {
    if (selectedType) {
      const typeInfo = getContentTypeInfo(selectedType);
      updateData({
        contentType: selectedType,
        jurisdiction: selectedJurisdiction,
        contentTypeConfig: typeInfo
      });
      onNext();
    }
  };

  // Regional compliance templates organized by continent
  const COMPLIANCE_REGIONS = {
    europe: {
      name: 'Europe',
      countries: {
        albania: { name: 'Albania', code: 'AL' },
        andorra: { name: 'Andorra', code: 'AD' },
        bosnia_herzegovina: { name: 'Bosnia and Herzegovina', code: 'BA' },
        bulgaria: { name: 'Bulgaria', code: 'BG' },
        croatia: { name: 'Croatia', code: 'HR' },
        cyprus: { name: 'Cyprus', code: 'CY' },
        estonia: { name: 'Estonia', code: 'EE' },
        finland: { name: 'Finland', code: 'FI' },
        france: { name: 'France', code: 'FR' },
        hungary: { name: 'Hungary', code: 'HU' },
        iceland: { name: 'Iceland', code: 'IS' },
        ireland: { name: 'Ireland', code: 'IE' },
        italy: { name: 'Italy', code: 'IT' },
        latvia: { name: 'Latvia', code: 'LV' },
        liechtenstein: { name: 'Liechtenstein', code: 'LI' },
        luxembourg: { name: 'Luxembourg', code: 'LU' },
        malta: { name: 'Malta', code: 'MT' },
        moldova: { name: 'Moldova', code: 'MD' },
        monaco: { name: 'Monaco', code: 'MC' },
        montenegro: { name: 'Montenegro', code: 'ME' },
        netherlands: { name: 'Netherlands', code: 'NL' },
        north_macedonia: { name: 'North Macedonia', code: 'MK' },
        norway: { name: 'Norway', code: 'NO' },
        poland: { name: 'Poland', code: 'PL' },
        portugal: { name: 'Portugal', code: 'PT' },
        romania: { name: 'Romania', code: 'RO' },
        russia: { name: 'Russia', code: 'RU' },
        san_marino: { name: 'San Marino', code: 'SM' },
        slovakia: { name: 'Slovakia', code: 'SK' },
        slovenia: { name: 'Slovenia', code: 'SI' },
        spain: { name: 'Spain', code: 'ES' },
        sweden: { name: 'Sweden', code: 'SE' },
        united_kingdom: { name: 'United Kingdom', code: 'GB' },
        vatican_city: { name: 'Vatican City (Holy See)', code: 'VA' }
      }
    },
    america: {
      name: 'America',
      countries: {
        argentina: { name: 'Argentina', code: 'AR' },
        antigua_barbuda: { name: 'Antigua and Barbuda', code: 'AG' },
        bahamas: { name: 'Bahamas', code: 'BS' },
        barbados: { name: 'Barbados', code: 'BB' },
        bolivia: { name: 'Bolivia', code: 'BO' },
        brazil: { name: 'Brazil', code: 'BR' },
        canada: { name: 'Canada', code: 'CA' },
        chile: { name: 'Chile', code: 'CL' },
        colombia: { name: 'Colombia', code: 'CO' },
        costa_rica: { name: 'Costa Rica', code: 'CR' },
        cuba: { name: 'Cuba', code: 'CU' },
        dominican_republic: { name: 'Dominican Republic', code: 'DO' },
        ecuador: { name: 'Ecuador', code: 'EC' },
        el_salvador: { name: 'El Salvador', code: 'SV' },
        grenada: { name: 'Grenada', code: 'GD' },
        guatemala: { name: 'Guatemala', code: 'GT' },
        guyana: { name: 'Guyana', code: 'GY' },
        haiti: { name: 'Haiti', code: 'HT' },
        honduras: { name: 'Honduras', code: 'HN' },
        jamaica: { name: 'Jamaica', code: 'JM' },
        mexico: { name: 'Mexico', code: 'MX' },
        nicaragua: { name: 'Nicaragua', code: 'NI' },
        panama: { name: 'Panama', code: 'PA' },
        paraguay: { name: 'Paraguay', code: 'PY' },
        peru: { name: 'Peru', code: 'PE' },
        saint_kitts_nevis: { name: 'Saint Kitts and Nevis', code: 'KN' },
        saint_lucia: { name: 'Saint Lucia', code: 'LC' },
        saint_vincent_grenadines: { name: 'Saint Vincent and the Grenadines', code: 'VC' },
        suriname: { name: 'Suriname', code: 'SR' },
        trinidad_tobago: { name: 'Trinidad and Tobago', code: 'TT' },
        united_states: { name: 'United States', code: 'US' },
        uruguay: { name: 'Uruguay', code: 'UY' },
        venezuela: { name: 'Venezuela', code: 'VE' }
      }
    },
    africa: {
      name: 'Africa',
      countries: {
        algeria: { name: 'Algeria', code: 'DZ' },
        angola: { name: 'Angola', code: 'AO' },
        botswana: { name: 'Botswana', code: 'BW' },
        burkina_faso: { name: 'Burkina Faso', code: 'BF' },
        burundi: { name: 'Burundi', code: 'BI' },
        cameroon: { name: 'Cameroon', code: 'CM' },
        cape_verde: { name: 'Cape Verde', code: 'CV' },
        central_african_republic: { name: 'Central African Republic', code: 'CF' },
        chad: { name: 'Chad', code: 'TD' },
        comoros: { name: 'Comoros', code: 'KM' },
        democratic_republic_congo: { name: 'Democratic Republic of the Congo', code: 'CD' },
        djibouti: { name: 'Djibouti', code: 'DJ' },
        egypt: { name: 'Egypt', code: 'EG' },
        equatorial_guinea: { name: 'Equatorial Guinea', code: 'GQ' },
        eritrea: { name: 'Eritrea', code: 'ER' },
        eswatini: { name: 'Eswatini (Swaziland)', code: 'SZ' },
        ethiopia: { name: 'Ethiopia', code: 'ET' },
        gabon: { name: 'Gabon', code: 'GA' },
        gambia: { name: 'Gambia', code: 'GM' },
        ghana: { name: 'Ghana', code: 'GH' },
        guinea: { name: 'Guinea', code: 'GN' },
        guinea_bissau: { name: 'Guinea-Bissau', code: 'GW' },
        ivory_coast: { name: 'Ivory Coast (Côte d\'Ivoire)', code: 'CI' },
        kenya: { name: 'Kenya', code: 'KE' },
        lesotho: { name: 'Lesotho', code: 'LS' },
        liberia: { name: 'Liberia', code: 'LR' },
        libya: { name: 'Libya', code: 'LY' },
        madagascar: { name: 'Madagascar', code: 'MG' },
        malawi: { name: 'Malawi', code: 'MW' },
        mali: { name: 'Mali', code: 'ML' },
        mauritania: { name: 'Mauritania', code: 'MR' },
        mauritius: { name: 'Mauritius', code: 'MU' },
        morocco: { name: 'Morocco', code: 'MA' },
        mozambique: { name: 'Mozambique', code: 'MZ' },
        namibia: { name: 'Namibia', code: 'NA' },
        niger: { name: 'Niger', code: 'NE' },
        nigeria: { name: 'Nigeria', code: 'NG' },
        rwanda: { name: 'Rwanda', code: 'RW' },
        senegal: { name: 'Senegal', code: 'SN' },
        seychelles: { name: 'Seychelles', code: 'SC' },
        sierra_leone: { name: 'Sierra Leone', code: 'SL' },
        somalia: { name: 'Somalia', code: 'SO' },
        south_africa: { name: 'South Africa', code: 'ZA' },
        sudan: { name: 'Sudan', code: 'SD' },
        tanzania: { name: 'Tanzania', code: 'TZ' },
        togo: { name: 'Togo', code: 'TG' },
        tunisia: { name: 'Tunisia', code: 'TN' },
        uganda: { name: 'Uganda', code: 'UG' },
        zambia: { name: 'Zambia', code: 'ZM' },
        zimbabwe: { name: 'Zimbabwe', code: 'ZW' }
      }
    },
    asia: {
      name: 'Asia',
      countries: {
        armenia: { name: 'Armenia', code: 'AM' },
        azerbaijan: { name: 'Azerbaijan', code: 'AZ' },
        bangladesh: { name: 'Bangladesh', code: 'BD' },
        bhutan: { name: 'Bhutan', code: 'BT' },
        brunei: { name: 'Brunei', code: 'BN' },
        cambodia: { name: 'Cambodia', code: 'KH' },
        china: { name: 'China', code: 'CN' },
        georgia: { name: 'Georgia', code: 'GE' },
        india: { name: 'India', code: 'IN' },
        indonesia: { name: 'Indonesia', code: 'ID' },
        japan: { name: 'Japan', code: 'JP' },
        jordan: { name: 'Jordan', code: 'JO' },
        kazakhstan: { name: 'Kazakhstan', code: 'KZ' },
        kuwait: { name: 'Kuwait', code: 'KW' },
        kyrgyzstan: { name: 'Kyrgyzstan', code: 'KG' },
        laos: { name: 'Laos', code: 'LA' },
        lebanon: { name: 'Lebanon', code: 'LB' },
        malaysia: { name: 'Malaysia', code: 'MY' },
        maldives: { name: 'Maldives', code: 'MV' },
        mongolia: { name: 'Mongolia', code: 'MN' },
        myanmar: { name: 'Myanmar (Burma)', code: 'MM' },
        nepal: { name: 'Nepal', code: 'NP' },
        pakistan: { name: 'Pakistan', code: 'PK' },
        philippines: { name: 'Philippines', code: 'PH' },
        qatar: { name: 'Qatar', code: 'QA' },
        saudi_arabia: { name: 'Saudi Arabia', code: 'SA' },
        singapore: { name: 'Singapore', code: 'SG' },
        south_korea: { name: 'South Korea', code: 'KR' },
        sri_lanka: { name: 'Sri Lanka', code: 'LK' },
        taiwan: { name: 'Taiwan', code: 'TW' },
        tajikistan: { name: 'Tajikistan', code: 'TJ' },
        thailand: { name: 'Thailand', code: 'TH' },
        timor_leste: { name: 'Timor-Leste (East Timor)', code: 'TL' },
        turkey: { name: 'Turkey', code: 'TR' },
        turkmenistan: { name: 'Turkmenistan', code: 'TM' },
        uzbekistan: { name: 'Uzbekistan', code: 'UZ' },
        vietnam: { name: 'Vietnam', code: 'VN' },
        yemen: { name: 'Yemen', code: 'YE' }
      }
    },
    oceania: {
      name: 'Oceania',
      countries: {
        australia: { name: 'Australia', code: 'AU' },
        fiji: { name: 'Fiji', code: 'FJ' },
        kiribati: { name: 'Kiribati', code: 'KI' },
        marshall_islands: { name: 'Marshall Islands', code: 'MH' },
        micronesia: { name: 'Micronesia', code: 'FM' },
        nauru: { name: 'Nauru', code: 'NR' },
        new_zealand: { name: 'New Zealand', code: 'NZ' },
        palau: { name: 'Palau', code: 'PW' },
        samoa: { name: 'Samoa', code: 'WS' },
        solomon_islands: { name: 'Solomon Islands', code: 'SB' },
        tonga: { name: 'Tonga', code: 'TO' },
        tuvalu: { name: 'Tuvalu', code: 'TV' },
        vanuatu: { name: 'Vanuatu', code: 'VU' }
      }
    }
  };

  // Create jurisdictions array from regions
  const jurisdictions = [
    { id: 'international', name: 'International', description: 'General international audience' },
    ...Object.entries(COMPLIANCE_REGIONS).flatMap(([regionKey, region]) =>
      Object.entries(region.countries).map(([countryKey, country]) => ({
        id: countryKey,
        name: country.name,
        description: `${region.name} - ${country.code}`,
        region: regionKey,
        code: country.code
      }))
    )
  ];

  return (
    <div className="space-y-8">
      {/* Content Type Selection */}
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-4">
          Select Content Type
        </h3>
        <p className="text-gray-600 mb-6">
          Choose the type of content you want to create. This will customize the AI prompts and structure for your specific needs.
        </p>
        
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Loading content types...</span>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {contentTypes.map((type) => {
              const typeInfo = getContentTypeInfo(type.content_type);
              return (
                <div
                  key={type.content_type}
                  onClick={() => handleTypeSelect(type.content_type)}
                  className={`p-6 rounded-xl border-2 cursor-pointer transition-all duration-200 hover:shadow-lg ${
                    selectedType === type.content_type
                      ? 'border-blue-500 bg-blue-50 shadow-md'
                      : 'border-gray-200 bg-white hover:border-gray-300'
                  }`}
                >
                  <div className="text-3xl mb-3">{typeInfo.icon}</div>
                  <h4 className="font-semibold text-gray-900 mb-2">{typeInfo.title}</h4>
                  <p className="text-sm text-gray-600 leading-relaxed">{typeInfo.description}</p>
                  {type.total_tasks > 0 && (
                    <div className="mt-3 text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full inline-block">
                      {type.total_tasks} articles created
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Jurisdiction Selection */}
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-4">
          Target Jurisdiction
        </h3>
        <p className="text-gray-600 mb-6">
          Select the primary jurisdiction for compliance and regulatory considerations.
        </p>
        
        <div className="space-y-4">
          {/* International Option */}
          <div className="mb-4">
            <button
              onClick={() => {
                handleJurisdictionChange('international');
                setSelectedRegion('');
              }}
              className={`w-full p-3 rounded-lg border text-left transition-all ${
                selectedJurisdiction === 'international'
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              <div className="font-medium">🌍 International</div>
              <div className="text-sm text-gray-500 mt-1">General international audience</div>
            </button>
          </div>

          <div className="border-t pt-4">
            <h4 className="font-medium text-gray-900 mb-3">Search for a country or select by region:</h4>
            
            {/* Search Input */}
            <div className="relative mb-4">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => handleSearchChange(e.target.value)}
                placeholder="🔍 Search for a country (e.g., United States, UK, JP)..."
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-10"
              />
              {searchQuery && (
                <button
                  onClick={() => {
                    setSearchQuery('');
                    setShowSearchResults(false);
                    setSelectedRegion('');
                    setSelectedJurisdiction('');
                  }}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              )}
              
              {/* Search Results Dropdown */}
              {showSearchResults && getSearchResults().length > 0 && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                  {getSearchResults().map((result, index) => (
                    <button
                      key={`${result.regionKey}-${result.countryKey}`}
                      onClick={() => handleSearchResultSelect(result)}
                      className="w-full text-left p-3 hover:bg-blue-50 border-b border-gray-100 last:border-b-0 focus:bg-blue-50 focus:outline-none"
                    >
                      <div className="font-medium text-gray-900">{result.country.name}</div>
                      <div className="text-sm text-gray-500">{result.regionName} • {result.country.code}</div>
                    </button>
                  ))}
                </div>
              )}
              
              {/* No Results Message */}
              {showSearchResults && searchQuery.length >= 2 && getSearchResults().length === 0 && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg p-3">
                  <div className="text-gray-500 text-center">No countries found matching "{searchQuery}"</div>
                </div>
              )}
            </div>
            
            {/* Step 1: Region Selection */}
            <select
              value={selectedRegion}
              onChange={(e) => handleRegionChange(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 mb-3"
            >
              <option value="">1. Choose a region...</option>
              {Object.entries(COMPLIANCE_REGIONS).map(([regionKey, region]) => (
                <option key={regionKey} value={regionKey}>
                  {region.name}
                </option>
              ))}
            </select>

            {/* Step 2: Country Selection */}
            {selectedRegion && (
              <select
                value={selectedJurisdiction}
                onChange={(e) => handleJurisdictionChange(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">2. Choose a country in {COMPLIANCE_REGIONS[selectedRegion]?.name}...</option>
                {selectedRegion && Object.entries(COMPLIANCE_REGIONS[selectedRegion].countries).map(([countryKey, country]) => (
                  <option key={countryKey} value={countryKey}>
                    {country.name} ({country.code})
                  </option>
                ))}
              </select>
            )}
          </div>
          
          {/* Selection Confirmation */}
          {selectedJurisdiction && selectedJurisdiction !== 'international' && (
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="font-medium text-blue-900">
                Selected: {jurisdictions.find(j => j.id === selectedJurisdiction)?.name}
              </div>
              <div className="text-sm text-blue-700 mt-1">
                Region: {COMPLIANCE_REGIONS[selectedRegion]?.name}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Compliance Notice */}
      {selectedType && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <div className="text-amber-600 text-xl">⚠️</div>
            <div>
              <h4 className="font-medium text-amber-800 mb-1">Compliance Notice</h4>
              <p className="text-sm text-amber-700">
                Content will be generated with appropriate compliance considerations for the selected jurisdiction. 
                Always review and verify regulatory requirements with legal professionals before publication.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Continue Button */}
      <div className="flex justify-end pt-6">
        <button
          onClick={handleContinue}
          disabled={!selectedType}
          className={`flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
            selectedType
              ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 shadow-lg'
              : 'bg-gray-200 text-gray-500 cursor-not-allowed'
          }`}
        >
          <span>Continue to Keyword Research</span>
          <ChevronRightIcon className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

export default ContentTypeSelection;