import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { PlayIcon } from '@heroicons/react/24/outline';
import apiClient from '../../utils/apiClient';

const FloatingCTA = ({ article }) => {
  const { t } = useTranslation();
  const [affiliateLink, setAffiliateLink] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const toggleVisibility = () => {
      // 显示浮动按钮在页面滚动超过100px后
      if (window.pageYOffset > 100) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);
    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  // 获取文章的第一个联盟链接（不区分位置）
  const fetchAffiliateLink = async () => {
    if (!article?.id) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const response = await apiClient.get(`/api/affiliate/articles/${article.id}/links`);
      
      if (response.success) {
        const links = response.data || [];
        // 获取第一个可用的联盟链接
        const link = links[0];
        setAffiliateLink(link);
      }
    } catch (error) {
      console.error('获取联盟链接失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理按钮点击
  const handleClick = async (e) => {
    e.preventDefault();
    
    const targetUrl = affiliateLink?.affiliate_url;
    
    if (affiliateLink && targetUrl) {
      // 记录点击数据
      try {
        const clickData = {
          affiliate_link_id: affiliateLink.affiliate_link_id || affiliateLink.id,
          article_id: article.id,
          article_url: window.location.href,
          article_title: article.title,
          click_position: 'floating_cta',
          user_agent: navigator.userAgent,
          referer: document.referrer,
          session_id: sessionStorage.getItem('session_id') || generateSessionId()
        };

        // 异步记录点击
        apiClient.post('/api/affiliate/clicks', clickData).catch(err => {
          console.error('记录点击失败:', err);
        });
      } catch (error) {
        console.error('处理点击事件失败:', error);
      }
      
      // 打开链接
      window.open(targetUrl, '_blank', 'noopener,noreferrer');
    }
  };

  // 生成会话ID
  const generateSessionId = () => {
    const sessionId = 'sess_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    sessionStorage.setItem('session_id', sessionId);
    return sessionId;
  };

  useEffect(() => {
    fetchAffiliateLink();
  }, [article?.id]);

  // 如果没有联盟链接或正在加载，不显示按钮
  if (loading || !affiliateLink) {
    return null;
  }

  const displayText = article?.language === 'pt' ? 'Visitar Cassino' : 'Visit Casino';

  return (
    <>
      {isVisible && (
        <button
          onClick={handleClick}
          className="fixed bottom-20 right-4 z-40 bg-gradient-to-r from-casino-gold-500 to-casino-gold-600 hover:from-casino-gold-600 hover:to-casino-gold-700 text-casino-dark-900 px-6 py-3 rounded-full shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 flex items-center space-x-2 font-bold"
          disabled={loading}
        >
          <PlayIcon className="w-5 h-5" />
          <span>{displayText}</span>
        </button>
      )}
    </>
  );
};

export default FloatingCTA;