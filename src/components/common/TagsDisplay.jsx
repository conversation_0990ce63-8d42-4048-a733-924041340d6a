import React from 'react';
import { useNavigate } from 'react-router-dom';
import { TagIcon } from '@heroicons/react/24/outline';
import { useTranslation } from 'react-i18next';

/**
 * 可重用的标签显示组件
 * 支持点击跳转到标签页面
 */
const TagsDisplay = ({ 
  tags, 
  title = null, 
  className = "", 
  showIcon = true,
  variant = "default" // default, minimal, inline
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // 如果没有标签，不显示
  if (!tags || !Array.isArray(tags) || tags.length === 0) {
    return null;
  }

  const handleTagClick = (tag) => {
    // 转换为URL友好的格式
    const urlFriendlyTag = encodeURIComponent(tag.toLowerCase().replace(/\s+/g, '-'));
    navigate(`/pt/tags/${urlFriendlyTag}`);
  };

  // 根据variant选择不同的样式
  const getContainerStyle = () => {
    switch (variant) {
      case 'minimal':
        return "flex flex-wrap gap-2";
      case 'inline':
        return "inline-flex flex-wrap gap-2";
      default:
        return "bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6";
    }
  };

  const getTagStyle = () => {
    switch (variant) {
      case 'minimal':
        return "px-3 py-1 bg-blue-500/20 text-blue-300 text-sm rounded-full border border-blue-500/30 hover:border-blue-500/50 transition-all duration-200 cursor-pointer font-medium hover:bg-blue-500/30";
      case 'inline':
        return "px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full hover:bg-gray-200 transition-colors cursor-pointer font-medium";
      default:
        return "px-4 py-2 bg-blue-500/20 text-blue-300 text-sm rounded-full border border-blue-500/30 hover:border-blue-500/50 transition-all duration-200 cursor-pointer font-medium hover:bg-blue-500/30 hover:scale-105";
    }
  };

  const getTitleStyle = () => {
    switch (variant) {
      case 'minimal':
      case 'inline':
        return "text-gray-600 font-semibold text-sm mr-3";
      default:
        return "text-white font-semibold text-lg";
    }
  };

  return (
    <div className={`${getContainerStyle()} ${className}`}>
      {/* 标题部分 */}
      {(title || (variant === 'default' && showIcon)) && (
        <div className={`flex items-center space-x-3 ${variant === 'default' ? 'mb-4' : 'mb-0'}`}>
          {showIcon && variant === 'default' && (
            <TagIcon className="w-6 h-6 text-blue-400" />
          )}
          <span className={getTitleStyle()}>
            {title || t('article.tags', 'Tags')}
          </span>
        </div>
      )}
      
      {/* 标签列表 */}
      <div className={`flex flex-wrap gap-3 ${variant === 'default' ? '' : 'items-center'}`}>
        {tags.map((tag, index) => (
          <span
            key={index}
            onClick={() => handleTagClick(tag)}
            className={getTagStyle()}
            role="button"
            tabIndex={0}
            onKeyPress={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                handleTagClick(tag);
              }
            }}
            aria-label={`View articles tagged with ${tag}`}
          >
            #{tag}
          </span>
        ))}
      </div>
    </div>
  );
};

export default TagsDisplay;