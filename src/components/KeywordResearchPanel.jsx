import React, { useState, useCallback, useRef, useMemo } from 'react';
import { ChevronDownIcon, ChevronUpIcon, QuestionMarkCircleIcon, TagIcon, CloudIcon } from '@heroicons/react/24/outline';

const KeywordResearchPanel = ({
  researchData,
  onKeywordSelect, // Keep if used for other purposes by Step0KeywordResearch
  onTopicSelect,   // Keep if used for other purposes by Step0KeywordResearch
  selectedKeywords = [], // New: array of selected keyword strings
  onToggleKeyword // New: function to toggle a keyword string's selection status
}) => {
  const [expandedSections, setExpandedSections] = useState({
    autocompleteSuggestions: true,
    relatedKeywords: true,
    wordCloud: true,
    peopleAlsoAsk: true
  });

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  if (!researchData) return null;

  const { autocompleteSuggestions, relatedKeywords, peopleAlsoAsk, wordCloudData, knowledgeGraph } = researchData;

  // Simplified selection checker using the new selectedKeywords prop
  const isSelected = (keyword) => {
    return selectedKeywords.includes(keyword);
  };

  // Simplified click handler
  const handleItemClick = (keyword) => {
    onToggleKeyword(keyword);
  };

  return (
    <div className="p-6 space-y-6">

      {/* Knowledge Graph */}
      {knowledgeGraph && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 shadow-sm">
          <h4 className="font-bold text-blue-900 mb-3 text-lg">{knowledgeGraph.title}</h4>
          {knowledgeGraph.type && (
            <p className="text-sm text-blue-700 mb-3 font-medium">{knowledgeGraph.type}</p>
          )}
          {knowledgeGraph.description && (
            <p className="text-sm text-blue-800 leading-relaxed">{knowledgeGraph.description}</p>
          )}
        </div>
      )}

      {/* 1. Autocomplete Suggestions - FIRST */}
      {autocompleteSuggestions && autocompleteSuggestions.length > 0 && (
        <div className="border-2 border-blue-200 rounded-xl bg-gradient-to-r from-blue-50 to-indigo-50 shadow-sm">
          <button
            onClick={() => toggleSection('autocompleteSuggestions')}
            className="w-full flex items-center justify-between p-5 text-left hover:bg-blue-100/50 transition-colors duration-200"
          >
            <div className="flex items-center justify-between w-full">
              <h4 className="font-bold text-lg text-blue-900 flex items-center">
                <TagIcon className="w-6 h-6 mr-3 text-blue-600" />
                🔍 Google Autocomplete Suggestions ({autocompleteSuggestions.length})
              </h4>
              {/* Limit display removed, will be handled in SelectionManager */}
            </div>
            {expandedSections.autocompleteSuggestions ? (
              <ChevronUpIcon className="w-5 h-5 text-blue-600 ml-3" />
            ) : (
              <ChevronDownIcon className="w-5 h-5 text-blue-600 ml-3" />
            )}
          </button>

          {expandedSections.autocompleteSuggestions && (
            <div className="px-5 pb-5">
              <div className="flex items-center justify-between mb-4">
                <p className="text-sm text-blue-800 font-medium">
                  💡 Click any suggestion to add or remove it from your selections!
                </p>
                {/* Limit display removed */}
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                {autocompleteSuggestions.map((suggestion, index) => {
                  const selected = isSelected(suggestion);
                  // Disability based on limits is removed, handled by parent or SelectionManager
                  const disabled = false; // Or some other logic if needed, but not limit-based here

                  return (
                    <button
                      key={index}
                      onClick={() => handleItemClick(suggestion)}
                      disabled={disabled} // Kept if other disabling logic applies
                      className={`text-left p-3 rounded-lg border cursor-pointer ${
                        selected
                          ? 'bg-blue-100 border-blue-500 text-blue-900 font-semibold'
                          : disabled
                          ? 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed'
                          : 'bg-white hover:bg-blue-50 border-blue-200 hover:border-blue-400 text-gray-900'
                      }`}
                    >
                      <span className="text-sm flex items-center">
                        <span className={`mr-2 text-base ${selected ? 'text-blue-600 font-bold' : ''}`}>
                          {selected ? '✅' : '➕'}
                        </span>
                        {suggestion}
                      </span>
                    </button>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      )}

      {/* 2. Related Keywords - SECOND */}
      {relatedKeywords && relatedKeywords.length > 0 && (
        <div className="border border-gray-200 rounded-xl bg-gradient-to-r from-gray-50 to-slate-50 shadow-sm">
          <button
            onClick={() => toggleSection('relatedKeywords')}
            className="w-full flex items-center justify-between p-5 text-left hover:bg-gray-100/50 transition-colors duration-200"
          >
            <div className="flex items-center justify-between w-full">
              <h4 className="font-bold text-lg text-gray-900">🏷️ Related Keywords ({relatedKeywords.length})</h4>
              {/* Limit display removed */}
            </div>
            {expandedSections.relatedKeywords ? (
              <ChevronUpIcon className="w-5 h-5 text-gray-600 ml-3" />
            ) : (
              <ChevronDownIcon className="w-5 h-5 text-gray-600 ml-3" />
            )}
          </button>

          {expandedSections.relatedKeywords && (
            <div className="px-5 pb-5">
              <div className="flex items-center justify-between mb-4">
                <p className="text-sm text-gray-700 font-medium">
                  💡 Click any keyword to add or remove it from your selections!
                </p>
                {/* Limit display removed */}
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                {relatedKeywords.map((keyword, index) => {
                  const selected = isSelected(keyword.query);
                  const disabled = false; // Limit-based disabling removed

                  return (
                    <button
                      key={index}
                      onClick={() => handleItemClick(keyword.query)}
                      disabled={disabled}
                      className={`text-left p-3 rounded-lg border cursor-pointer ${
                        selected
                          ? 'bg-green-100 border-green-500 text-green-900 font-semibold'
                          : disabled
                          ? 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed'
                          : 'bg-gray-50 hover:bg-green-50 border-gray-200 hover:border-green-300 text-gray-900'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <span className="text-sm flex items-center">
                          <span className={`mr-2 text-base ${selected ? 'text-green-600 font-bold' : ''}`}>
                            {selected ? '✅' : '➕'}
                          </span>
                          {keyword.query}
                        </span>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          keyword.relevance === 'high' ? 'bg-green-100 text-green-800' :
                          keyword.relevance === 'medium' ? 'bg-orange-100 text-orange-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {keyword.relevance}
                        </span>
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      )}

      {/* 3. Word Cloud - THIRD */}
      {wordCloudData && wordCloudData.words && wordCloudData.words.length > 0 && (
        <div className="border border-purple-200 rounded-xl bg-gradient-to-r from-purple-50 to-violet-50 shadow-sm">
          <button
            onClick={() => toggleSection('wordCloud')}
            className="w-full flex items-center justify-between p-5 text-left hover:bg-purple-100/50 transition-colors duration-200"
          >
            <div className="flex items-center justify-between w-full">
              <h4 className="font-bold text-lg text-purple-900 flex items-center">
                <CloudIcon className="w-6 h-6 mr-3 text-purple-600" />
                ☁️ Key Terms ({wordCloudData.words.length})
              </h4>
              {/* Limit display removed */}
            </div>
            {expandedSections.wordCloud ? (
              <ChevronUpIcon className="w-5 h-5 text-purple-600 ml-3" />
            ) : (
              <ChevronDownIcon className="w-5 h-5 text-purple-600 ml-3" />
            )}
          </button>

          {expandedSections.wordCloud && (
            <div className="px-5 pb-5">
              <div className="flex items-center justify-between mb-4">
                <p className="text-sm text-purple-800 font-medium">
                  💡 Click any term to add or remove it from your selections!
                </p>
                {/* Limit display removed */}
              </div>
              <div className="flex flex-wrap gap-2">
                {wordCloudData.words.slice(0, 30).map((wordData, index) => {
                  const selected = isSelected(wordData.word);
                  const disabled = false; // Limit-based disabling removed

                  return (
                    <button
                      key={index}
                      onClick={() => handleItemClick(wordData.word)}
                      disabled={disabled}
                      className={`inline-block px-3 py-2 m-1 rounded-lg text-sm border cursor-pointer ${
                        selected
                          ? 'bg-purple-200 text-purple-900 border-purple-500 font-semibold'
                          : disabled
                          ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                          : 'bg-purple-100 text-purple-800 border-purple-300 hover:bg-purple-200'
                      }`}
                    >
                      {selected ? '✅ ' : ''}{wordData.word} ({wordData.count})
                    </button>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      )}

      {/* 4. People Also Ask - FOURTH */}
      {peopleAlsoAsk && peopleAlsoAsk.length > 0 && (
        <div className="border-2 border-orange-200 rounded-xl bg-gradient-to-r from-orange-50 to-amber-50 shadow-sm">
          <button
            onClick={() => toggleSection('peopleAlsoAsk')}
            className="w-full flex items-center justify-between p-5 text-left hover:bg-orange-100/50 transition-colors duration-200"
          >
            <div className="flex items-center justify-between w-full">
              <h4 className="font-bold text-lg text-orange-900 flex items-center">
                <QuestionMarkCircleIcon className="w-6 h-6 mr-3 text-orange-600" />
                🔥 Popular Questions People Ask ({peopleAlsoAsk.length})
              </h4>
              {/* Limit display removed */}
            </div>
            {expandedSections.peopleAlsoAsk ? (
              <ChevronUpIcon className="w-5 h-5 text-orange-600 ml-3" />
            ) : (
              <ChevronDownIcon className="w-5 h-5 text-orange-600 ml-3" />
            )}
          </button>

          {expandedSections.peopleAlsoAsk && (
            <div className="px-5 pb-5 space-y-3">
              <div className="flex items-center justify-between mb-4">
                <p className="text-sm text-orange-800 font-medium">
                  💡 Click any question to add or remove it from your selections!
                </p>
                {/* Limit display removed */}
              </div>
              {peopleAlsoAsk.map((item, index) => {
                const selected = isSelected(item.question);
                const disabled = false; // Limit-based disabling removed

                return (
                  <button
                    key={index}
                    onClick={() => handleItemClick(item.question)}
                    disabled={disabled}
                    className={`w-full text-left p-3 rounded-lg border cursor-pointer ${
                      selected
                        ? 'bg-orange-100 border-orange-500 text-orange-900 font-semibold'
                        : disabled
                        ? 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed'
                        : 'bg-white hover:bg-orange-50 border-orange-200 hover:border-orange-400 text-gray-900'
                    }`}
                  >
                    <h5 className="font-medium text-base flex items-center">
                      <span className={`mr-2 text-lg ${selected ? 'text-orange-600 font-bold' : ''}`}>
                        {selected ? '✅' : '➕'}
                      </span>
                      {item.question}
                    </h5>
                  </button>
                );
              })}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default KeywordResearchPanel;
