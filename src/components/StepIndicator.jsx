import React from 'react';
import { CheckIcon } from '@heroicons/react/24/solid';

const StepIndicator = ({ steps, currentStep, onStepClick, isTaskCompleted = false }) => {
  return (
    <div className="bg-white rounded-lg p-4 border border-slate-300 shadow-lg">
      <h3 className="text-lg font-semibold text-slate-800 mb-4">Steps</h3>

      <nav className="space-y-2">
        {steps.map((step, index) => {
          // If task is completed, all steps are completed
          const isCompleted = isTaskCompleted ? true : index < currentStep;
          const isCurrent = isTaskCompleted ? false : index === currentStep;
          const isClickable = index <= currentStep;

          return (
            <div key={step.id} className="relative">
              <div
                className={`flex items-center p-3 rounded-md cursor-pointer transition-all duration-200 ${
                  isClickable ? 'hover:bg-slate-50' : 'cursor-not-allowed opacity-60'
                } ${
                  isCurrent ? 'bg-orange-50 border border-orange-200' : ''
                }`}
                onClick={() => isClickable && onStepClick(index)}
              >
                {/* Step Number/Icon */}
                <div className="flex-shrink-0">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-200 ${
                      isCompleted
                        ? 'bg-green-500 text-white'
                        : isCurrent
                        ? 'bg-orange-500 text-white'
                        : 'bg-slate-200 text-slate-600 border border-slate-300'
                    }`}
                  >
                    {isCompleted ? (
                      <CheckIcon className="w-4 h-4" />
                    ) : (
                      step.id
                    )}
                  </div>
                </div>

                {/* Step Content */}
                <div className="ml-3 flex-1 min-w-0">
                  <p
                    className={`text-sm font-medium ${
                      isCurrent
                        ? 'text-slate-800'
                        : isCompleted
                        ? 'text-slate-700'
                        : 'text-slate-600'
                    }`}
                  >
                    {step.title}
                  </p>
                  <p className={`text-xs mt-1 ${
                    isCurrent ? 'text-slate-600' : 'text-slate-500'
                  }`}>
                    {step.subtitle}
                  </p>
                </div>

                {/* Status Indicator */}
                <div className="flex-shrink-0">
                  {isCurrent && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 border border-orange-200">
                      Current
                    </span>
                  )}
                </div>
              </div>

              {/* Connector Line */}
              {index < steps.length - 1 && (
                <div className="ml-7 w-0.5 h-4 bg-slate-300"></div>
              )}
            </div>
          );
        })}
      </nav>

      {/* Progress Summary */}
      <div className="mt-6 pt-4 border-t border-slate-200">
        <div className="flex items-center justify-between text-sm">
          <span className="text-slate-600">Progress</span>
          <span className="font-medium text-slate-800">
            {isTaskCompleted ? '100%' : `${Math.round((currentStep / steps.length) * 100)}%`}
          </span>
        </div>
        <div className="mt-2 w-full bg-slate-200 rounded-full h-2 border border-slate-300">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${
              isTaskCompleted ? 'bg-green-500' : 'bg-orange-500'
            }`}
            style={{ width: `${isTaskCompleted ? 100 : (currentStep / steps.length) * 100}%` }}
          ></div>
        </div>
        <div className="flex justify-between text-xs text-slate-500 mt-2">
          <span>{isTaskCompleted ? steps.length : currentStep} completed</span>
          <span>{isTaskCompleted ? 0 : steps.length - currentStep} remaining</span>
        </div>
      </div>
    </div>
  );
};

export default StepIndicator;
