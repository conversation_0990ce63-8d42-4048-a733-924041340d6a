import React, { useState, useEffect } from 'react';
import ArticleAffiliateDisplay from './ArticleAffiliateDisplay';
import AffiliateButton from './AffiliateButton';
import apiClient from '../../utils/apiClient';

/**
 * 智能联盟按钮组件
 * 优先显示用户配置的联盟链接，如果没有配置则显示推荐链接
 */
const SmartAffiliateButton = ({ 
  article, 
  position = 'primary_cta',
  size = 'default',
  className = '',
  showIcon = true,
  trackingData = {} 
}) => {
  const [hasConfiguredLinks, setHasConfiguredLinks] = useState(false);
  const [loading, setLoading] = useState(true);

  // 检查是否有配置的联盟链接
  const checkConfiguredLinks = async () => {
    if (!article?.id) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const response = await apiClient.get(`/api/affiliate/articles/${article.id}/links`);
      
      if (response.success) {
        const links = response.data || [];
        
        // 检查是否有指定位置的链接
        const hasLinksForPosition = links.some(link => link.position === position);
        setHasConfiguredLinks(hasLinksForPosition);
      }
    } catch (error) {
      console.error('检查联盟链接失败:', error);
      setHasConfiguredLinks(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkConfiguredLinks();
  }, [article?.id, position]);

  // 如果正在加载，显示占位符
  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="bg-gray-300 rounded-xl h-12 w-32"></div>
      </div>
    );
  }

  // 如果有配置的链接，使用ArticleAffiliateDisplay
  if (hasConfiguredLinks) {
    return (
      <ArticleAffiliateDisplay
        articleId={article.id}
        position={position}
        size={size}
        className={className}
        showIcon={showIcon}
        maxLinks={1}
        layout="horizontal"
      />
    );
  }

  // 否则使用智能推荐的AffiliateButton
  return (
    <AffiliateButton
      article={article}
      position={position}
      size={size}
      className={className}
      showIcon={showIcon}
      trackingData={trackingData}
    />
  );
};

/**
 * 智能联盟按钮组合组件
 * 支持多个位置的按钮组合显示
 */
export const SmartAffiliateButtonGroup = ({ 
  article, 
  positions = ['primary_cta', 'secondary_cta'],
  className = '',
  layout = 'horizontal',
  trackingData = {}
}) => {
  const containerClass = layout === 'vertical' 
    ? 'space-y-4' 
    : 'flex flex-wrap gap-4';

  return (
    <div className={`${containerClass} ${className}`}>
      {positions.map(position => (
        <SmartAffiliateButton
          key={position}
          article={article}
          position={position}
          className={layout === 'vertical' ? 'w-full' : ''}
          trackingData={{...trackingData, position}}
        />
      ))}
    </div>
  );
};

export default SmartAffiliateButton;