import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  PlusIcon,
  MinusIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import api from '../../config/api';

/**
 * 联盟链接创建/编辑表单组件
 */
const AffiliateLinkForm = ({ link, onSave, onCancel }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    affiliate_url: '',
    display_text: '',
    button_style: 'primary',
    content_types: [],
    categories: [],
    target_countries: [],
    target_languages: [],
    commission_rate: '',
    commission_type: 'percentage',
    tracking_code: '',
    tracking_params: {},
    priority: 0,
    is_active: true,
    start_date: '',
    end_date: '',
    notes: ''
  });
  
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  // 预定义的选项
  const contentTypeOptions = [
    { value: 'casino_review', label: '赌场评论' },
    { value: 'sports_betting', label: '体育博彩' },
    { value: 'game_guide', label: '游戏指南' },
    { value: 'strategy_article', label: '策略文章' },
    { value: 'industry_news', label: '行业新闻' },
    { value: 'bonus_analysis', label: '奖金分析' },
    { value: 'regulatory_update', label: '监管更新' },
    { value: 'brand_copy', label: '品牌文案' },
    { value: 'generic', label: '通用内容' }
  ];

  const categoryOptions = [
    { value: 'casino', label: '赌场' },
    { value: 'sportsbook', label: '体育书' },
    { value: 'poker', label: '扑克' },
    { value: 'esports', label: '电子竞技' },
    { value: 'crypto', label: '加密货币' },
    { value: 'exchange', label: '交易所' },
    { value: 'software', label: '软件' },
    { value: 'payment', label: '支付' }
  ];

  const languageOptions = [
    { value: 'en', label: '英语' },
    { value: 'pt', label: '葡萄牙语' },
    { value: 'es', label: '西班牙语' },
    { value: 'zh', label: '中文' },
    { value: 'de', label: '德语' },
    { value: 'fr', label: '法语' },
    { value: 'it', label: '意大利语' },
    { value: 'ja', label: '日语' }
  ];

  const countryOptions = [
    { value: 'US', label: '美国' },
    { value: 'BR', label: '巴西' },
    { value: 'ES', label: '西班牙' },
    { value: 'DE', label: '德国' },
    { value: 'CA', label: '加拿大' },
    { value: 'MX', label: '墨西哥' },
    { value: 'AR', label: '阿根廷' },
    { value: 'FR', label: '法国' },
    { value: 'IT', label: '意大利' },
    { value: 'CN', label: '中国' },
    { value: 'JP', label: '日本' },
    { value: 'EU', label: '欧盟' },
    { value: 'AS', label: '亚洲' }
  ];

  const buttonStyleOptions = [
    { value: 'primary', label: '主要按钮 (蓝色)' },
    { value: 'secondary', label: '次要按钮 (灰色)' },
    { value: 'success', label: '成功按钮 (绿色)' },
    { value: 'warning', label: '警告按钮 (橙色)' },
    { value: 'danger', label: '危险按钮 (红色)' }
  ];

  // 初始化表单数据
  useEffect(() => {
    if (link) {
      setFormData({
        ...link,
        start_date: link.start_date ? link.start_date.split('T')[0] : '',
        end_date: link.end_date ? link.end_date.split('T')[0] : '',
        tracking_params: link.tracking_params || {}
      });
    }
  }, [link]);

  // 处理输入变化
  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  // 处理多选字段
  const handleMultiSelect = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].includes(value)
        ? prev[field].filter(item => item !== value)
        : [...prev[field], value]
    }));
  };

  // 处理 UTM 参数
  const handleUTMChange = (key, value) => {
    setFormData(prev => ({
      ...prev,
      tracking_params: {
        ...prev.tracking_params,
        [key]: value
      }
    }));
  };

  // 表单验证
  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = '链接名称为必填项';
    }

    if (!formData.affiliate_url.trim()) {
      newErrors.affiliate_url = '联盟链接URL为必填项';
    } else {
      try {
        new URL(formData.affiliate_url);
      } catch {
        newErrors.affiliate_url = '请输入有效的URL';
      }
    }

    if (!formData.display_text.trim()) {
      newErrors.display_text = '显示文字为必填项';
    }

    if (formData.commission_rate && (isNaN(formData.commission_rate) || formData.commission_rate < 0 || formData.commission_rate > 100)) {
      newErrors.commission_rate = '佣金率应该是0-100之间的数字';
    }

    if (formData.priority && (isNaN(formData.priority) || formData.priority < 0)) {
      newErrors.priority = '优先级应该是正数';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 提交表单
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const submitData = {
        ...formData,
        commission_rate: parseFloat(formData.commission_rate) || null,
        priority: parseInt(formData.priority) || 0,
        start_date: formData.start_date || null,
        end_date: formData.end_date || null
      };

      let response;
      if (link) {
        response = await api.put(`/affiliate/links/${link.id}`, submitData);
      } else {
        response = await api.post('/affiliate/links', submitData);
      }

      if (response.data.success) {
        onSave();
      }
    } catch (error) {
      console.error('保存联盟链接失败:', error);
      // 可以添加错误提示
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 基本信息 */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="text-lg font-medium text-gray-900 mb-4">基本信息</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              链接名称 *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                errors.name ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="例如: Bet365 Casino"
            />
            {errors.name && <p className="mt-1 text-xs text-red-600">{errors.name}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              显示文字 *
            </label>
            <input
              type="text"
              value={formData.display_text}
              onChange={(e) => handleChange('display_text', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                errors.display_text ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="例如: 访问 Bet365"
            />
            {errors.display_text && <p className="mt-1 text-xs text-red-600">{errors.display_text}</p>}
          </div>
        </div>

        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            联盟链接URL *
          </label>
          <input
            type="url"
            value={formData.affiliate_url}
            onChange={(e) => handleChange('affiliate_url', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
              errors.affiliate_url ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="https://example.com/?ref=writer777"
          />
          {errors.affiliate_url && <p className="mt-1 text-xs text-red-600">{errors.affiliate_url}</p>}
        </div>

        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            描述
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => handleChange('description', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            placeholder="简要描述这个联盟链接..."
          />
        </div>
      </div>

      {/* 样式和配置 */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="text-lg font-medium text-gray-900 mb-4">样式和配置</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              按钮样式
            </label>
            <select
              value={formData.button_style}
              onChange={(e) => handleChange('button_style', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              {buttonStyleOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              优先级
            </label>
            <input
              type="number"
              value={formData.priority}
              onChange={(e) => handleChange('priority', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                errors.priority ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="0"
              min="0"
            />
            {errors.priority && <p className="mt-1 text-xs text-red-600">{errors.priority}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              状态
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_active}
                onChange={(e) => handleChange('is_active', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">激活链接</span>
            </label>
          </div>
        </div>
      </div>

      {/* 内容类型和分类 */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="text-lg font-medium text-gray-900 mb-4">内容类型和分类</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              适用内容类型
            </label>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {contentTypeOptions.map(option => (
                <label key={option.value} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.content_types.includes(option.value)}
                    onChange={() => handleMultiSelect('content_types', option.value)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">{option.label}</span>
                </label>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              分类
            </label>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {categoryOptions.map(option => (
                <label key={option.value} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.categories.includes(option.value)}
                    onChange={() => handleMultiSelect('categories', option.value)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">{option.label}</span>
                </label>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 地理和语言定位 */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="text-lg font-medium text-gray-900 mb-4">地理和语言定位</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              目标国家
            </label>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {countryOptions.map(option => (
                <label key={option.value} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.target_countries.includes(option.value)}
                    onChange={() => handleMultiSelect('target_countries', option.value)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">{option.label}</span>
                </label>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              目标语言
            </label>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {languageOptions.map(option => (
                <label key={option.value} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.target_languages.includes(option.value)}
                    onChange={() => handleMultiSelect('target_languages', option.value)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">{option.label}</span>
                </label>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 佣金和追踪 */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="text-lg font-medium text-gray-900 mb-4">佣金和追踪</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              佣金率 (%)
            </label>
            <input
              type="number"
              value={formData.commission_rate}
              onChange={(e) => handleChange('commission_rate', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                errors.commission_rate ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="35.00"
              min="0"
              max="100"
              step="0.01"
            />
            {errors.commission_rate && <p className="mt-1 text-xs text-red-600">{errors.commission_rate}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              佣金类型
            </label>
            <select
              value={formData.commission_type}
              onChange={(e) => handleChange('commission_type', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="percentage">百分比</option>
              <option value="fixed">固定金额</option>
              <option value="cpa">每次获客</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              追踪代码
            </label>
            <input
              type="text"
              value={formData.tracking_code}
              onChange={(e) => handleChange('tracking_code', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              placeholder="WR777BT365"
            />
          </div>
        </div>

        {/* UTM 参数 */}
        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            UTM 追踪参数
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-3">
            {['utm_source', 'utm_medium', 'utm_campaign', 'utm_content', 'utm_term'].map(param => (
              <div key={param}>
                <label className="block text-xs text-gray-500 mb-1">
                  {param.replace('utm_', '').toUpperCase()}
                </label>
                <input
                  type="text"
                  value={formData.tracking_params[param] || ''}
                  onChange={(e) => handleUTMChange(param, e.target.value)}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                  placeholder={param === 'utm_source' ? 'writer777' : ''}
                />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 有效期 */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="text-lg font-medium text-gray-900 mb-4">有效期设置</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              开始日期
            </label>
            <input
              type="date"
              value={formData.start_date}
              onChange={(e) => handleChange('start_date', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              结束日期
            </label>
            <input
              type="date"
              value={formData.end_date}
              onChange={(e) => handleChange('end_date', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
      </div>

      {/* 备注 */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="text-lg font-medium text-gray-900 mb-4">备注</h4>
        <textarea
          value={formData.notes}
          onChange={(e) => handleChange('notes', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          placeholder="任何额外的备注信息..."
        />
      </div>

      {/* 表单按钮 */}
      <div className="flex justify-end space-x-3 pt-6 border-t">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          取消
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {loading ? '保存中...' : (link ? '更新链接' : '创建链接')}
        </button>
      </div>
    </form>
  );
};

export default AffiliateLinkForm;