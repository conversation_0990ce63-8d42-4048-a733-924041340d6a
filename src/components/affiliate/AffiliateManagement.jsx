import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  ChartBarIcon,
  CursorArrowRaysIcon,
  AdjustmentsHorizontalIcon,
  MagnifyingGlassIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import api from '../../config/api';

/**
 * 联盟营销管理主界面
 * 包括链接列表、创建/编辑、统计报表等功能
 */
const AffiliateManagement = () => {
  const { t } = useTranslation();
  const [links, setLinks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedLink, setSelectedLink] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState('create'); // 'create', 'edit', 'stats'
  const [stats, setStats] = useState(null);

  // 筛选和分页状态
  const [filters, setFilters] = useState({
    search: '',
    content_type: '',
    category: '',
    language: '',
    country: '',
    is_active: 'true'
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  // 获取联盟链接列表
  const fetchLinks = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pagination.page,
        limit: pagination.limit,
        ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v))
      });

      const response = await api.get(`/affiliate/links?${params}`);
      if (response.data.success) {
        setLinks(response.data.data);
        setPagination(prev => ({
          ...prev,
          ...response.data.pagination
        }));
      }
    } catch (error) {
      console.error('获取联盟链接失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取统计数据
  const fetchStats = async (linkId) => {
    try {
      const response = await api.get(`/affiliate/stats/links/${linkId}`);
      if (response.data.success) {
        setStats(response.data.data);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  };

  // 删除联盟链接
  const deleteLink = async (linkId) => {
    if (!window.confirm(t('affiliate.confirmDelete'))) return;

    try {
      const response = await api.delete(`/affiliate/links/${linkId}`);
      if (response.data.success) {
        await fetchLinks();
      }
    } catch (error) {
      console.error('删除联盟链接失败:', error);
    }
  };

  useEffect(() => {
    fetchLinks();
  }, [pagination.page, filters]);

  // 打开模态框
  const openModal = (type, link = null) => {
    setModalType(type);
    setSelectedLink(link);
    setShowModal(true);
    
    if (type === 'stats' && link) {
      fetchStats(link.id);
    }
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedLink(null);
    setStats(null);
  };

  // 筛选组件
  const FilterSection = () => (
    <div className="bg-white rounded-lg shadow p-6 mb-6">
      <div className="flex items-center mb-4">
        <FunnelIcon className="w-5 h-5 text-gray-500 mr-2" />
        <h3 className="text-lg font-medium text-gray-900">筛选和搜索</h3>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {/* 搜索框 */}
        <div className="relative">
          <MagnifyingGlassIcon className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
          <input
            type="text"
            placeholder="搜索链接..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            value={filters.search}
            onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
          />
        </div>

        {/* 内容类型 */}
        <select
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          value={filters.content_type}
          onChange={(e) => setFilters(prev => ({ ...prev, content_type: e.target.value }))}
        >
          <option value="">所有内容类型</option>
          <option value="casino_review">赌场评论</option>
          <option value="sports_betting">体育博彩</option>
          <option value="game_guide">游戏指南</option>
          <option value="strategy_article">策略文章</option>
          <option value="industry_news">行业新闻</option>
        </select>

        {/* 分类 */}
        <select
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          value={filters.category}
          onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
        >
          <option value="">所有分类</option>
          <option value="casino">赌场</option>
          <option value="sportsbook">体育书</option>
          <option value="poker">扑克</option>
          <option value="crypto">加密货币</option>
          <option value="software">软件</option>
        </select>

        {/* 语言 */}
        <select
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          value={filters.language}
          onChange={(e) => setFilters(prev => ({ ...prev, language: e.target.value }))}
        >
          <option value="">所有语言</option>
          <option value="en">英语</option>
          <option value="pt">葡萄牙语</option>
          <option value="es">西班牙语</option>
          <option value="zh">中文</option>
          <option value="de">德语</option>
        </select>

        {/* 国家 */}
        <select
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          value={filters.country}
          onChange={(e) => setFilters(prev => ({ ...prev, country: e.target.value }))}
        >
          <option value="">所有国家</option>
          <option value="US">美国</option>
          <option value="BR">巴西</option>
          <option value="ES">西班牙</option>
          <option value="DE">德国</option>
          <option value="CA">加拿大</option>
        </select>

        {/* 状态 */}
        <select
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          value={filters.is_active}
          onChange={(e) => setFilters(prev => ({ ...prev, is_active: e.target.value }))}
        >
          <option value="">所有状态</option>
          <option value="true">激活</option>
          <option value="false">禁用</option>
        </select>
      </div>
    </div>
  );

  // 链接列表组件
  const LinkTable = () => (
    <div className="bg-white shadow-sm rounded-lg overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">联盟链接列表</h3>
        <button
          onClick={() => openModal('create')}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <PlusIcon className="w-4 h-4 mr-2" />
          创建新链接
        </button>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                名称和描述
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                目标和分类
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                性能数据
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {links.map((link) => (
              <tr key={link.id} className="hover:bg-gray-50">
                <td className="px-6 py-4">
                  <div>
                    <div className="text-sm font-medium text-gray-900">{link.name}</div>
                    <div className="text-sm text-gray-500 truncate max-w-xs">{link.description}</div>
                    <div className="text-xs text-blue-600 mt-1">{link.display_text}</div>
                  </div>
                </td>
                
                <td className="px-6 py-4">
                  <div className="text-xs">
                    <div className="mb-1">
                      <span className="text-gray-500">内容类型:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {link.content_types.slice(0, 2).map((type, idx) => (
                          <span key={idx} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            {type}
                          </span>
                        ))}
                        {link.content_types.length > 2 && (
                          <span className="text-gray-400">+{link.content_types.length - 2}</span>
                        )}
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-500">分类:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {link.categories.slice(0, 2).map((cat, idx) => (
                          <span key={idx} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                            {cat}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </td>

                <td className="px-6 py-4">
                  <div className="text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-500">点击数:</span>
                      <span className="font-medium">{link.click_count}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">转化数:</span>
                      <span className="font-medium">{link.conversion_count}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">转化率:</span>
                      <span className="font-medium">{link.conversion_rate}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">佣金率:</span>
                      <span className="font-medium">{link.commission_rate}%</span>
                    </div>
                  </div>
                </td>

                <td className="px-6 py-4">
                  <div className="text-sm">
                    <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      link.is_active 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {link.is_active ? '激活' : '禁用'}
                    </div>
                    <div className="mt-1 text-xs text-gray-500">
                      优先级: {link.priority}
                    </div>
                  </div>
                </td>

                <td className="px-6 py-4 text-right text-sm font-medium">
                  <div className="flex justify-end space-x-2">
                    <button
                      onClick={() => openModal('stats', link)}
                      className="text-blue-600 hover:text-blue-900"
                      title="查看统计"
                    >
                      <ChartBarIcon className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => openModal('edit', link)}
                      className="text-indigo-600 hover:text-indigo-900"
                      title="编辑"
                    >
                      <PencilIcon className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => deleteLink(link.id)}
                      className="text-red-600 hover:text-red-900"
                      title="删除"
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 分页 */}
      {pagination.totalPages > 1 && (
        <div className="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
          <div className="text-sm text-gray-700">
            显示 {(pagination.page - 1) * pagination.limit + 1} 到{' '}
            {Math.min(pagination.page * pagination.limit, pagination.total)} 条，共 {pagination.total} 条
          </div>
          <div className="flex space-x-1">
            <button
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
              disabled={pagination.page <= 1}
              className="px-3 py-1 text-sm border rounded-md disabled:opacity-50"
            >
              上一页
            </button>
            <button
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
              disabled={pagination.page >= pagination.totalPages}
              className="px-3 py-1 text-sm border rounded-md disabled:opacity-50"
            >
              下一页
            </button>
          </div>
        </div>
      )}
    </div>
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">联盟营销管理</h1>
        <p className="mt-2 text-gray-600">管理您的联盟链接、追踪性能并优化转化率</p>
      </div>

      <FilterSection />
      <LinkTable />

      {/* 模态框组件 */}
      {showModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">
                {modalType === 'create' && '创建联盟链接'}
                {modalType === 'edit' && '编辑联盟链接'}
                {modalType === 'stats' && '链接统计'}
              </h3>
              <button
                onClick={closeModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <span className="sr-only">关闭</span>
                ✕
              </button>
            </div>

            {/* 根据模态框类型显示不同内容 */}
            {modalType === 'stats' && stats && (
              <div className="space-y-6">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{stats.basic.total_clicks}</div>
                    <div className="text-sm text-gray-600">总点击数</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{stats.basic.unique_sessions}</div>
                    <div className="text-sm text-gray-600">独立会话</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">{stats.basic.unique_visitors}</div>
                    <div className="text-sm text-gray-600">独立访客</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">{stats.basic.conversion_rate}%</div>
                    <div className="text-sm text-gray-600">转化率</div>
                  </div>
                </div>
                
                {/* 可以添加更多统计图表 */}
              </div>
            )}

            {(modalType === 'create' || modalType === 'edit') && (
              <div>
                {/* 这里可以添加创建/编辑表单组件 */}
                <p className="text-gray-600">表单组件将在下一步实现</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AffiliateManagement;