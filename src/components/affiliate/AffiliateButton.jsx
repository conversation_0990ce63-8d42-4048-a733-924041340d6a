import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  PlayIcon, 
  GiftIcon, 
  ArrowTopRightOnSquareIcon 
} from '@heroicons/react/24/outline';
import api from '../../config/api';

/**
 * 智能联盟按钮组件
 * 根据文章内容自动选择最合适的联盟链接并展示
 */
const AffiliateButton = ({ 
  article, 
  position = 'primary_cta', 
  className = '',
  size = 'default', // 'small', 'default', 'large'
  showIcon = true,
  trackingData = {} 
}) => {
  const { t } = useTranslation();
  const [affiliateLinks, setAffiliateLinks] = useState([]);
  const [loading, setLoading] = useState(true);

  // 按钮尺寸样式
  const sizeClasses = {
    small: 'py-2 px-4 text-sm',
    default: 'py-3 px-6 text-base',
    large: 'py-4 px-8 text-lg'
  };

  // 按钮样式主题
  const buttonStyles = {
    primary: 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white',
    secondary: 'bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white',
    success: 'bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white',
    warning: 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white',
    danger: 'bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white'
  };

  // 根据位置选择图标
  const getIcon = (pos) => {
    switch (pos) {
      case 'primary_cta':
        return PlayIcon;
      case 'secondary_cta':
        return GiftIcon;
      default:
        return ArrowTopRightOnSquareIcon;
    }
  };

  // 获取推荐的联盟链接
  const fetchRecommendedLinks = async () => {
    try {
      setLoading(true);
      
      // 从文章中提取推荐参数
      const criteria = {
        content_type: article.content_type || 'casino_review',
        language: article.language || 'en',
        country: article.target_country || 'US',
        position: position,
        limit: position === 'primary_cta' ? 1 : 2
      };

      const response = await api.post('/affiliate/recommend', criteria);
      if (response.data.success) {
        setAffiliateLinks(response.data.data);
      }
    } catch (error) {
      // API调用失败，使用默认链接
      setAffiliateLinks([]);
    } finally {
      setLoading(false);
    }
  };

  // 追踪点击事件
  const trackClick = async (affiliateLink) => {
    try {
      const clickData = {
        affiliate_link_id: affiliateLink.id,
        article_id: article.id,
        article_url: window.location.href,
        article_title: article.title,
        click_position: position,
        user_ip: null, // 将在后端获取
        user_agent: navigator.userAgent,
        referer: document.referrer,
        session_id: sessionStorage.getItem('session_id') || generateSessionId(),
        ...trackingData
      };

      // 异步记录点击，不阻塞用户操作
      api.post('/affiliate/clicks', clickData).catch(error => {
        console.error('记录点击失败:', error);
      });

      // 打开联盟链接
      window.open(affiliateLink.affiliate_url, '_blank', 'noopener,noreferrer');
    } catch (error) {
      console.error('处理点击事件失败:', error);
      // 即使追踪失败，也要打开链接
      window.open(affiliateLink.affiliate_url, '_blank', 'noopener,noreferrer');
    }
  };

  // 生成会话ID
  const generateSessionId = () => {
    const sessionId = 'sess_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    sessionStorage.setItem('session_id', sessionId);
    return sessionId;
  };

  useEffect(() => {
    if (article) {
      fetchRecommendedLinks();
    }
  }, [article, position]);

  // 如果正在加载或没有链接，显示默认按钮
  if (loading || affiliateLinks.length === 0) {
    const defaultText = position === 'primary_cta' 
      ? (article?.language === 'pt' ? 'Visitar Cassino' : 'Visit Casino')
      : (article?.language === 'pt' ? 'Reivindicar Bônus' : 'Claim Bonus');

    const IconComponent = getIcon(position);

    return (
      <button
        className={`
          ${buttonStyles.primary}
          ${sizeClasses[size]}
          font-bold rounded-xl transition-all duration-300 
          flex items-center space-x-3 shadow-lg hover:shadow-xl 
          transform hover:-translate-y-1 ${className}
        `}
        disabled={loading}
      >
        {showIcon && <IconComponent className="w-5 h-5" />}
        <span>{loading ? '加载中...' : defaultText}</span>
      </button>
    );
  }

  // 渲染联盟按钮
  return (
    <div className={`flex flex-wrap gap-4 ${className}`}>
      {affiliateLinks.map((link, index) => {
        const IconComponent = getIcon(position);
        const buttonStyle = buttonStyles[link.button_style] || buttonStyles.primary;
        
        return (
          <button
            key={link.id}
            onClick={() => trackClick(link)}
            className={`
              ${buttonStyle}
              ${sizeClasses[size]}
              font-bold rounded-xl transition-all duration-300 
              flex items-center space-x-3 shadow-lg hover:shadow-xl 
              transform hover:-translate-y-1
            `}
            title={link.description}
          >
            {showIcon && <IconComponent className="w-5 h-5" />}
            <span>{link.display_text}</span>
          </button>
        );
      })}
    </div>
  );
};

/**
 * 联盟按钮容器组件
 * 支持多个位置的按钮组合显示
 */
export const AffiliateButtonGroup = ({ 
  article, 
  positions = ['primary_cta', 'secondary_cta'],
  className = '',
  layout = 'horizontal' // 'horizontal', 'vertical'
}) => {
  const containerClass = layout === 'vertical' 
    ? 'space-y-4' 
    : 'flex flex-wrap gap-4';

  return (
    <div className={`${containerClass} ${className}`}>
      {positions.map(position => (
        <AffiliateButton
          key={position}
          article={article}
          position={position}
          className={layout === 'vertical' ? 'w-full' : ''}
        />
      ))}
    </div>
  );
};

export default AffiliateButton;