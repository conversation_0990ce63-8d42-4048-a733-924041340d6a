import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  Cog6ToothIcon,
  CheckIcon,
  XMarkIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import api from '../../config/api';

/**
 * 批量联盟链接管理组件
 * 支持批量设置、删除、更新文章的联盟链接
 */
const BatchManagement = () => {
  const { t } = useTranslation();
  const [articles, setArticles] = useState([]);
  const [affiliateLinks, setAffiliateLinks] = useState([]);
  const [selectedArticles, setSelectedArticles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [operationLoading, setOperationLoading] = useState(false);
  
  // 筛选条件
  const [filters, setFilters] = useState({
    content_type: '',
    language: '',
    status: '',
    date_from: '',
    date_to: '',
    search: ''
  });

  // 批量操作设置
  const [batchOperation, setBatchOperation] = useState({
    action: 'add', // 'add', 'remove', 'update'
    affiliate_link_id: '',
    position: 'primary_cta',
    custom_display_text: '',
    custom_button_style: ''
  });

  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0
  });

  // 获取文章列表
  const fetchArticles = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pagination.page,
        limit: pagination.limit,
        ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v))
      });

      const response = await api.get(`/tasks?${params}`);
      if (response.data.success) {
        setArticles(response.data.tasks || []);
        setPagination(prev => ({
          ...prev,
          total: response.data.total || 0,
          totalPages: Math.ceil((response.data.total || 0) / pagination.limit)
        }));
      }
    } catch (error) {
      console.error('获取文章列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取联盟链接列表
  const fetchAffiliateLinks = async () => {
    try {
      const response = await api.get('/affiliate/links?limit=100&is_active=true');
      if (response.data.success) {
        setAffiliateLinks(response.data.data || []);
      }
    } catch (error) {
      console.error('获取联盟链接失败:', error);
    }
  };

  // 执行批量操作
  const executeBatchOperation = async () => {
    if (selectedArticles.length === 0) {
      alert('请选择要操作的文章');
      return;
    }

    if (batchOperation.action !== 'remove' && !batchOperation.affiliate_link_id) {
      alert('请选择联盟链接');
      return;
    }

    try {
      setOperationLoading(true);
      const operationData = {
        action: batchOperation.action,
        article_ids: selectedArticles,
        affiliate_link_id: batchOperation.affiliate_link_id,
        position: batchOperation.position,
        custom_display_text: batchOperation.custom_display_text,
        custom_button_style: batchOperation.custom_button_style
      };

      const response = await api.post('/affiliate/articles/batch-links', operationData);
      
      if (response.data.success) {
        alert(`批量操作完成：成功处理 ${response.data.data.successCount} 篇文章`);
        setSelectedArticles([]);
        // 刷新列表
        await fetchArticles();
      }
    } catch (error) {
      console.error('批量操作失败:', error);
      alert('批量操作失败：' + (error.response?.data?.message || error.message));
    } finally {
      setOperationLoading(false);
    }
  };

  // 切换文章选择状态
  const toggleArticleSelection = (articleId) => {
    setSelectedArticles(prev =>
      prev.includes(articleId)
        ? prev.filter(id => id !== articleId)
        : [...prev, articleId]
    );
  };

  // 全选/取消全选
  const toggleSelectAll = () => {
    if (selectedArticles.length === articles.length) {
      setSelectedArticles([]);
    } else {
      setSelectedArticles(articles.map(article => article.id));
    }
  };

  useEffect(() => {
    fetchArticles();
    fetchAffiliateLinks();
  }, [pagination.page, filters]);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">批量联盟链接管理</h1>
        <p className="mt-2 text-gray-600">批量设置多篇文章的联盟营销链接</p>
      </div>

      {/* 筛选区域 */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <div className="flex items-center mb-4">
          <FunnelIcon className="w-5 h-5 text-gray-500 mr-2" />
          <h3 className="text-lg font-medium text-gray-900">筛选条件</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div className="relative">
            <MagnifyingGlassIcon className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
            <input
              type="text"
              placeholder="搜索文章..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
            />
          </div>

          <select
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            value={filters.content_type}
            onChange={(e) => setFilters(prev => ({ ...prev, content_type: e.target.value }))}
          >
            <option value="">所有内容类型</option>
            <option value="casino_review">赌场评论</option>
            <option value="sports_betting">体育博彩</option>
            <option value="game_guide">游戏指南</option>
            <option value="strategy_article">策略文章</option>
          </select>

          <select
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            value={filters.language}
            onChange={(e) => setFilters(prev => ({ ...prev, language: e.target.value }))}
          >
            <option value="">所有语言</option>
            <option value="en">英语</option>
            <option value="pt">葡萄牙语</option>
            <option value="es">西班牙语</option>
            <option value="zh">中文</option>
          </select>

          <select
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
          >
            <option value="">所有状态</option>
            <option value="Published">已发布</option>
            <option value="Draft">草稿</option>
          </select>

          <input
            type="date"
            value={filters.date_from}
            onChange={(e) => setFilters(prev => ({ ...prev, date_from: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            placeholder="开始日期"
          />

          <input
            type="date"
            value={filters.date_to}
            onChange={(e) => setFilters(prev => ({ ...prev, date_to: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            placeholder="结束日期"
          />
        </div>
      </div>

      {/* 批量操作区域 */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <div className="flex items-center mb-4">
          <Cog6ToothIcon className="w-5 h-5 text-gray-500 mr-2" />
          <h3 className="text-lg font-medium text-gray-900">批量操作</h3>
          <span className="ml-4 text-sm text-gray-500">
            已选择 {selectedArticles.length} 篇文章
          </span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">操作类型</label>
            <select
              value={batchOperation.action}
              onChange={(e) => setBatchOperation(prev => ({ ...prev, action: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="add">添加链接</option>
              <option value="remove">移除链接</option>
              <option value="update">更新链接</option>
            </select>
          </div>

          {batchOperation.action !== 'remove' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">联盟链接</label>
                <select
                  value={batchOperation.affiliate_link_id}
                  onChange={(e) => setBatchOperation(prev => ({ ...prev, affiliate_link_id: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">选择联盟链接</option>
                  {affiliateLinks.map(link => (
                    <option key={link.id} value={link.id}>
                      {link.name} - {link.display_text}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">按钮位置</label>
                <select
                  value={batchOperation.position}
                  onChange={(e) => setBatchOperation(prev => ({ ...prev, position: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="primary_cta">主要按钮</option>
                  <option value="secondary_cta">次要按钮</option>
                  <option value="inline">内联</option>
                  <option value="footer">底部</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">自定义文字</label>
                <input
                  type="text"
                  value={batchOperation.custom_display_text}
                  onChange={(e) => setBatchOperation(prev => ({ ...prev, custom_display_text: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  placeholder="覆盖默认文字"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">按钮样式</label>
                <select
                  value={batchOperation.custom_button_style}
                  onChange={(e) => setBatchOperation(prev => ({ ...prev, custom_button_style: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">使用默认样式</option>
                  <option value="primary">主要</option>
                  <option value="secondary">次要</option>
                  <option value="success">成功</option>
                  <option value="warning">警告</option>
                  <option value="danger">危险</option>
                </select>
              </div>
            </>
          )}

          <div className="flex items-end">
            <button
              onClick={executeBatchOperation}
              disabled={operationLoading || selectedArticles.length === 0}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {operationLoading ? '处理中...' : '执行操作'}
            </button>
          </div>
        </div>
      </div>

      {/* 文章列表 */}
      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900">文章列表</h3>
          <button
            onClick={toggleSelectAll}
            className="text-sm text-blue-600 hover:text-blue-700"
          >
            {selectedArticles.length === articles.length ? '取消全选' : '全选'}
          </button>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedArticles.length === articles.length && articles.length > 0}
                      onChange={toggleSelectAll}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    文章信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    内容类型/语言
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    创建时间
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {articles.map((article) => (
                  <tr key={article.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <input
                        type="checkbox"
                        checked={selectedArticles.includes(article.id)}
                        onChange={() => toggleArticleSelection(article.id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </td>
                    <td className="px-6 py-4">
                      <div>
                        <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                          {article.name}
                        </div>
                        <div className="text-sm text-gray-500 mt-1">
                          ID: {article.id}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm">
                        <div className="text-gray-900">{article.content_type || 'N/A'}</div>
                        <div className="text-gray-500">{article.target_language || 'N/A'}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        article.status === 'Published' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {article.status || 'Draft'}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      {new Date(article.created_at).toLocaleDateString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* 分页 */}
        {pagination.totalPages > 1 && (
          <div className="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
            <div className="text-sm text-gray-700">
              显示 {(pagination.page - 1) * pagination.limit + 1} 到{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} 条，共 {pagination.total} 条
            </div>
            <div className="flex space-x-1">
              <button
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                disabled={pagination.page <= 1}
                className="px-3 py-1 text-sm border rounded-md disabled:opacity-50"
              >
                上一页
              </button>
              <button
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                disabled={pagination.page >= pagination.totalPages}
                className="px-3 py-1 text-sm border rounded-md disabled:opacity-50"
              >
                下一页
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BatchManagement;