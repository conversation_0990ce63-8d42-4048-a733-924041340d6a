import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  LinkIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../../contexts/AuthContext';
import apiClient from '../../utils/apiClient';

/**
 * 简化的联盟营销管理界面
 * 专注于核心功能和国际化支持
 */
const SimpleAffiliateManagement = () => {
  const { t } = useTranslation();
  const [links, setLinks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingLink, setEditingLink] = useState(null);

  const { user } = useAuth();

  const fetchLinks = async () => {
    try {
      setLoading(true);
      console.log('Fetching affiliate links with search term:', searchTerm);
      
      const response = await apiClient.get('/api/affiliate/links', {
        params: { search: searchTerm }
      });
      
      console.log('Fetch links API response:', response);
      
      if (response.success || response.data?.success) {
        // 后端返回 {success: true, data: array}，所以需要获取 data 字段
        const linksData = response.data || [];
        console.log('Setting links data:', linksData);
        setLinks(Array.isArray(linksData) ? linksData : []);
      } else {
        console.error('API returned unsuccessful response:', response);
        setLinks([]);
      }
    } catch (error) {
      console.error('Error fetching affiliate links:', error);
      setLinks([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLinks();
  }, [searchTerm]);

  const filteredLinks = links.filter(link =>
    link.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    link.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleCreateLink = async (linkData) => {
    try {
      setLoading(true);
      console.log('Creating affiliate link with data:', linkData);
      
      const response = await apiClient.post('/api/affiliate/links', linkData);
      console.log('API response:', response);
      
      if (response.success || response.data?.success) {
        console.log('Link created successfully, refreshing list...');
        await fetchLinks(); // 重新获取列表
        setShowCreateModal(false);
      } else {
        console.error('API returned unsuccessful response:', response);
        alert('Failed to create affiliate link: API returned unsuccessful response');
      }
    } catch (error) {
      console.error('Error creating affiliate link:', error);
      alert('Failed to create affiliate link: ' + (error.response?.data?.error || error.message));
    } finally {
      setLoading(false);
    }
  };

  const handleEditLink = async (linkData) => {
    try {
      setLoading(true);
      console.log('Updating affiliate link with data:', linkData);
      
      const response = await apiClient.put(`/api/affiliate/links/${editingLink.id}`, linkData);
      console.log('Update API response:', response);
      
      if (response.success || response.data?.success) {
        console.log('Link updated successfully, refreshing list...');
        await fetchLinks(); // 重新获取列表
        setEditingLink(null);
      } else {
        console.error('API returned unsuccessful response:', response);
        alert('Failed to update affiliate link: API returned unsuccessful response');
      }
    } catch (error) {
      console.error('Error updating affiliate link:', error);
      alert('Failed to update affiliate link: ' + (error.response?.data?.error || error.message));
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteLink = async (linkId) => {
    if (window.confirm(t('affiliate.confirmDelete'))) {
      try {
        setLoading(true);
        const response = await apiClient.delete(`/api/affiliate/links/${linkId}`);
        console.log('Delete API response:', response);
        
        if (response.success || response.data?.success) {
          console.log('Link deleted successfully, refreshing list...');
          await fetchLinks(); // 重新获取列表
        } else {
          console.error('API returned unsuccessful response:', response);
          alert('Failed to delete affiliate link: API returned unsuccessful response');
        }
      } catch (error) {
        console.error('Error deleting affiliate link:', error);
        alert('Failed to delete affiliate link: ' + (error.response?.data?.error || error.message));
      } finally {
        setLoading(false);
      }
    }
  };

  const CreateLinkModal = ({ isOpen, onClose, onSave, editData = null }) => {
    const [formData, setFormData] = useState({
      name: '',
      url: '',
      shortCode: '',
      description: '',
      category: 'casino_review',
      language: 'en',
      country: 'US',
      isActive: true,
      ...editData
    });

    if (!isOpen) return null;

    const handleSubmit = (e) => {
      e.preventDefault();
      onSave(formData);
      setFormData({
        name: '',
        url: '',
        shortCode: '',
        description: '',
        category: 'casino_review',
        language: 'en',
        country: 'US',
        isActive: true
      });
    };

    return (
      <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50">
        <div className="bg-casino-dark-800 border border-casino-gold-500/30 rounded-xl p-6 w-full max-w-md mx-4 shadow-2xl">
          <h3 className="text-lg font-medium text-casino-gold-400 mb-4">
            {editData ? t('affiliate.editLink') : t('affiliate.createNew')}
          </h3>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-casino-gold-300">
                {t('affiliate.name')}
              </label>
              <input
                type="text"
                required
                className="mt-1 block w-full bg-casino-dark-700/50 border border-casino-gold-500/30 rounded-lg px-3 py-2 text-casino-gold-100 placeholder-casino-gold-400/50 focus:outline-none focus:ring-2 focus:ring-casino-gold-500 focus:border-casino-gold-500 transition-all duration-200"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-casino-gold-300">
                URL
              </label>
              <input
                type="url"
                required
                className="mt-1 block w-full bg-casino-dark-700/50 border border-casino-gold-500/30 rounded-lg px-3 py-2 text-casino-gold-100 placeholder-casino-gold-400/50 focus:outline-none focus:ring-2 focus:ring-casino-gold-500 focus:border-casino-gold-500 transition-all duration-200"
                value={formData.url}
                onChange={(e) => setFormData({...formData, url: e.target.value})}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-casino-gold-300">
                Short Code
              </label>
              <input
                type="text"
                required
                className="mt-1 block w-full bg-casino-dark-700/50 border border-casino-gold-500/30 rounded-lg px-3 py-2 text-casino-gold-100 placeholder-casino-gold-400/50 focus:outline-none focus:ring-2 focus:ring-casino-gold-500 focus:border-casino-gold-500 transition-all duration-200"
                value={formData.shortCode}
                onChange={(e) => setFormData({...formData, shortCode: e.target.value.toUpperCase()})}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-casino-gold-300">
                Description
              </label>
              <textarea
                className="mt-1 block w-full bg-casino-dark-700/50 border border-casino-gold-500/30 rounded-lg px-3 py-2 text-casino-gold-100 placeholder-casino-gold-400/50 focus:outline-none focus:ring-2 focus:ring-casino-gold-500 focus:border-casino-gold-500 transition-all duration-200"
                rows="3"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-casino-gold-300">
                  Category
                </label>
                <select
                  className="mt-1 block w-full bg-casino-dark-700/50 border border-casino-gold-500/30 rounded-lg px-3 py-2 text-casino-gold-100 placeholder-casino-gold-400/50 focus:outline-none focus:ring-2 focus:ring-casino-gold-500 focus:border-casino-gold-500 transition-all duration-200"
                  value={formData.category}
                  onChange={(e) => setFormData({...formData, category: e.target.value})}
                >
                  <option value="casino_review">Casino Review</option>
                  <option value="game_guide">Game Guide</option>
                  <option value="bonus_analysis">Bonus Analysis</option>
                  <option value="industry_news">Industry News</option>
                  <option value="sports_betting">Sports Betting</option>
                  <option value="regulatory_update">Regulatory Update</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-casino-gold-300">
                  Language
                </label>
                <select
                  className="mt-1 block w-full bg-casino-dark-700/50 border border-casino-gold-500/30 rounded-lg px-3 py-2 text-casino-gold-100 placeholder-casino-gold-400/50 focus:outline-none focus:ring-2 focus:ring-casino-gold-500 focus:border-casino-gold-500 transition-all duration-200"
                  value={formData.language}
                  onChange={(e) => setFormData({...formData, language: e.target.value})}
                >
                  <option value="en">English</option>
                  <option value="pt">Portuguese</option>
                  <option value="es">Spanish</option>
                  <option value="de">German</option>
                  <option value="fr">French</option>
                  <option value="it">Italian</option>
                  <option value="ja">Japanese</option>
                  <option value="zh">Chinese</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-casino-gold-300">
                  Country
                </label>
                <select
                  className="mt-1 block w-full bg-casino-dark-700/50 border border-casino-gold-500/30 rounded-lg px-3 py-2 text-casino-gold-100 placeholder-casino-gold-400/50 focus:outline-none focus:ring-2 focus:ring-casino-gold-500 focus:border-casino-gold-500 transition-all duration-200"
                  value={formData.country}
                  onChange={(e) => setFormData({...formData, country: e.target.value})}
                >
                  <option value="US">United States</option>
                  <option value="BR">Brazil</option>
                  <option value="ES">Spain</option>
                  <option value="DE">Germany</option>
                  <option value="FR">France</option>
                  <option value="IT">Italy</option>
                  <option value="JP">Japan</option>
                  <option value="CN">China</option>
                </select>
              </div>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="isActive"
                className="h-4 w-4 text-casino-gold-500 focus:ring-casino-gold-500 border-casino-gold-500/30 rounded bg-casino-dark-700/50"
                checked={formData.isActive}
                onChange={(e) => setFormData({...formData, isActive: e.target.checked})}
              />
              <label htmlFor="isActive" className="ml-2 text-sm text-casino-gold-300">
                {t('affiliate.active')}
              </label>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-casino-gold-300 bg-casino-dark-700/50 border border-casino-gold-500/30 rounded-lg hover:bg-casino-dark-700 focus:outline-none focus:ring-2 focus:ring-casino-gold-500 transition-all duration-200"
              >
                {t('common.cancel')}
              </button>
              <button
                type="submit"
                className="px-4 py-2 text-sm font-medium text-casino-dark-900 bg-gradient-to-r from-casino-gold-500 to-casino-gold-600 hover:from-casino-gold-600 hover:to-casino-gold-700 border border-transparent rounded-lg shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-casino-gold-500 transition-all duration-200"
              >
                {t('common.save')}
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-casino-dark-800 via-casino-dark-900 to-casino-dark-800 p-6">
      {/* 标题 */}
      <div className="mb-6">
        <h2 className="text-3xl font-bold text-casino-gold-400 neon-glow">{t('affiliate.title')}</h2>
        <p className="text-casino-gold-200 mt-2">{t('affiliate.subtitle')}</p>
      </div>

      {/* 操作栏 */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4 justify-between">
        {/* 搜索 */}
        <div className="relative flex-1 max-w-md">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-casino-gold-400" />
          <input
            type="text"
            placeholder={t('affiliate.searchPlaceholder')}
            className="w-full pl-10 pr-4 py-2 bg-casino-dark-700/50 border border-casino-gold-500/30 rounded-lg text-casino-gold-100 placeholder-casino-gold-400/50 focus:ring-2 focus:ring-casino-gold-500 focus:border-casino-gold-500 transition-all duration-200"
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
            }}
          />
        </div>

        {/* 创建按钮 */}
        <button
          onClick={() => setShowCreateModal(true)}
          className="inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-casino-gold-500 to-casino-gold-600 hover:from-casino-gold-600 hover:to-casino-gold-700 text-casino-dark-900 font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
        >
          <PlusIcon className="w-4 h-4 mr-2" />
          {t('affiliate.createNew')}
        </button>
      </div>

      {/* 链接列表 */}
      <div className="bg-casino-dark-700/50 backdrop-blur-xl rounded-xl overflow-hidden border border-casino-gold-500/30 shadow-2xl">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-casino-gold-500/20">
            <thead className="bg-casino-dark-800/50">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium text-casino-gold-400 uppercase tracking-wider">
                  {t('affiliate.name')}
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-casino-gold-400 uppercase tracking-wider">
                  {t('affiliate.targetAndCategory')}
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-casino-gold-400 uppercase tracking-wider">
                  {t('affiliate.performance')}
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-casino-gold-400 uppercase tracking-wider">
                  {t('affiliate.status')}
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-casino-gold-400 uppercase tracking-wider">
                  {t('affiliate.actions')}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-casino-gold-500/10">
              {loading ? (
                <tr>
                  <td colSpan="5" className="px-6 py-8 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-casino-gold-500 mx-auto"></div>
                  </td>
                </tr>
              ) : filteredLinks.length === 0 ? (
                <tr>
                  <td colSpan="5" className="px-6 py-8 text-center">
                    <div className="text-casino-gold-200">
                      <LinkIcon className="w-12 h-12 mx-auto mb-4 text-casino-gold-400" />
                      <p className="text-lg font-medium text-casino-gold-300">{t('affiliate.noLinksFound')}</p>
                      <p className="text-sm text-casino-gold-200">{t('affiliate.createFirstLink')}</p>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredLinks.map((link) => {
                  console.log('Rendering link:', JSON.stringify(link, null, 2)); // Debug log with full object
                  console.log('isActive value:', link.isActive, 'type:', typeof link.isActive, 'boolean check:', Boolean(link.isActive)); // Debug isActive
                  return (
                  <tr key={link.id} className="hover:bg-casino-gold-500/5 transition-colors duration-200">
                    <td className="px-6 py-4">
                      <div>
                        <div className="text-sm font-medium text-casino-gold-100">{link.name}</div>
                        <div className="text-sm text-casino-gold-300">{link.shortCode || link.shortcode}</div>
                        <div className="text-xs text-casino-gold-400/70 mt-1">{link.description}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-casino-gold-500/20 text-casino-gold-300 capitalize border border-casino-gold-500/30">
                          {link.category}
                        </span>
                      </div>
                      <div className="text-xs text-casino-gold-400 mt-1">{link.language.toUpperCase()} • {link.country}</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-casino-gold-100">{link.clicks} {t('affiliate.clicks')}</div>
                      <div className="text-sm text-casino-gold-300">{link.conversions} conversions</div>
                      <div className="text-xs text-casino-gold-400">CTR: {((link.conversions / link.clicks) * 100).toFixed(1)}%</div>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        (link.isActive !== undefined ? link.isActive : link.isactive)
                          ? 'bg-casino-green-500/20 text-casino-green-400 border border-casino-green-500/30'
                          : 'bg-casino-red-500/20 text-casino-red-400 border border-casino-red-500/30'
                      }`}>
                        {(link.isActive !== undefined ? link.isActive : link.isactive) ? t('affiliate.active') : t('affiliate.inactive')}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setEditingLink(link)}
                          className="text-casino-gold-400 hover:text-casino-gold-300 transition-colors duration-200"
                          title={t('affiliate.edit')}
                        >
                          <PencilIcon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => window.open(link.url, '_blank')}
                          className="text-casino-gold-400/70 hover:text-casino-gold-300 transition-colors duration-200"
                          title="View"
                        >
                          <EyeIcon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteLink(link.id)}
                          className="text-casino-red-400 hover:text-casino-red-300 transition-colors duration-200"
                          title={t('affiliate.delete')}
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* 模态框 */}
      <CreateLinkModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSave={handleCreateLink}
      />

      <CreateLinkModal
        isOpen={!!editingLink}
        onClose={() => setEditingLink(null)}
        onSave={handleEditLink}
        editData={editingLink ? {
          ...editingLink,
          shortCode: editingLink.shortCode || editingLink.shortcode,
          isActive: editingLink.isActive !== undefined ? editingLink.isActive : editingLink.isactive
        } : null}
      />
    </div>
  );
};

export default SimpleAffiliateManagement;