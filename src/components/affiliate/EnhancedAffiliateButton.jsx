import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  PlayIcon, 
  GiftIcon, 
  ArrowTopRightOnSquareIcon 
} from '@heroicons/react/24/outline';
import apiClient from '../../utils/apiClient';

/**
 * 增强型联盟按钮组件
 * 保持原有的按钮文本和样式，但将链接目标改为用户配置的联盟链接
 */
const EnhancedAffiliateButton = ({ 
  article, 
  position = 'primary_cta', 
  className = '',
  size = 'default',
  showIcon = true,
  defaultText = null,
  defaultUrl = '#',
  trackingData = {} 
}) => {
  const { t } = useTranslation();
  const [affiliateLink, setAffiliateLink] = useState(null);
  const [loading, setLoading] = useState(true);

  // 按钮尺寸样式
  const sizeClasses = {
    small: 'py-2 px-4 text-sm',
    default: 'py-3 px-6 text-base',
    large: 'py-4 px-8 text-lg'
  };

  // 按钮样式（使用casino主题）
  const buttonStyles = {
    primary: 'bg-gradient-to-r from-casino-gold-500 to-casino-gold-600 hover:from-casino-gold-600 hover:to-casino-gold-700 text-casino-dark-900',
    secondary: 'bg-gradient-to-r from-casino-dark-600 to-casino-dark-700 hover:from-casino-dark-700 hover:to-casino-dark-800 text-casino-gold-100 border border-casino-gold-500/30'
  };

  // 根据位置选择图标
  const getIcon = (pos) => {
    switch (pos) {
      case 'primary_cta':
        return PlayIcon;
      case 'secondary_cta':
        return GiftIcon;
      default:
        return ArrowTopRightOnSquareIcon;
    }
  };

  // 获取默认文本
  const getDefaultText = () => {
    if (defaultText) return defaultText;
    
    return position === 'primary_cta' 
      ? (article?.language === 'pt' ? 'Visitar Cassino' : 'Visit Casino')
      : (article?.language === 'pt' ? 'Reivindicar Bônus' : 'Claim Bonus');
  };

  // 获取文章配置的联盟链接（所有按钮使用同一个链接）
  const fetchAffiliateLink = async () => {
    if (!article?.id) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const response = await apiClient.get(`/api/affiliate/articles/${article.id}/links`);
      
      if (response.success) {
        const links = response.data || [];
        // 使用第一个可用的联盟链接，不区分位置
        const link = links[0];
        setAffiliateLink(link);
      }
    } catch (error) {
      console.error('获取联盟链接失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理按钮点击
  const handleClick = async (e) => {
    e.preventDefault();
    
    const targetUrl = affiliateLink?.affiliate_url || defaultUrl;
    
    if (affiliateLink) {
      // 记录点击数据
      try {
        const clickData = {
          affiliate_link_id: affiliateLink.affiliate_link_id || affiliateLink.id,
          article_id: article.id,
          article_url: window.location.href,
          article_title: article.title,
          click_position: position,
          user_agent: navigator.userAgent,
          referer: document.referrer,
          session_id: sessionStorage.getItem('session_id') || generateSessionId(),
          ...trackingData
        };

        // 异步记录点击
        apiClient.post('/api/affiliate/clicks', clickData).catch(err => {
          console.error('记录点击失败:', err);
        });
      } catch (error) {
        console.error('处理点击事件失败:', error);
      }
    }
    
    // 打开链接
    if (targetUrl !== '#') {
      window.open(targetUrl, '_blank', 'noopener,noreferrer');
    }
  };

  // 生成会话ID
  const generateSessionId = () => {
    const sessionId = 'sess_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    sessionStorage.setItem('session_id', sessionId);
    return sessionId;
  };

  useEffect(() => {
    fetchAffiliateLink();
  }, [article?.id]);

  const IconComponent = getIcon(position);
  const displayText = getDefaultText();
  const buttonStyle = position === 'primary_cta' ? buttonStyles.primary : buttonStyles.secondary;

  return (
    <button
      onClick={handleClick}
      className={`
        ${buttonStyle}
        ${sizeClasses[size]}
        font-bold rounded-xl transition-all duration-300 
        flex items-center space-x-3 shadow-lg hover:shadow-xl 
        transform hover:-translate-y-1 hover:scale-105 ${className}
      `}
      disabled={loading}
    >
      {showIcon && <IconComponent className="w-5 h-5" />}
      <span>{displayText}</span>
    </button>
  );
};

/**
 * 增强型联盟按钮组
 * 显示多个位置的按钮，保持原有UI但增强链接功能
 */
export const EnhancedAffiliateButtonGroup = ({ 
  article, 
  positions = ['primary_cta', 'secondary_cta'],
  className = '',
  layout = 'horizontal',
  size = 'default',
  trackingData = {}
}) => {
  const containerClass = layout === 'vertical' 
    ? 'space-y-4' 
    : 'flex flex-wrap gap-4';

  return (
    <div className={`${containerClass} ${className}`}>
      {positions.map(position => (
        <EnhancedAffiliateButton
          key={position}
          article={article}
          position={position}
          size={size}
          className={layout === 'vertical' ? 'w-full' : ''}
          trackingData={{...trackingData, position}}
        />
      ))}
    </div>
  );
};

export default EnhancedAffiliateButton;