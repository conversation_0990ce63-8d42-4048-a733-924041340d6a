import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  PlayIcon, 
  GiftIcon, 
  ArrowTopRightOnSquareIcon,
  LinkIcon
} from '@heroicons/react/24/outline';
import apiClient from '../../utils/apiClient';

/**
 * 文章联盟链接显示组件
 * 显示文章中配置的联盟链接，而不是推荐算法生成的链接
 */
const ArticleAffiliateDisplay = ({ 
  articleId, 
  position = 'primary_cta',
  className = '',
  size = 'default',
  layout = 'horizontal',
  showIcon = true,
  maxLinks = null
}) => {
  const { t } = useTranslation();
  const [affiliateLinks, setAffiliateLinks] = useState([]);
  const [loading, setLoading] = useState(true);

  // 按钮尺寸样式
  const sizeClasses = {
    small: 'py-2 px-4 text-sm',
    default: 'py-3 px-6 text-base',
    large: 'py-4 px-8 text-lg'
  };

  // Casino主题按钮样式
  const buttonStyles = {
    primary: 'bg-gradient-to-r from-casino-gold-500 to-casino-gold-600 hover:from-casino-gold-600 hover:to-casino-gold-700 text-casino-dark-900',
    secondary: 'bg-gradient-to-r from-casino-dark-600 to-casino-dark-700 hover:from-casino-dark-700 hover:to-casino-dark-800 text-casino-gold-100 border border-casino-gold-500/30',
    outline: 'border-2 border-casino-gold-500 text-casino-gold-500 hover:bg-casino-gold-500 hover:text-casino-dark-900 bg-transparent',
    text: 'text-casino-gold-500 hover:text-casino-gold-400 underline bg-transparent shadow-none'
  };

  // 根据位置选择图标
  const getIcon = (pos) => {
    switch (pos) {
      case 'primary_cta':
        return PlayIcon;
      case 'secondary_cta':
        return GiftIcon;
      case 'content_inline':
        return LinkIcon;
      default:
        return ArrowTopRightOnSquareIcon;
    }
  };

  // 获取文章的联盟链接
  const fetchArticleLinks = async () => {
    if (!articleId) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const response = await apiClient.get(`/api/affiliate/articles/${articleId}/links`);
      
      if (response.success) {
        let links = response.data || [];
        
        // 过滤指定位置的链接
        if (position !== 'all') {
          links = links.filter(link => link.position === position);
        }
        
        // 限制显示数量
        if (maxLinks && links.length > maxLinks) {
          links = links.slice(0, maxLinks);
        }
        
        // 按优先级排序（如果有的话）
        links.sort((a, b) => (b.priority || 0) - (a.priority || 0));
        
        setAffiliateLinks(links);
      }
    } catch (error) {
      console.error('获取文章联盟链接失败:', error);
      setAffiliateLinks([]);
    } finally {
      setLoading(false);
    }
  };

  // 追踪点击事件
  const trackClick = async (affiliateLink) => {
    try {
      const clickData = {
        affiliate_link_id: affiliateLink.affiliate_link_id || affiliateLink.id,
        article_id: articleId,
        article_url: window.location.href,
        article_title: document.title,
        click_position: position,
        user_agent: navigator.userAgent,
        referer: document.referrer,
        session_id: sessionStorage.getItem('session_id') || generateSessionId()
      };

      // 异步记录点击，不阻塞用户操作
      apiClient.post('/api/affiliate/clicks', clickData).catch(error => {
        console.error('记录点击失败:', error);
      });

      // 打开联盟链接
      window.open(affiliateLink.affiliate_url, '_blank', 'noopener,noreferrer');
    } catch (error) {
      console.error('处理点击事件失败:', error);
      // 即使追踪失败，也要打开链接
      window.open(affiliateLink.affiliate_url, '_blank', 'noopener,noreferrer');
    }
  };

  // 生成会话ID
  const generateSessionId = () => {
    const sessionId = 'sess_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    sessionStorage.setItem('session_id', sessionId);
    return sessionId;
  };

  useEffect(() => {
    fetchArticleLinks();
  }, [articleId, position]);

  // 如果正在加载
  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="bg-casino-dark-700/30 rounded-xl h-12 w-32"></div>
      </div>
    );
  }

  // 如果没有链接，返回空
  if (affiliateLinks.length === 0) {
    return null;
  }

  // 容器样式
  const containerClass = layout === 'vertical' 
    ? 'space-y-4' 
    : 'flex flex-wrap gap-4';

  // 渲染联盟按钮
  return (
    <div className={`${containerClass} ${className}`}>
      {affiliateLinks.map((link, index) => {
        const IconComponent = getIcon(link.position || position);
        const buttonStyle = buttonStyles[link.custom_button_style] || buttonStyles.primary;
        const displayText = link.custom_display_text || link.display_text || link.name;
        
        return (
          <button
            key={`${link.id}-${index}`}
            onClick={() => trackClick(link)}
            className={`
              ${buttonStyle}
              ${sizeClasses[size]}
              font-bold rounded-xl transition-all duration-300 
              flex items-center space-x-3 shadow-lg hover:shadow-xl 
              transform hover:-translate-y-1 hover:scale-105
              ${layout === 'vertical' ? 'w-full justify-center' : ''}
            `}
            title={link.description}
          >
            {showIcon && <IconComponent className="w-5 h-5" />}
            <span>{displayText}</span>
            {link.is_featured && (
              <span className="bg-casino-red-500 text-white text-xs px-2 py-1 rounded-full ml-2">
                热门
              </span>
            )}
          </button>
        );
      })}
    </div>
  );
};

/**
 * 多位置联盟链接显示组件
 * 支持在一个组件中显示多个位置的联盟链接
 */
export const ArticleAffiliateGroup = ({ 
  articleId,
  positions = ['primary_cta', 'secondary_cta'],
  className = '',
  layout = 'horizontal',
  showEmpty = false
}) => {
  const [hasLinks, setHasLinks] = useState(false);

  if (!articleId) return null;

  const containerClass = layout === 'vertical' 
    ? 'space-y-6' 
    : 'flex flex-wrap gap-6';

  return (
    <div className={`${containerClass} ${className}`}>
      {positions.map(position => (
        <div key={position} className={layout === 'vertical' ? 'w-full' : ''}>
          <ArticleAffiliateDisplay
            articleId={articleId}
            position={position}
            layout={layout}
            className={layout === 'vertical' ? 'w-full' : ''}
          />
        </div>
      ))}
      
      {showEmpty && !hasLinks && (
        <div className="text-center py-8">
          <LinkIcon className="w-12 h-12 mx-auto mb-4 text-casino-gold-400 opacity-50" />
          <p className="text-casino-gold-200">暂无联盟链接</p>
        </div>
      )}
    </div>
  );
};

export default ArticleAffiliateDisplay;