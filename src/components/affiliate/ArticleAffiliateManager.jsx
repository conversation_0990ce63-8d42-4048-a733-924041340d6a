import React, { useState, useEffect } from 'react';
import {
  PlusIcon,
  TrashIcon,
  EyeIcon,
  LinkIcon
} from '@heroicons/react/24/outline';
import apiClient from '../../utils/apiClient';

/**
 * Article Affiliate Links Management Component
 * Used to manage affiliate links associated with articles in the article editor
 */
const ArticleAffiliateManager = ({ articleId, articleData, onUpdate }) => {
  const [articleLinks, setArticleLinks] = useState([]);
  const [availableLinks, setAvailableLinks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showLinkSelector, setShowLinkSelector] = useState(false);

  useEffect(() => {
    if (articleId) {
      fetchArticleLinks();
    }
    fetchAvailableLinks();
  }, [articleId]);

  const fetchArticleLinks = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get(`/api/affiliate/articles/${articleId}/links`);
      if (response.success) {
        setArticleLinks(response.data || []);
      }
    } catch (error) {
      console.error('Failed to fetch article affiliate links:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailableLinks = async () => {
    try {
      const response = await apiClient.get('/api/affiliate/links', {
        params: {
          category: articleData?.content_type,
          is_active: 'true'
        }
      });
      if (response.success) {
        setAvailableLinks(response.data || []);
      }
    } catch (error) {
      console.error('Failed to fetch available affiliate links:', error);
    }
  };

  const handleAddLink = async (linkData) => {
    try {
      setLoading(true);
      const response = await apiClient.post(`/api/affiliate/articles/${articleId}/links`, {
        affiliate_link_id: linkData.affiliate_link_id,
        position: linkData.position || 'primary_cta',
        custom_display_text: linkData.custom_display_text,
        custom_button_style: linkData.custom_button_style
      });
      
      if (response.success) {
        await fetchArticleLinks();
        setShowLinkSelector(false);
        if (onUpdate) onUpdate();
      }
    } catch (error) {
      console.error('Failed to add article affiliate link:', error);
      alert('Failed to add affiliate link: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveLink = async (linkId) => {
    if (window.confirm('Are you sure you want to remove this affiliate link?')) {
      try {
        setLoading(true);
        const response = await apiClient.delete(`/api/affiliate/articles/${articleId}/links/${linkId}`);
        
        if (response.success) {
          await fetchArticleLinks();
          if (onUpdate) onUpdate();
        }
      } catch (error) {
        console.error('Failed to remove article affiliate link:', error);
        alert('Failed to remove affiliate link: ' + error.message);
      } finally {
        setLoading(false);
      }
    }
  };

  // 使用内联形式而不是模态框
  const LinkSelectorForm = () => {
    const [selectedLinkId, setSelectedLinkId] = useState('');
    const [position, setPosition] = useState('primary_cta');
    const [customDisplayText, setCustomDisplayText] = useState('');
    const [customButtonStyle, setCustomButtonStyle] = useState('');

    const handleSubmit = async () => {
      if (!selectedLinkId) return;

      await handleAddLink({
        affiliate_link_id: selectedLinkId,
        position,
        custom_display_text: customDisplayText,
        custom_button_style: customButtonStyle
      });
      
      // Reset form
      setSelectedLinkId('');
      setPosition('primary_cta');
      setCustomDisplayText('');
      setCustomButtonStyle('');
    };

    const selectedLink = availableLinks.find(link => link.id === parseInt(selectedLinkId));

    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h4 className="text-lg font-medium text-blue-900">Add New Affiliate Link</h4>
          <button
            onClick={() => setShowLinkSelector(false)}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            Cancel
          </button>
        </div>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Select Affiliate Link
            </label>
            <select
              value={selectedLinkId}
              onChange={(e) => setSelectedLinkId(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900"
              required
            >
              <option value="">Choose an affiliate link...</option>
              {availableLinks.map((link) => (
                <option key={link.id} value={link.id}>
                  {link.name} ({link.shortCode || link.shortcode})
                </option>
              ))}
            </select>
          </div>

          {selectedLink && (
            <div className="bg-gray-50 rounded-md p-3">
              <div className="text-sm text-gray-600">
                <strong>Description:</strong> {selectedLink.description || 'No description'}
              </div>
              <div className="text-sm text-gray-600 mt-1">
                <strong>Category:</strong> {selectedLink.category} | <strong>Language:</strong> {selectedLink.language?.toUpperCase()}
              </div>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Position
              </label>
              <select
                value={position}
                onChange={(e) => setPosition(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900"
              >
                <option value="primary_cta">Primary CTA</option>
                <option value="secondary_cta">Secondary CTA</option>
                <option value="content_inline">Content Inline</option>
                <option value="sidebar">Sidebar</option>
                <option value="footer">Footer</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Button Style
              </label>
              <select
                value={customButtonStyle}
                onChange={(e) => setCustomButtonStyle(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900"
              >
                <option value="">Default Style</option>
                <option value="primary">Primary Button</option>
                <option value="secondary">Secondary Button</option>
                <option value="outline">Outline Button</option>
                <option value="text">Text Link</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Custom Display Text (Optional)
            </label>
            <input
              type="text"
              value={customDisplayText}
              onChange={(e) => setCustomDisplayText(e.target.value)}
              placeholder="Leave empty to use default text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={() => setShowLinkSelector(false)}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleSubmit}
              disabled={!selectedLinkId || loading}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Adding...' : 'Add Link'}
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Affiliate Links Management</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Manage affiliate links displayed in this article
          </p>
        </div>
        
        {articleId && !showLinkSelector && (
          <button
            onClick={() => setShowLinkSelector(true)}
            className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg shadow hover:shadow-lg transition-all duration-200"
            type="button"
          >
            <PlusIcon className="w-4 h-4 mr-2" />
            Add Affiliate Link
          </button>
        )}
      </div>

      {!articleId && (
        <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <div className="text-amber-600 dark:text-amber-400 text-xl">ℹ️</div>
            <div>
              <h4 className="font-medium text-amber-800 dark:text-amber-200">Notice</h4>
              <p className="text-sm text-amber-700 dark:text-amber-300 mt-1">
                Please save the article first, then you can add affiliate links.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Inline Link Selector Form */}
      {showLinkSelector && <LinkSelectorForm />}

      {/* Affiliate Links List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        {loading ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 dark:text-gray-400 mt-2">Loading...</p>
          </div>
        ) : articleLinks.length === 0 ? (
          <div className="p-6 text-center">
            <LinkIcon className="w-12 h-12 mx-auto mb-4 text-gray-400 opacity-50" />
            <p className="text-gray-600 dark:text-gray-400">No affiliate links</p>
            <p className="text-sm text-gray-500 dark:text-gray-500 mt-1">
              Add affiliate links to improve article monetization
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {articleLinks.map((link, index) => (
              <div key={link.id} className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {link.custom_display_text || link.name}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {link.description}
                    </p>
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-500">
                      <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded">
                        {link.position || 'primary_cta'}
                      </span>
                      {link.display_text && (
                        <span>Code: {link.display_text}</span>
                      )}
                      {link.link_active && (
                        <span className="text-green-600 dark:text-green-400">● Active</span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => window.open(link.affiliate_url, '_blank')}
                      className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-1"
                      title="View link"
                      type="button"
                    >
                      <EyeIcon className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleRemoveLink(link.affiliate_link_id)}
                      className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 p-1"
                      title="Remove link"
                      type="button"
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Recommended Links */}
      {articleData && availableLinks.length > 0 && !showLinkSelector && (
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 dark:text-white mb-2">Recommended Affiliate Links</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {availableLinks
              .filter(link => link.category === articleData.content_type)
              .slice(0, 4)
              .map((link) => (
                <div key={link.id} className="bg-white dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {link.name}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {link.shortCode || link.shortcode} • {link.category}
                  </div>
                </div>
              ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ArticleAffiliateManager;