import React, { useState, useEffect } from 'react';
import {
  PlusIcon,
  TrashIcon,
  EyeIcon,
  LinkIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import apiClient from '../../utils/apiClient';

/**
 * 模态窗口形式的文章联盟链接管理组件
 * 避免表单嵌套问题，提供独立的管理界面
 */
const ArticleAffiliateModal = ({ isOpen, onClose, articleId, articleTitle }) => {
  const [article, setArticle] = useState(null);
  const [articleLinks, setArticleLinks] = useState([]);
  const [availableLinks, setAvailableLinks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);

  // 表单状态
  const [selectedLinkId, setSelectedLinkId] = useState('');
  const [position, setPosition] = useState('primary_cta');
  const [customDisplayText, setCustomDisplayText] = useState('');
  const [customButtonStyle, setCustomButtonStyle] = useState('');

  useEffect(() => {
    if (isOpen && articleId) {
      fetchArticle();
      fetchArticleLinks();
      fetchAvailableLinks();
    }
  }, [isOpen, articleId]);

  // 禁用背景滚动
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const fetchArticle = async () => {
    try {
      const response = await apiClient.get(`/api/articles/${articleId}`);
      if (response.success) {
        setArticle(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch article:', error);
    }
  };

  const fetchArticleLinks = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get(`/api/affiliate/articles/${articleId}/links`);
      if (response.success) {
        setArticleLinks(response.data || []);
      }
    } catch (error) {
      console.error('Failed to fetch article affiliate links:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailableLinks = async () => {
    try {
      const response = await apiClient.get('/api/affiliate/links', {
        params: {
          category: article?.content_type,
          is_active: 'true'
        }
      });
      if (response.success) {
        setAvailableLinks(response.data || []);
      }
    } catch (error) {
      console.error('Failed to fetch available affiliate links:', error);
    }
  };

  const handleAddLink = async () => {
    if (!selectedLinkId) return;

    try {
      setLoading(true);
      const response = await apiClient.post(`/api/affiliate/articles/${articleId}/links`, {
        affiliate_link_id: selectedLinkId,
        position: position || 'primary_cta',
        custom_display_text: customDisplayText,
        custom_button_style: customButtonStyle
      });
      
      if (response.success) {
        await fetchArticleLinks();
        // 重置表单
        setSelectedLinkId('');
        setPosition('primary_cta');
        setCustomDisplayText('');
        setCustomButtonStyle('');
        setShowAddForm(false);
      }
    } catch (error) {
      console.error('Failed to add article affiliate link:', error);
      alert('Failed to add affiliate link: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveLink = async (recordId) => {
    if (window.confirm('Are you sure you want to remove this affiliate link?')) {
      try {
        setLoading(true);
        const response = await apiClient.delete(`/api/affiliate/articles/${articleId}/links/record/${recordId}`);
        
        if (response.success) {
          await fetchArticleLinks();
        }
      } catch (error) {
        console.error('Failed to remove article affiliate link:', error);
        alert('Failed to remove affiliate link: ' + error.message);
      } finally {
        setLoading(false);
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-4 mx-auto p-5 border w-full max-w-4xl max-h-[95vh] overflow-y-auto shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Affiliate Links Management</h1>
            <p className="text-gray-600 mt-1">
              Managing affiliate links for: <span className="font-medium">{articleTitle || article?.title}</span>
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Add Link Section */}
        <div className="bg-gray-50 rounded-lg p-6 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-medium text-gray-900">Add New Affiliate Link</h2>
            {!showAddForm && (
              <button
                onClick={() => setShowAddForm(true)}
                className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg"
              >
                <PlusIcon className="w-4 h-4 mr-2" />
                Add Link
              </button>
            )}
          </div>

          {showAddForm && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Select Affiliate Link
                </label>
                <select
                  value={selectedLinkId}
                  onChange={(e) => setSelectedLinkId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Choose an affiliate link...</option>
                  {availableLinks.map((link) => (
                    <option key={link.id} value={link.id}>
                      {link.name} ({link.shortCode || link.shortcode})
                    </option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Position
                  </label>
                  <select
                    value={position}
                    onChange={(e) => setPosition(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="primary_cta">Primary CTA</option>
                    <option value="secondary_cta">Secondary CTA</option>
                    <option value="content_inline">Content Inline</option>
                    <option value="sidebar">Sidebar</option>
                    <option value="footer">Footer</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Button Style
                  </label>
                  <select
                    value={customButtonStyle}
                    onChange={(e) => setCustomButtonStyle(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Default Style</option>
                    <option value="primary">Primary Button</option>
                    <option value="secondary">Secondary Button</option>
                    <option value="outline">Outline Button</option>
                    <option value="text">Text Link</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Custom Display Text (Optional)
                </label>
                <input
                  type="text"
                  value={customDisplayText}
                  onChange={(e) => setCustomDisplayText(e.target.value)}
                  placeholder="Leave empty to use default text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowAddForm(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddLink}
                  disabled={!selectedLinkId || loading}
                  className="px-4 py-2 text-white bg-blue-600 hover:bg-blue-700 rounded-md disabled:opacity-50"
                >
                  {loading ? 'Adding...' : 'Add Link'}
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Existing Links */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Current Affiliate Links</h2>
            <p className="text-sm text-gray-600 mt-1">
              Links currently associated with this article
            </p>
          </div>

          {loading && !showAddForm ? (
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600 mt-2">Loading...</p>
            </div>
          ) : articleLinks.length === 0 ? (
            <div className="p-6 text-center">
              <LinkIcon className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <p className="text-gray-600">No affiliate links</p>
              <p className="text-sm text-gray-500 mt-1">
                Add affiliate links to improve article monetization
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {articleLinks.map((link) => (
                <div key={link.id} className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">
                        {link.custom_display_text || link.name}
                      </h3>
                      <p className="text-sm text-gray-600 mt-1">
                        {link.description}
                      </p>
                      <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                          {link.position || 'primary_cta'}
                        </span>
                        {link.display_text && (
                          <span>Code: {link.display_text}</span>
                        )}
                        {link.link_active && (
                          <span className="text-green-600">● Active</span>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={() => window.open(link.affiliate_url, '_blank')}
                        className="text-gray-500 hover:text-gray-700 p-2"
                        title="View link"
                      >
                        <EyeIcon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleRemoveLink(link.id)}
                        className="text-red-500 hover:text-red-700 p-2"
                        title="Remove link"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Recommended Links */}
        {availableLinks.length > 0 && (
          <div className="bg-gray-50 rounded-lg p-6 mt-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Recommended Links</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {availableLinks
                .filter(link => link.category === article?.content_type)
                .slice(0, 6)
                .map((link) => (
                  <div key={link.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="text-sm font-medium text-gray-900">
                      {link.name}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {link.shortCode || link.shortcode} • {link.category}
                    </div>
                  </div>
                ))}
            </div>
          </div>
        )}

        {/* Close Button */}
        <div className="flex justify-end pt-6 border-t mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ArticleAffiliateModal;