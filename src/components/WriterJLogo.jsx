import React from 'react';

const WriterJLogo = ({ className = "w-12 h-12", showText = false, textClassName = "text-2xl font-bold text-gray-900" }) => {
  return (
    <div className="flex items-center space-x-3">
      <div className={`${className} bg-gradient-to-r from-red-600 via-yellow-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg relative overflow-hidden border-2 border-yellow-400`}>
        {/* Casino-style decorative elements */}
        <div className="absolute top-1 right-1 w-1 h-1 bg-yellow-300 rounded-full shadow-sm"></div>
        <div className="absolute bottom-1 left-1 w-0.5 h-0.5 bg-red-300 rounded-full"></div>
        <div className="absolute top-2 right-2 w-0.5 h-0.5 bg-yellow-200 rounded-full"></div>

        {/* Additional casino sparkles */}
        <div className="absolute top-1 left-2 w-0.5 h-0.5 bg-white rounded-full opacity-80"></div>
        <div className="absolute bottom-2 right-1 w-0.5 h-0.5 bg-white rounded-full opacity-60"></div>

        {/* 777 Text with casino styling */}
        <div className="text-white font-bold text-lg leading-none tracking-tight font-mono drop-shadow-lg">
          777
        </div>
      </div>

      {showText && (
        <span className={textClassName.replace('Writer J', 'Writer 777')}>
          Writer <span className="text-red-600 font-mono">777</span>
        </span>
      )}
    </div>
  );
};

export default WriterJLogo;
