import React, { useState, useEffect } from 'react';
import { ChevronDownIcon, CheckIcon } from '@heroicons/react/24/outline';

const LanguageSelector = ({ 
  selectedLanguage = 'en', 
  onLanguageChange, 
  className = '',
  disabled = false 
}) => {
  const [supportedLanguages, setSupportedLanguages] = useState([]);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchSupportedLanguages();
  }, []);

  const fetchSupportedLanguages = async () => {
    try {
      const response = await fetch('/api/risk-analysis/supported-languages');
      if (response.ok) {
        const data = await response.json();
        setSupportedLanguages(data.languages);
      }
    } catch (error) {
      console.error('Error fetching supported languages:', error);
      // Fallback to default languages (aligned with frontend i18n)
      setSupportedLanguages([
        { code: 'en', name: 'English', nativeName: 'English' },
        { code: 'zh', name: 'Chinese', nativeName: '中文' },
        { code: 'pt', name: 'Portuguese', nativeName: 'Português' },
        { code: 'es', name: 'Spanish', nativeName: 'Español' },
        { code: 'de', name: 'German', nativeName: 'Deutsch' },
        { code: 'fr', name: 'French', nativeName: 'Français' },
        { code: 'it', name: 'Italian', nativeName: 'Italiano' },
        { code: 'ja', name: 'Japanese', nativeName: '日本語' }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const selectedLang = supportedLanguages.find(lang => lang.code === selectedLanguage) || 
                      supportedLanguages[0] || 
                      { code: 'en', name: 'English', nativeName: 'English' };

  const handleLanguageSelect = (languageCode) => {
    setIsOpen(false);
    if (onLanguageChange) {
      onLanguageChange(languageCode);
    }
  };

  if (isLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-10 bg-gray-200 rounded-lg"></div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`
          relative w-full bg-white border border-gray-300 rounded-lg shadow-sm pl-3 pr-10 py-2 text-left cursor-default focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm
          ${disabled ? 'bg-gray-50 text-gray-500 cursor-not-allowed' : 'hover:border-gray-400'}
        `}
      >
        <span className="flex items-center">
          <span className="ml-3 block truncate">
            {selectedLang.nativeName} ({selectedLang.name})
          </span>
        </span>
        <span className="ml-3 absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
          <ChevronDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
        </span>
      </button>

      {isOpen && !disabled && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute z-20 mt-1 w-full bg-white shadow-lg max-h-56 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
            {supportedLanguages.map((language) => (
              <button
                key={language.code}
                onClick={() => handleLanguageSelect(language.code)}
                className={`
                  relative w-full text-left cursor-default select-none py-2 pl-3 pr-9 hover:bg-blue-50
                  ${selectedLanguage === language.code ? 'text-blue-900 bg-blue-50' : 'text-gray-900'}
                `}
              >
                <div className="flex items-center">
                  <span className={`ml-3 block truncate ${selectedLanguage === language.code ? 'font-semibold' : 'font-normal'}`}>
                    {language.nativeName} ({language.name})
                  </span>
                </div>

                {selectedLanguage === language.code && (
                  <span className="absolute inset-y-0 right-0 flex items-center pr-4 text-blue-600">
                    <CheckIcon className="h-5 w-5" aria-hidden="true" />
                  </span>
                )}
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default LanguageSelector;
