import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';

/**
 * Multi-language SEO component
 * Handles meta tags, hreflang attributes, and language-specific SEO
 */
const MultiLanguageSEO = ({ 
  title, 
  description, 
  keywords,
  article = null,
  contentType = null 
}) => {
  const { i18n, t } = useTranslation();
  const location = useLocation();

  useEffect(() => {
    // Update document title with language suffix
    const langSuffix = i18n.language !== 'en' ? ` | ${i18n.language.toUpperCase()}` : '';
    document.title = `${title || t('nav.brand')} | ${t('nav.subtitle')}${langSuffix}`;

    // Update meta description
    const metaDescription = description || t('footer.description');
    updateMetaTag('description', metaDescription);

    // Update meta keywords
    if (keywords) {
      updateMetaTag('keywords', keywords);
    }

    // Update language meta tags
    updateMetaTag('language', i18n.language);
    updateMetaTag('content-language', i18n.language);

    // Update Open Graph tags
    updateMetaTag('og:title', title || t('nav.brand'), 'property');
    updateMetaTag('og:description', metaDescription, 'property');
    updateMetaTag('og:locale', getOGLocale(i18n.language), 'property');
    updateMetaTag('og:type', article ? 'article' : 'website', 'property');

    // Add article-specific meta tags
    if (article) {
      updateMetaTag('article:author', article.author, 'property');
      updateMetaTag('article:published_time', article.published_at, 'property');
      updateMetaTag('article:section', t(`contentTypes.${article.content_type}`), 'property');
      
      if (article.tags) {
        const tags = typeof article.tags === 'string' ? JSON.parse(article.tags) : article.tags;
        tags.forEach(tag => {
          addMetaTag('article:tag', tag, 'property');
        });
      }
    }

    // Update hreflang links
    updateHrefLangLinks();

    // Update canonical URL
    updateCanonicalUrl();

  }, [i18n.language, title, description, keywords, article, location.pathname]);

  const updateMetaTag = (name, content, attribute = 'name') => {
    if (!content) return;

    let meta = document.querySelector(`meta[${attribute}="${name}"]`);
    if (!meta) {
      meta = document.createElement('meta');
      meta.setAttribute(attribute, name);
      document.head.appendChild(meta);
    }
    meta.content = content;
  };

  const addMetaTag = (name, content, attribute = 'name') => {
    if (!content) return;

    const meta = document.createElement('meta');
    meta.setAttribute(attribute, name);
    meta.content = content;
    document.head.appendChild(meta);
  };

  const updateHrefLangLinks = () => {
    // Remove existing hreflang links
    const existingLinks = document.querySelectorAll('link[hreflang]');
    existingLinks.forEach(link => link.remove());

    const currentPath = location.pathname;
    const baseUrl = window.location.origin;
    const languages = ['en', 'pt', 'es'];

    languages.forEach(lang => {
      const link = document.createElement('link');
      link.rel = 'alternate';
      link.hreflang = lang;
      
      // Generate URL for each language
      if (lang === 'en') {
        // Remove language prefix for English
        const pathWithoutLang = currentPath.replace(/^\/(pt|es)/, '') || '/';
        link.href = `${baseUrl}${pathWithoutLang}`;
      } else {
        const pathWithoutLang = currentPath.replace(/^\/(pt|es)/, '') || '/';
        link.href = `${baseUrl}/${lang}${pathWithoutLang}`;
      }
      
      document.head.appendChild(link);
    });

    // Add x-default for English
    const defaultLink = document.createElement('link');
    defaultLink.rel = 'alternate';
    defaultLink.hreflang = 'x-default';
    const pathWithoutLang = currentPath.replace(/^\/(pt|es)/, '') || '/';
    defaultLink.href = `${baseUrl}${pathWithoutLang}`;
    document.head.appendChild(defaultLink);
  };

  const updateCanonicalUrl = () => {
    let canonical = document.querySelector('link[rel="canonical"]');
    if (!canonical) {
      canonical = document.createElement('link');
      canonical.rel = 'canonical';
      document.head.appendChild(canonical);
    }

    const baseUrl = window.location.origin;
    const currentPath = location.pathname;
    
    // Canonical URL should point to the current language version
    canonical.href = `${baseUrl}${currentPath}`;
  };

  const getOGLocale = (language) => {
    const localeMap = {
      'en': 'en_US',
      'pt': 'pt_BR',
      'es': 'es_ES'
    };
    return localeMap[language] || 'en_US';
  };

  // This component doesn't render anything
  return null;
};

export default MultiLanguageSEO;