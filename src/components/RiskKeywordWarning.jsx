import React, { useState, useEffect } from 'react';
import { ExclamationTriangleIcon, InformationCircleIcon, XMarkIcon } from '@heroicons/react/24/outline';

const RiskKeywordWarning = ({
  keywords = [],
  contentType = '',
  jurisdiction = 'international',
  languageCode = 'en',
  useAI = false,
  onDismiss
}) => {
  const [riskAnalysis, setRiskAnalysis] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  useEffect(() => {
    if (keywords.length > 0 && !isDismissed) {
      analyzeKeywords();
    }
  }, [keywords, contentType, jurisdiction, languageCode, useAI]);

  const analyzeKeywords = async () => {
    if (keywords.length === 0) return;

    setIsLoading(true);
    try {
      const response = await fetch('/api/risk-analysis/check-keywords', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          keywords,
          contentType,
          languageCode,
          useAI
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setRiskAnalysis(data.analysis);
      }
    } catch (error) {
      console.error('Error analyzing keywords:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDismiss = () => {
    setIsDismissed(true);
    if (onDismiss) {
      onDismiss();
    }
  };

  // Don't show if dismissed or no risks detected
  if (isDismissed || !riskAnalysis || riskAnalysis.safe) {
    return null;
  }

  const getRiskLevelColor = (level) => {
    switch (level) {
      case 'high':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'medium':
        return 'bg-orange-50 border-orange-200 text-orange-800';
      default:
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
    }
  };

  const getRiskIcon = (level) => {
    switch (level) {
      case 'high':
        return <ExclamationTriangleIcon className="w-5 h-5 text-red-600" />;
      case 'medium':
        return <ExclamationTriangleIcon className="w-5 h-5 text-orange-600" />;
      default:
        return <InformationCircleIcon className="w-5 h-5 text-yellow-600" />;
    }
  };

  const getHighestRiskLevel = () => {
    if (riskAnalysis.risks.some(risk => risk.risks.some(r => r.level === 'high'))) {
      return 'high';
    }
    if (riskAnalysis.risks.some(risk => risk.risks.some(r => r.level === 'medium'))) {
      return 'medium';
    }
    return 'low';
  };

  const highestRisk = getHighestRiskLevel();

  return (
    <div className={`rounded-lg border p-4 ${getRiskLevelColor(highestRisk)}`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          {getRiskIcon(highestRisk)}
          <div className="flex-1">
            <h4 className="font-semibold mb-2">
              {highestRisk === 'high' ? 'High-Risk Keywords Detected' : 
               highestRisk === 'medium' ? 'Medium-Risk Keywords Detected' : 
               'Keyword Review Recommended'}
            </h4>
            
            <p className="text-sm mb-3">
              {highestRisk === 'high' 
                ? 'Some keywords may violate compliance guidelines for gambling content. Consider replacing them with safer alternatives.'
                : 'Some keywords require careful use with appropriate disclaimers and context.'}
            </p>

            {/* Risk Details */}
            <div className="space-y-3">
              {riskAnalysis.risks.map((riskItem, index) => (
                <div key={index} className="bg-white/50 rounded-lg p-3 border border-current/20">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="font-medium text-sm">Keyword:</span>
                    <code className="bg-white/70 px-2 py-1 rounded text-sm font-mono">
                      {riskItem.keyword}
                    </code>
                  </div>
                  
                  {riskItem.risks.map((risk, riskIndex) => (
                    <div key={riskIndex} className="mb-2 last:mb-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          risk.level === 'high' ? 'bg-red-100 text-red-800' : 'bg-orange-100 text-orange-800'
                        }`}>
                          {risk.level} risk
                        </span>
                        <span className="text-xs text-gray-600 capitalize">
                          {risk.category.replace('_', ' ')}
                        </span>
                      </div>
                      <p className="text-xs text-gray-700 ml-2">
                        {risk.message}
                      </p>
                    </div>
                  ))}
                </div>
              ))}
            </div>

            {/* Recommendations */}
            <div className="mt-4 p-3 bg-white/30 rounded-lg">
              <h5 className="font-medium text-sm mb-2">Recommendations:</h5>
              <ul className="text-xs space-y-1">
                <li>• Review keywords for compliance with {jurisdiction} regulations</li>
                <li>• Consider using educational or informational alternatives</li>
                <li>• Ensure appropriate disclaimers are included in content</li>
                <li>• Focus on responsible gaming messaging</li>
              </ul>
            </div>

            {/* Safe Alternatives Button */}
            <div className="mt-3">
              <button
                onClick={() => {
                  // In a real implementation, this would show safe alternatives
                  alert('Safe alternatives feature would be implemented here');
                }}
                className="text-xs underline hover:no-underline"
              >
                View safe alternatives →
              </button>
            </div>
          </div>
        </div>

        {/* Dismiss Button */}
        <button
          onClick={handleDismiss}
          className="flex-shrink-0 p-1 hover:bg-white/30 rounded-full transition-colors"
          title="Dismiss warning"
        >
          <XMarkIcon className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

export default RiskKeywordWarning;
