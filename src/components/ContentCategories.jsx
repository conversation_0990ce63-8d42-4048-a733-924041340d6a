import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import {
  PlusIcon,
  DocumentTextIcon,
  ClockIcon,
  CheckCircleIcon,
  FunnelIcon,
  ChevronRightIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import { API_CONFIG } from '../config/api';

const ContentCategories = () => {
  const { t } = useTranslation();
  const { authenticatedFetch } = useAuth();
  const [categorizedContent, setCategorizedContent] = useState({});
  const [contentTypes, setContentTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [stats, setStats] = useState({});

  useEffect(() => {
    fetchContentData();
  }, []);

  const fetchContentData = async () => {
    try {
      setLoading(true);
      
      // Fetch categorized content and content types
      const [categoriesResponse, typesResponse, statsResponse] = await Promise.all([
        authenticatedFetch(`/api/public/content/categories`),
        authenticatedFetch(`/api/public/content-types`),
        authenticatedFetch(`/api/content/stats`)
      ]);

      if (categoriesResponse.ok) {
        const categoriesData = await categoriesResponse.json();
        setCategorizedContent(categoriesData.categories);
      }

      if (typesResponse.ok) {
        const typesData = await typesResponse.json();
        setContentTypes(typesData.contentTypes);
      }

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData);
      }
    } catch (error) {
      console.error('Error fetching content data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getContentTypeInfo = (contentType) => {
    const typeMap = {
      'casino_review': { icon: '🎰', label: t('contentTypes.casino_review'), color: 'bg-purple-100 text-purple-800' },
      'game_guide': { icon: '🎮', label: t('contentTypes.game_guide'), color: 'bg-blue-100 text-blue-800' },
      'strategy_article': { icon: '📊', label: t('contentTypes.strategy_article', 'Strategy Articles'), color: 'bg-green-100 text-green-800' },
      'brand_copy': { icon: '✨', label: t('contentTypes.brand_copy', 'Brand Copy'), color: 'bg-yellow-100 text-yellow-800' },
      'industry_news': { icon: '📰', label: t('contentTypes.industry_news'), color: 'bg-red-100 text-red-800' },
      'sports_betting': { icon: '⚽', label: t('contentTypes.sports_betting'), color: 'bg-indigo-100 text-indigo-800' },
      'bonus_analysis': { icon: '🎁', label: t('contentTypes.bonus_analysis'), color: 'bg-yellow-100 text-yellow-800' },
      'regulatory_update': { icon: '⚖️', label: t('contentTypes.regulatory_update'), color: 'bg-red-100 text-red-800' },
      'generic': { icon: '📝', label: t('contentTypes.general'), color: 'bg-gray-100 text-gray-800' },
      'uncategorized': { icon: '📄', label: 'Uncategorized', color: 'bg-gray-100 text-gray-800' }
    };
    
    return typeMap[contentType] || typeMap['uncategorized'];
  };

  const getStatusIcon = (status) => {
    if (status.includes('Completed')) {
      return <CheckCircleIcon className="w-4 h-4 text-green-500" />;
    } else if (status.includes('Generating')) {
      return <ClockIcon className="w-4 h-4 text-yellow-500 animate-spin" />;
    } else {
      return <DocumentTextIcon className="w-4 h-4 text-blue-500" />;
    }
  };

  const filteredCategories = selectedCategory === 'all' 
    ? categorizedContent 
    : { [selectedCategory]: categorizedContent[selectedCategory] || [] };

  if (loading) {
    return (
      <div className="min-h-screen casino-bg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-casino-gold-500 mx-auto"></div>
          <p className="mt-4 text-casino-gold-200">Loading content categories...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen casino-bg">
      {/* Header */}
      <div className="bg-gradient-to-r from-casino-dark-800/90 via-casino-dark-700/90 to-casino-dark-800/90 backdrop-blur-xl border-b border-casino-gold-500/30 sticky top-0 z-50 shadow-lg shadow-casino-gold-500/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-casino-gold-400 neon-glow font-casino">Content Categories</h1>
              <p className="text-casino-gold-200">Browse your content by type and category</p>
            </div>
            <Link
              to="/tasks/new"
              className="casino-button inline-flex items-center px-4 py-2 text-white rounded-lg chip-shadow animate-pulse-gold"
            >
              <PlusIcon className="w-5 h-5 mr-2" />
              Create Content
            </Link>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="casino-card rounded-xl p-6 chip-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-casino-gold-300">Total Content</p>
                <p className="text-2xl font-bold text-casino-gold-400 neon-glow">
                  {stats.overall?.total_tasks || 0}
                </p>
              </div>
              <DocumentTextIcon className="w-8 h-8 text-casino-gold-500" />
            </div>
          </div>

          <div className="casino-card rounded-xl p-6 chip-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-casino-gold-300">Completed</p>
                <p className="text-2xl font-bold text-casino-gold-400">
                  {stats.overall?.completed_tasks || 0}
                </p>
              </div>
              <CheckCircleIcon className="w-8 h-8 text-green-500" />
            </div>
          </div>

          <div className="casino-card rounded-xl p-6 chip-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-casino-gold-300">In Progress</p>
                <p className="text-2xl font-bold text-casino-gold-400">
                  {stats.overall?.generating_tasks || 0}
                </p>
              </div>
              <ClockIcon className="w-8 h-8 text-yellow-500" />
            </div>
          </div>

          <div className="casino-card rounded-xl p-6 chip-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-casino-gold-300">Categories</p>
                <p className="text-2xl font-bold text-casino-gold-400">
                  {Object.keys(categorizedContent).length}
                </p>
              </div>
              <FunnelIcon className="w-8 h-8 text-casino-gold-500" />
            </div>
          </div>
        </div>

        {/* Category Filter */}
        <div className="casino-card rounded-xl p-6 mb-8 chip-shadow">
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => setSelectedCategory('all')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                selectedCategory === 'all'
                  ? 'bg-casino-gold-500 text-casino-dark-800'
                  : 'bg-casino-dark-700 text-casino-gold-200 hover:bg-casino-gold-500/20'
              }`}
            >
              All Categories ({Object.values(categorizedContent).flat().length})
            </button>
            
            {Object.keys(categorizedContent).map(contentType => {
              const typeInfo = getContentTypeInfo(contentType);
              const count = categorizedContent[contentType]?.length || 0;
              
              return (
                <button
                  key={contentType}
                  onClick={() => setSelectedCategory(contentType)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-2 ${
                    selectedCategory === contentType
                      ? 'bg-casino-gold-500 text-casino-dark-800'
                      : 'bg-casino-dark-700 text-casino-gold-200 hover:bg-casino-gold-500/20'
                  }`}
                >
                  <span>{typeInfo.icon}</span>
                  <span>{typeInfo.label} ({count})</span>
                </button>
              );
            })}
          </div>
        </div>

        {/* Content Categories */}
        <div className="space-y-8">
          {Object.keys(filteredCategories).length === 0 ? (
            <div className="casino-card rounded-xl p-12 text-center chip-shadow">
              <SparklesIcon className="w-16 h-16 text-casino-gold-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-casino-gold-400 mb-2">No content yet</h3>
              <p className="text-casino-gold-200 mb-6">Start creating content to see it organized by categories</p>
              <Link
                to="/tasks/new"
                className="casino-button inline-flex items-center px-6 py-3 text-white rounded-lg chip-shadow"
              >
                <PlusIcon className="w-5 h-5 mr-2" />
                Create Your First Content
              </Link>
            </div>
          ) : (
            Object.entries(filteredCategories).map(([contentType, tasks]) => {
              const typeInfo = getContentTypeInfo(contentType);
              
              return (
                <div key={contentType} className="casino-card rounded-xl overflow-hidden chip-shadow">
                  <div className="px-6 py-4 border-b border-casino-gold-500/30 bg-gradient-to-r from-casino-dark-800/50 to-casino-dark-700/50">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{typeInfo.icon}</span>
                        <div>
                          <h3 className="text-lg font-semibold text-casino-gold-400">{typeInfo.label}</h3>
                          <p className="text-sm text-casino-gold-200">{tasks.length} articles</p>
                        </div>
                      </div>
                      <Link
                        to={`/content/category/${contentType}`}
                        className="text-casino-gold-500 hover:text-casino-gold-400 text-sm font-medium transition-colors flex items-center space-x-1"
                      >
                        <span>View all</span>
                        <ChevronRightIcon className="w-4 h-4" />
                      </Link>
                    </div>
                  </div>

                  <div className="divide-y divide-casino-gold-500/20">
                    {tasks.slice(0, 5).map((task) => (
                      <Link
                        key={task.id}
                        to={`/tasks/${task.id}`}
                        className="block px-6 py-4 hover:bg-casino-gold-500/10 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3 flex-1 min-w-0">
                            {getStatusIcon(task.status)}
                            <div className="flex-1 min-w-0">
                              <h4 className="text-sm font-medium text-casino-gold-200 truncate">{task.name}</h4>
                              <p className="text-xs text-casino-gold-300">
                                Updated {new Date(task.updated_at).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${typeInfo.color}`}>
                            {task.status}
                          </span>
                        </div>
                      </Link>
                    ))}
                    
                    {tasks.length > 5 && (
                      <div className="px-6 py-3 bg-casino-dark-800/30">
                        <Link
                          to={`/content/category/${contentType}`}
                          className="text-casino-gold-500 hover:text-casino-gold-400 text-sm font-medium transition-colors"
                        >
                          View {tasks.length - 5} more articles →
                        </Link>
                      </div>
                    )}
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>
    </div>
  );
};

export default ContentCategories;
