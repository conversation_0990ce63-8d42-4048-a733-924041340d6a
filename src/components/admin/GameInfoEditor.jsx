import React, { useState, useEffect } from 'react';
import { InformationCircleIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';

const GameInfoEditor = ({ gameInfo, onChange, onValidationChange }) => {
  const [localGameInfo, setLocalGameInfo] = useState({
    provider: '',
    rtp: '',
    volatility: 'Medium',
    minBet: '',
    maxBet: '',
    maxWin: '',
    reels: 5,
    rows: 3,
    paylines: '',
    bonusFeatures: [],
    theme: '',
    gameType: 'Video Slot',
    rating: 4.0,
    mobileOptimized: true,
    demoAvailable: true,
    recommendedCasinos: [],
    // 新增字段
    gameImages: [],
    pros: [],
    cons: [],
    editorReview: {
      authorName: '',
      authorTitle: '',
      authorImage: '',
      rating: 4.2,
      reviewText: ''
    },
    faqItems: [],
    relatedGames: [],
    similarGames: []
  });

  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (gameInfo) {
      setLocalGameInfo(prev => ({
        ...prev,
        ...gameInfo
      }));
    }
  }, [gameInfo]);

  const validateField = (name, value) => {
    const newErrors = { ...errors };

    switch (name) {
      case 'provider':
        if (!value || value.trim().length === 0) {
          newErrors.provider = 'Provider is required';
        } else {
          delete newErrors.provider;
        }
        break;
      
      case 'rtp':
        if (!value || !/^\d{1,3}\.\d{1,2}%$/.test(value)) {
          newErrors.rtp = 'RTP must be in format like "96.50%"';
        } else {
          delete newErrors.rtp;
        }
        break;
      
      case 'rating':
        const rating = parseFloat(value);
        if (isNaN(rating) || rating < 1 || rating > 5) {
          newErrors.rating = 'Rating must be between 1 and 5';
        } else {
          delete newErrors.rating;
        }
        break;

      case 'minBet':
      case 'maxBet':
        if (!value || !value.startsWith('$')) {
          newErrors[name] = 'Bet amount must start with $ (e.g., $0.20)';
        } else {
          delete newErrors[name];
        }
        break;

      case 'maxWin':
        if (!value || (!value.includes('x') && !value.startsWith('$'))) {
          newErrors.maxWin = 'Max win should include multiplier (e.g., "1000x") or amount (e.g., "$10000")';
        } else {
          delete newErrors.maxWin;
        }
        break;

      default:
        break;
    }

    setErrors(newErrors);
    if (onValidationChange) {
      onValidationChange(Object.keys(newErrors).length === 0);
    }
  };

  const handleChange = (name, value) => {
    const newGameInfo = { ...localGameInfo, [name]: value };
    setLocalGameInfo(newGameInfo);
    
    validateField(name, value);
    
    if (onChange) {
      onChange(newGameInfo);
    }
  };

  const handleArrayChange = (arrayName, index, value) => {
    const newArray = [...localGameInfo[arrayName]];
    newArray[index] = value;
    handleChange(arrayName, newArray);
  };

  const addArrayItem = (arrayName, defaultValue = '') => {
    const newArray = [...localGameInfo[arrayName], defaultValue];
    handleChange(arrayName, newArray);
  };

  const removeArrayItem = (arrayName, index) => {
    const newArray = localGameInfo[arrayName].filter((_, i) => i !== index);
    handleChange(arrayName, newArray);
  };

  const addCasino = () => {
    addArrayItem('recommendedCasinos', {
      name: '',
      bonus: '',
      rating: 4.5,
      playUrl: '#'
    });
  };

  const updateCasino = (index, field, value) => {
    const newCasinos = [...localGameInfo.recommendedCasinos];
    newCasinos[index] = { ...newCasinos[index], [field]: value };
    handleChange('recommendedCasinos', newCasinos);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2 mb-4">
        <InformationCircleIcon className="w-5 h-5 text-blue-500" />
        <h3 className="text-lg font-semibold text-gray-900">Game Information</h3>
        <span className="text-sm text-gray-500">(For Game Guide articles)</span>
      </div>

      {/* Basic Game Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Provider *
          </label>
          <input
            type="text"
            value={localGameInfo.provider}
            onChange={(e) => handleChange('provider', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md ${errors.provider ? 'border-red-500' : 'border-gray-300'}`}
            placeholder="e.g., Pragmatic Play"
          />
          {errors.provider && <p className="text-red-500 text-xs mt-1">{errors.provider}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            RTP *
          </label>
          <input
            type="text"
            value={localGameInfo.rtp}
            onChange={(e) => handleChange('rtp', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md ${errors.rtp ? 'border-red-500' : 'border-gray-300'}`}
            placeholder="e.g., 96.50%"
          />
          {errors.rtp && <p className="text-red-500 text-xs mt-1">{errors.rtp}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Volatility
          </label>
          <select
            value={localGameInfo.volatility}
            onChange={(e) => handleChange('volatility', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value="Low">Low</option>
            <option value="Medium">Medium</option>
            <option value="High">High</option>
            <option value="Very High">Very High</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Game Type
          </label>
          <select
            value={localGameInfo.gameType}
            onChange={(e) => handleChange('gameType', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value="Video Slot">Video Slot</option>
            <option value="Classic Slot">Classic Slot</option>
            <option value="Jackpot Slot">Jackpot Slot</option>
            <option value="Table Game">Table Game</option>
            <option value="Live Casino">Live Casino</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Min Bet *
          </label>
          <input
            type="text"
            value={localGameInfo.minBet}
            onChange={(e) => handleChange('minBet', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md ${errors.minBet ? 'border-red-500' : 'border-gray-300'}`}
            placeholder="e.g., $0.20"
          />
          {errors.minBet && <p className="text-red-500 text-xs mt-1">{errors.minBet}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Max Bet *
          </label>
          <input
            type="text"
            value={localGameInfo.maxBet}
            onChange={(e) => handleChange('maxBet', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md ${errors.maxBet ? 'border-red-500' : 'border-gray-300'}`}
            placeholder="e.g., $100"
          />
          {errors.maxBet && <p className="text-red-500 text-xs mt-1">{errors.maxBet}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Max Win *
          </label>
          <input
            type="text"
            value={localGameInfo.maxWin}
            onChange={(e) => handleChange('maxWin', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md ${errors.maxWin ? 'border-red-500' : 'border-gray-300'}`}
            placeholder="e.g., 21,100x or $50,000"
          />
          {errors.maxWin && <p className="text-red-500 text-xs mt-1">{errors.maxWin}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Rating (1-5)
          </label>
          <input
            type="number"
            min="1"
            max="5"
            step="0.1"
            value={localGameInfo.rating}
            onChange={(e) => handleChange('rating', parseFloat(e.target.value))}
            className={`w-full px-3 py-2 border rounded-md ${errors.rating ? 'border-red-500' : 'border-gray-300'}`}
          />
          {errors.rating && <p className="text-red-500 text-xs mt-1">{errors.rating}</p>}
        </div>
      </div>

      {/* Slot-specific info */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Reels
          </label>
          <input
            type="number"
            min="1"
            max="10"
            value={localGameInfo.reels}
            onChange={(e) => handleChange('reels', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Rows
          </label>
          <input
            type="number"
            min="1"
            max="10"
            value={localGameInfo.rows}
            onChange={(e) => handleChange('rows', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Paylines
          </label>
          <input
            type="text"
            value={localGameInfo.paylines}
            onChange={(e) => handleChange('paylines', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            placeholder="e.g., 25 paylines or Cluster Pays"
          />
        </div>
      </div>

      {/* Theme */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Theme
        </label>
        <input
          type="text"
          value={localGameInfo.theme}
          onChange={(e) => handleChange('theme', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md"
          placeholder="e.g., Egyptian, Fantasy, Fruit"
        />
      </div>

      {/* Bonus Features */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Bonus Features
        </label>
        <div className="space-y-2">
          {localGameInfo.bonusFeatures.map((feature, index) => (
            <div key={index} className="flex items-center space-x-2">
              <input
                type="text"
                value={feature}
                onChange={(e) => handleArrayChange('bonusFeatures', index, e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                placeholder="e.g., Free Spins"
              />
              <button
                type="button"
                onClick={() => removeArrayItem('bonusFeatures', index)}
                className="p-2 text-red-500 hover:text-red-700"
              >
                <TrashIcon className="w-4 h-4" />
              </button>
            </div>
          ))}
          <button
            type="button"
            onClick={() => addArrayItem('bonusFeatures', '')}
            className="flex items-center space-x-1 text-blue-500 hover:text-blue-700"
          >
            <PlusIcon className="w-4 h-4" />
            <span>Add Bonus Feature</span>
          </button>
        </div>
      </div>

      {/* Flags */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="mobileOptimized"
            checked={localGameInfo.mobileOptimized}
            onChange={(e) => handleChange('mobileOptimized', e.target.checked)}
            className="rounded"
          />
          <label htmlFor="mobileOptimized" className="text-sm font-medium text-gray-700">
            Mobile Optimized
          </label>
        </div>

        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="demoAvailable"
            checked={localGameInfo.demoAvailable}
            onChange={(e) => handleChange('demoAvailable', e.target.checked)}
            className="rounded"
          />
          <label htmlFor="demoAvailable" className="text-sm font-medium text-gray-700">
            Demo Available
          </label>
        </div>
      </div>

      {/* Recommended Casinos */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <label className="block text-sm font-medium text-gray-700">
            Recommended Casinos
          </label>
          <button
            type="button"
            onClick={addCasino}
            className="flex items-center space-x-1 text-blue-500 hover:text-blue-700"
          >
            <PlusIcon className="w-4 h-4" />
            <span>Add Casino</span>
          </button>
        </div>
        <div className="space-y-4">
          {localGameInfo.recommendedCasinos.map((casino, index) => (
            <div key={index} className="p-4 border border-gray-200 rounded-md">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">
                    Casino Name
                  </label>
                  <input
                    type="text"
                    value={casino.name}
                    onChange={(e) => updateCasino(index, 'name', e.target.value)}
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                    placeholder="e.g., BetVIP Casino"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">
                    Bonus
                  </label>
                  <input
                    type="text"
                    value={casino.bonus}
                    onChange={(e) => updateCasino(index, 'bonus', e.target.value)}
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                    placeholder="e.g., 100% up to $500"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">
                    Rating (1-5)
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="5"
                    step="0.1"
                    value={casino.rating}
                    onChange={(e) => updateCasino(index, 'rating', parseFloat(e.target.value))}
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                  />
                </div>
                <div className="flex items-end">
                  <button
                    type="button"
                    onClick={() => removeArrayItem('recommendedCasinos', index)}
                    className="flex items-center space-x-1 text-red-500 hover:text-red-700 text-sm"
                  >
                    <TrashIcon className="w-4 h-4" />
                    <span>Remove</span>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Game Images */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <label className="block text-sm font-medium text-gray-700">
            游戏截图 URLs
          </label>
          <button
            type="button"
            onClick={() => addArrayItem('gameImages', '')}
            className="flex items-center space-x-1 text-blue-500 hover:text-blue-700"
          >
            <PlusIcon className="w-4 h-4" />
            <span>添加图片</span>
          </button>
        </div>
        <div className="space-y-2">
          {localGameInfo.gameImages.map((imageUrl, index) => (
            <div key={index} className="flex items-center space-x-2">
              <input
                type="url"
                value={imageUrl}
                onChange={(e) => handleArrayChange('gameImages', index, e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                placeholder="https://example.com/game-screenshot.jpg"
              />
              <button
                type="button"
                onClick={() => removeArrayItem('gameImages', index)}
                className="p-2 text-red-500 hover:text-red-700"
              >
                <TrashIcon className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Pros and Cons */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Pros */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium text-gray-700">
              优点 (Pros)
            </label>
            <button
              type="button"
              onClick={() => addArrayItem('pros', '')}
              className="flex items-center space-x-1 text-green-500 hover:text-green-700"
            >
              <PlusIcon className="w-4 h-4" />
              <span>添加优点</span>
            </button>
          </div>
          <div className="space-y-2">
            {localGameInfo.pros.map((pro, index) => (
              <div key={index} className="flex items-center space-x-2">
                <input
                  type="text"
                  value={pro}
                  onChange={(e) => handleArrayChange('pros', index, e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="e.g., High RTP of 96.50%"
                />
                <button
                  type="button"
                  onClick={() => removeArrayItem('pros', index)}
                  className="p-2 text-red-500 hover:text-red-700"
                >
                  <TrashIcon className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Cons */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium text-gray-700">
              缺点 (Cons)
            </label>
            <button
              type="button"
              onClick={() => addArrayItem('cons', '')}
              className="flex items-center space-x-1 text-red-500 hover:text-red-700"
            >
              <PlusIcon className="w-4 h-4" />
              <span>添加缺点</span>
            </button>
          </div>
          <div className="space-y-2">
            {localGameInfo.cons.map((con, index) => (
              <div key={index} className="flex items-center space-x-2">
                <input
                  type="text"
                  value={con}
                  onChange={(e) => handleArrayChange('cons', index, e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="e.g., High volatility"
                />
                <button
                  type="button"
                  onClick={() => removeArrayItem('cons', index)}
                  className="p-2 text-red-500 hover:text-red-700"
                >
                  <TrashIcon className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Editor Review */}
      <div>
        <h4 className="text-lg font-semibold text-gray-900 mb-4">编辑评论</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              编辑姓名
            </label>
            <input
              type="text"
              value={localGameInfo.editorReview.authorName}
              onChange={(e) => handleChange('editorReview', {...localGameInfo.editorReview, authorName: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="e.g., John Smith"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              编辑职位
            </label>
            <input
              type="text"
              value={localGameInfo.editorReview.authorTitle}
              onChange={(e) => handleChange('editorReview', {...localGameInfo.editorReview, authorTitle: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="e.g., Senior Game Analyst"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              编辑头像 URL
            </label>
            <input
              type="url"
              value={localGameInfo.editorReview.authorImage}
              onChange={(e) => handleChange('editorReview', {...localGameInfo.editorReview, authorImage: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="https://example.com/editor-photo.jpg"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              编辑评分 (1-5)
            </label>
            <input
              type="number"
              min="1"
              max="5"
              step="0.1"
              value={localGameInfo.editorReview.rating}
              onChange={(e) => handleChange('editorReview', {...localGameInfo.editorReview, rating: parseFloat(e.target.value)})}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            编辑评论内容
          </label>
          <textarea
            value={localGameInfo.editorReview.reviewText}
            onChange={(e) => handleChange('editorReview', {...localGameInfo.editorReview, reviewText: e.target.value})}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            placeholder="输入编辑的详细评论..."
          />
        </div>
      </div>

      {/* FAQ Items */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <label className="block text-sm font-medium text-gray-700">
            常见问题 (FAQ)
          </label>
          <button
            type="button"
            onClick={() => addArrayItem('faqItems', { question: '', answer: '' })}
            className="flex items-center space-x-1 text-blue-500 hover:text-blue-700"
          >
            <PlusIcon className="w-4 h-4" />
            <span>添加问题</span>
          </button>
        </div>
        <div className="space-y-4">
          {localGameInfo.faqItems.map((faq, index) => (
            <div key={index} className="p-4 border border-gray-200 rounded-md">
              <div className="space-y-3">
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">
                    问题
                  </label>
                  <input
                    type="text"
                    value={faq.question}
                    onChange={(e) => {
                      const newFaq = [...localGameInfo.faqItems];
                      newFaq[index] = {...newFaq[index], question: e.target.value};
                      handleChange('faqItems', newFaq);
                    }}
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                    placeholder="e.g., What is the RTP of this game?"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">
                    答案
                  </label>
                  <textarea
                    value={faq.answer}
                    onChange={(e) => {
                      const newFaq = [...localGameInfo.faqItems];
                      newFaq[index] = {...newFaq[index], answer: e.target.value};
                      handleChange('faqItems', newFaq);
                    }}
                    rows={2}
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                    placeholder="输入详细答案..."
                  />
                </div>
                <div className="flex justify-end">
                  <button
                    type="button"
                    onClick={() => removeArrayItem('faqItems', index)}
                    className="flex items-center space-x-1 text-red-500 hover:text-red-700 text-sm"
                  >
                    <TrashIcon className="w-4 h-4" />
                    <span>删除</span>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Related Games */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <label className="block text-sm font-medium text-gray-700">
            相关游戏推荐
          </label>
          <button
            type="button"
            onClick={() => addArrayItem('relatedGames', { name: '', rtp: '', rating: 4.5, imageUrl: '' })}
            className="flex items-center space-x-1 text-blue-500 hover:text-blue-700"
          >
            <PlusIcon className="w-4 h-4" />
            <span>添加游戏</span>
          </button>
        </div>
        <div className="space-y-4">
          {localGameInfo.relatedGames.map((game, index) => (
            <div key={index} className="p-4 border border-gray-200 rounded-md">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">
                    游戏名称
                  </label>
                  <input
                    type="text"
                    value={game.name}
                    onChange={(e) => {
                      const newGames = [...localGameInfo.relatedGames];
                      newGames[index] = {...newGames[index], name: e.target.value};
                      handleChange('relatedGames', newGames);
                    }}
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                    placeholder="e.g., Sweet Bonanza"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">
                    RTP
                  </label>
                  <input
                    type="text"
                    value={game.rtp}
                    onChange={(e) => {
                      const newGames = [...localGameInfo.relatedGames];
                      newGames[index] = {...newGames[index], rtp: e.target.value};
                      handleChange('relatedGames', newGames);
                    }}
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                    placeholder="e.g., 96.51%"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">
                    评分 (1-5)
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="5"
                    step="0.1"
                    value={game.rating}
                    onChange={(e) => {
                      const newGames = [...localGameInfo.relatedGames];
                      newGames[index] = {...newGames[index], rating: parseFloat(e.target.value)};
                      handleChange('relatedGames', newGames);
                    }}
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                  />
                </div>
                <div className="flex items-end">
                  <button
                    type="button"
                    onClick={() => removeArrayItem('relatedGames', index)}
                    className="flex items-center space-x-1 text-red-500 hover:text-red-700 text-sm"
                  >
                    <TrashIcon className="w-4 h-4" />
                    <span>删除</span>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default GameInfoEditor;