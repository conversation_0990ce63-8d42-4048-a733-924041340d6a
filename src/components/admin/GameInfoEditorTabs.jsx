import React, { useState, useEffect } from 'react';
import { InformationCircleIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';
import { API_CONFIG } from '../../config/api';
import { useAuth } from '../../contexts/AuthContext';

const GameInfoEditorTabs = ({ gameInfo, onChange, onValidationChange, activeTab: parentActiveTab }) => {
  const { token } = useAuth();
  // Use parent activeTab if provided, otherwise maintain own state
  const [internalActiveTab, setInternalActiveTab] = useState('basic');
  const activeTab = parentActiveTab || internalActiveTab;
  const setActiveTab = parentActiveTab ? () => {} : setInternalActiveTab;
  const [availableGameGuides, setAvailableGameGuides] = useState([]);
  const [availableCasinoReviews, setAvailableCasinoReviews] = useState([]);
  const [localGameInfo, setLocalGameInfo] = useState({
    provider: '',
    rtp: '',
    volatility: 'Medium',
    minBet: '',
    maxBet: '',
    maxWin: '',
    reels: 5,
    rows: 3,
    paylines: '',
    bonusFeatures: [],
    theme: '',
    gameType: 'Video Slot',
    rating: 4.0,
    mobileOptimized: true,
    demoAvailable: true,
    recommendedCasinos: [],
    // 新增字段
    gameImages: [],
    pros: [],
    cons: [],
    editorReview: {
      authorName: '',
      authorTitle: '',
      authorImage: '',
      rating: 4.2,
      reviewText: ''
    },
    faqItems: [],
    relatedGames: [],
    similarGames: []
  });

  const [errors, setErrors] = useState({});
  const [uploading, setUploading] = useState(false);
  const [uploadError, setUploadError] = useState('');

  // Initialize empty arrays for related data (API calls completely disabled)
  useEffect(() => {
    // Set empty arrays to prevent UI errors - API calls disabled to focus on image upload functionality
    setAvailableGameGuides([]);
    setAvailableCasinoReviews([]);
    
    // Note: Related games and casino reviews functionality is temporarily disabled
    // This focuses the UI on the core image upload feature which works properly
  }, []);

  useEffect(() => {
    if (gameInfo) {
      setLocalGameInfo(prev => ({
        ...prev,
        ...gameInfo
      }));
    }
  }, [gameInfo]);

  const validateField = (name, value) => {
    const newErrors = { ...errors };

    switch (name) {
      case 'provider':
        if (!value || value.trim().length === 0) {
          newErrors.provider = 'Game provider is required';
        } else {
          delete newErrors.provider;
        }
        break;
      
      case 'rtp':
        if (!value || !/^\d{1,3}\.\d{1,2}%$/.test(value)) {
          newErrors.rtp = 'RTP格式应为 "96.50%"';
        } else {
          delete newErrors.rtp;
        }
        break;
      
      case 'rating':
        const rating = parseFloat(value);
        if (isNaN(rating) || rating < 1 || rating > 5) {
          newErrors.rating = 'Rating should be between 1-5';
        } else {
          delete newErrors.rating;
        }
        break;

      case 'minBet':
      case 'maxBet':
        if (!value || !value.startsWith('$')) {
          newErrors[name] = 'Bet amount should start with $ (e.g. $0.20)';
        } else {
          delete newErrors[name];
        }
        break;

      case 'maxWin':
        if (!value || (!value.includes('x') && !value.startsWith('$'))) {
          newErrors.maxWin = 'Max win should include multiplier (e.g. "1000x") or amount (e.g. "$10000")';
        } else {
          delete newErrors.maxWin;
        }
        break;

      default:
        break;
    }

    setErrors(newErrors);
    if (onValidationChange) {
      onValidationChange(Object.keys(newErrors).length === 0);
    }
  };

  const handleChange = (name, value) => {
    const newGameInfo = { ...localGameInfo, [name]: value };
    setLocalGameInfo(newGameInfo);
    
    validateField(name, value);
    
    if (onChange) {
      onChange(newGameInfo);
    }
  };

  const handleArrayChange = (arrayName, index, value) => {
    const newArray = [...localGameInfo[arrayName]];
    newArray[index] = value;
    handleChange(arrayName, newArray);
  };

  const addArrayItem = (arrayName, defaultValue = '') => {
    const newArray = [...localGameInfo[arrayName], defaultValue];
    handleChange(arrayName, newArray);
  };

  const removeArrayItem = (arrayName, index) => {
    const newArray = localGameInfo[arrayName].filter((_, i) => i !== index);
    handleChange(arrayName, newArray);
  };

  const handleImageUpload = async (file, index = null, arrayName = 'gameImages') => {
    setUploading(true);
    setUploadError('');

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch(`${API_CONFIG.BASE_URL}/api/upload/image`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const result = await response.json();
      const uploadedUrl = `${API_CONFIG.BASE_URL}${result.fileUrl}`;
      
      if (index !== null && arrayName) {
        // Update specific index in array
        handleArrayChange(arrayName, index, uploadedUrl);
      } else {
        // Add to end of gameImages array (backward compatibility)
        const newImages = [...localGameInfo.gameImages, uploadedUrl];
        handleChange('gameImages', newImages);
      }
      
    } catch (err) {
      setUploadError(err.message);
    } finally {
      setUploading(false);
    }
  };

  const handleFileSelect = (e, index = null, arrayName = 'gameImages') => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        setUploadError('Only image files are allowed (JPEG, PNG, GIF, WebP)');
        return;
      }

      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        setUploadError('File too large. Maximum size is 5MB.');
        return;
      }

      handleImageUpload(file, index, arrayName);
    }
    
    // Clear the file input after selection
    e.target.value = '';
  };

  const tabs = [
    { id: 'basic', label: 'Basic Info', icon: '🎮' },
    { id: 'visual', label: 'Visual Content', icon: '🖼️' },
    { id: 'review', label: 'Review Analysis', icon: '⭐' },
    { id: 'related', label: 'Related Games', icon: '🔗' }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm">
      {/* Tab Navigation - only show if not controlled by parent */}
      {!parentActiveTab && (
        <div className="border-b border-gray-200 flex-shrink-0">
          <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setActiveTab(tab.id);
                }}
                type="button"
                className={`
                  whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm
                  ${activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      )}

      {/* Tab Content */}
      <div className={`p-6 ${!parentActiveTab ? 'overflow-y-auto flex-1 max-h-[60vh]' : ''}`}>
        {/* Basic Info Tab */}
        {activeTab === 'basic' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Game Basic Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Game Provider *
                </label>
                <input
                  type="text"
                  value={localGameInfo.provider}
                  onChange={(e) => handleChange('provider', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md ${errors.provider ? 'border-red-500' : 'border-gray-300'}`}
                  placeholder="e.g. Pragmatic Play"
                />
                {errors.provider && <p className="text-red-500 text-xs mt-1">{errors.provider}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  RTP (Return to Player) *
                </label>
                <input
                  type="text"
                  value={localGameInfo.rtp}
                  onChange={(e) => handleChange('rtp', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md ${errors.rtp ? 'border-red-500' : 'border-gray-300'}`}
                  placeholder="e.g. 96.50%"
                />
                {errors.rtp && <p className="text-red-500 text-xs mt-1">{errors.rtp}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Volatility
                </label>
                <select
                  value={localGameInfo.volatility}
                  onChange={(e) => handleChange('volatility', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="Low">Low</option>
                  <option value="Medium">Medium</option>
                  <option value="High">High</option>
                  <option value="Very High">Very High</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Game Type
                </label>
                <select
                  value={localGameInfo.gameType}
                  onChange={(e) => handleChange('gameType', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="Video Slot">Video Slot</option>
                  <option value="Classic Slot">Classic Slot</option>
                  <option value="Jackpot Slot">Jackpot Slot</option>
                  <option value="Table Game">Table Game</option>
                  <option value="Live Casino">Live Casino</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Min Bet *
                </label>
                <input
                  type="text"
                  value={localGameInfo.minBet}
                  onChange={(e) => handleChange('minBet', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md ${errors.minBet ? 'border-red-500' : 'border-gray-300'}`}
                  placeholder="e.g. $0.20"
                />
                {errors.minBet && <p className="text-red-500 text-xs mt-1">{errors.minBet}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Bet *
                </label>
                <input
                  type="text"
                  value={localGameInfo.maxBet}
                  onChange={(e) => handleChange('maxBet', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md ${errors.maxBet ? 'border-red-500' : 'border-gray-300'}`}
                  placeholder="e.g. $100"
                />
                {errors.maxBet && <p className="text-red-500 text-xs mt-1">{errors.maxBet}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Win *
                </label>
                <input
                  type="text"
                  value={localGameInfo.maxWin}
                  onChange={(e) => handleChange('maxWin', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md ${errors.maxWin ? 'border-red-500' : 'border-gray-300'}`}
                  placeholder="e.g. 21,100x or $50,000"
                />
                {errors.maxWin && <p className="text-red-500 text-xs mt-1">{errors.maxWin}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Game Rating (1-5)
                </label>
                <input
                  type="number"
                  min="1"
                  max="5"
                  step="0.1"
                  value={localGameInfo.rating}
                  onChange={(e) => handleChange('rating', parseFloat(e.target.value))}
                  className={`w-full px-3 py-2 border rounded-md ${errors.rating ? 'border-red-500' : 'border-gray-300'}`}
                />
                {errors.rating && <p className="text-red-500 text-xs mt-1">{errors.rating}</p>}
              </div>
            </div>

            {/* Game Mechanics Info */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Reels
                </label>
                <input
                  type="number"
                  min="1"
                  max="10"
                  value={localGameInfo.reels}
                  onChange={(e) => handleChange('reels', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Rows
                </label>
                <input
                  type="number"
                  min="1"
                  max="10"
                  value={localGameInfo.rows}
                  onChange={(e) => handleChange('rows', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Paylines
                </label>
                <input
                  type="text"
                  value={localGameInfo.paylines}
                  onChange={(e) => handleChange('paylines', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="e.g. 25 paylines or Cluster Pays"
                />
              </div>
            </div>

            {/* Game Theme */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Game Theme
              </label>
              <input
                type="text"
                value={localGameInfo.theme}
                onChange={(e) => handleChange('theme', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="e.g. Egyptian, Fantasy, Fruit"
              />
            </div>

            {/* Special Features */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Bonus Features
              </label>
              <div className="space-y-2">
                {localGameInfo.bonusFeatures.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={feature}
                      onChange={(e) => handleArrayChange('bonusFeatures', index, e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                      placeholder="e.g. Free Spins"
                    />
                    <button
                      type="button"
                      onClick={() => removeArrayItem('bonusFeatures', index)}
                      className="p-2 text-red-500 hover:text-red-700"
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={() => addArrayItem('bonusFeatures', '')}
                  className="flex items-center space-x-1 text-blue-500 hover:text-blue-700"
                >
                  <PlusIcon className="w-4 h-4" />
                  <span>Add Bonus Feature</span>
                </button>
              </div>
            </div>

            {/* Feature Flags */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="mobileOptimized"
                  checked={localGameInfo.mobileOptimized}
                  onChange={(e) => handleChange('mobileOptimized', e.target.checked)}
                  className="rounded"
                />
                <label htmlFor="mobileOptimized" className="text-sm font-medium text-gray-700">
                  Mobile Optimized
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="demoAvailable"
                  checked={localGameInfo.demoAvailable}
                  onChange={(e) => handleChange('demoAvailable', e.target.checked)}
                  className="rounded"
                />
                <label htmlFor="demoAvailable" className="text-sm font-medium text-gray-700">
                  Demo Available
                </label>
              </div>
            </div>
          </div>
        )}

        {/* Visual Content Tab */}
        {activeTab === 'visual' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Game Visual Content</h3>
            
            {/* Upload Error Display */}
            {uploadError && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
                <div className="flex">
                  <svg className="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <p className="ml-3 text-sm text-red-800">{uploadError}</p>
                  <button
                    onClick={() => setUploadError('')}
                    className="ml-auto -mx-1.5 -my-1.5 bg-red-50 text-red-500 rounded-lg p-1.5 hover:bg-red-100 inline-flex h-8 w-8"
                  >
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>
            )}
            
            {/* Game Screenshots */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  Game Screenshots (Recommended: 4 images)
                </label>
                <button
                  type="button"
                  onClick={() => addArrayItem('gameImages', '')}
                  className="flex items-center space-x-1 text-blue-500 hover:text-blue-700"
                >
                  <PlusIcon className="w-4 h-4" />
                  <span>Add Image</span>
                </button>
              </div>
              <div className="space-y-2">
                {localGameInfo.gameImages.map((imageUrl, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <input
                        type="url"
                        value={imageUrl}
                        onChange={(e) => handleArrayChange('gameImages', index, e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                        placeholder="https://example.com/game-screenshot.jpg"
                      />
                      <label className="bg-blue-600 text-white px-3 py-2 rounded-md text-sm cursor-pointer hover:bg-blue-700 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-500">
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => handleFileSelect(e, index, 'gameImages')}
                          className="sr-only"
                          disabled={uploading}
                        />
                        {uploading ? (
                          <span className="flex items-center">
                            <svg className="animate-spin -ml-1 mr-1 h-3 w-3 text-white" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Uploading...
                          </span>
                        ) : (
                          <span className="flex items-center">
                            <svg className="mr-1 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            Upload
                          </span>
                        )}
                      </label>
                      <button
                        type="button"
                        onClick={() => removeArrayItem('gameImages', index)}
                        className="p-2 text-red-500 hover:text-red-700"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    </div>
                    {imageUrl && (
                      <div className="ml-2">
                        <p className="text-xs text-gray-600 mb-1">Preview:</p>
                        <img
                          src={imageUrl}
                          alt={`Game screenshot ${index + 1} preview`}
                          className="max-w-xs max-h-20 object-cover rounded border"
                          onError={(e) => {
                            e.target.style.display = 'none';
                          }}
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
              <p className="text-sm text-gray-500 mt-2">
                Tip: You can either upload images directly or enter image URLs. High-quality game screenshots improve user engagement.
              </p>
            </div>
          </div>
        )}

        {/* Review Analysis Tab */}
        {activeTab === 'review' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Review & Analysis</h3>
            
            {/* Pros & Cons Comparison */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Pros */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Game Pros
                  </label>
                  <button
                    type="button"
                    onClick={() => addArrayItem('pros', '')}
                    className="flex items-center space-x-1 text-green-500 hover:text-green-700"
                  >
                    <PlusIcon className="w-4 h-4" />
                    <span>Add Pros</span>
                  </button>
                </div>
                <div className="space-y-2">
                  {localGameInfo.pros.map((pro, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={pro}
                        onChange={(e) => handleArrayChange('pros', index, e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                        placeholder="e.g. Up to 96.50% RTP"
                      />
                      <button
                        type="button"
                        onClick={() => removeArrayItem('pros', index)}
                        className="p-2 text-red-500 hover:text-red-700"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Cons */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Game Cons
                  </label>
                  <button
                    type="button"
                    onClick={() => addArrayItem('cons', '')}
                    className="flex items-center space-x-1 text-red-500 hover:text-red-700"
                  >
                    <PlusIcon className="w-4 h-4" />
                    <span>Add Cons</span>
                  </button>
                </div>
                <div className="space-y-2">
                  {localGameInfo.cons.map((con, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={con}
                        onChange={(e) => handleArrayChange('cons', index, e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                        placeholder="e.g. High Volatility"
                      />
                      <button
                        type="button"
                        onClick={() => removeArrayItem('cons', index)}
                        className="p-2 text-red-500 hover:text-red-700"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Editor Review */}
            <div className="border-t pt-6">
              <h4 className="text-md font-semibold text-gray-900 mb-4">专家评论</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    评论者姓名
                  </label>
                  <input
                    type="text"
                    value={localGameInfo.editorReview.authorName}
                    onChange={(e) => handleChange('editorReview', {...localGameInfo.editorReview, authorName: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="e.g. John Smith"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    评论者职位
                  </label>
                  <input
                    type="text"
                    value={localGameInfo.editorReview.authorTitle}
                    onChange={(e) => handleChange('editorReview', {...localGameInfo.editorReview, authorTitle: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="e.g. Senior Game Analyst"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    评论者头像 URL
                  </label>
                  <input
                    type="url"
                    value={localGameInfo.editorReview.authorImage}
                    onChange={(e) => handleChange('editorReview', {...localGameInfo.editorReview, authorImage: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="https://example.com/editor-photo.jpg"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Expert Rating (1-5)
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="5"
                    step="0.1"
                    value={localGameInfo.editorReview.rating}
                    onChange={(e) => handleChange('editorReview', {...localGameInfo.editorReview, rating: parseFloat(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  评论内容
                </label>
                <textarea
                  value={localGameInfo.editorReview.reviewText}
                  onChange={(e) => handleChange('editorReview', {...localGameInfo.editorReview, reviewText: e.target.value})}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="Enter professional game review..."
                />
              </div>
            </div>

            {/* FAQ */}
            <div className="border-t pt-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-md font-semibold text-gray-900">Frequently Asked Questions</h4>
                <button
                  type="button"
                  onClick={() => addArrayItem('faqItems', { question: '', answer: '' })}
                  className="flex items-center space-x-1 text-blue-500 hover:text-blue-700"
                >
                  <PlusIcon className="w-4 h-4" />
                  <span>Add Question</span>
                </button>
              </div>
              <div className="space-y-4">
                {localGameInfo.faqItems.map((faq, index) => (
                  <div key={index} className="p-4 border border-gray-200 rounded-md">
                    <div className="space-y-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-600 mb-1">
                          Question
                        </label>
                        <input
                          type="text"
                          value={faq.question}
                          onChange={(e) => {
                            const newFaq = [...localGameInfo.faqItems];
                            newFaq[index] = {...newFaq[index], question: e.target.value};
                            handleChange('faqItems', newFaq);
                          }}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                          placeholder="e.g. What is the RTP of this game?"
                        />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-600 mb-1">
                          Answer
                        </label>
                        <textarea
                          value={faq.answer}
                          onChange={(e) => {
                            const newFaq = [...localGameInfo.faqItems];
                            newFaq[index] = {...newFaq[index], answer: e.target.value};
                            handleChange('faqItems', newFaq);
                          }}
                          rows={2}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                          placeholder="Enter detailed answer..."
                        />
                      </div>
                      <div className="flex justify-end">
                        <button
                          type="button"
                          onClick={() => removeArrayItem('faqItems', index)}
                          className="flex items-center space-x-1 text-red-500 hover:text-red-700 text-sm"
                        >
                          <TrashIcon className="w-4 h-4" />
                          <span>Delete</span>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Related Recommendations Tab */}
        {activeTab === 'related' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Recommendations</h3>
            
            {/* Recommended Casinos */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-md font-semibold text-gray-900">Recommended Casinos</h4>
                <div className="text-sm text-gray-500">
                  Select from published casino reviews in the system
                </div>
              </div>
              
              {/* Available Casino List */}
              {availableCasinoReviews.length > 0 && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Recommended Casinos (from existing casino reviews)
                  </label>
                  <div className="max-h-40 overflow-y-auto border border-gray-200 rounded-md">
                    {availableCasinoReviews.map((casino) => {
                      const isSelected = localGameInfo.recommendedCasinos.some(rc => rc.id === casino.id);
                      return (
                        <div
                          key={casino.id}
                          className={`p-3 border-b border-gray-100 cursor-pointer hover:bg-gray-50 flex items-center justify-between ${
                            isSelected ? 'bg-blue-50 border-blue-200' : ''
                          }`}
                          onClick={() => {
                            if (isSelected) {
                              // Remove from selected
                              const newCasinos = localGameInfo.recommendedCasinos.filter(rc => rc.id !== casino.id);
                              handleChange('recommendedCasinos', newCasinos);
                            } else {
                              // Add to selected
                              const newCasinos = [...localGameInfo.recommendedCasinos, {
                                id: casino.id,
                                name: casino.name,
                                bonus: casino.bonus,
                                rating: casino.rating,
                                playUrl: casino.playUrl
                              }];
                              handleChange('recommendedCasinos', newCasinos);
                            }
                          }}
                        >
                          <div>
                            <div className="font-medium text-sm text-gray-900">{casino.title}</div>
                            <div className="text-xs text-gray-500">Rating: {casino.rating} | View Details</div>
                          </div>
                          <div className={`w-4 h-4 border-2 rounded ${
                            isSelected ? 'bg-blue-500 border-blue-500' : 'border-gray-300'
                          }`}>
                            {isSelected && (
                              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
              
              {/* Selected Recommended Casinos */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Selected Recommended Casinos ({localGameInfo.recommendedCasinos.length})
                </label>
                {localGameInfo.recommendedCasinos.length === 0 ? (
                  <div className="text-gray-500 text-sm p-4 border border-dashed border-gray-300 rounded-md text-center mb-4">
                    {availableCasinoReviews.length > 0 
                      ? 'Please select recommended casinos from the list above' 
                      : 'No published casino reviews available in the system, cannot add casino recommendations'}
                  </div>
                ) : (
                  <div className="space-y-3 mb-4">
                    {localGameInfo.recommendedCasinos.map((casino, index) => (
                      <div key={casino.id || index} className="p-4 border border-gray-200 rounded-md">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <div className="font-medium text-gray-900">{casino.name}</div>
                            <div className="text-sm text-gray-500">Rating: {casino.rating}</div>
                          </div>
                          <button
                            type="button"
                            onClick={() => removeArrayItem('recommendedCasinos', index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </div>
                        
                        {/* Editable Bonus Information */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <div>
                            <label className="block text-xs font-medium text-gray-600 mb-1">
                              Bonus Offers
                            </label>
                            <input
                              type="text"
                              value={casino.bonus}
                              onChange={(e) => {
                                const newCasinos = [...localGameInfo.recommendedCasinos];
                                newCasinos[index] = {...newCasinos[index], bonus: e.target.value};
                                handleChange('recommendedCasinos', newCasinos);
                              }}
                              className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                              placeholder="e.g. 100% Welcome Bonus up to $500"
                            />
                          </div>
                          <div>
                            <label className="block text-xs font-medium text-gray-600 mb-1">
                              Rating (1-5)
                            </label>
                            <input
                              type="number"
                              min="1"
                              max="5"
                              step="0.1"
                              value={casino.rating}
                              onChange={(e) => {
                                const newCasinos = [...localGameInfo.recommendedCasinos];
                                newCasinos[index] = {...newCasinos[index], rating: parseFloat(e.target.value)};
                                handleChange('recommendedCasinos', newCasinos);
                              }}
                              className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Related Games */}
            <div className="border-t pt-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-md font-semibold text-gray-900">Related Game Recommendations</h4>
                <div className="text-sm text-gray-500">
                  Select from published game guides in the system
                </div>
              </div>
              
              {/* Available Game List */}
              {availableGameGuides.length > 0 && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Related Games (from existing game guides)
                  </label>
                  <div className="max-h-40 overflow-y-auto border border-gray-200 rounded-md">
                    {availableGameGuides.map((guide) => {
                      const isSelected = localGameInfo.relatedGames.some(rg => rg.id === guide.id);
                      return (
                        <div
                          key={guide.id}
                          className={`p-3 border-b border-gray-100 cursor-pointer hover:bg-gray-50 flex items-center justify-between ${
                            isSelected ? 'bg-blue-50 border-blue-200' : ''
                          }`}
                          onClick={() => {
                            if (isSelected) {
                              // Remove from selected
                              const newGames = localGameInfo.relatedGames.filter(rg => rg.id !== guide.id);
                              handleChange('relatedGames', newGames);
                            } else {
                              // Add to selected
                              const newGames = [...localGameInfo.relatedGames, {
                                id: guide.id,
                                name: guide.name,
                                rtp: guide.rtp,
                                rating: guide.rating,
                                imageUrl: guide.imageUrl
                              }];
                              handleChange('relatedGames', newGames);
                            }
                          }}
                        >
                          <div>
                            <div className="font-medium text-sm text-gray-900">{guide.title}</div>
                            <div className="text-xs text-gray-500">RTP: {guide.rtp} | Rating: {guide.rating}</div>
                          </div>
                          <div className={`w-4 h-4 border-2 rounded ${
                            isSelected ? 'bg-blue-500 border-blue-500' : 'border-gray-300'
                          }`}>
                            {isSelected && (
                              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
              
              {/* Selected Related Games */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Selected Related Games ({localGameInfo.relatedGames.length})
                </label>
                {localGameInfo.relatedGames.length === 0 ? (
                  <div className="text-gray-500 text-sm p-4 border border-dashed border-gray-300 rounded-md text-center">
                    {availableGameGuides.length > 0 
                      ? 'Please select related games from the list above' 
                      : 'No other published game guides available in the system, cannot add related game recommendations'}
                  </div>
                ) : (
                  <div className="space-y-2">
                    {localGameInfo.relatedGames.map((game, index) => (
                      <div key={game.id || index} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                        <div>
                          <div className="font-medium text-sm text-gray-900">{game.name}</div>
                          <div className="text-xs text-gray-500">RTP: {game.rtp} | Rating: {game.rating}</div>
                        </div>
                        <button
                          type="button"
                          onClick={() => removeArrayItem('relatedGames', index)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default GameInfoEditorTabs;