import React, { useState, useEffect } from 'react';
import {
  StarIcon,
  CheckCircleIcon,
  XCircleIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  DevicePhoneMobileIcon,
  ChatBubbleLeftRightIcon,
  GiftIcon,
  CreditCardIcon,
  GlobeAltIcon,
  QuestionMarkCircleIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

/**
 * Casino Info Editor Tabs - Specialized editor for casino review data
 */
const CasinoInfoEditorTabs = ({ casinoInfo, onChange, onValidationChange, activeTab }) => {
  const [localCasinoInfo, setLocalCasinoInfo] = useState({
    // Ratings (0-5 scale)
    ratings: {
      overall: 4.2,
      games: 4.5,
      bonuses: 4.0,
      support: 3.8,
      security: 4.7,
      mobile: 4.1,
      payments: 4.3,
      usability: 4.4
    },
    // Basic Information
    established: '2020',
    license: 'Curacao Gaming License',
    owner: 'Gaming Corporation Ltd',
    languages: ['English', 'Spanish', 'German', 'French'],
    currencies: ['USD', 'EUR', 'GBP', 'CAD'],
    countries_restricted: ['US', 'UK', 'France'],
    
    // Pros and Cons
    pros: [
      'Wide selection of games',
      'Generous welcome bonus',
      'Multiple payment options',
      'Mobile-friendly platform'
    ],
    cons: [
      'Limited live chat hours',
      'Withdrawal processing time',
      'Geographic restrictions'
    ],
    
    // FAQ Section
    faq: [
      {
        question: 'Is this casino legitimate and safe?',
        answer: 'Yes, the casino is licensed and uses SSL encryption to protect player data. The casino follows strict regulatory guidelines to ensure fair play and secure transactions.'
      },
      {
        question: 'What payment methods are accepted?',
        answer: 'The casino accepts various payment methods including Visa, Mastercard, e-wallets like Skrill and Neteller, cryptocurrencies, and bank transfers. Check the banking section for full details.'
      },
      {
        question: 'How long do withdrawals take?',
        answer: 'Withdrawal times vary by payment method. E-wallets typically process within 24 hours, while bank transfers may take 3-5 business days.'
      }
    ],
    
    // Bonus Information
    welcome_bonus: '100% up to $500 + 200 Free Spins',
    bonus_wagering: '35x',
    max_cashout: '$5000',
    
    // Payment Methods
    deposit_methods: ['Visa', 'Mastercard', 'Bitcoin', 'eWallets'],
    withdrawal_methods: ['Bank Transfer', 'eWallets', 'Crypto'],
    min_deposit: '$10',
    min_withdrawal: '$20',
    withdrawal_time: '1-3 business days',
    
    // Games
    total_games: '2000+',
    slots_count: '1500+',
    live_games: '100+',
    providers: ['NetEnt', 'Microgaming', 'Evolution Gaming', 'Pragmatic Play'],
    
    // Support
    support_methods: ['Live Chat', 'Email', 'Phone'],
    support_hours: '24/7',
    support_languages: ['English', 'Spanish', 'German']
  });

  const [isValid, setIsValid] = useState(true);

  useEffect(() => {
    if (casinoInfo) {
      setLocalCasinoInfo({ ...localCasinoInfo, ...casinoInfo });
    }
  }, [casinoInfo]);

  useEffect(() => {
    // Validate casino info
    const valid = validateCasinoInfo(localCasinoInfo);
    setIsValid(valid);
    onValidationChange?.(valid);
    onChange?.(localCasinoInfo);
  }, [localCasinoInfo]);

  const validateCasinoInfo = (info) => {
    // Basic validation
    if (!info.established || !info.license || !info.owner) return false;
    if (!info.ratings || Object.keys(info.ratings).length === 0) return false;
    return true;
  };

  const updateRating = (category, rating) => {
    setLocalCasinoInfo(prev => ({
      ...prev,
      ratings: {
        ...prev.ratings,
        [category]: rating
      }
    }));
  };

  const updateField = (field, value) => {
    setLocalCasinoInfo(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateArrayField = (field, index, value) => {
    setLocalCasinoInfo(prev => ({
      ...prev,
      [field]: prev[field].map((item, i) => i === index ? value : item)
    }));
  };

  const addArrayItem = (field) => {
    setLocalCasinoInfo(prev => ({
      ...prev,
      [field]: [...prev[field], '']
    }));
  };

  const removeArrayItem = (field, index) => {
    setLocalCasinoInfo(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }));
  };

  const updateFAQItem = (index, field, value) => {
    setLocalCasinoInfo(prev => ({
      ...prev,
      faq: prev.faq.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    }));
  };

  const addFAQItem = () => {
    setLocalCasinoInfo(prev => ({
      ...prev,
      faq: [...prev.faq, { question: '', answer: '' }]
    }));
  };

  const removeFAQItem = (index) => {
    setLocalCasinoInfo(prev => ({
      ...prev,
      faq: prev.faq.filter((_, i) => i !== index)
    }));
  };

  const renderStars = (rating, onRatingChange) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <button
          key={i}
          type="button"
          onClick={() => onRatingChange(i)}
          className="focus:outline-none"
        >
          {i <= rating ? (
            <StarIconSolid className="w-6 h-6 text-yellow-400 hover:text-yellow-500" />
          ) : (
            <StarIcon className="w-6 h-6 text-gray-300 hover:text-yellow-400" />
          )}
        </button>
      );
    }
    return stars;
  };

  const ArrayEditor = ({ label, field, placeholder }) => (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">{label}</label>
      {localCasinoInfo[field].map((item, index) => (
        <div key={index} className="flex items-center space-x-2">
          <input
            type="text"
            value={item}
            onChange={(e) => updateArrayField(field, index, e.target.value)}
            placeholder={placeholder}
            className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
          />
          <button
            type="button"
            onClick={() => removeArrayItem(field, index)}
            className="text-red-600 hover:text-red-800"
          >
            <XCircleIcon className="w-5 h-5" />
          </button>
        </div>
      ))}
      <button
        type="button"
        onClick={() => addArrayItem(field)}
        className="text-purple-600 hover:text-purple-800 text-sm font-medium"
      >
        + Add {label.toLowerCase()}
      </button>
    </div>
  );

  // Only render when it's the visual, review, or related tab for casino reviews
  if (activeTab === 'basic') return null;

  return (
    <div className="space-y-8">
      {/* Ratings Section */}
      {activeTab === 'visual' && (
        <div className="space-y-6">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <StarIcon className="w-6 h-6 mr-2 text-yellow-500" />
            Casino Ratings
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {Object.entries(localCasinoInfo.ratings).map(([category, rating]) => (
              <div key={category} className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <label className="text-sm font-medium text-gray-700 capitalize">
                    {category.replace('_', ' ')} Rating
                  </label>
                  <span className="text-lg font-bold text-yellow-600">{rating}</span>
                </div>
                <div className="flex space-x-1">
                  {renderStars(rating, (newRating) => updateRating(category, newRating))}
                </div>
              </div>
            ))}
          </div>

          {/* Pros and Cons */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <ArrayEditor 
                label="Pros" 
                field="pros" 
                placeholder="Enter a positive aspect" 
              />
            </div>
            <div className="bg-red-50 p-4 rounded-lg border border-red-200">
              <ArrayEditor 
                label="Cons" 
                field="cons" 
                placeholder="Enter a negative aspect" 
              />
            </div>
          </div>
        </div>
      )}

      {/* Casino Details */}
      {activeTab === 'review' && (
        <div className="space-y-6">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <ShieldCheckIcon className="w-6 h-6 mr-2 text-blue-500" />
            Casino Information
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Established Year</label>
              <input
                type="text"
                value={localCasinoInfo.established}
                onChange={(e) => updateField('established', e.target.value)}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                placeholder="2020"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">License</label>
              <input
                type="text"
                value={localCasinoInfo.license}
                onChange={(e) => updateField('license', e.target.value)}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                placeholder="Curacao Gaming License"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Owner</label>
              <input
                type="text"
                value={localCasinoInfo.owner}
                onChange={(e) => updateField('owner', e.target.value)}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                placeholder="Gaming Corporation Ltd"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Total Games</label>
              <input
                type="text"
                value={localCasinoInfo.total_games}
                onChange={(e) => updateField('total_games', e.target.value)}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                placeholder="2000+"
              />
            </div>
          </div>

          {/* Array Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <ArrayEditor 
              label="Supported Languages" 
              field="languages" 
              placeholder="Enter language" 
            />
            <ArrayEditor 
              label="Supported Currencies" 
              field="currencies" 
              placeholder="Enter currency" 
            />
            <ArrayEditor 
              label="Game Providers" 
              field="providers" 
              placeholder="Enter provider name" 
            />
            <ArrayEditor 
              label="Restricted Countries" 
              field="countries_restricted" 
              placeholder="Enter country code" 
            />
          </div>
        </div>
      )}

      {/* Bonuses and Payments */}
      {activeTab === 'related' && (
        <div className="space-y-6">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <GiftIcon className="w-6 h-6 mr-2 text-green-500" />
            Bonuses & Payments
          </h3>

          {/* Bonus Information */}
          <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
            <h4 className="font-medium text-gray-900 mb-4">Bonus Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Welcome Bonus</label>
                <input
                  type="text"
                  value={localCasinoInfo.welcome_bonus}
                  onChange={(e) => updateField('welcome_bonus', e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                  placeholder="100% up to $500 + 200 Free Spins"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Wagering Requirement</label>
                <input
                  type="text"
                  value={localCasinoInfo.bonus_wagering}
                  onChange={(e) => updateField('bonus_wagering', e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                  placeholder="35x"
                />
              </div>
            </div>
          </div>

          {/* Payment Information */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h4 className="font-medium text-gray-900 mb-4">Payment Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Min Deposit</label>
                <input
                  type="text"
                  value={localCasinoInfo.min_deposit}
                  onChange={(e) => updateField('min_deposit', e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                  placeholder="$10"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Withdrawal Time</label>
                <input
                  type="text"
                  value={localCasinoInfo.withdrawal_time}
                  onChange={(e) => updateField('withdrawal_time', e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                  placeholder="1-3 business days"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <ArrayEditor 
                label="Deposit Methods" 
                field="deposit_methods" 
                placeholder="Enter payment method" 
              />
              <ArrayEditor 
                label="Withdrawal Methods" 
                field="withdrawal_methods" 
                placeholder="Enter withdrawal method" 
              />
            </div>
          </div>

          {/* Support Information */}
          <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
            <h4 className="font-medium text-gray-900 mb-4">Customer Support</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Support Hours</label>
                <input
                  type="text"
                  value={localCasinoInfo.support_hours}
                  onChange={(e) => updateField('support_hours', e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                  placeholder="24/7"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <ArrayEditor 
                label="Support Methods" 
                field="support_methods" 
                placeholder="Enter support method" 
              />
              <ArrayEditor 
                label="Support Languages" 
                field="support_languages" 
                placeholder="Enter language" 
              />
            </div>
          </div>

          {/* FAQ Section */}
          <div className="bg-indigo-50 p-4 rounded-lg border border-indigo-200">
            <h4 className="font-medium text-gray-900 mb-4 flex items-center">
              <QuestionMarkCircleIcon className="w-5 h-5 mr-2 text-indigo-600" />
              Frequently Asked Questions
            </h4>
            <div className="space-y-4">
              {localCasinoInfo.faq.map((faqItem, index) => (
                <div key={index} className="bg-white p-4 rounded-lg border border-gray-200">
                  <div className="flex items-start justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">FAQ #{index + 1}</span>
                    <button
                      type="button"
                      onClick={() => removeFAQItem(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <XCircleIcon className="w-5 h-5" />
                    </button>
                  </div>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Question</label>
                      <input
                        type="text"
                        value={faqItem.question}
                        onChange={(e) => updateFAQItem(index, 'question', e.target.value)}
                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                        placeholder="Enter the question"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Answer</label>
                      <textarea
                        value={faqItem.answer}
                        onChange={(e) => updateFAQItem(index, 'answer', e.target.value)}
                        rows={3}
                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                        placeholder="Enter the answer"
                      />
                    </div>
                  </div>
                </div>
              ))}
              <button
                type="button"
                onClick={addFAQItem}
                className="w-full border-2 border-dashed border-gray-300 rounded-lg p-4 text-gray-600 hover:text-gray-800 hover:border-gray-400 transition-colors flex items-center justify-center space-x-2"
              >
                <PlusIcon className="w-5 h-5" />
                <span>Add FAQ Item</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Validation Status */}
      <div className={`p-4 rounded-lg border ${isValid ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
        <div className="flex items-center">
          {isValid ? (
            <CheckCircleIcon className="w-5 h-5 text-green-600 mr-2" />
          ) : (
            <XCircleIcon className="w-5 h-5 text-red-600 mr-2" />
          )}
          <span className={`text-sm font-medium ${isValid ? 'text-green-800' : 'text-red-800'}`}>
            {isValid ? 'Casino information is complete' : 'Please complete required casino information'}
          </span>
        </div>
      </div>
    </div>
  );
};

export default CasinoInfoEditorTabs;