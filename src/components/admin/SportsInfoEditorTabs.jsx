import React, { useState, useEffect } from 'react';
import {
  PlusIcon,
  TrashIcon,
  TrophyIcon,
  ChartBarIcon,
  PlayIcon,
  StarIcon,
  BanknotesIcon,
  ClockIcon,
  InformationCircleIcon,
  FireIcon
} from '@heroicons/react/24/outline';

/**
 * Sports Info Editor Tabs - Professional sports betting article editor
 * Based on Gazeta Esportiva and Dimers standards
 */
const SportsInfoEditorTabs = ({ article, onContentChange }) => {
  const [activeTab, setActiveTab] = useState('match');
  const [sportsData, setSportsData] = useState({
    matchInfo: {
      homeTeam: '',
      awayTeam: '',
      date: '',
      time: '',
      venue: '',
      league: '',
      odds: {
        home: { odds: '', percentage: 50 },
        draw: { odds: '', percentage: 25 },
        away: { odds: '', percentage: 25 }
      },
      status: 'upcoming'
    },
    predictions: [
      {
        type: 'Match Result',
        prediction: '',
        confidence: 70,
        reasoning: ''
      }
    ],
    playerProps: [
      {
        player: '',
        prop: '',
        line: '',
        odds: '',
        recommendation: 'YES',
        confidence: 70
      }
    ],
    expertPicks: [
      {
        expert: '',
        pick: '',
        odds: '',
        units: 1,
        reasoning: ''
      }
    ],
    statistics: {
      homeStats: {
        wins: 0,
        draws: 0,
        losses: 0,
        goalsFor: 0,
        goalsAgainst: 0,
        form: ['W', 'W', 'W', 'D', 'W']
      },
      awayStats: {
        wins: 0,
        draws: 0,
        losses: 0,
        goalsFor: 0,
        goalsAgainst: 0,
        form: ['W', 'L', 'W', 'W', 'D']
      },
      headToHead: {
        totalMeetings: 0,
        homeWins: 0,
        draws: 0,
        awayWins: 0
      }
    }
  });

  // Extract existing sports data from article content when component mounts
  useEffect(() => {
    if (article?.content) {
      const extractedData = extractSportsDataFromContent(article.content);
      if (extractedData) {
        setSportsData(prev => ({ ...prev, ...extractedData }));
      }
    }
  }, [article?.content]);

  // Extract sports data from article content
  const extractSportsDataFromContent = (content) => {
    try {
      const data = {};
      
      // Extract match info
      const matchInfoMatch = content.match(/MATCH_INFO_START([\s\S]*?)MATCH_INFO_END/);
      if (matchInfoMatch) {
        data.matchInfo = JSON.parse(matchInfoMatch[1]);
      }
      
      // Extract predictions
      const predictionsMatch = content.match(/PREDICTIONS_START([\s\S]*?)PREDICTIONS_END/);
      if (predictionsMatch) {
        data.predictions = JSON.parse(predictionsMatch[1]);
      }
      
      // Extract player props
      const propsMatch = content.match(/PLAYER_PROPS_START([\s\S]*?)PLAYER_PROPS_END/);
      if (propsMatch) {
        data.playerProps = JSON.parse(propsMatch[1]);
      }
      
      // Extract expert picks
      const picksMatch = content.match(/EXPERT_PICKS_START([\s\S]*?)EXPERT_PICKS_END/);
      if (picksMatch) {
        data.expertPicks = JSON.parse(picksMatch[1]);
      }
      
      // Extract statistics
      const statsMatch = content.match(/STATISTICS_START([\s\S]*?)STATISTICS_END/);
      if (statsMatch) {
        data.statistics = JSON.parse(statsMatch[1]);
      }
      
      return Object.keys(data).length > 0 ? data : null;
    } catch (error) {
      console.warn('Failed to extract sports data from content:', error);
      return null;
    }
  };

  // Update article content with sports data
  const updateArticleContent = (newSportsData) => {
    if (!article?.content || !onContentChange) return;

    let updatedContent = article.content;

    // Remove existing sports data blocks
    updatedContent = updatedContent.replace(/MATCH_INFO_START[\s\S]*?MATCH_INFO_END\n?/g, '');
    updatedContent = updatedContent.replace(/PREDICTIONS_START[\s\S]*?PREDICTIONS_END\n?/g, '');
    updatedContent = updatedContent.replace(/PLAYER_PROPS_START[\s\S]*?PLAYER_PROPS_END\n?/g, '');
    updatedContent = updatedContent.replace(/EXPERT_PICKS_START[\s\S]*?EXPERT_PICKS_END\n?/g, '');
    updatedContent = updatedContent.replace(/STATISTICS_START[\s\S]*?STATISTICS_END\n?/g, '');

    // Add new sports data blocks at the end
    const sportsDataBlocks = [
      `MATCH_INFO_START${JSON.stringify(newSportsData.matchInfo, null, 2)}MATCH_INFO_END`,
      `PREDICTIONS_START${JSON.stringify(newSportsData.predictions, null, 2)}PREDICTIONS_END`,
      `PLAYER_PROPS_START${JSON.stringify(newSportsData.playerProps, null, 2)}PLAYER_PROPS_END`,
      `EXPERT_PICKS_START${JSON.stringify(newSportsData.expertPicks, null, 2)}EXPERT_PICKS_END`,
      `STATISTICS_START${JSON.stringify(newSportsData.statistics, null, 2)}STATISTICS_END`
    ];

    updatedContent += '\n\n' + sportsDataBlocks.join('\n\n');

    onContentChange({
      ...article,
      content: updatedContent
    });
  };

  // Handle sports data changes
  const handleSportsDataChange = (field, value) => {
    const newSportsData = { ...sportsData, [field]: value };
    setSportsData(newSportsData);
    updateArticleContent(newSportsData);
  };

  // Add new item to arrays
  const addArrayItem = (arrayField, defaultItem) => {
    const newArray = [...sportsData[arrayField], defaultItem];
    handleSportsDataChange(arrayField, newArray);
  };

  // Remove item from arrays
  const removeArrayItem = (arrayField, index) => {
    const newArray = sportsData[arrayField].filter((_, i) => i !== index);
    handleSportsDataChange(arrayField, newArray);
  };

  // Update array item
  const updateArrayItem = (arrayField, index, updates) => {
    const newArray = sportsData[arrayField].map((item, i) => 
      i === index ? { ...item, ...updates } : item
    );
    handleSportsDataChange(arrayField, newArray);
  };

  // Tab configuration
  const tabs = [
    {
      id: 'match',
      name: 'Match Info',
      icon: TrophyIcon,
      color: 'text-green-400'
    },
    {
      id: 'predictions',
      name: 'Predictions',
      icon: ChartBarIcon,
      color: 'text-blue-400'
    },
    {
      id: 'props',
      name: 'Player Props',
      icon: PlayIcon,
      color: 'text-orange-400'
    },
    {
      id: 'picks',
      name: 'Expert Picks',
      icon: StarIcon,
      color: 'text-yellow-400'
    },
    {
      id: 'stats',
      name: 'Statistics',
      icon: ChartBarIcon,
      color: 'text-purple-400'
    }
  ];

  return (
    <div className="sports-info-editor-tabs">
      {/* Tab Navigation */}
      <div className="flex flex-wrap border-b border-gray-600 mb-6">
        {tabs.map((tab) => {
          const IconComponent = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.id
                  ? `border-casino-gold-400 ${tab.color}`
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
            >
              <IconComponent className="w-4 h-4" />
              <span>{tab.name}</span>
            </button>
          );
        })}
      </div>

      {/* Tab Content */}
      <div className="tab-content">
        
        {/* Match Info Tab */}
        {activeTab === 'match' && (
          <div className="space-y-6">
            <div className="flex items-center space-x-2 mb-4">
              <TrophyIcon className="w-5 h-5 text-green-400" />
              <h3 className="text-lg font-semibold text-white">Match Information</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Match Info */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Home Team</label>
                  <input
                    type="text"
                    value={sportsData.matchInfo.homeTeam}
                    onChange={(e) => handleSportsDataChange('matchInfo', {
                      ...sportsData.matchInfo,
                      homeTeam: e.target.value
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                    placeholder="e.g., Manchester United"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Away Team</label>
                  <input
                    type="text"
                    value={sportsData.matchInfo.awayTeam}
                    onChange={(e) => handleSportsDataChange('matchInfo', {
                      ...sportsData.matchInfo,
                      awayTeam: e.target.value
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                    placeholder="e.g., Liverpool"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">League</label>
                  <input
                    type="text"
                    value={sportsData.matchInfo.league}
                    onChange={(e) => handleSportsDataChange('matchInfo', {
                      ...sportsData.matchInfo,
                      league: e.target.value
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                    placeholder="e.g., Premier League"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Venue</label>
                  <input
                    type="text"
                    value={sportsData.matchInfo.venue}
                    onChange={(e) => handleSportsDataChange('matchInfo', {
                      ...sportsData.matchInfo,
                      venue: e.target.value
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                    placeholder="e.g., Old Trafford"
                  />
                </div>
              </div>

              {/* Date, Time, Odds */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Date</label>
                  <input
                    type="date"
                    value={sportsData.matchInfo.date?.split('T')[0] || ''}
                    onChange={(e) => handleSportsDataChange('matchInfo', {
                      ...sportsData.matchInfo,
                      date: new Date(e.target.value).toISOString()
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Time</label>
                  <input
                    type="time"
                    value={sportsData.matchInfo.time}
                    onChange={(e) => handleSportsDataChange('matchInfo', {
                      ...sportsData.matchInfo,
                      time: e.target.value
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                  />
                </div>

                {/* Odds Section */}
                <div className="space-y-3">
                  <h4 className="text-md font-semibold text-gray-300 flex items-center">
                    <FireIcon className="w-4 h-4 mr-2 text-red-400" />
                    Live Odds
                  </h4>
                  
                  {['home', 'draw', 'away'].map((type) => (
                    <div key={type} className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-400 mb-1 capitalize">{type} Odds</label>
                        <input
                          type="text"
                          value={sportsData.matchInfo.odds[type].odds}
                          onChange={(e) => handleSportsDataChange('matchInfo', {
                            ...sportsData.matchInfo,
                            odds: {
                              ...sportsData.matchInfo.odds,
                              [type]: {
                                ...sportsData.matchInfo.odds[type],
                                odds: e.target.value
                              }
                            }
                          })}
                          className="w-full px-2 py-1 text-sm bg-gray-700 border border-gray-600 rounded text-white focus:outline-none focus:ring-1 focus:ring-casino-gold-400"
                          placeholder="e.g., +150"
                        />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-400 mb-1">Percentage</label>
                        <input
                          type="number"
                          min="0"
                          max="100"
                          value={sportsData.matchInfo.odds[type].percentage}
                          onChange={(e) => handleSportsDataChange('matchInfo', {
                            ...sportsData.matchInfo,
                            odds: {
                              ...sportsData.matchInfo.odds,
                              [type]: {
                                ...sportsData.matchInfo.odds[type],
                                percentage: parseInt(e.target.value) || 0
                              }
                            }
                          })}
                          className="w-full px-2 py-1 text-sm bg-gray-700 border border-gray-600 rounded text-white focus:outline-none focus:ring-1 focus:ring-casino-gold-400"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Predictions Tab */}
        {activeTab === 'predictions' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <ChartBarIcon className="w-5 h-5 text-blue-400" />
                <h3 className="text-lg font-semibold text-white">Betting Predictions</h3>
              </div>
              <button
                onClick={() => addArrayItem('predictions', {
                  type: 'New Prediction',
                  prediction: '',
                  confidence: 70,
                  reasoning: ''
                })}
                className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <PlusIcon className="w-4 h-4" />
                <span>Add Prediction</span>
              </button>
            </div>

            <div className="space-y-4">
              {sportsData.predictions.map((prediction, index) => (
                <div key={index} className="bg-gray-800 rounded-lg p-4 border border-gray-700">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-md font-semibold text-white">Prediction #{index + 1}</h4>
                    <button
                      onClick={() => removeArrayItem('predictions', index)}
                      className="text-red-400 hover:text-red-300 transition-colors"
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Prediction Type</label>
                      <input
                        type="text"
                        value={prediction.type}
                        onChange={(e) => updateArrayItem('predictions', index, { type: e.target.value })}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                        placeholder="e.g., Match Result, Total Goals"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Prediction</label>
                      <input
                        type="text"
                        value={prediction.prediction}
                        onChange={(e) => updateArrayItem('predictions', index, { prediction: e.target.value })}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                        placeholder="e.g., Home Win, Over 2.5"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Confidence: {prediction.confidence}%
                      </label>
                      <input
                        type="range"
                        min="0"
                        max="100"
                        value={prediction.confidence}
                        onChange={(e) => updateArrayItem('predictions', index, { confidence: parseInt(e.target.value) })}
                        className="w-full"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Reasoning</label>
                      <textarea
                        value={prediction.reasoning}
                        onChange={(e) => updateArrayItem('predictions', index, { reasoning: e.target.value })}
                        rows="3"
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                        placeholder="Explain the reasoning behind this prediction..."
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Player Props Tab */}
        {activeTab === 'props' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <PlayIcon className="w-5 h-5 text-orange-400" />
                <h3 className="text-lg font-semibold text-white">Player Props</h3>
              </div>
              <button
                onClick={() => addArrayItem('playerProps', {
                  player: '',
                  prop: '',
                  line: '',
                  odds: '',
                  recommendation: 'YES',
                  confidence: 70
                })}
                className="flex items-center space-x-2 px-3 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors"
              >
                <PlusIcon className="w-4 h-4" />
                <span>Add Player Prop</span>
              </button>
            </div>

            <div className="space-y-4">
              {sportsData.playerProps.map((prop, index) => (
                <div key={index} className="bg-gray-800 rounded-lg p-4 border border-gray-700">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-md font-semibold text-white">Player Prop #{index + 1}</h4>
                    <button
                      onClick={() => removeArrayItem('playerProps', index)}
                      className="text-red-400 hover:text-red-300 transition-colors"
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Player Name</label>
                      <input
                        type="text"
                        value={prop.player}
                        onChange={(e) => updateArrayItem('playerProps', index, { player: e.target.value })}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                        placeholder="e.g., Marcus Rashford"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Prop Type</label>
                      <input
                        type="text"
                        value={prop.prop}
                        onChange={(e) => updateArrayItem('playerProps', index, { prop: e.target.value })}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                        placeholder="e.g., Anytime Goalscorer"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Line (if applicable)</label>
                      <input
                        type="text"
                        value={prop.line}
                        onChange={(e) => updateArrayItem('playerProps', index, { line: e.target.value })}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                        placeholder="e.g., 2.5"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Odds</label>
                      <input
                        type="text"
                        value={prop.odds}
                        onChange={(e) => updateArrayItem('playerProps', index, { odds: e.target.value })}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                        placeholder="e.g., +180"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Recommendation</label>
                      <select
                        value={prop.recommendation}
                        onChange={(e) => updateArrayItem('playerProps', index, { recommendation: e.target.value })}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                      >
                        <option value="YES">YES</option>
                        <option value="NO">NO</option>
                        <option value="OVER">OVER</option>
                        <option value="UNDER">UNDER</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Confidence: {prop.confidence}%
                      </label>
                      <input
                        type="range"
                        min="0"
                        max="100"
                        value={prop.confidence}
                        onChange={(e) => updateArrayItem('playerProps', index, { confidence: parseInt(e.target.value) })}
                        className="w-full"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Expert Picks Tab */}
        {activeTab === 'picks' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <StarIcon className="w-5 h-5 text-yellow-400" />
                <h3 className="text-lg font-semibold text-white">Expert Picks</h3>
              </div>
              <button
                onClick={() => addArrayItem('expertPicks', {
                  expert: '',
                  pick: '',
                  odds: '',
                  units: 1,
                  reasoning: ''
                })}
                className="flex items-center space-x-2 px-3 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors"
              >
                <PlusIcon className="w-4 h-4" />
                <span>Add Expert Pick</span>
              </button>
            </div>

            <div className="space-y-4">
              {sportsData.expertPicks.map((pick, index) => (
                <div key={index} className="bg-gray-800 rounded-lg p-4 border border-gray-700">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-md font-semibold text-white">Expert Pick #{index + 1}</h4>
                    <button
                      onClick={() => removeArrayItem('expertPicks', index)}
                      className="text-red-400 hover:text-red-300 transition-colors"
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Expert Name</label>
                      <input
                        type="text"
                        value={pick.expert}
                        onChange={(e) => updateArrayItem('expertPicks', index, { expert: e.target.value })}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                        placeholder="e.g., Sports Analyst"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Pick</label>
                      <input
                        type="text"
                        value={pick.pick}
                        onChange={(e) => updateArrayItem('expertPicks', index, { pick: e.target.value })}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                        placeholder="e.g., Home Team -1.5"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Odds</label>
                      <input
                        type="text"
                        value={pick.odds}
                        onChange={(e) => updateArrayItem('expertPicks', index, { odds: e.target.value })}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                        placeholder="e.g., +125"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Units</label>
                      <input
                        type="number"
                        min="1"
                        max="5"
                        value={pick.units}
                        onChange={(e) => updateArrayItem('expertPicks', index, { units: parseInt(e.target.value) || 1 })}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-300 mb-2">Reasoning</label>
                      <textarea
                        value={pick.reasoning}
                        onChange={(e) => updateArrayItem('expertPicks', index, { reasoning: e.target.value })}
                        rows="3"
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                        placeholder="Explain the reasoning behind this expert pick..."
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Statistics Tab */}
        {activeTab === 'stats' && (
          <div className="space-y-6">
            <div className="flex items-center space-x-2 mb-4">
              <ChartBarIcon className="w-5 h-5 text-purple-400" />
              <h3 className="text-lg font-semibold text-white">Team Statistics</h3>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Home Team Stats */}
              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <h4 className="text-lg font-semibold text-green-400 mb-4">Home Team Stats</h4>
                
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Wins</label>
                    <input
                      type="number"
                      min="0"
                      value={sportsData.statistics.homeStats.wins}
                      onChange={(e) => handleSportsDataChange('statistics', {
                        ...sportsData.statistics,
                        homeStats: {
                          ...sportsData.statistics.homeStats,
                          wins: parseInt(e.target.value) || 0
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Draws</label>
                    <input
                      type="number"
                      min="0"
                      value={sportsData.statistics.homeStats.draws}
                      onChange={(e) => handleSportsDataChange('statistics', {
                        ...sportsData.statistics,
                        homeStats: {
                          ...sportsData.statistics.homeStats,
                          draws: parseInt(e.target.value) || 0
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Losses</label>
                    <input
                      type="number"
                      min="0"
                      value={sportsData.statistics.homeStats.losses}
                      onChange={(e) => handleSportsDataChange('statistics', {
                        ...sportsData.statistics,
                        homeStats: {
                          ...sportsData.statistics.homeStats,
                          losses: parseInt(e.target.value) || 0
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Goals For</label>
                    <input
                      type="number"
                      min="0"
                      value={sportsData.statistics.homeStats.goalsFor}
                      onChange={(e) => handleSportsDataChange('statistics', {
                        ...sportsData.statistics,
                        homeStats: {
                          ...sportsData.statistics.homeStats,
                          goalsFor: parseInt(e.target.value) || 0
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                    />
                  </div>

                  <div className="col-span-2">
                    <label className="block text-sm font-medium text-gray-300 mb-2">Goals Against</label>
                    <input
                      type="number"
                      min="0"
                      value={sportsData.statistics.homeStats.goalsAgainst}
                      onChange={(e) => handleSportsDataChange('statistics', {
                        ...sportsData.statistics,
                        homeStats: {
                          ...sportsData.statistics.homeStats,
                          goalsAgainst: parseInt(e.target.value) || 0
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                    />
                  </div>
                </div>

                {/* Form */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Recent Form (Last 5)</label>
                  <div className="flex space-x-2">
                    {sportsData.statistics.homeStats.form.map((result, index) => (
                      <select
                        key={index}
                        value={result}
                        onChange={(e) => {
                          const newForm = [...sportsData.statistics.homeStats.form];
                          newForm[index] = e.target.value;
                          handleSportsDataChange('statistics', {
                            ...sportsData.statistics,
                            homeStats: {
                              ...sportsData.statistics.homeStats,
                              form: newForm
                            }
                          });
                        }}
                        className="px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-casino-gold-400"
                      >
                        <option value="W">W</option>
                        <option value="D">D</option>
                        <option value="L">L</option>
                      </select>
                    ))}
                  </div>
                </div>
              </div>

              {/* Away Team Stats */}
              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <h4 className="text-lg font-semibold text-red-400 mb-4">Away Team Stats</h4>
                
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Wins</label>
                    <input
                      type="number"
                      min="0"
                      value={sportsData.statistics.awayStats.wins}
                      onChange={(e) => handleSportsDataChange('statistics', {
                        ...sportsData.statistics,
                        awayStats: {
                          ...sportsData.statistics.awayStats,
                          wins: parseInt(e.target.value) || 0
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Draws</label>
                    <input
                      type="number"
                      min="0"
                      value={sportsData.statistics.awayStats.draws}
                      onChange={(e) => handleSportsDataChange('statistics', {
                        ...sportsData.statistics,
                        awayStats: {
                          ...sportsData.statistics.awayStats,
                          draws: parseInt(e.target.value) || 0
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Losses</label>
                    <input
                      type="number"
                      min="0"
                      value={sportsData.statistics.awayStats.losses}
                      onChange={(e) => handleSportsDataChange('statistics', {
                        ...sportsData.statistics,
                        awayStats: {
                          ...sportsData.statistics.awayStats,
                          losses: parseInt(e.target.value) || 0
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Goals For</label>
                    <input
                      type="number"
                      min="0"
                      value={sportsData.statistics.awayStats.goalsFor}
                      onChange={(e) => handleSportsDataChange('statistics', {
                        ...sportsData.statistics,
                        awayStats: {
                          ...sportsData.statistics.awayStats,
                          goalsFor: parseInt(e.target.value) || 0
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                    />
                  </div>

                  <div className="col-span-2">
                    <label className="block text-sm font-medium text-gray-300 mb-2">Goals Against</label>
                    <input
                      type="number"
                      min="0"
                      value={sportsData.statistics.awayStats.goalsAgainst}
                      onChange={(e) => handleSportsDataChange('statistics', {
                        ...sportsData.statistics,
                        awayStats: {
                          ...sportsData.statistics.awayStats,
                          goalsAgainst: parseInt(e.target.value) || 0
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                    />
                  </div>
                </div>

                {/* Form */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Recent Form (Last 5)</label>
                  <div className="flex space-x-2">
                    {sportsData.statistics.awayStats.form.map((result, index) => (
                      <select
                        key={index}
                        value={result}
                        onChange={(e) => {
                          const newForm = [...sportsData.statistics.awayStats.form];
                          newForm[index] = e.target.value;
                          handleSportsDataChange('statistics', {
                            ...sportsData.statistics,
                            awayStats: {
                              ...sportsData.statistics.awayStats,
                              form: newForm
                            }
                          });
                        }}
                        className="px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-casino-gold-400"
                      >
                        <option value="W">W</option>
                        <option value="D">D</option>
                        <option value="L">L</option>
                      </select>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Head to Head */}
            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h4 className="text-lg font-semibold text-blue-400 mb-4">Head to Head</h4>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Total Meetings</label>
                  <input
                    type="number"
                    min="0"
                    value={sportsData.statistics.headToHead.totalMeetings}
                    onChange={(e) => handleSportsDataChange('statistics', {
                      ...sportsData.statistics,
                      headToHead: {
                        ...sportsData.statistics.headToHead,
                        totalMeetings: parseInt(e.target.value) || 0
                      }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Home Wins</label>
                  <input
                    type="number"
                    min="0"
                    value={sportsData.statistics.headToHead.homeWins}
                    onChange={(e) => handleSportsDataChange('statistics', {
                      ...sportsData.statistics,
                      headToHead: {
                        ...sportsData.statistics.headToHead,
                        homeWins: parseInt(e.target.value) || 0
                      }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Draws</label>
                  <input
                    type="number"
                    min="0"
                    value={sportsData.statistics.headToHead.draws}
                    onChange={(e) => handleSportsDataChange('statistics', {
                      ...sportsData.statistics,
                      headToHead: {
                        ...sportsData.statistics.headToHead,
                        draws: parseInt(e.target.value) || 0
                      }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Away Wins</label>
                  <input
                    type="number"
                    min="0"
                    value={sportsData.statistics.headToHead.awayWins}
                    onChange={(e) => handleSportsDataChange('statistics', {
                      ...sportsData.statistics,
                      headToHead: {
                        ...sportsData.statistics.headToHead,
                        awayWins: parseInt(e.target.value) || 0
                      }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-casino-gold-400"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SportsInfoEditorTabs;