import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { API_CONFIG } from '../../config/api';

const AuthTest = () => {
  const { user, token, loading, isAuthenticated } = useAuth();

  const testAPI = async () => {
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}/api/auth/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
      } else {
        const errorData = await response.text();
      }
    } catch (error) {
      console.error('API test error:', error);
    }
  };

  if (loading) {
    return <div>Loading auth state...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold mb-8">认证状态测试</h1>
        
        <div className="bg-white rounded-lg shadow p-6 space-y-4">
          <div>
            <strong>Loading:</strong> {loading ? 'true' : 'false'}
          </div>
          <div>
            <strong>Is Authenticated:</strong> {isAuthenticated ? 'true' : 'false'}
          </div>
          <div>
            <strong>Token exists:</strong> {token ? 'yes' : 'no'}
          </div>
          <div>
            <strong>Token (first 50 chars):</strong> {token ? token.substring(0, 50) + '...' : 'none'}
          </div>
          <div>
            <strong>User:</strong> {user ? JSON.stringify(user, null, 2) : 'none'}
          </div>
          <div>
            <strong>API Base URL:</strong> {API_CONFIG.BASE_URL}
          </div>
          
          <button
            onClick={testAPI}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            测试API调用
          </button>
        </div>
      </div>
    </div>
  );
};

export default AuthTest;