import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { CONTENT_TYPE_CONFIG } from '../../utils/contentTypeUtils';
import { API_CONFIG } from '../../config/api';
import GameInfoEditorTabs from '../admin/GameInfoEditorTabs';
import CasinoInfoEditorTabs from '../admin/CasinoInfoEditorTabs';
import SportsInfoEditorTabs from '../admin/SportsInfoEditorTabs';
import ArticleAffiliateManager from '../affiliate/ArticleAffiliateManager';
import { extractGameInfo } from '../../utils/gameInfoParser';

const ArticleEditor = ({ article, onSave, onCancel }) => {
  const { token } = useAuth();
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    excerpt: '',
    content_type: 'strategy_article',
    status: 'draft',
    featured_image: '',
    categories: [],
    tags: [],
    game_info: null,
    casino_info: null,
    sports_info: null
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [gameInfoValid, setGameInfoValid] = useState(true);
  const [casinoInfoValid, setCasinoInfoValid] = useState(true);
  const [sportsInfoValid, setSportsInfoValid] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [uploadError, setUploadError] = useState('');
  const [activeTab, setActiveTab] = useState('basic');
  const [imageLoadError, setImageLoadError] = useState(false);

  // Define tabs for the editor
  const tabs = [
    { id: 'basic', label: 'Basic Info', icon: '📝' },
    { id: 'visual', label: 'Visual Content', icon: '🖼️' },
    { id: 'review', label: 'Review Analysis', icon: '⭐' },
    { id: 'related', label: 'Related Games', icon: '🔗' }
  ];

  useEffect(() => {
    // Prevent background scrolling when modal is open
    document.body.style.overflow = 'hidden';
    
    return () => {
      // Restore scrolling when modal closes
      document.body.style.overflow = 'unset';
    };
  }, []);

  useEffect(() => {
    if (article) {
      // Try to extract game info from content if not stored
      let gameInfo = null;
      if (article.game_info) {
        try {
          gameInfo = typeof article.game_info === 'string' 
            ? JSON.parse(article.game_info) 
            : article.game_info;
        } catch (error) {
          console.error('Error parsing stored game_info:', error);
        }
      }
      
      // If no stored game info, try to extract from content
      if (!gameInfo && article.content) {
        gameInfo = extractGameInfo(article.content);
      }

      // Try to extract casino info if it's a casino review
      let casinoInfo = null;
      if (article.casino_info) {
        try {
          casinoInfo = typeof article.casino_info === 'string' 
            ? JSON.parse(article.casino_info) 
            : article.casino_info;
        } catch (error) {
          console.error('Error parsing stored casino_info:', error);
        }
      }

      // Try to extract sports info if it's a sports betting article
      let sportsInfo = null;
      if (article.sports_info) {
        try {
          sportsInfo = typeof article.sports_info === 'string' 
            ? JSON.parse(article.sports_info) 
            : article.sports_info;
        } catch (error) {
          console.error('Error parsing stored sports_info:', error);
        }
      }

      setFormData({
        title: article.title || '',
        content: article.content || '',
        excerpt: article.excerpt || '',
        content_type: article.content_type || 'strategy_article',
        status: article.status || 'draft',
        featured_image: article.featured_image || '',
        categories: article.categories || [],
        tags: article.tags || [],
        game_info: gameInfo,
        casino_info: casinoInfo,
        sports_info: sportsInfo
      });
    }
  }, [article]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const url = `${API_CONFIG.BASE_URL}/api/articles/${article.id}`;
      const method = 'PUT';

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Save failed');
      }

      const result = await response.json();
      onSave(result.article);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Reset image load error when featured_image changes
    if (field === 'featured_image') {
      setImageLoadError(false);
    }
  };

  const handleImageUpload = async (file) => {
    setUploading(true);
    setUploadError('');

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch(`${API_CONFIG.BASE_URL}/api/upload/image`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const result = await response.json();
      
      // Update the featured_image field with the uploaded file URL
      // Check if the URL is relative and needs the base URL
      const imageUrl = result.fileUrl.startsWith('http') 
        ? result.fileUrl 
        : `${API_CONFIG.BASE_URL}${result.fileUrl}`;
      handleChange('featured_image', imageUrl);
      
    } catch (err) {
      setUploadError(err.message);
    } finally {
      setUploading(false);
    }
  };

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        setUploadError('Only image files are allowed (JPEG, PNG, GIF, WebP)');
        return;
      }

      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        setUploadError('File too large. Maximum size is 5MB.');
        return;
      }

      handleImageUpload(file);
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-4 mx-auto p-5 border w-full max-w-6xl max-h-[95vh] overflow-y-auto shadow-lg rounded-md bg-white">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium text-gray-900">
            Edit Article
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div className="flex">
              <svg className="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              <p className="ml-3 text-sm text-red-800">{error}</p>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6 flex flex-col h-full">
          {/* Tab Navigation */}
          <div className="border-b border-gray-200 flex-shrink-0">
            <nav className="-mb-px flex space-x-8" aria-label="Tabs">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  type="button"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setActiveTab(tab.id);
                  }}
                  className={`
                    whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm
                    ${activeTab === tab.id
                      ? 'border-purple-500 text-purple-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="flex-1 overflow-y-auto">
            {/* Basic Info Tab */}
            {activeTab === 'basic' && (
              <div className="space-y-6">
                {/* Title */}
                <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Article Title *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleChange('title', e.target.value)}
              required
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
              placeholder="Enter article title"
            />
          </div>

          {/* Content type and status */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Content Type
              </label>
              <select
                value={formData.content_type}
                onChange={(e) => handleChange('content_type', e.target.value)}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
              >
                {Object.entries(CONTENT_TYPE_CONFIG).map(([key, config]) => (
                  <option key={key} value={key}>{config.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleChange('status', e.target.value)}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
              >
                <option value="draft">Draft</option>
                <option value="published">Published</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Featured Image
              </label>
              <div className="space-y-3">
                {/* Upload Button */}
                <div className="flex items-center space-x-3">
                  <label className="bg-white border border-gray-300 rounded-md px-3 py-2 text-sm cursor-pointer hover:bg-gray-50 focus-within:outline-none focus-within:ring-2 focus-within:ring-purple-500">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileSelect}
                      className="sr-only"
                      disabled={uploading}
                    />
                    {uploading ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-purple-600" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Uploading...
                      </span>
                    ) : (
                      <span className="flex items-center">
                        <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                        Upload Image
                      </span>
                    )}
                  </label>
                  <span className="text-sm text-gray-500">or enter URL below</span>
                </div>
                
                {/* Upload Error */}
                {uploadError && (
                  <div className="text-red-600 text-sm bg-red-50 border border-red-200 rounded-md p-2">
                    {uploadError}
                  </div>
                )}
                
                {/* URL Input */}
                <input
                  type="url"
                  value={formData.featured_image}
                  onChange={(e) => handleChange('featured_image', e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                  placeholder="https://example.com/image.jpg"
                />
                
                {/* Image Preview */}
                {formData.featured_image && (
                  <div className="mt-2">
                    <p className="text-sm text-gray-600 mb-2">Preview:</p>
                    <div className="relative">
                      {!imageLoadError ? (
                        <img
                          src={formData.featured_image}
                          alt="Featured image preview"
                          className="max-w-xs max-h-32 object-cover rounded-md border"
                          onError={() => {
                            console.error('Image failed to load:', formData.featured_image);
                            setImageLoadError(true);
                          }}
                          onLoad={() => {
                            setImageLoadError(false);
                          }}
                        />
                      ) : (
                        <div className="text-red-600 text-sm bg-red-50 border border-red-200 rounded-md p-3">
                          <div className="flex items-center space-x-2">
                            <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>Failed to load image. Please check the URL or try uploading again.</span>
                          </div>
                          <div className="mt-2 text-xs text-gray-600">
                            URL: {formData.featured_image}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Excerpt */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Article Excerpt
            </label>
            <textarea
              value={formData.excerpt}
              onChange={(e) => handleChange('excerpt', e.target.value)}
              rows={3}
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
              placeholder="Enter article excerpt (leave blank to auto-extract from content)"
            />
          </div>

          {/* Content */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Article Content *
            </label>
            <textarea
              value={formData.content}
              onChange={(e) => handleChange('content', e.target.value)}
              required
              rows={20}
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 font-mono text-sm"
              placeholder="Enter article content (Markdown format supported)"
            />
            <p className="mt-1 text-sm text-gray-500">
              Supports Markdown format. HTML tags will be automatically converted.
            </p>
          </div>

                </div>
              )}

            {/* Game Info tabs - only shown for game guide type */}
            {formData.content_type === 'game_guide' && activeTab !== 'basic' && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                <p className="text-blue-800 text-sm">
                  💡 Tip: AI will automatically generate game information, you only need to review and fine-tune. If information is incomplete, you can supplement it here.
                </p>
              </div>
            )}

            {formData.content_type === 'game_guide' && (
              <GameInfoEditorTabs
                gameInfo={formData.game_info}
                onChange={(gameInfo) => handleChange('game_info', gameInfo)}
                onValidationChange={setGameInfoValid}
                activeTab={activeTab}
              />
            )}

            {/* Casino Info tabs - only shown for casino review type */}
            {formData.content_type === 'casino_review' && activeTab !== 'basic' && (
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-4">
                <p className="text-purple-800 text-sm">
                  🎰 Tip: Complete the casino information sections to enhance your review. Ratings, pros/cons, and key details will be automatically integrated into the review layout.
                </p>
              </div>
            )}

            {formData.content_type === 'casino_review' && (
              <CasinoInfoEditorTabs
                casinoInfo={formData.casino_info}
                onChange={(casinoInfo) => handleChange('casino_info', casinoInfo)}
                onValidationChange={setCasinoInfoValid}
                activeTab={activeTab}
              />
            )}

            {/* Sports Info tabs - only shown for sports betting type */}
            {formData.content_type === 'sports_betting' && activeTab !== 'basic' && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                <p className="text-green-800 text-sm">
                  ⚽ Tip: Complete the sports betting information sections to enhance your article. Match info, predictions, player props, and expert picks will be automatically integrated into the betting layout.
                </p>
              </div>
            )}

            {formData.content_type === 'sports_betting' && (
              <SportsInfoEditorTabs
                article={formData}
                onContentChange={(updatedArticle) => {
                  setFormData(updatedArticle);
                  if (updatedArticle.sports_info) {
                    handleChange('sports_info', updatedArticle.sports_info);
                  }
                }}
              />
            )}

          </div>

          {/* Buttons */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || (formData.content_type === 'game_guide' && !gameInfoValid) || (formData.content_type === 'casino_review' && !casinoInfoValid) || (formData.content_type === 'sports_betting' && !sportsInfoValid)}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"
            >
              {loading ? 'Saving...' : 'Save'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ArticleEditor;