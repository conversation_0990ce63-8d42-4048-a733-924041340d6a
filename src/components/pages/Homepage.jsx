import React from 'react';
import { Link } from 'react-router-dom';
import {
  SparklesIcon,
  DocumentTextIcon,
  ClockIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  StarIcon,
  UserGroupIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import <PERSON><PERSON><PERSON><PERSON> from '../WriterJLogo';

const Homepage = () => {
  const features = [
    {
      icon: <SparklesIcon className="w-8 h-8 text-casino-gold-500" />,
      title: "AI-Powered Writing",
      description: "Generate high-quality articles with advanced AI technology that understands your needs"
    },
    {
      icon: <DocumentTextIcon className="w-8 h-8 text-casino-gold-500" />,
      title: "Professional Content",
      description: "Create well-structured, engaging articles that resonate with your audience"
    },
    {
      icon: <ClockIcon className="w-8 h-8 text-casino-gold-500" />,
      title: "Time Efficient",
      description: "Save hours of writing time with our streamlined content generation process"
    },
    {
      icon: <CheckCircleIcon className="w-8 h-8 text-casino-gold-500" />,
      title: "Quality Assured",
      description: "Every article is optimized for readability, SEO, and engagement"
    }
  ];

  const stats = [
    { label: "Articles Generated", value: "10,000+", icon: <DocumentTextIcon className="w-6 h-6" /> },
    { label: "Happy Users", value: "1,500+", icon: <UserGroupIcon className="w-6 h-6" /> },
    { label: "Hours Saved", value: "50,000+", icon: <ClockIcon className="w-6 h-6" /> },
    { label: "Success Rate", value: "98%", icon: <ChartBarIcon className="w-6 h-6" /> }
  ];

  return (
    <div className="min-h-screen casino-bg">
      {/* Navigation */}
      <nav className="bg-gradient-to-r from-casino-dark-800/95 via-casino-dark-700/95 to-casino-dark-800/95 backdrop-blur-xl border-b border-casino-gold-500/30 sticky top-0 z-50 shadow-lg shadow-casino-gold-500/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <WriterJLogo className="w-10 h-10" showText={true} textClassName="text-2xl font-bold text-casino-gold-400" />
            
            <div className="hidden md:flex items-center space-x-8">
              <Link to="/features" className="text-casino-gold-200 hover:text-casino-gold-400 transition-colors font-medium">Features</Link>
              <Link to="/pricing" className="text-casino-gold-200 hover:text-casino-gold-400 transition-colors font-medium">Pricing</Link>
              <Link to="/blog" className="text-casino-gold-200 hover:text-casino-gold-400 transition-colors font-medium">Blog</Link>
            </div>

            <div className="flex items-center space-x-4">
              <Link
                to="/login"
                className="text-casino-gold-200 hover:text-casino-gold-400 transition-colors font-medium"
              >
                Sign In
              </Link>
              <Link
                to="/register"
                className="casino-button text-white px-6 py-3 rounded-lg font-medium"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <div className="mb-8">
            <div className="inline-flex items-center space-x-2 bg-casino-gold-500/20 text-casino-gold-400 px-6 py-3 rounded-full text-sm font-medium mb-6 border border-casino-gold-500/30">
              <SparklesIcon className="w-5 h-5" />
              <span>AI-Powered Content Generation</span>
            </div>
            <h1 className="text-5xl md:text-7xl font-bold text-casino-gold-400 mb-6 font-casino" style={{textShadow: '0 0 2px #f59e0b, 1px 1px 2px rgba(0, 0, 0, 0.5)'}}>
              Create Amazing{' '}
              <span className="bg-gradient-to-r from-casino-gold-400 to-casino-red-400 bg-clip-text text-transparent">
                AI Articles
              </span>
              <br />
              <span className="text-casino-red-400">In Minutes</span>
            </h1>
            <p className="text-xl text-casino-gold-200 mb-8 max-w-3xl mx-auto" style={{textShadow: '1px 1px 2px rgba(0, 0, 0, 0.5)'}}>
              Transform your ideas into high-quality, engaging articles with our advanced AI writing assistant.
              Perfect for bloggers, marketers, and content creators.
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12">
            <Link
              to="/register"
              className="casino-button inline-flex items-center justify-center px-10 py-5 text-white text-lg font-semibold rounded-xl chip-shadow animate-pulse-gold"
            >
              Start Writing for Free
              <ArrowRightIcon className="w-5 h-5 ml-2" />
            </Link>
            <Link
              to="/generator"
              className="inline-flex items-center justify-center px-10 py-5 casino-card text-casino-gold-400 text-lg font-semibold rounded-xl transition-all duration-200 border-2 border-casino-gold-500/50 hover:border-casino-gold-400 chip-shadow"
            >
              Try Demo
            </Link>
          </div>

          {/* Demo Preview */}
          <div className="relative max-w-4xl mx-auto">
            <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-200">
              <div className="aspect-video bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl flex items-center justify-center relative overflow-hidden">
                <div className="text-center">
                  <div className="flex justify-center space-x-4 mb-6">
                    <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white text-2xl font-bold shadow-lg">
                      <SparklesIcon className="w-8 h-8" />
                    </div>
                    <div className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center text-white text-2xl font-bold shadow-lg">
                      <DocumentTextIcon className="w-8 h-8" />
                    </div>
                    <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center text-white text-2xl font-bold shadow-lg">
                      <CheckCircleIcon className="w-8 h-8" />
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">AI Article Generator</h3>
                  <p className="text-gray-600">Transform your ideas into professional articles instantly</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="flex justify-center mb-4">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center text-blue-600">
                    {stat.icon}
                  </div>
                </div>
                <div className="text-4xl font-bold text-gray-900 mb-2">{stat.value}</div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Powerful AI Features</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Experience the power of AI-driven content creation with our advanced features
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white p-8 rounded-xl border border-gray-200 hover:border-blue-300 transition-all duration-300 group hover:transform hover:-translate-y-2 shadow-lg">
                <div className="flex justify-center mb-6">
                  <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 text-3xl">
                    {feature.icon}
                  </div>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4 text-center">{feature.title}</h3>
                <p className="text-gray-600 text-center leading-relaxed">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-blue-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-5xl font-bold text-white mb-6">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-blue-100 mb-10">
            Join thousands of writers who are already creating amazing content with our AI-powered platform!
          </p>
          <Link
            to="/register"
            className="inline-flex items-center justify-center px-12 py-6 bg-white text-blue-600 text-xl font-bold rounded-xl transition-all duration-200 transform hover:-translate-y-1 shadow-lg"
          >
            Start Writing for Free
            <ArrowRightIcon className="w-6 h-6 ml-3" />
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-gray-300 py-12 border-t border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h3 className="text-3xl font-bold mb-4 text-white">AI Article Generator</h3>
            <p className="text-gray-400 mb-6">
              Empowering writers with intelligent content creation tools
            </p>
            <div className="flex justify-center space-x-8">
              <Link to="/features" className="text-gray-300 hover:text-white transition-colors font-medium">
                Features
              </Link>
              <Link to="/pricing" className="text-gray-300 hover:text-white transition-colors font-medium">
                Pricing
              </Link>
              <Link to="/login" className="text-gray-300 hover:text-white transition-colors font-medium">
                Sign In
              </Link>
            </div>
            <div className="mt-8 pt-8 border-t border-gray-700">
              <p className="text-gray-400">
                © 2024 AI Article Generator. All rights reserved.
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Homepage;
