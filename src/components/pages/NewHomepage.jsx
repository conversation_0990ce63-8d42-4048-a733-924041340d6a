import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  TrophyIcon,
  FireIcon,
  ClockIcon,
  UserIcon,
  ArrowRightIcon,
  EyeIcon,
  BookmarkIcon
} from '@heroicons/react/24/outline';
import MainNavigation from '../layout/MainNavigation';
import VideoBackground from '../VideoBackground';
import { API_CONFIG } from '../../config/api';
import { formatDate, calculateReadingTime } from '../../utils/articleUtils';
import { getContentTypeImage, getContentTypeCardBackground, getThemedImage } from '../../utils/imageUtils';
import { useCurrentLanguage, getLocaleForLanguage } from '../../utils/languageUtils';

const NewHomepage = () => {
  const { t } = useTranslation();
  const currentLanguage = useCurrentLanguage();
  const [featuredArticles, setFeaturedArticles] = useState([]);
  const [latestArticles, setLatestArticles] = useState([]);
  const [contentTypes, setContentTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isLanguageChange, setIsLanguageChange] = useState(false);
  const [previousLanguage, setPreviousLanguage] = useState(currentLanguage);
  const [stats, setStats] = useState({
    totalArticles: 0,
    totalViews: 0,
    totalCategories: 0
  });
  const [heroImage, setHeroImage] = useState('');

  useEffect(() => {
    // Check if this is a language change vs initial load
    const isLanguageSwitch = previousLanguage && previousLanguage !== currentLanguage;
    
    if (isLanguageSwitch) {
      // This is a language change, show minimal loading
      setIsLanguageChange(true);
    } else {
      // This is initial load - only set loading if we have no data
      setIsLanguageChange(false);
      const hasNoData = featuredArticles.length === 0 && latestArticles.length === 0;
      if (hasNoData) {
        setLoading(true);
      }
    }

    // Update previous language
    setPreviousLanguage(currentLanguage);

    // Reduced debounce time for smoother experience
    const timeoutId = setTimeout(() => {
      fetchData();
    }, isLanguageSwitch ? 50 : 100); // Much faster for better UX
    
    return () => clearTimeout(timeoutId);
  }, [currentLanguage]); // Only currentLanguage as dependency

  const fetchData = async () => {
    // Capture isLanguageChange at the start since it might change during execution
    const wasLanguageChange = isLanguageChange;
    
    try {
      // For language changes, don't show loading screen at all
      // For initial loads, still show loading screen
      const shouldShowLoading = !wasLanguageChange && (featuredArticles.length === 0 && latestArticles.length === 0);
      
      if (shouldShowLoading) {
        setLoading(true);
      }
      
      await Promise.all([
        fetchFeaturedArticles(),
        fetchLatestArticles(),
        fetchContentTypes(),
        fetchStats(),
        // Skip hero image reload for language changes
        !wasLanguageChange ? fetchHeroImage() : Promise.resolve()
      ]);
    } catch (error) {
      console.error('Error fetching homepage data:', error);
    } finally {
      setLoading(false);
      setIsLanguageChange(false);
    }
  };

  const fetchHeroImage = async () => {
    try {
      const image = await getThemedImage('main');
      setHeroImage(image);
    } catch (error) {
      console.warn('Failed to load hero image:', error);
      // Use fallback - will be handled by getThemedImage
    }
  };

  const fetchFeaturedArticles = async () => {
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}/api/public/articles?limit=6&sortBy=published_at&language=${currentLanguage}`);
      if (response.ok) {
        const data = await response.json();
        setFeaturedArticles(data.articles.slice(0, 6));
      } else if (response.status === 429) {
        console.warn('Rate limited for featured articles. Using empty state.');
        setFeaturedArticles([]);
      } else {
        setFeaturedArticles([]);
      }
    } catch (error) {
      console.error('Error fetching featured articles:', error);
      setFeaturedArticles([]);
    }
  };

  const fetchLatestArticles = async () => {
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}/api/public/articles?limit=8&language=${currentLanguage}`);
      if (response.ok) {
        const data = await response.json();
        setLatestArticles(data.articles);
      } else if (response.status === 429) {
        console.warn('Rate limited for latest articles. Using empty state.');
        setLatestArticles([]);
      } else {
        setLatestArticles([]);
      }
    } catch (error) {
      console.error('Error fetching latest articles:', error);
      setLatestArticles([]);
    }
  };

  const fetchContentTypes = async () => {
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}/api/public/content-types?language=${currentLanguage}`);
      if (response.ok) {
        const data = await response.json();
        setContentTypes(data.contentTypes.slice(0, 6));
      } else if (response.status === 429) {
        console.warn('Rate limited for content types. Using empty state.');
        setContentTypes([]);
      } else {
        setContentTypes([]);
      }
    } catch (error) {
      console.error('Error fetching content types:', error);
      setContentTypes([]);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}/api/public/content-types?language=${currentLanguage}`);
      if (response.ok) {
        const data = await response.json();
        const totalArticles = data.contentTypes.reduce((sum, type) => sum + type.article_count, 0);
        setStats({
          totalArticles,
          totalViews: Math.floor(totalArticles * 1500), // Mock views
          totalCategories: data.contentTypes.length
        });
      } else if (response.status === 429) {
        console.warn('Rate limited for stats. Using default values.');
        setStats({
          totalArticles: 50,
          totalViews: 75000,
          totalCategories: 8
        });
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
      setStats({
        totalArticles: 50,
        totalViews: 75000,
        totalCategories: 8
      });
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <MainNavigation />
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-400 mx-auto"></div>
            <p className="mt-4 text-purple-200">{t('homepage.sections.loadingContent')}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-indigo-900">
      <MainNavigation />
      
      {/* Language change overlay - subtle loading indicator */}
      {isLanguageChange && (
        <div className="fixed top-0 left-0 right-0 z-50 h-1 bg-gradient-to-r from-blue-600 to-cyan-600 opacity-70">
          <div className="h-full bg-white/30 animate-pulse"></div>
        </div>
      )}

      {/* Hero Section */}
      <div className="relative h-[85vh] sm:h-[80vh] md:h-[75vh] lg:h-[70vh] min-h-[680px] sm:min-h-[700px] md:min-h-[650px] lg:min-h-[600px] max-h-[1000px] sm:max-h-[950px] md:max-h-[900px] lg:max-h-[850px] xl:max-h-[800px] border-b border-blue-500/30 overflow-hidden z-10">
        {/* Video Background */}
        <VideoBackground
          posterImage="/777-poster.jpg"
          fallbackImage={heroImage || 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=1200&h=600&fit=crop&crop=center'}
          overlay={true}
          overlayOpacity={0.8}
        />
        
        {/* Hero Content - Centered with dynamic spacing */}
        <div className="absolute inset-0 z-10 flex items-center justify-center">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center mb-[15vh] sm:mb-[18vh] md:mb-[20vh] lg:mb-[22vh] xl:mb-[24vh] 2xl:mb-[26vh]">
            <div className="inline-flex items-center space-x-2 bg-cyan-500/20 text-cyan-300 px-4 sm:px-6 py-2 sm:py-3 rounded-full text-xs sm:text-sm font-medium mb-4 sm:mb-6 border border-cyan-500/30">
              <FireIcon className="w-4 sm:w-5 h-4 sm:h-5" />
              <span>{t('homepage.hero.badge')}</span>
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-3 sm:mb-4 bg-gradient-to-r from-blue-400 via-cyan-400 to-teal-400 bg-clip-text text-transparent px-2">
              {t('homepage.hero.title')}
            </h1>
            <p className="text-base sm:text-lg md:text-xl text-slate-300 max-w-3xl mx-auto leading-relaxed px-4">
              {t('homepage.hero.subtitle')}
            </p>
          </div>
        </div>

        {/* Content Categories Glass Panel - Overlaid on Video */}
        <div className="absolute bottom-4 sm:bottom-6 md:bottom-8 left-2 sm:left-4 right-2 sm:right-4 z-20">
          <div className="max-w-7xl mx-auto">
            {/* Frosted Glass Container with compact sizing for small screens */}
            <div className="backdrop-blur-xl bg-white/10 border border-white/20 rounded-xl sm:rounded-2xl p-2 sm:p-3 md:p-4 lg:p-5 xl:p-6 shadow-2xl">
              <div className="text-center mb-2 sm:mb-3 md:mb-4 lg:mb-5">
                <h2 className="text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl font-bold text-white mb-0.5 sm:mb-1 lg:mb-2">{t('homepage.sections.contentCategories')}</h2>
                <p className="text-white/80 max-w-4xl mx-auto text-xs sm:text-sm md:text-base hidden md:block">
                  {t('homepage.sections.categoriesSubtitle')}
                </p>
              </div>

              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-2 sm:gap-3 md:gap-4">
                {contentTypes.slice(0, 6).map((type) => (
                  <Link
                    key={type.content_type}
                    to={`/category/${type.content_type}`}
                    className="group relative overflow-hidden rounded-lg sm:rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105 p-1.5 sm:p-2 md:p-3 lg:p-3.5 xl:p-4"
                  >
                    <div className="text-center">
                      <div className={`w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-lg bg-gradient-to-br ${type.color} flex items-center justify-center text-sm sm:text-lg md:text-xl shadow-lg mx-auto mb-1 sm:mb-2`}>
                        {type.icon}
                      </div>
                      <h3 className="text-xs sm:text-xs md:text-sm font-semibold text-white group-hover:text-purple-200 transition-colors mb-0.5 sm:mb-1 leading-tight">
                        {t(`contentTypes.${type.content_type}`)}
                      </h3>
                      <p className="text-xs text-white/60">
                        {type.article_count} {type.article_count === 1 ? t('common.article') : t('common.articles')}
                      </p>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Featured Articles Section */}
      <div className={`max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 transition-opacity duration-300 ${isLanguageChange ? 'opacity-70' : 'opacity-100'}`}>
        <div className="mb-16">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-3xl font-bold text-white mb-2">{t('homepage.sections.featuredArticles')}</h2>
              <p className="text-slate-400">{t('homepage.sections.featuredSubtitle')}</p>
            </div>
            <Link
              to="/articles"
              className="text-cyan-400 hover:text-cyan-300 font-medium flex items-center space-x-2"
            >
              <span>{t('homepage.sections.viewAll')}</span>
              <ArrowRightIcon className="w-4 h-4" />
            </Link>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredArticles.map((article, index) => {
              const articleImage = article.featured_image || getContentTypeImage(article.content_type, 'thumbnail');
              return (
                <Link
                  key={article.id}
                  to={`/articles/${article.slug}`}
                  className="group glass-card rounded-xl overflow-hidden glass-interactive"
                >
                  {/* Article Image */}
                  <div className="relative h-48 overflow-hidden">
                    <img
                      src={articleImage}
                      alt={article.title}
                      className="w-full h-full object-cover article-image"
                      onError={(e) => {
                        e.target.src = getContentTypeImage(article.content_type, 'thumbnail');
                      }}
                      loading="lazy"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                    

                    {/* Content Type Badge */}
                    <div className="absolute top-4 right-4 z-10">
                      <div className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full text-xs font-medium glass-light text-white shadow-lg border border-white/20`}>
                        <span>{article.contentTypeInfo.icon}</span>
                        <span>{article.contentTypeInfo.label}</span>
                      </div>
                    </div>
                  </div>

                  <div className="p-6">
                    <h3 className="text-xl font-bold text-white mb-3 line-clamp-2 group-hover:text-cyan-300 transition-colors">
                      {article.title}
                    </h3>

                    <p className="text-slate-300 text-sm mb-6 line-clamp-3 leading-relaxed">
                      {article.excerpt}
                    </p>

                    <div className="flex items-center justify-between text-xs text-slate-400 border-t border-slate-500/20 pt-4">
                      <div className="flex items-center space-x-2">
                        <UserIcon className="w-4 h-4" />
                        <span>{article.author}</span>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-1">
                          <ClockIcon className="w-4 h-4" />
                          <span>{t('article.readingTime', { time: calculateReadingTime(article.content) })}</span>
                        </div>
                        <span>{formatDate(article.published_at, getLocaleForLanguage(currentLanguage))}</span>
                      </div>
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>
        </div>

        {/* Latest Articles Section */}
        <div>
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-3xl font-bold text-white mb-2">{t('homepage.sections.latestArticles')}</h2>
              <p className="text-slate-400">{t('homepage.sections.latestSubtitle')}</p>
            </div>
            <Link
              to="/articles"
              className="text-cyan-400 hover:text-cyan-300 font-medium flex items-center space-x-2"
            >
              <span>{t('homepage.sections.viewAll')}</span>
              <ArrowRightIcon className="w-4 h-4" />
            </Link>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {latestArticles.map((article) => {
              const articleImage = article.featured_image || getContentTypeImage(article.content_type, 'thumbnail');
              return (
                <Link
                  key={article.id}
                  to={`/articles/${article.slug}`}
                  className="group glass-card rounded-xl overflow-hidden glass-interactive"
                >
                  {/* Small Article Image */}
                  <div className="relative h-32 overflow-hidden">
                    <img
                      src={articleImage}
                      alt={article.title}
                      className="w-full h-full object-cover article-image"
                      onError={(e) => {
                        e.target.src = getContentTypeImage(article.content_type, 'thumbnail');
                      }}
                      loading="lazy"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                    
                    {/* Content Type Badge */}
                    <div className="absolute top-2 left-2 z-10">
                      <div className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r ${article.contentTypeInfo.color} text-white shadow-lg backdrop-blur-sm`}>
                        <span className="text-xs">{article.contentTypeInfo.icon}</span>
                        <span>{article.contentTypeInfo.label}</span>
                      </div>
                    </div>
                  </div>

                  <div className="p-4">
                    <h3 className="text-lg font-semibold text-white mb-2 line-clamp-2 group-hover:text-cyan-300 transition-colors">
                      {article.title}
                    </h3>

                    <p className="text-slate-300 text-sm mb-4 line-clamp-2">
                      {article.excerpt}
                    </p>

                    <div className="flex items-center justify-between text-xs text-slate-400">
                      <div className="flex items-center space-x-1">
                        <ClockIcon className="w-3 h-3" />
                        <span>{t('article.readingTime', { time: calculateReadingTime(article.content) })}</span>
                      </div>
                      <span>{formatDate(article.published_at, getLocaleForLanguage(currentLanguage))}</span>
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-slate-900/50 border-t border-blue-500/30 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-slate-400">
            <p>&copy; 2025 Writer 777. Professional Gaming Content Platform.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default NewHomepage;