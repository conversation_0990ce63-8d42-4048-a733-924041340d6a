import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import {
  PlusIcon,
  DocumentTextIcon,
  ClockIcon,
  CheckCircleIcon,
  ArrowLeftIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { API_CONFIG } from '../config/api';

const CategoryView = () => {
  const { contentType } = useParams();
  const { t } = useTranslation();
  const { authenticatedFetch } = useAuth();
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [pagination, setPagination] = useState({ total: 0, limit: 20, offset: 0, hasMore: false });

  useEffect(() => {
    if (contentType) {
      fetchCategoryTasks();
    }
  }, [contentType, statusFilter]);

  const fetchCategoryTasks = async (offset = 0) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        limit: pagination.limit.toString(),
        offset: offset.toString()
      });
      
      if (statusFilter) {
        params.append('status', statusFilter);
      }

      // Fetching category tasks
      
      const response = await authenticatedFetch(
        `/api/content/category/${contentType}?${params}`
      );
      
      // Response received
      
      if (response.ok) {
        const data = await response.json();
        // Data received successfully
        if (offset === 0) {
          setTasks(data.tasks || []);
        } else {
          setTasks(prev => [...prev, ...(data.tasks || [])]);
        }
        setPagination(data.pagination || { total: 0, limit: 20, offset: 0, hasMore: false });
      } else {
        const errorData = await response.text();
        console.error('Failed to fetch category tasks:', response.status, errorData);
      }
    } catch (error) {
      console.error('Error fetching category tasks:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadMore = () => {
    if (pagination.hasMore && !loading) {
      fetchCategoryTasks(pagination.offset + pagination.limit);
    }
  };

  const deleteTask = async (taskId) => {
    if (!confirm('Are you sure you want to delete this task? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await authenticatedFetch(API_CONFIG.ENDPOINTS.TASKS.DELETE(taskId), {
        method: 'DELETE'
      });

      if (response.ok) {
        setTasks(tasks.filter(task => task.id !== taskId));
      } else {
        alert('Failed to delete task');
      }
    } catch (error) {
      console.error('Error deleting task:', error);
      alert('Error deleting task');
    }
  };

  const getContentTypeInfo = (contentType) => {
    const typeMap = {
      'casino_review': { icon: '🎰', label: t('contentTypes.casino_review'), description: 'Comprehensive casino platform reviews' },
      'game_guide': { icon: '🎮', label: t('contentTypes.game_guide'), description: 'Strategic guides for casino games' },
      'strategy_article': { icon: '📊', label: t('contentTypes.strategy_article', 'Strategy Articles'), description: 'Mathematical analysis and gaming strategies' },
      'brand_copy': { icon: '✨', label: t('contentTypes.brand_copy', 'Brand Copy'), description: 'Compelling marketing content and messaging' },
      'industry_news': { icon: '📰', label: t('contentTypes.industry_news'), description: 'Latest industry updates and insights' },
      'sports_betting': { icon: '⚽', label: t('contentTypes.sports_betting'), description: 'Sports betting analysis and guides' },
      'bonus_analysis': { icon: '🎁', label: t('contentTypes.bonus_analysis'), description: 'Bonus and promotion analysis' },
      'regulatory_update': { icon: '⚖️', label: t('contentTypes.regulatory_update'), description: 'Regulatory news and updates' },
      'generic': { icon: '📝', label: t('contentTypes.general'), description: 'General purpose content' },
      'uncategorized': { icon: '📄', label: 'Uncategorized', description: 'Content without specific category' }
    };
    
    return typeMap[contentType] || typeMap['uncategorized'];
  };

  const getStatusIcon = (status) => {
    if (status.includes('Completed')) {
      return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
    } else if (status.includes('Generating')) {
      return <ClockIcon className="w-5 h-5 text-yellow-500 animate-spin" />;
    } else {
      return <DocumentTextIcon className="w-5 h-5 text-blue-500" />;
    }
  };

  const getStatusColor = (status) => {
    if (status.includes('Completed')) return 'bg-green-100 text-green-800';
    if (status.includes('Generating')) return 'bg-yellow-100 text-yellow-800';
    if (status.includes('Review')) return 'bg-cyan-100 text-cyan-800';
    return 'bg-blue-100 text-blue-800';
  };

  const filteredTasks = tasks.filter(task =>
    task.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const typeInfo = getContentTypeInfo(contentType);

  if (loading && tasks.length === 0) {
    return (
      <div className="min-h-screen casino-bg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-casino-gold-500 mx-auto"></div>
          <p className="mt-4 text-casino-gold-200">Loading {typeInfo.label.toLowerCase()}...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen casino-bg">
      {/* Header */}
      <div className="bg-gradient-to-r from-casino-dark-800/90 via-casino-dark-700/90 to-casino-dark-800/90 backdrop-blur-xl border-b border-casino-gold-500/30 sticky top-0 z-50 shadow-lg shadow-casino-gold-500/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link
                to="/content/categories"
                className="p-2 rounded-lg text-casino-gold-200 hover:bg-casino-gold-500/20 hover:text-casino-gold-100 transition-all duration-200"
              >
                <ArrowLeftIcon className="w-5 h-5" />
              </Link>
              <div className="flex items-center space-x-3">
                <span className="text-3xl">{typeInfo.icon}</span>
                <div>
                  <h1 className="text-3xl font-bold text-casino-gold-400 neon-glow font-casino">{typeInfo.label}</h1>
                  <p className="text-casino-gold-200">{typeInfo.description}</p>
                </div>
              </div>
            </div>
            <Link
              to="/tasks/new"
              className="casino-button inline-flex items-center px-4 py-2 text-white rounded-lg chip-shadow animate-pulse-gold"
            >
              <PlusIcon className="w-5 h-5 mr-2" />
              Create {typeInfo.label.slice(0, -1)}
            </Link>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters and Search */}
        <div className="casino-card rounded-xl p-6 mb-8 chip-shadow">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-casino-gold-400" />
                <input
                  type="text"
                  placeholder={`Search ${typeInfo.label.toLowerCase()}...`}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-casino-dark-700 border border-casino-gold-500/30 rounded-lg focus:ring-2 focus:ring-casino-gold-500 focus:border-casino-gold-500 text-casino-gold-200 placeholder-casino-gold-300"
                />
              </div>
            </div>

            {/* Status Filter */}
            <div className="flex items-center space-x-2">
              <FunnelIcon className="w-5 h-5 text-casino-gold-400" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="bg-casino-dark-700 border border-casino-gold-500/30 rounded-lg px-3 py-2 text-casino-gold-200 focus:ring-2 focus:ring-casino-gold-500 focus:border-casino-gold-500"
              >
                <option value="">All Status</option>
                <option value="Draft - Step 1">Draft</option>
                <option value="Ready to Generate">Ready to Generate</option>
                <option value="Generating">Generating</option>
                <option value="Review & Refine">Review & Refine</option>
                <option value="Completed">Completed</option>
              </select>
            </div>
          </div>
        </div>

        {/* Tasks List */}
        {filteredTasks.length === 0 ? (
          <div className="casino-card rounded-xl p-12 text-center chip-shadow">
            <span className="text-6xl mb-4 block">{typeInfo.icon}</span>
            <h3 className="text-xl font-semibold text-casino-gold-400 mb-2">
              {searchTerm || statusFilter ? `No ${typeInfo.label.toLowerCase()} found` : `No ${typeInfo.label.toLowerCase()} yet`}
            </h3>
            <p className="text-casino-gold-200 mb-6">
              {searchTerm || statusFilter
                ? 'Try adjusting your search or filter criteria'
                : `Start creating ${typeInfo.label.toLowerCase()} to see them here`
              }
            </p>
            {!searchTerm && !statusFilter && (
              <Link
                to="/tasks/new"
                className="casino-button inline-flex items-center px-6 py-3 text-white rounded-lg chip-shadow"
              >
                <PlusIcon className="w-5 h-5 mr-2" />
                Create Your First {typeInfo.label.slice(0, -1)}
              </Link>
            )}
          </div>
        ) : (
          <div className="casino-card rounded-xl overflow-hidden chip-shadow">
            <div className="divide-y divide-casino-gold-500/20">
              {filteredTasks.map((task) => (
                <div key={task.id} className="px-6 py-4 hover:bg-casino-gold-500/10 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 flex-1 min-w-0">
                      {getStatusIcon(task.status)}
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-casino-gold-200 truncate">{task.name}</h4>
                        <p className="text-xs text-casino-gold-300">
                          Step {task.current_step + 1} of 7 • Updated {new Date(task.updated_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 flex-shrink-0">
                      {/* Progress Bar */}
                      <div className="w-24 bg-casino-dark-700 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-casino-gold-500 to-casino-gold-400 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${task.status === 'Completed' ? 100 : task.has_article ? 95 : ((task.current_step + 1) / 7) * 90}%` }}
                        ></div>
                      </div>

                      {/* Status Badge */}
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                        {task.status}
                      </span>

                      {/* Action Buttons */}
                      <div className="flex items-center space-x-2">
                        <Link
                          to={`/tasks/${task.id}`}
                          className="text-casino-gold-500 hover:text-casino-gold-400 text-sm font-medium transition-colors"
                        >
                          View
                        </Link>
                        <Link
                          to={`/tasks/${task.id}/edit`}
                          className="p-1 text-casino-gold-400 hover:text-casino-gold-300 transition-colors"
                        >
                          <PencilIcon className="w-4 h-4" />
                        </Link>
                        <button
                          onClick={() => deleteTask(task.id)}
                          className="p-1 text-casino-gold-400 hover:text-red-400 transition-colors"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Load More Button */}
            {pagination.hasMore && (
              <div className="px-6 py-4 border-t border-casino-gold-500/20 bg-casino-dark-800/30">
                <button
                  onClick={loadMore}
                  disabled={loading}
                  className="w-full casino-button py-2 text-white rounded-lg chip-shadow disabled:opacity-50"
                >
                  {loading ? 'Loading...' : `Load More (${pagination.total - tasks.length} remaining)`}
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default CategoryView;
