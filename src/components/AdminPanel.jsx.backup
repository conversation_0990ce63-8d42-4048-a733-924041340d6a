import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeftIcon, CogIcon, DocumentTextIcon, NewspaperIcon } from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { CONTENT_TYPE_CONFIG } from '../utils/contentTypeUtils';
import ArticleEditor from './pages/ArticleEditor';
import AuthenticatedLayout from './layout/AuthenticatedLayout';

const AdminPanel = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('templates');
  const [templates, setTemplates] = useState([]);
  const [settings, setSettings] = useState({});
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState({ text: '', type: '' });
  const [editingTemplate, setEditingTemplate] = useState(null);
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  
  // Article management state
  const [articles, setArticles] = useState([]);
  const [articleStats, setArticleStats] = useState({});
  const [editingArticle, setEditingArticle] = useState(null);
  const [deleteConfirm, setDeleteConfirm] = useState(null);
  const [articleFilters, setArticleFilters] = useState({
    status: 'all',
    contentType: 'all',
    search: '',
    sortBy: 'updated_at',
    sortOrder: 'DESC',
    page: 1,
    limit: 10
  });

  // Check if user is admin
  if (!user || (user.role !== 'admin' && user.role !== 'super_admin')) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-casino-dark-800 via-casino-dark-900 to-black flex items-center justify-center">
        <div className="casino-card p-8 text-center">
          <h1 className="text-2xl font-bold text-casino-gold-100 mb-4">Access Denied</h1>
          <p className="text-casino-gold-400 mb-6">You don't have permission to access the admin panel.</p>
          <Link to="/dashboard" className="btn-casino-primary">
            Back to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  const makeRequest = async (endpoint, options = {}) => {
    const token = localStorage.getItem('token');
    const baseURL = process.env.NODE_ENV === 'production' 
      ? 'https://writer777-production.up.railway.app' 
      : 'http://localhost:3008';
    
    const response = await fetch(`${baseURL}/api/admin${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers
      },
      ...options
    });

    if (!response.ok) {
      throw new Error(`Request failed: ${response.statusText}`);
    }

    return response.json();
  };

  const loadTemplates = async () => {
    try {
      const response = await makeRequest('/templates');
      setTemplates(response.data || []);
    } catch (error) {
      setMessage({ text: `Failed to load templates: ${error.message}`, type: 'error' });
    }
  };

  const loadSettings = async () => {
    try {
      const response = await makeRequest('/settings');
      setSettings(response.data || {});
    } catch (error) {
      setMessage({ text: `Failed to load settings: ${error.message}`, type: 'error' });
    }
  };

  const updateModel = async (newModel) => {
    try {
      await makeRequest('/settings', {
        method: 'PUT',
        body: JSON.stringify({ ai_model: newModel })
      });
      setMessage({ text: 'Model updated successfully!', type: 'success' });
      loadSettings();
    } catch (error) {
      setMessage({ text: `Failed to update model: ${error.message}`, type: 'error' });
    }
  };

  const updateApiKey = async (newApiKey) => {
    try {
      await makeRequest('/settings', {
        method: 'PUT',
        body: JSON.stringify({ api_key: newApiKey })
      });
      setMessage({ text: 'API key updated successfully!', type: 'success' });
      loadSettings();
    } catch (error) {
      setMessage({ text: `Failed to update API key: ${error.message}`, type: 'error' });
    }
  };

  const editTemplate = async (templateId) => {
    try {
      const response = await makeRequest(`/templates/${templateId}`);
      setEditingTemplate(response.data);
      setShowTemplateModal(true);
    } catch (error) {
      setMessage({ text: `Failed to load template: ${error.message}`, type: 'error' });
    }
  };

  const saveTemplate = async (templateData) => {
    try {
      if (editingTemplate?.id) {
        await makeRequest(`/templates/${editingTemplate.id}`, {
          method: 'PUT',
          body: JSON.stringify(templateData)
        });
        setMessage({ text: 'Template updated successfully!', type: 'success' });
      } else {
        await makeRequest('/templates', {
          method: 'POST',
          body: JSON.stringify(templateData)
        });
        setMessage({ text: 'Template created successfully!', type: 'success' });
      }
      setShowTemplateModal(false);
      setEditingTemplate(null);
      loadTemplates();
    } catch (error) {
      setMessage({ text: `Failed to save template: ${error.message}`, type: 'error' });
    }
  };

  const deleteTemplate = async (templateId) => {
    if (!confirm('Are you sure you want to deactivate this template?')) {
      return;
    }
    try {
      await makeRequest(`/templates/${templateId}`, {
        method: 'DELETE'
      });
      setMessage({ text: 'Template deactivated successfully!', type: 'success' });
      loadTemplates();
    } catch (error) {
      setMessage({ text: `Failed to delete template: ${error.message}`, type: 'error' });
    }
  };

  // Article management functions
  const makeArticleRequest = async (endpoint, options = {}) => {
    const token = localStorage.getItem('token');
    const baseURL = process.env.NODE_ENV === 'production' 
      ? 'https://writer777-production.up.railway.app' 
      : 'http://localhost:3001';
    
    const response = await fetch(`${baseURL}/api/articles${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers
      },
      ...options
    });

    if (!response.ok) {
      throw new Error(`Request failed: ${response.statusText}`);
    }

    return response.json();
  };

  const loadArticles = async () => {
    try {
      const params = new URLSearchParams();
      Object.entries(articleFilters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });

      const response = await makeArticleRequest(`?${params}`);
      setArticles(response.articles || []);
    } catch (error) {
      setMessage({ text: `Failed to load articles: ${error.message}`, type: 'error' });
    }
  };

  const loadArticleStats = async () => {
    try {
      const response = await makeArticleRequest('/admin/stats');
      setArticleStats(response.stats || {});
    } catch (error) {
      console.error('Failed to load article stats:', error);
    }
  };

  const handleDeleteArticle = async (id) => {
    try {
      await makeArticleRequest(`/${id}`, { method: 'DELETE' });
      setArticles(articles.filter(a => a.id !== id));
      setDeleteConfirm(null);
      loadArticleStats();
      setMessage({ text: 'Article deleted successfully!', type: 'success' });
    } catch (error) {
      setMessage({ text: `Failed to delete article: ${error.message}`, type: 'error' });
    }
  };

  const handleStatusToggle = async (article) => {
    try {
      const newStatus = article.status === 'published' ? 'draft' : 'published';
      
      await makeArticleRequest(`/${article.id}`, {
        method: 'PUT',
        body: JSON.stringify({
          title: article.title,
          content: article.content,
          content_type: article.content_type,
          status: newStatus
        })
      });

      setArticles(articles.map(a => 
        a.id === article.id ? { ...a, status: newStatus } : a
      ));
      loadArticleStats();
      setMessage({ text: 'Article status updated successfully!', type: 'success' });
    } catch (error) {
      setMessage({ text: `Failed to update article status: ${error.message}`, type: 'error' });
    }
  };

  const loadFullArticle = async (articleId) => {
    try {
      const response = await makeArticleRequest(`/${articleId}`);
      return response.article;
    } catch (error) {
      setMessage({ text: `Failed to load article: ${error.message}`, type: 'error' });
      return null;
    }
  };

  const handleEditArticle = async (article) => {
    setLoading(true);
    try {
      // Load full article content for editing
      const fullArticle = await loadFullArticle(article.id);
      if (fullArticle) {
        setEditingArticle(fullArticle);
      }
    } catch (error) {
      setMessage({ text: `Failed to load article for editing: ${error.message}`, type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleEditSave = (savedArticle) => {
    if (editingArticle && editingArticle.id) {
      setArticles(articles.map(a => 
        a.id === savedArticle.id ? { ...a, ...savedArticle } : a
      ));
    } else {
      setArticles([savedArticle, ...articles]);
    }
    setEditingArticle(null);
    loadArticleStats();
    setMessage({ text: 'Article saved successfully!', type: 'success' });
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getContentTypeLabel = (contentType) => {
    const config = CONTENT_TYPE_CONFIG[contentType];
    return config ? config.label : contentType;
  };

  const getContentTypeColor = (contentType) => {
    const config = CONTENT_TYPE_CONFIG[contentType];
    return config ? config.color : 'from-gray-500 to-gray-600';
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        await Promise.all([loadTemplates(), loadSettings()]);
      } finally {
        setLoading(false);
      }
    };
    loadData();
  }, []);

  useEffect(() => {
    if (activeTab === 'articles') {
      loadArticles();
      loadArticleStats();
    }
  }, [activeTab, articleFilters]);

  if (loading) {
    return (
      <AuthenticatedLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-casino-gold-500 mx-auto"></div>
            <p className="mt-4 text-casino-gold-400">Loading admin panel...</p>
          </div>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-casino-gold-100 mb-2">Admin Panel</h1>
          <p className="text-casino-gold-400">Manage templates, settings, and content</p>
        </div>
        {message.text && (
          <div className={`mb-6 p-4 rounded-xl ${
            message.type === 'success' 
              ? 'bg-green-500/20 border border-green-500/30 text-green-300' 
              : 'bg-red-500/20 border border-red-500/30 text-red-300'
          }`}>
            {message.text}
          </div>
        )}

        {/* Tabs */}
        <div className="mb-8">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('templates')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                activeTab === 'templates'
                  ? 'bg-casino-gold-500 text-casino-dark-900 font-semibold'
                  : 'text-casino-gold-300 hover:text-casino-gold-100'
              }`}
            >
              <DocumentTextIcon className="w-5 h-5" />
              <span>Templates</span>
            </button>
            <button
              onClick={() => setActiveTab('articles')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                activeTab === 'articles'
                  ? 'bg-casino-gold-500 text-casino-dark-900 font-semibold'
                  : 'text-casino-gold-300 hover:text-casino-gold-100'
              }`}
            >
              <NewspaperIcon className="w-5 h-5" />
              <span>Articles</span>
            </button>
            <button
              onClick={() => setActiveTab('settings')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                activeTab === 'settings'
                  ? 'bg-casino-gold-500 text-casino-dark-900 font-semibold'
                  : 'text-casino-gold-300 hover:text-casino-gold-100'
              }`}
            >
              <CogIcon className="w-5 h-5" />
              <span>Settings</span>
            </button>
          </nav>
        </div>

        {/* Content */}
        <div className="casino-card p-6">
          {activeTab === 'templates' && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-casino-gold-100">Prompt Templates</h2>
                <button
                  onClick={() => {
                    setEditingTemplate(null);
                    setShowTemplateModal(true);
                  }}
                  className="px-4 py-2 bg-casino-gold-500 text-casino-dark-900 rounded-lg font-semibold hover:bg-casino-gold-400 transition-colors"
                >
                  Create New Template
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {templates.map((template) => (
                  <div key={template.id} className="casino-card p-4 border border-casino-gold-500/20">
                    <h3 className="text-lg font-semibold text-casino-gold-100 mb-2">
                      {template.template_name}
                    </h3>
                    <p className="text-casino-gold-400 text-sm mb-3">
                      {template.content_type?.replace('_', ' ').toUpperCase()}
                    </p>
                    <p className="text-casino-gold-300 text-sm mb-3">
                      Version: {template.template_version}
                    </p>
                    <div className="flex justify-between items-center">
                      <span className={`px-2 py-1 rounded text-xs ${
                        template.is_active
                          ? 'bg-green-500/20 text-green-300'
                          : 'bg-red-500/20 text-red-300'
                      }`}>
                        {template.is_active ? 'Active' : 'Inactive'}
                      </span>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => editTemplate(template.id)}
                          className="px-3 py-1 bg-blue-500/20 text-blue-300 rounded text-xs hover:bg-blue-500/30 transition-colors"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => deleteTemplate(template.id)}
                          className="px-3 py-1 bg-red-500/20 text-red-300 rounded text-xs hover:bg-red-500/30 transition-colors"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'settings' && (
            <div>
              <h2 className="text-2xl font-bold text-casino-gold-100 mb-6">System Settings</h2>

              {/* API Key Configuration */}
              <div className="casino-card p-6 border border-casino-gold-500/20 mb-6">
                <h3 className="text-lg font-semibold text-casino-gold-100 mb-4">API Key Configuration</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-casino-gold-300 text-sm font-medium mb-2">
                      Current API Key Status
                    </label>
                    <div className={`px-3 py-2 rounded-lg border ${
                      settings.api_key_configured
                        ? 'bg-green-500/20 border-green-500/30 text-green-300'
                        : 'bg-red-500/20 border-red-500/30 text-red-300'
                    }`}>
                      {settings.api_key_configured ? '✅ Configured' : '❌ Missing'}
                    </div>
                  </div>

                  <div>
                    <label className="block text-casino-gold-300 text-sm font-medium mb-2">
                      New API Key
                    </label>
                    <div className="flex space-x-2">
                      <input
                        type="password"
                        placeholder="Enter your Gemini API key..."
                        className="flex-1 px-3 py-2 bg-casino-dark-600 border border-casino-gold-500/30 rounded-lg text-casino-gold-100 placeholder-casino-gold-500"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            updateApiKey(e.target.value);
                            e.target.value = '';
                          }
                        }}
                      />
                      <button
                        onClick={(e) => {
                          const input = e.target.previousElementSibling;
                          updateApiKey(input.value);
                          input.value = '';
                        }}
                        className="px-4 py-2 bg-casino-gold-500 text-casino-dark-900 rounded-lg font-semibold hover:bg-casino-gold-400 transition-colors"
                      >
                        Update
                      </button>
                    </div>
                    <p className="text-casino-gold-400 text-xs mt-1">
                      Get your API key from <a href="https://aistudio.google.com/app/apikey" target="_blank" rel="noopener noreferrer" className="text-casino-gold-300 hover:text-casino-gold-100 underline">Google AI Studio</a>
                    </p>
                  </div>
                </div>
              </div>

              {/* AI Model Configuration */}
              <div className="casino-card p-6 border border-casino-gold-500/20">
                <h3 className="text-lg font-semibold text-casino-gold-100 mb-4">AI Model Configuration</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-casino-gold-300 text-sm font-medium mb-2">
                      Current Model
                    </label>
                    <input
                      type="text"
                      value={settings.ai_model || 'Not configured'}
                      readOnly
                      className="w-full px-3 py-2 bg-casino-dark-600 border border-casino-gold-500/30 rounded-lg text-casino-gold-100"
                    />
                  </div>

                  <div>
                    <label className="block text-casino-gold-300 text-sm font-medium mb-2">
                      New Model Name
                    </label>
                    <div className="flex space-x-2">
                      <input
                        type="text"
                        placeholder="e.g., gemini-2.0-flash-001"
                        className="flex-1 px-3 py-2 bg-casino-dark-600 border border-casino-gold-500/30 rounded-lg text-casino-gold-100 placeholder-casino-gold-500"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            updateModel(e.target.value);
                            e.target.value = '';
                          }
                        }}
                      />
                      <button
                        onClick={(e) => {
                          const input = e.target.previousElementSibling;
                          updateModel(input.value);
                          input.value = '';
                        }}
                        className="px-4 py-2 bg-casino-gold-500 text-casino-dark-900 rounded-lg font-semibold hover:bg-casino-gold-400 transition-colors"
                      >
                        Update
                      </button>
                    </div>
                    <p className="text-casino-gold-400 text-xs mt-1">
                      Available models: gemini-2.0-flash-001, gemini-1.5-pro, gemini-1.5-flash
                    </p>
                  </div>
                </div>

                <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="casino-card p-4 border border-casino-gold-500/20">
                    <div className="text-casino-gold-300 text-sm">API Key Status</div>
                    <div className={`text-sm font-semibold ${
                      settings.api_key_configured ? 'text-green-300' : 'text-red-300'
                    }`}>
                      {settings.api_key_configured ? 'Configured' : 'Missing'}
                    </div>
                  </div>
                  
                  <div className="casino-card p-4 border border-casino-gold-500/20">
                    <div className="text-casino-gold-300 text-sm">Environment</div>
                    <div className="text-casino-gold-100 text-sm font-semibold">
                      {settings.environment || 'Unknown'}
                    </div>
                  </div>
                  
                  <div className="casino-card p-4 border border-casino-gold-500/20">
                    <div className="text-casino-gold-300 text-sm">Current Model</div>
                    <div className="text-casino-gold-100 text-sm font-semibold">
                      {settings.ai_model || 'Not set'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'articles' && (
            <div>
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-casino-gold-100">Article Management</h2>
                <p className="text-casino-gold-400 mt-2">Manage articles published from the article generator</p>
              </div>

              {/* Statistics */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div className="casino-card p-6 border border-casino-gold-500/20">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-500/20 rounded-lg">
                      <DocumentTextIcon className="w-6 h-6 text-blue-400" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-casino-gold-300">Total Articles</p>
                      <p className="text-2xl font-semibold text-casino-gold-100">{articleStats.total || 0}</p>
                    </div>
                  </div>
                </div>
                
                <div className="casino-card p-6 border border-casino-gold-500/20">
                  <div className="flex items-center">
                    <div className="p-2 bg-green-500/20 rounded-lg">
                      <svg className="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-casino-gold-300">Published</p>
                      <p className="text-2xl font-semibold text-casino-gold-100">{articleStats.published || 0}</p>
                    </div>
                  </div>
                </div>
                
                <div className="casino-card p-6 border border-casino-gold-500/20">
                  <div className="flex items-center">
                    <div className="p-2 bg-yellow-500/20 rounded-lg">
                      <svg className="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-casino-gold-300">Drafts</p>
                      <p className="text-2xl font-semibold text-casino-gold-100">{articleStats.draft || 0}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Filters */}
              <div className="casino-card p-6 border border-casino-gold-500/20 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-casino-gold-300 mb-2">Status</label>
                    <select
                      value={articleFilters.status}
                      onChange={(e) => setArticleFilters({...articleFilters, status: e.target.value, page: 1})}
                      className="w-full px-3 py-2 bg-casino-dark-600 border border-casino-gold-500/30 rounded-lg text-casino-gold-100"
                    >
                      <option value="all">All Status</option>
                      <option value="published">Published</option>
                      <option value="draft">Draft</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-casino-gold-300 mb-2">Content Type</label>
                    <select
                      value={articleFilters.contentType}
                      onChange={(e) => setArticleFilters({...articleFilters, contentType: e.target.value, page: 1})}
                      className="w-full px-3 py-2 bg-casino-dark-600 border border-casino-gold-500/30 rounded-lg text-casino-gold-100"
                    >
                      <option value="all">All Types</option>
                      {Object.entries(CONTENT_TYPE_CONFIG).map(([key, config]) => (
                        <option key={key} value={key}>{config.label}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-casino-gold-300 mb-2">Search</label>
                    <input
                      type="text"
                      value={articleFilters.search}
                      onChange={(e) => setArticleFilters({...articleFilters, search: e.target.value, page: 1})}
                      placeholder="Search titles..."
                      className="w-full px-3 py-2 bg-casino-dark-600 border border-casino-gold-500/30 rounded-lg text-casino-gold-100 placeholder-casino-gold-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-casino-gold-300 mb-2">Sort</label>
                    <select
                      value={`${articleFilters.sortBy}-${articleFilters.sortOrder}`}
                      onChange={(e) => {
                        const [sortBy, sortOrder] = e.target.value.split('-');
                        setArticleFilters({...articleFilters, sortBy, sortOrder, page: 1});
                      }}
                      className="w-full px-3 py-2 bg-casino-dark-600 border border-casino-gold-500/30 rounded-lg text-casino-gold-100"
                    >
                      <option value="updated_at-DESC">Latest Updated</option>
                      <option value="created_at-DESC">Latest Created</option>
                      <option value="title-ASC">Title A-Z</option>
                      <option value="status-ASC">Status</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Articles List */}
              <div className="casino-card border border-casino-gold-500/20">
                {articles.length === 0 ? (
                  <div className="p-8 text-center">
                    <NewspaperIcon className="mx-auto h-12 w-12 text-casino-gold-400" />
                    <h3 className="mt-2 text-sm font-medium text-casino-gold-100">No articles found</h3>
                    <p className="mt-1 text-sm text-casino-gold-400">Try adjusting your search criteria or create a new article.</p>
                  </div>
                ) : (
                  <div className="divide-y divide-casino-gold-500/20">
                    {articles.map((article) => (
                      <div key={article.id} className="p-6">
                        <div className="flex items-center justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center">
                              <h3 className="text-lg font-medium text-casino-gold-100 truncate">
                                {article.title}
                              </h3>
                              <span className={`ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r ${getContentTypeColor(article.content_type)} text-white`}>
                                {getContentTypeLabel(article.content_type)}
                              </span>
                              <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                article.status === 'published' 
                                  ? 'bg-green-500/20 text-green-300 border border-green-500/30' 
                                  : 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30'
                              }`}>
                                {article.status === 'published' ? 'Published' : 'Draft'}
                              </span>
                            </div>
                            <p className="mt-1 text-sm text-casino-gold-400 line-clamp-2">
                              {article.excerpt}
                            </p>
                            <div className="mt-2 flex items-center text-sm text-casino-gold-500">
                              <span>Author: {article.author}</span>
                              <span className="mx-2">•</span>
                              <span>Updated: {formatDate(article.updated_at)}</span>
                              {article.published_at && (
                                <>
                                  <span className="mx-2">•</span>
                                  <span>Published: {formatDate(article.published_at)}</span>
                                </>
                              )}
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-2 ml-4">
                            <button
                              onClick={() => handleEditArticle(article)}
                              className="p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-500/20 rounded-md transition-colors"
                              title="Edit article"
                            >
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                            </button>
                            
                            <button
                              onClick={() => handleStatusToggle(article)}
                              className={`p-2 rounded-md transition-colors ${
                                article.status === 'published'
                                  ? 'text-yellow-400 hover:text-yellow-300 hover:bg-yellow-500/20'
                                  : 'text-green-400 hover:text-green-300 hover:bg-green-500/20'
                              }`}
                              title={article.status === 'published' ? 'Set as draft' : 'Publish article'}
                            >
                              {article.status === 'published' ? (
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                </svg>
                              ) : (
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                </svg>
                              )}
                            </button>
                            
                            <button
                              onClick={() => setDeleteConfirm(article)}
                              className="p-2 text-red-400 hover:text-red-300 hover:bg-red-500/20 rounded-md transition-colors"
                              title="Delete article"
                            >
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Template Edit Modal */}
      {showTemplateModal && (
        <TemplateEditModal
          template={editingTemplate}
          onSave={saveTemplate}
          onClose={() => {
            setShowTemplateModal(false);
            setEditingTemplate(null);
          }}
        />
      )}

      {/* Article Editor Modal */}
      {editingArticle && (
        <ArticleEditor
          article={editingArticle}
          onSave={handleEditSave}
          onCancel={() => setEditingArticle(null)}
        />
      )}

      {/* Delete Confirmation Modal */}
      {deleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-casino-dark-800">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-500/20">
                <svg className="h-6 w-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-casino-gold-100 mt-4">Confirm Delete</h3>
              <p className="text-sm text-casino-gold-400 mt-2">
                Are you sure you want to delete the article "{deleteConfirm.title}"? This action cannot be undone.
              </p>
              <div className="flex justify-center space-x-3 mt-6">
                <button
                  onClick={() => setDeleteConfirm(null)}
                  className="px-4 py-2 bg-casino-dark-600 text-casino-gold-300 rounded-lg hover:bg-casino-dark-500 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleDeleteArticle(deleteConfirm.id)}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    </AuthenticatedLayout>
  );
};

// Template Edit Modal Component
const TemplateEditModal = ({ template, onSave, onClose }) => {
  const [formData, setFormData] = useState({
    content_type: template?.content_type || 'casino_review',
    template_name: template?.template_name || '',
    template_version: template?.template_version || '1.0',
    prompt_content: template?.prompt_content || '',
    persona_description: template?.persona_description || '',
    specialized_instructions: template?.specialized_instructions || '',
    compliance_rules: Array.isArray(template?.compliance_rules)
      ? template.compliance_rules.join('\n')
      : (template?.compliance_rules || ''),
    is_active: template?.is_active ?? true
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    const submitData = {
      ...formData,
      compliance_rules: formData.compliance_rules
        .split('\n')
        .map(rule => rule.trim())
        .filter(rule => rule.length > 0)
    };
    onSave(submitData);
  };

  const contentTypes = [
    { value: 'casino_review', label: '🎰 Casino Review' },
    { value: 'game_guide', label: '🎮 Game Guide' },
    { value: 'strategy_article', label: '📊 Strategy Article' },
    { value: 'brand_copy', label: '🏷️ Brand Copy' },
    { value: 'industry_news', label: '📰 Industry News' },
    { value: 'sports_betting', label: '⚽ Sports Betting' },
    { value: 'generic', label: '📝 Generic' }
  ];

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="casino-card p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-bold text-casino-gold-100">
            {template ? 'Edit Template' : 'Create New Template'}
          </h3>
          <button
            onClick={onClose}
            className="text-casino-gold-400 hover:text-casino-gold-100 text-2xl"
          >
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-casino-gold-300 text-sm font-medium mb-2">
                Content Type
              </label>
              <select
                value={formData.content_type}
                onChange={(e) => setFormData({...formData, content_type: e.target.value})}
                className="w-full px-3 py-2 bg-casino-dark-600 border border-casino-gold-500/30 rounded-lg text-casino-gold-100"
                required
              >
                {contentTypes.map(type => (
                  <option key={type.value} value={type.value}>{type.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-casino-gold-300 text-sm font-medium mb-2">
                Template Name
              </label>
              <input
                type="text"
                value={formData.template_name}
                onChange={(e) => setFormData({...formData, template_name: e.target.value})}
                className="w-full px-3 py-2 bg-casino-dark-600 border border-casino-gold-500/30 rounded-lg text-casino-gold-100"
                required
              />
            </div>

            <div>
              <label className="block text-casino-gold-300 text-sm font-medium mb-2">
                Version
              </label>
              <input
                type="text"
                value={formData.template_version}
                onChange={(e) => setFormData({...formData, template_version: e.target.value})}
                className="w-full px-3 py-2 bg-casino-dark-600 border border-casino-gold-500/30 rounded-lg text-casino-gold-100"
                required
              />
            </div>

            <div>
              <label className="block text-casino-gold-300 text-sm font-medium mb-2">
                Status
              </label>
              <select
                value={formData.is_active}
                onChange={(e) => setFormData({...formData, is_active: e.target.value === 'true'})}
                className="w-full px-3 py-2 bg-casino-dark-600 border border-casino-gold-500/30 rounded-lg text-casino-gold-100"
              >
                <option value="true">✅ Active</option>
                <option value="false">❌ Inactive</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-casino-gold-300 text-sm font-medium mb-2">
              Persona Description
            </label>
            <textarea
              value={formData.persona_description}
              onChange={(e) => setFormData({...formData, persona_description: e.target.value})}
              rows={3}
              className="w-full px-3 py-2 bg-casino-dark-600 border border-casino-gold-500/30 rounded-lg text-casino-gold-100"
              placeholder="Describe the AI's personality, expertise, and writing style..."
            />
          </div>

          <div>
            <label className="block text-casino-gold-300 text-sm font-medium mb-2">
              Specialized Instructions
            </label>
            <textarea
              value={formData.specialized_instructions}
              onChange={(e) => setFormData({...formData, specialized_instructions: e.target.value})}
              rows={3}
              className="w-full px-3 py-2 bg-casino-dark-600 border border-casino-gold-500/30 rounded-lg text-casino-gold-100"
              placeholder="Content-type specific guidelines and requirements..."
            />
          </div>

          <div>
            <label className="block text-casino-gold-300 text-sm font-medium mb-2">
              Prompt Content
            </label>
            <textarea
              value={formData.prompt_content}
              onChange={(e) => setFormData({...formData, prompt_content: e.target.value})}
              rows={8}
              className="w-full px-3 py-2 bg-casino-dark-600 border border-casino-gold-500/30 rounded-lg text-casino-gold-100 font-mono text-sm"
              placeholder="Enter the prompt template content..."
              required
            />
            <p className="text-casino-gold-400 text-xs mt-1">
              Available placeholders: {`{{TOPICS_SECTION}}, {{KEYWORDS_SECTION}}, {{SOURCES_SECTION}}, {{COMPLIANCE_SECTION}}`}
            </p>
          </div>

          <div>
            <label className="block text-casino-gold-300 text-sm font-medium mb-2">
              Compliance Rules
            </label>
            <textarea
              value={formData.compliance_rules}
              onChange={(e) => setFormData({...formData, compliance_rules: e.target.value})}
              rows={4}
              className="w-full px-3 py-2 bg-casino-dark-600 border border-casino-gold-500/30 rounded-lg text-casino-gold-100"
              placeholder="Enter each compliance rule on a separate line..."
            />
          </div>

          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-casino-dark-600 text-casino-gold-300 rounded-lg hover:bg-casino-dark-500 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-casino-gold-500 text-casino-dark-900 rounded-lg font-semibold hover:bg-casino-gold-400 transition-colors"
            >
              {template ? 'Update Template' : 'Create Template'}
            </button>
          </div>
        </form>
      </div>
  );
};

export default AdminPanel;
