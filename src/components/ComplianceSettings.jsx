import React, { useState, useEffect } from 'react';
import { ExclamationTriangleIcon, ShieldCheckIcon, InformationCircleIcon } from '@heroicons/react/24/outline';

// Regional compliance templates organized by continent
const COMPLIANCE_REGIONS = {
  europe: {
    name: 'Europe',
    countries: {
      albania: { name: 'Albania', code: 'AL' },
      andorra: { name: 'Andorra', code: 'AD' },
      bosnia_herzegovina: { name: 'Bosnia and Herzegovina', code: 'BA' },
      bulgaria: { name: 'Bulgaria', code: 'BG' },
      croatia: { name: 'Croatia', code: 'HR' },
      cyprus: { name: 'Cyprus', code: 'CY' },
      estonia: { name: 'Estonia', code: 'EE' },
      finland: { name: 'Finland', code: 'FI' },
      france: { name: 'France', code: 'FR' },
      hungary: { name: 'Hungary', code: 'HU' },
      iceland: { name: 'Iceland', code: 'IS' },
      ireland: { name: 'Ireland', code: 'IE' },
      italy: { name: 'Italy', code: 'IT' },
      latvia: { name: 'Latvia', code: 'LV' },
      liechtenstein: { name: 'Liechtenstein', code: 'LI' },
      luxembourg: { name: 'Luxembourg', code: 'LU' },
      malta: { name: 'Malta', code: 'MT' },
      moldova: { name: 'Moldova', code: 'MD' },
      monaco: { name: 'Monaco', code: 'MC' },
      montenegro: { name: 'Montenegro', code: 'ME' },
      netherlands: { name: 'Netherlands', code: 'NL' },
      north_macedonia: { name: 'North Macedonia', code: 'MK' },
      norway: { name: 'Norway', code: 'NO' },
      poland: { name: 'Poland', code: 'PL' },
      portugal: { name: 'Portugal', code: 'PT' },
      romania: { name: 'Romania', code: 'RO' },
      russia: { name: 'Russia', code: 'RU' },
      san_marino: { name: 'San Marino', code: 'SM' },
      slovakia: { name: 'Slovakia', code: 'SK' },
      slovenia: { name: 'Slovenia', code: 'SI' },
      spain: { name: 'Spain', code: 'ES' },
      sweden: { name: 'Sweden', code: 'SE' },
      united_kingdom: { name: 'United Kingdom', code: 'GB' },
      vatican_city: { name: 'Vatican City (Holy See)', code: 'VA' }
    }
  },
  america: {
    name: 'America',
    countries: {
      argentina: { name: 'Argentina', code: 'AR' },
      antigua_barbuda: { name: 'Antigua and Barbuda', code: 'AG' },
      bahamas: { name: 'Bahamas', code: 'BS' },
      barbados: { name: 'Barbados', code: 'BB' },
      bolivia: { name: 'Bolivia', code: 'BO' },
      brazil: { name: 'Brazil', code: 'BR' },
      canada: { name: 'Canada', code: 'CA' },
      chile: { name: 'Chile', code: 'CL' },
      colombia: { name: 'Colombia', code: 'CO' },
      costa_rica: { name: 'Costa Rica', code: 'CR' },
      cuba: { name: 'Cuba', code: 'CU' },
      dominican_republic: { name: 'Dominican Republic', code: 'DO' },
      ecuador: { name: 'Ecuador', code: 'EC' },
      el_salvador: { name: 'El Salvador', code: 'SV' },
      grenada: { name: 'Grenada', code: 'GD' },
      guatemala: { name: 'Guatemala', code: 'GT' },
      guyana: { name: 'Guyana', code: 'GY' },
      haiti: { name: 'Haiti', code: 'HT' },
      honduras: { name: 'Honduras', code: 'HN' },
      jamaica: { name: 'Jamaica', code: 'JM' },
      mexico: { name: 'Mexico', code: 'MX' },
      nicaragua: { name: 'Nicaragua', code: 'NI' },
      panama: { name: 'Panama', code: 'PA' },
      paraguay: { name: 'Paraguay', code: 'PY' },
      peru: { name: 'Peru', code: 'PE' },
      saint_kitts_nevis: { name: 'Saint Kitts and Nevis', code: 'KN' },
      saint_lucia: { name: 'Saint Lucia', code: 'LC' },
      saint_vincent_grenadines: { name: 'Saint Vincent and the Grenadines', code: 'VC' },
      suriname: { name: 'Suriname', code: 'SR' },
      trinidad_tobago: { name: 'Trinidad and Tobago', code: 'TT' },
      united_states: { name: 'United States', code: 'US' },
      uruguay: { name: 'Uruguay', code: 'UY' },
      venezuela: { name: 'Venezuela', code: 'VE' }
    }
  },
  africa: {
    name: 'Africa',
    countries: {
      algeria: { name: 'Algeria', code: 'DZ' },
      angola: { name: 'Angola', code: 'AO' },
      botswana: { name: 'Botswana', code: 'BW' },
      burkina_faso: { name: 'Burkina Faso', code: 'BF' },
      burundi: { name: 'Burundi', code: 'BI' },
      cameroon: { name: 'Cameroon', code: 'CM' },
      cape_verde: { name: 'Cape Verde', code: 'CV' },
      central_african_republic: { name: 'Central African Republic', code: 'CF' },
      chad: { name: 'Chad', code: 'TD' },
      comoros: { name: 'Comoros', code: 'KM' },
      democratic_republic_congo: { name: 'Democratic Republic of the Congo', code: 'CD' },
      djibouti: { name: 'Djibouti', code: 'DJ' },
      egypt: { name: 'Egypt', code: 'EG' },
      equatorial_guinea: { name: 'Equatorial Guinea', code: 'GQ' },
      eritrea: { name: 'Eritrea', code: 'ER' },
      eswatini: { name: 'Eswatini (Swaziland)', code: 'SZ' },
      ethiopia: { name: 'Ethiopia', code: 'ET' },
      gabon: { name: 'Gabon', code: 'GA' },
      gambia: { name: 'Gambia', code: 'GM' },
      ghana: { name: 'Ghana', code: 'GH' },
      guinea: { name: 'Guinea', code: 'GN' },
      guinea_bissau: { name: 'Guinea-Bissau', code: 'GW' },
      ivory_coast: { name: 'Ivory Coast (Côte d\'Ivoire)', code: 'CI' },
      kenya: { name: 'Kenya', code: 'KE' },
      lesotho: { name: 'Lesotho', code: 'LS' },
      liberia: { name: 'Liberia', code: 'LR' },
      libya: { name: 'Libya', code: 'LY' },
      madagascar: { name: 'Madagascar', code: 'MG' },
      malawi: { name: 'Malawi', code: 'MW' },
      mali: { name: 'Mali', code: 'ML' },
      mauritania: { name: 'Mauritania', code: 'MR' },
      mauritius: { name: 'Mauritius', code: 'MU' },
      morocco: { name: 'Morocco', code: 'MA' },
      mozambique: { name: 'Mozambique', code: 'MZ' },
      namibia: { name: 'Namibia', code: 'NA' },
      niger: { name: 'Niger', code: 'NE' },
      nigeria: { name: 'Nigeria', code: 'NG' },
      rwanda: { name: 'Rwanda', code: 'RW' },
      senegal: { name: 'Senegal', code: 'SN' },
      seychelles: { name: 'Seychelles', code: 'SC' },
      sierra_leone: { name: 'Sierra Leone', code: 'SL' },
      somalia: { name: 'Somalia', code: 'SO' },
      south_africa: { name: 'South Africa', code: 'ZA' },
      sudan: { name: 'Sudan', code: 'SD' },
      tanzania: { name: 'Tanzania', code: 'TZ' },
      togo: { name: 'Togo', code: 'TG' },
      tunisia: { name: 'Tunisia', code: 'TN' },
      uganda: { name: 'Uganda', code: 'UG' },
      zambia: { name: 'Zambia', code: 'ZM' },
      zimbabwe: { name: 'Zimbabwe', code: 'ZW' }
    }
  },
  asia: {
    name: 'Asia',
    countries: {
      armenia: { name: 'Armenia', code: 'AM' },
      azerbaijan: { name: 'Azerbaijan', code: 'AZ' },
      bangladesh: { name: 'Bangladesh', code: 'BD' },
      bhutan: { name: 'Bhutan', code: 'BT' },
      brunei: { name: 'Brunei', code: 'BN' },
      cambodia: { name: 'Cambodia', code: 'KH' },
      china: { name: 'China', code: 'CN' },
      georgia: { name: 'Georgia', code: 'GE' },
      india: { name: 'India', code: 'IN' },
      indonesia: { name: 'Indonesia', code: 'ID' },
      japan: { name: 'Japan', code: 'JP' },
      jordan: { name: 'Jordan', code: 'JO' },
      kazakhstan: { name: 'Kazakhstan', code: 'KZ' },
      kuwait: { name: 'Kuwait', code: 'KW' },
      kyrgyzstan: { name: 'Kyrgyzstan', code: 'KG' },
      laos: { name: 'Laos', code: 'LA' },
      lebanon: { name: 'Lebanon', code: 'LB' },
      malaysia: { name: 'Malaysia', code: 'MY' },
      maldives: { name: 'Maldives', code: 'MV' },
      mongolia: { name: 'Mongolia', code: 'MN' },
      myanmar: { name: 'Myanmar (Burma)', code: 'MM' },
      nepal: { name: 'Nepal', code: 'NP' },
      pakistan: { name: 'Pakistan', code: 'PK' },
      philippines: { name: 'Philippines', code: 'PH' },
      qatar: { name: 'Qatar', code: 'QA' },
      saudi_arabia: { name: 'Saudi Arabia', code: 'SA' },
      singapore: { name: 'Singapore', code: 'SG' },
      south_korea: { name: 'South Korea', code: 'KR' },
      sri_lanka: { name: 'Sri Lanka', code: 'LK' },
      taiwan: { name: 'Taiwan', code: 'TW' },
      tajikistan: { name: 'Tajikistan', code: 'TJ' },
      thailand: { name: 'Thailand', code: 'TH' },
      timor_leste: { name: 'Timor-Leste (East Timor)', code: 'TL' },
      turkey: { name: 'Turkey', code: 'TR' },
      turkmenistan: { name: 'Turkmenistan', code: 'TM' },
      uzbekistan: { name: 'Uzbekistan', code: 'UZ' },
      vietnam: { name: 'Vietnam', code: 'VN' },
      yemen: { name: 'Yemen', code: 'YE' }
    }
  },
  oceania: {
    name: 'Oceania',
    countries: {
      australia: { name: 'Australia', code: 'AU' },
      fiji: { name: 'Fiji', code: 'FJ' },
      kiribati: { name: 'Kiribati', code: 'KI' },
      marshall_islands: { name: 'Marshall Islands', code: 'MH' },
      micronesia: { name: 'Micronesia', code: 'FM' },
      nauru: { name: 'Nauru', code: 'NR' },
      new_zealand: { name: 'New Zealand', code: 'NZ' },
      palau: { name: 'Palau', code: 'PW' },
      samoa: { name: 'Samoa', code: 'WS' },
      solomon_islands: { name: 'Solomon Islands', code: 'SB' },
      tonga: { name: 'Tonga', code: 'TO' },
      tuvalu: { name: 'Tuvalu', code: 'TV' },
      vanuatu: { name: 'Vanuatu', code: 'VU' }
    }
  }
};

// Generate compliance templates for all countries
const generateComplianceTemplate = (countryName, countryCode, region) => {
  const baseTemplate = {
    name: countryName,
    code: countryCode,
    region: region,
    requirements: [
      'Age verification (18+) prominently displayed',
      'Responsible gambling tools and resources',
      'Clear terms and conditions disclosure',
      'Player protection information',
      'Support resources and helplines',
      'Privacy and security measures'
    ],
    disclaimers: [
      'Gambling can be addictive. Please play responsibly.',
      'Must be of legal gambling age in your jurisdiction.',
      'Terms and conditions apply.',
      'Seek help if gambling becomes a problem.'
    ],
    mandatoryElements: [
      'responsible_gambling_notice',
      'age_verification',
      'support_resources',
      'terms_disclosure'
    ]
  };

  // Add region-specific customizations
  switch (region) {
    case 'europe':
      if (countryCode === 'GB') {
        baseTemplate.requirements.unshift('GambleAware.org links and information');
        baseTemplate.disclaimers.unshift('BeGambleAware.org - For help and support.');
      }
      if (countryCode === 'MT') {
        baseTemplate.requirements.unshift('Malta Gaming Authority license display');
        baseTemplate.disclaimers.unshift('Licensed and regulated by the Malta Gaming Authority.');
      }
      break;
    case 'america':
      if (countryCode === 'US') {
        baseTemplate.requirements.push('State-specific licensing information', 'Geolocation compliance');
        baseTemplate.disclaimers[1] = 'Must be 21+ years old (18+ where legally permitted).';
        baseTemplate.disclaimers.push('Gambling problem? Call 1-800-GAMBLER.');
      }
      break;
  }

  return baseTemplate;
};

// Create compliance templates for all countries
const COMPLIANCE_TEMPLATES = {};

// Add all countries from regions
Object.entries(COMPLIANCE_REGIONS).forEach(([regionKey, region]) => {
  Object.entries(region.countries).forEach(([countryKey, country]) => {
    COMPLIANCE_TEMPLATES[countryKey] = generateComplianceTemplate(
      country.name,
      country.code,
      regionKey
    );
  });
});

// Add special templates
COMPLIANCE_TEMPLATES.international = {
  name: 'International',
  code: 'INTL',
  region: 'global',
  requirements: [
    'General responsible gambling information',
    'Age verification (18+)',
    'Player protection guidelines',
    'Support resources',
    'Fair gaming practices',
    'Privacy and security measures'
  ],
  disclaimers: [
    'Please gamble responsibly.',
    'Must be of legal gambling age in your jurisdiction.',
    'Gambling can be addictive - seek help if needed.',
    'Terms and conditions apply.'
  ],
  mandatoryElements: [
    'responsible_gambling',
    'age_verification',
    'support_resources',
    'fair_gaming'
  ]
};


const ComplianceSettings = ({ data, updateData, onNext, onPrev }) => {
  // Create a mapping from Step0 country codes to compliance jurisdictions
  const getJurisdictionFromKeywordResearch = () => {
    // Extract country from keyword research data if available
    const keywordResearchData = data?.keywordResearchData;
    if (!keywordResearchData) return 'international';
    
    // Try to get country from research parameters or use a general mapping
    // This is a basic mapping - you may need to expand it based on your needs
    const countryToJurisdictionMap = {
      'us': 'united_states',
      'gb': 'united_kingdom', 
      'uk': 'united_kingdom',
      'ca': 'canada',
      'au': 'australia',
      'br': 'brazil',
      'de': 'germany',
      'fr': 'france',
      'es': 'spain',
      'it': 'italy',
      'pt': 'portugal',
      'nl': 'netherlands',
      'se': 'sweden',
      'no': 'norway',
      'fi': 'finland',
      'dk': 'denmark',
      'pl': 'poland',
      'mx': 'mexico',
      'ar': 'argentina',
      'cl': 'chile',
      'co': 'colombia',
      'jp': 'japan',
      'kr': 'south_korea',
      'cn': 'china',
      'in': 'india',
      'sg': 'singapore',
      'za': 'south_africa',
      'eg': 'egypt',
      'nz': 'new_zealand'
    };

    // Look for country in the research data or use Step0's selectedCountry
    const countryCode = data?.selectedCountry || keywordResearchData?.country;
    return countryToJurisdictionMap[countryCode?.toLowerCase()] || 'international';
  };

  // Auto-select jurisdiction based on keyword research country
  const autoSelectedJurisdiction = getJurisdictionFromKeywordResearch();
  
  const [selectedJurisdiction, setSelectedJurisdiction] = useState(
    data?.jurisdiction || autoSelectedJurisdiction || 'international'
  );
  const [customDisclaimers, setCustomDisclaimers] = useState(data?.customDisclaimers || []);
  const [additionalRequirements, setAdditionalRequirements] = useState(data?.additionalRequirements || '');

  // Auto-update jurisdiction when keyword research data changes
  useEffect(() => {
    if (!data?.jurisdiction) { // Only auto-set if not manually selected before
      const newJurisdiction = getJurisdictionFromKeywordResearch();
      if (newJurisdiction !== selectedJurisdiction) {
        setSelectedJurisdiction(newJurisdiction);
        updateData({ 
          jurisdiction: newJurisdiction,
          complianceTemplate: COMPLIANCE_TEMPLATES[newJurisdiction]
        });
      }
    }
  }, [data?.keywordResearchData, data?.selectedCountry, selectedJurisdiction, updateData]);

  const currentTemplate = COMPLIANCE_TEMPLATES[selectedJurisdiction] || COMPLIANCE_TEMPLATES.international;

  const handleJurisdictionChange = (jurisdiction) => {
    setSelectedJurisdiction(jurisdiction);
    updateData({ 
      jurisdiction,
      complianceTemplate: COMPLIANCE_TEMPLATES[jurisdiction]
    });
  };

  const handleCustomDisclaimerAdd = () => {
    const newDisclaimer = prompt('Enter custom disclaimer:');
    if (newDisclaimer && newDisclaimer.trim()) {
      const updated = [...customDisclaimers, newDisclaimer.trim()];
      setCustomDisclaimers(updated);
      updateData({ customDisclaimers: updated });
    }
  };

  const handleCustomDisclaimerRemove = (index) => {
    const updated = customDisclaimers.filter((_, i) => i !== index);
    setCustomDisclaimers(updated);
    updateData({ customDisclaimers: updated });
  };


  const handleAdditionalRequirementsChange = (value) => {
    setAdditionalRequirements(value);
    updateData({ additionalRequirements: value });
  };

  const handleContinue = () => {
    updateData({
      jurisdiction: selectedJurisdiction,
      complianceTemplate: currentTemplate,
      customDisclaimers,
      additionalRequirements
    });
    onNext();
  };

  return (
    <div className="space-y-8">
      {/* Jurisdiction Selection */}
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-4">
          Compliance Jurisdiction
        </h3>
        <p className="text-gray-600 mb-4">
          Select the primary regulatory jurisdiction for compliance requirements.
        </p>
        
        {/* Auto-selection notification */}
        {autoSelectedJurisdiction !== 'international' && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-center">
              <InformationCircleIcon className="h-5 w-5 text-blue-500 mr-2" />
              <span className="text-sm text-blue-700">
                Auto-selected based on your keyword research country. You can change this if needed.
              </span>
            </div>
          </div>
        )}
        
        <div className="mb-6">
          <select
            value={selectedJurisdiction}
            onChange={(e) => handleJurisdictionChange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select Jurisdiction</option>
            
            {/* International option */}
            <option value="international">
              {COMPLIANCE_TEMPLATES.international.name}
            </option>
            
            {/* Regional groupings */}
            {Object.entries(COMPLIANCE_REGIONS).map(([regionKey, region]) => (
              <optgroup key={regionKey} label={region.name}>
                {Object.entries(region.countries).map(([countryKey, country]) => (
                  <option key={countryKey} value={countryKey}>
                    {country.name}
                  </option>
                ))}
              </optgroup>
            ))}
          </select>
        </div>
        
        {/* Selected jurisdiction info */}
        {selectedJurisdiction && currentTemplate && (
          <div className="p-4 rounded-lg border border-blue-200 bg-blue-50">
            <div className="flex items-center mb-2">
              <ShieldCheckIcon className="h-5 w-5 text-blue-600 mr-2" />
              <h4 className="font-medium text-blue-900">{currentTemplate.name}</h4>
              {currentTemplate.code && (
                <span className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded">
                  {currentTemplate.code}
                </span>
              )}
            </div>
            <p className="text-sm text-blue-700">
              {currentTemplate.requirements.length} compliance requirements will be applied
            </p>
          </div>
        )}
      </div>

      {/* Compliance Requirements */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
          <ShieldCheckIcon className="w-5 h-5 mr-2 text-blue-600" />
          Compliance Requirements for {currentTemplate.name}
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {currentTemplate.requirements.map((requirement, index) => (
            <div key={index} className="flex items-start space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
              <span className="text-sm text-gray-700">{requirement}</span>
            </div>
          ))}
        </div>

        <h5 className="font-medium text-gray-900 mb-3">Standard Disclaimers</h5>
        <div className="space-y-2">
          {currentTemplate.disclaimers.map((disclaimer, index) => (
            <div key={index} className="bg-white p-3 rounded border text-sm text-gray-700">
              {disclaimer}
            </div>
          ))}
        </div>
      </div>

      {/* Custom Disclaimers */}
      <div>
        <h4 className="font-semibold text-gray-900 mb-4">Custom Disclaimers</h4>
        <p className="text-gray-600 mb-4 text-sm">
          Add any additional disclaimers specific to your content or brand.
        </p>
        
        <div className="space-y-2 mb-4">
          {customDisclaimers.map((disclaimer, index) => (
            <div key={index} className="flex items-center justify-between bg-white p-3 rounded border">
              <span className="text-sm text-gray-700">{disclaimer}</span>
              <button
                onClick={() => handleCustomDisclaimerRemove(index)}
                className="text-red-500 hover:text-red-700 text-sm"
              >
                Remove
              </button>
            </div>
          ))}
        </div>
        
        <button
          onClick={handleCustomDisclaimerAdd}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
        >
          Add Custom Disclaimer
        </button>
      </div>


      {/* Additional Requirements */}
      <div>
        <h4 className="font-semibold text-gray-900 mb-4">Additional Requirements</h4>
        <textarea
          value={additionalRequirements}
          onChange={(e) => handleAdditionalRequirementsChange(e.target.value)}
          placeholder="Enter any additional compliance requirements, brand guidelines, or specific instructions..."
          className="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          rows={4}
        />
      </div>

      {/* Warning Notice */}
      <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <InformationCircleIcon className="w-5 h-5 text-amber-600 mt-0.5" />
          <div>
            <h4 className="font-medium text-amber-800 mb-1">Important Notice</h4>
            <p className="text-sm text-amber-700">
              These compliance settings provide guidance for content generation, but do not guarantee 
              regulatory compliance. Always consult with qualified legal professionals and review all 
              content before publication. Gambling regulations vary by jurisdiction and change frequently.
            </p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-between pt-6">
        <button
          onClick={onPrev}
          className="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl font-medium hover:bg-gray-200 transition-colors"
        >
          Previous
        </button>
        
        <button
          onClick={handleContinue}
          className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg"
        >
          Continue to Generation
        </button>
      </div>
    </div>
  );
};

export default ComplianceSettings;