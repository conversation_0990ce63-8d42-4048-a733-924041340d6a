import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

const SUPPORTED_LANGUAGES = ['pt', 'es', 'en', 'de', 'fr', 'it', 'ja', 'zh'];
const DEFAULT_LANGUAGE = 'pt';

/**
 * Language Router Component
 * Ensures all URLs have language prefix: /en, /pt, /es, etc.
 */
const LanguageRouter = ({ children }) => {
  const { i18n } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const { lang } = useParams();

  useEffect(() => {
    const pathname = location.pathname;
    const segments = pathname.split('/').filter(Boolean);
    const firstSegment = segments[0];

    // Check if URL has a valid language prefix
    if (!firstSegment || !SUPPORTED_LANGUAGES.includes(firstSegment)) {
      // No language prefix, redirect to include default language
      const currentLang = i18n.language && SUPPORTED_LANGUAGES.includes(i18n.language) 
        ? i18n.language 
        : DEFAULT_LANGUAGE;
      
      const newPath = `/${currentLang}${pathname === '/' ? '' : pathname}`;
      navigate(newPath, { replace: true });
      return;
    }

    // URL has valid language prefix, update i18n if needed
    if (i18n.language !== firstSegment) {
      i18n.changeLanguage(firstSegment);
    }
  }, [location.pathname, i18n, navigate]);

  // Note: Language switching navigation is handled by LanguageSwitcher component

  // Only render children if we have a valid language in the URL
  const pathname = location.pathname;
  const segments = pathname.split('/').filter(Boolean);
  const firstSegment = segments[0];
  
  if (!firstSegment || !SUPPORTED_LANGUAGES.includes(firstSegment)) {
    return null; // Will redirect in useEffect
  }

  return children;
};

export default LanguageRouter;