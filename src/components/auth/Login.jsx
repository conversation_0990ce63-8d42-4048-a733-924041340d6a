import React, { useState } from 'react';
import { Link, useNavigate, useLocation, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import { EyeIcon, EyeSlashIcon, SparklesIcon } from '@heroicons/react/24/outline';

const Login = () => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { lang } = useParams();

  // Get the current language from URL or default to 'en' for dashboard
  const currentLang = lang || 'pt';
  const from = location.state?.from?.pathname || `/en/dashboard`;

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    setError(''); // Clear error when user types
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    const result = await login(formData.email, formData.password);

    if (result.success) {
      navigate(from, { replace: true });
    } else {
      setError(result.error);
    }

    setLoading(false);
  };

  return (
    <div className="min-h-screen casino-bg flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center">
            <div className="w-16 h-16 bg-gradient-to-r from-casino-red-600 via-casino-gold-500 to-casino-red-600 rounded-xl flex items-center justify-center shadow-lg border-2 border-casino-gold-400">
              <div className="text-white font-bold text-lg leading-none tracking-tight font-mono drop-shadow-lg">
                777
              </div>
            </div>
          </div>
          <h2 className="mt-6 text-3xl font-bold text-casino-gold-400 neon-glow font-casino">
            {t('auth.login')} Writer 777
          </h2>
          <p className="mt-2 text-sm text-casino-gold-200">
            {t('auth.dontHaveAccount')}{' '}
            <Link
              to="/register"
              className="font-medium text-casino-gold-400 hover:text-casino-gold-300 transition-colors"
            >
              {t('auth.registerButton')}
            </Link>
          </p>
        </div>

        {/* Form */}
        <div className="casino-card rounded-xl p-8 chip-shadow">
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div className="space-y-4">
              {/* Email */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-casino-gold-300">
                  {t('auth.email')}
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className="mt-1 block w-full px-3 py-2 bg-casino-dark-700 border border-casino-gold-500/30 rounded-lg shadow-sm placeholder-casino-gold-400 text-casino-gold-200 focus:outline-none focus:ring-2 focus:ring-casino-gold-500 focus:border-casino-gold-500 transition-colors"
                  placeholder={t('auth.email')}
                />
              </div>

              {/* Password */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-casino-gold-300">
                  {t('auth.password')}
                </label>
                <div className="mt-1 relative">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="current-password"
                    required
                    value={formData.password}
                    onChange={handleChange}
                    className="block w-full px-3 py-2 pr-10 bg-casino-dark-700 border border-casino-gold-500/30 rounded-lg shadow-sm placeholder-casino-gold-400 text-casino-gold-200 focus:outline-none focus:ring-2 focus:ring-casino-gold-500 focus:border-casino-gold-500 transition-colors"
                    placeholder={t('auth.password')}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="h-5 w-5 text-casino-gold-400 hover:text-casino-gold-300" />
                    ) : (
                      <EyeIcon className="h-5 w-5 text-casino-gold-400 hover:text-casino-gold-300" />
                    )}
                  </button>
                </div>
              </div>
            </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Forgot Password Link */}
          <div className="flex items-center justify-end">
            <Link
              to="/forgot-password"
              className="text-sm text-blue-600 hover:text-blue-500 transition-colors"
            >
              {t('auth.forgotPassword')}
            </Link>
          </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading}
              className="casino-button group relative w-full flex justify-center py-3 px-4 text-sm font-medium rounded-lg text-white disabled:opacity-50 disabled:cursor-not-allowed chip-shadow"
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {t('auth.loginButton')}...
                </div>
              ) : (
                t('auth.loginButton')
              )}
            </button>
          </form>
        </div>

        {/* Footer */}
        <div className="text-center">
          <p className="text-xs text-gray-500">
            {t('authPages.signInAgreement')}{' '}
            <Link to="/terms" className="text-blue-600 hover:text-blue-500">
              {t('authPages.termsOfService')}
            </Link>{' '}
            {t('common.and')}{' '}
            <Link to="/privacy" className="text-blue-600 hover:text-blue-500">
              {t('authPages.privacyPolicy')}
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
