import React, { useState, useCallback, useRef, useEffect } from 'react';
import { SparklesIcon } from '@heroicons/react/24/outline';

const Step1TopicSelection = ({ data, updateData, isGeneratingTopics }) => {
  // Initialize selectedTopics as an array, ensuring we have up to 5 topics
  const selectedTopics = data.selectedTopics || [];
  const maxTopics = 5;

  // Debouncing and click protection for topic selection
  const clickTimeoutRef = useRef(null);
  const lastClickRef = useRef(null);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (clickTimeoutRef.current) {
        clearTimeout(clickTimeoutRef.current);
      }
    };
  }, []);

  const toggleTopicSelection = useCallback((topic) => {
    // Create a unique identifier for this click
    const clickId = `topic-${topic}-${Date.now()}`;

    // Prevent rapid duplicate clicks
    if (lastClickRef.current === clickId) {
      return;
    }

    // Clear any pending timeout
    if (clickTimeoutRef.current) {
      clearTimeout(clickTimeoutRef.current);
    }

    // Set a short timeout to debounce rapid clicks
    clickTimeoutRef.current = setTimeout(() => {
      lastClickRef.current = clickId;

      const isSelected = selectedTopics.some(selected => selected.original === topic);

      if (isSelected) {
        // Remove topic
        const newSelectedTopics = selectedTopics.filter(selected => selected.original !== topic);
        updateData({ selectedTopics: newSelectedTopics });
      } else if (selectedTopics.length < maxTopics) {
        // Add topic (up to 5)
        const newSelectedTopics = [...selectedTopics, { original: topic, edited: topic }];
        updateData({ selectedTopics: newSelectedTopics });
      }

      // Clear the last click reference after a delay
      setTimeout(() => {
        lastClickRef.current = null;
      }, 100);
    }, 50); // 50ms debounce
  }, [selectedTopics, maxTopics, updateData]);

  const updateTopicText = (index, newText) => {
    const newSelectedTopics = [...selectedTopics];
    newSelectedTopics[index] = { ...newSelectedTopics[index], edited: newText };
    updateData({ selectedTopics: newSelectedTopics });
  };

  const removeSelectedTopic = (index) => {
    const newSelectedTopics = selectedTopics.filter((_, i) => i !== index);
    updateData({ selectedTopics: newSelectedTopics });
  };

  const isTopicSelected = (topic) => {
    return selectedTopics.some(selected => selected.original === topic);
  };

  // Custom topic functionality
  const [customTopic, setCustomTopic] = useState('');

  const addCustomTopic = () => {
    if (!customTopic.trim() || selectedTopics.length >= maxTopics) return;

    // Check if this custom topic already exists
    const exists = selectedTopics.some(selected =>
      selected.original.toLowerCase() === customTopic.trim().toLowerCase()
    );

    if (!exists) {
      const newSelectedTopics = [...selectedTopics, {
        original: customTopic.trim(),
        edited: customTopic.trim(),
        isCustom: true
      }];
      updateData({ selectedTopics: newSelectedTopics });
      setCustomTopic('');
    }
  };

  const handleCustomTopicKeyPress = (e) => {
    if (e.key === 'Enter') {
      addCustomTopic();
    }
  };

  // Function to get the appropriate message based on number of selected topics
  const getTopicSelectionMessage = (count) => {
    switch (count) {
      case 1:
        return {
          title: "You've selected 1 topic. For a focused piece, consider these lengths:",
          suggestions: [
            "Snippet (~500 words) for a concise overview or single idea.",
            "Pillar Page Module (~700 words) if this is a dedicated section of a larger guide.",
            "Short Post (~800 words) if the topic warrants a bit more detail."
          ]
        };
      case 2:
        return {
          title: "With 2 topics, we recommend these lengths for adequate coverage of each:",
          suggestions: [
            "Short Post (~800 words) for a balanced discussion.",
            "Medium Article (~1500 words) if each topic needs more in-depth exploration."
          ]
        };
      case 3:
        return {
          title: "You've selected 3 topics. To provide balanced depth for each, the Medium Article (~1500 words) is ideal. This allows roughly 500 words per topic.",
          suggestions: []
        };
      case 4:
        return {
          title: "For 4 topics, a more comprehensive article is needed to explore each effectively. Consider these options:",
          suggestions: [
            "Medium Article (~1500 words) if the topics are concise and highly related.",
            "Long-Form Guide (~2500 words) for more thorough exploration of each topic."
          ]
        };
      case 5:
        return {
          title: "With 5 topics, aim for a detailed and comprehensive piece. The Long-Form Guide (~2500 words) is most suitable.",
          suggestions: [
            "Tip: For best results with 5 topics, ensure they are closely related to form a cohesive and strong central narrative."
          ]
        };
      default:
        return null;
    }
  };

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4 ">Choose Your Article Focus</h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          <span className="font-semibold">Step 2 of 6:</span> Select up to {maxTopics} topics that will form the foundation of your article.
          Choose from AI-generated suggestions based on your keyword research, or add your own custom topics.
          You can edit any selected topic to perfectly match your vision.
        </p>
      </div>

      {/* Topic Generation Loading State */}
      {isGeneratingTopics && (
        <div className="bg-white border border-gray-200 shadow-sm border-2 border-gray-200 rounded-xl p-8">
          {/* Main Loading Animation */}
          <div className="text-center mb-6">
            <div className="relative mx-auto mb-4">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-gray-300 border-t-blue-500 mx-auto"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <SparklesIcon className="w-6 h-6 text-blue-600 animate-pulse" />
              </div>
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2 ">🤖 AI is generating article topics...</h3>
            <p className="text-gray-600 font-medium">Processing {(data.keywords || []).length} selected keywords</p>
          </div>

          {/* Progress Steps */}
          <div className="space-y-4 mb-6">
            <div className="bg-gray-100 rounded-lg p-4 border border-gray-300">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">1</span>
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-900">Analyzing your selected keywords</p>
                  <p className="text-sm text-gray-600">Processing {(data.keywords || []).length} keyword(s) for topic generation</p>
                </div>
                <div className="text-blue-600">✓</div>
              </div>
            </div>

            <div className="bg-gray-100 rounded-lg p-4 border border-gray-300">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center animate-pulse">
                  <span className="text-white text-sm font-bold">2</span>
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-900">Generating diverse topic angles</p>
                  <p className="text-sm text-gray-600">Creating trending, problem-focused, and comparison topics</p>
                </div>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-300 border-t-blue-500"></div>
              </div>
            </div>

            <div className="bg-gray-100 rounded-lg p-4 border border-gray-200">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center border border-gray-300">
                  <span className="text-gray-600 text-sm font-bold">3</span>
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-600">Crafting question-based topics</p>
                  <p className="text-sm text-blue-600">Generating high-intent questions and answers</p>
                </div>
                <div className="w-4 h-4 rounded-full border-2 border-gray-300"></div>
              </div>
            </div>

            <div className="bg-gray-100 rounded-lg p-4 border border-gray-200">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center border border-gray-300">
                  <span className="text-gray-600 text-sm font-bold">4</span>
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-600">Finalizing topic suggestions</p>
                  <p className="text-sm text-blue-600">Quality check and optimization for your selection</p>
                </div>
                <div className="w-4 h-4 rounded-full border-2 border-gray-300"></div>
              </div>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="bg-gray-100 rounded-lg p-4 border border-gray-300">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-600">Generation Progress</span>
              <span className="text-sm text-gray-700">Processing...</span>
            </div>
            <div className="w-full bg-gray-100 rounded-full h-3 border border-gray-300">
              <div className="bg-gradient-to-r from-blue-500 to-blue-400 h-3 rounded-full animate-pulse shadow-sm shadow-blue-500/50" style={{ width: '60%' }}></div>
            </div>
          </div>

          {/* Tips while waiting */}
          <div className="mt-6 bg-gray-100 rounded-lg p-4 border border-gray-300">
            <h4 className="font-medium text-gray-600 mb-2">💡 While you wait:</h4>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>• AI is analyzing your keywords to find the best topic angles</li>
              <li>• Multiple topic types are being generated for variety</li>
              <li>• Each topic is optimized for engagement and SEO potential</li>
              <li>• You'll be able to select up to {maxTopics} topics for your article</li>
            </ul>
          </div>
        </div>
      )}

      {/* Main Content - Hidden during topic generation */}
      {!isGeneratingTopics && (
        <>

      {/* Selected Keywords Display */}
      {data.keywords && data.keywords.length > 0 && (
        <div className="bg-white border border-gray-200 shadow-sm rounded-xl p-6 border border-gray-300">
          <h3 className="text-lg font-semibold text-gray-900 mb-3 ">📋 Your Selected Keywords</h3>
          <div className="flex flex-wrap gap-2">
            {data.keywords.map((keyword, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-blue-500/20 text-gray-900 rounded-full text-sm font-medium border border-gray-200"
              >
                {keyword}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Connection Error Display */}
      {data.topicSuggestions && data.topicSuggestions.error && (
        <div className="bg-white border border-gray-200 shadow-sm rounded-xl p-8 border border-red-500/50 text-center bg-gradient-to-r from-red-900/20 to-red-800/20">
          <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4 border border-red-500/50">
            <span className="text-2xl">⚠️</span>
          </div>
          <h3 className="text-xl font-semibold text-red-300 mb-2 ">Connection Issue</h3>
          <p className="text-red-200 mb-4">
            {data.topicSuggestions.errorMessage || 'Server is encountering connection issues'}
          </p>
          <p className="text-red-300 text-sm">
            You can still enter a custom topic below to continue.
          </p>
        </div>
      )}

      {/* Topic Ideas Section */}
      {data.topicSuggestions && !data.topicSuggestions.error && Object.keys(data.topicSuggestions).length > 0 && (
        <div className="space-y-8">
          {/* AI Generated Topics - Clustered Display */}
          {data.topicSuggestions.hasClusters && data.topicSuggestions.clustered && Object.keys(data.topicSuggestions.clustered).length > 0 ? (
            <div className="bg-white border border-gray-200 shadow-sm rounded-xl p-6 border border-gray-300">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900 ">🤖 AI-Generated Article Topics</h3>
                <div className="text-sm text-gray-900 bg-blue-500/20 px-3 py-1 rounded-full border border-gray-200">
                  Based on {data.topicSuggestions.generatedFromKeywords?.length || 0} selected keywords
                </div>
              </div>

              {/* Clustered Topics Display */}
              <div className="space-y-8">
                {Object.entries(data.topicSuggestions.clustered).map(([clusterName, topics], clusterIndex) => (
                  <div key={clusterIndex} className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-blue-500/30 rounded-lg flex items-center justify-center border border-gray-200">
                          <span className="text-gray-600 font-bold text-sm">
                            {clusterIndex + 1}
                          </span>
                        </div>
                      </div>
                      <h4 className="text-lg font-semibold text-gray-900">{clusterName}</h4>
                      <div className="flex-1 h-px bg-blue-500/30"></div>
                      <span className="text-sm text-gray-900 bg-blue-500/20 px-2 py-1 rounded border border-gray-200">
                        {topics.length} topics
                      </span>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 ml-11">
                      {topics.map((topic, topicIndex) => {
                        const isSelected = isTopicSelected(topic);
                        const isDisabled = !isSelected && selectedTopics.length >= maxTopics;

                        return (
                          <button
                            key={`${clusterIndex}-${topicIndex}`}
                            onClick={() => toggleTopicSelection(topic)}
                            disabled={isDisabled}
                            className={`p-3 rounded-lg border text-left transition-all hover:shadow-md hover:shadow-blue-500/20 relative ${
                              isSelected
                                ? 'border-blue-500 bg-blue-500/20 shadow-md shadow-blue-500/20'
                                : isDisabled
                                ? 'border-gray-200 bg-gray-100 opacity-50 cursor-not-allowed'
                                : 'border-gray-300 bg-gray-200/50 hover:border-blue-300'
                            }`}
                          >
                            <div className="flex items-start space-x-3">
                              <div className="flex-shrink-0 mt-1">
                                <div className={`w-5 h-5 rounded flex items-center justify-center ${
                                  isSelected
                                    ? 'bg-blue-500 text-white'
                                    : 'bg-blue-500/20 border border-gray-200'
                                }`}>
                                  <span className={`font-bold text-xs ${
                                    isSelected ? 'text-white' : 'text-gray-600'
                                  }`}>
                                    {isSelected ? '✓' : '✨'}
                                  </span>
                                </div>
                              </div>
                              <div className="flex-1 min-w-0">
                                <h5 className={`font-medium text-sm leading-tight ${
                                  isSelected ? 'text-gray-900' : isDisabled ? 'text-gray-600' : 'text-gray-600'
                                }`}>
                                  {topic}
                                </h5>
                              </div>
                              {isDisabled && (
                                <div className="absolute top-2 right-2 text-xs text-blue-600 bg-gray-100 px-2 py-1 rounded border border-gray-300">
                                  Max {maxTopics}
                                </div>
                              )}
                            </div>
                          </button>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            /* Fallback: Non-clustered AI Generated Topics */
            data.topicSuggestions.aiGenerated && data.topicSuggestions.aiGenerated.length > 0 && (
              <div className="bg-white border border-gray-200 shadow-sm rounded-xl p-6 border border-gray-300">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-gray-900 ">🤖 AI-Generated Article Topics</h3>
                  <div className="text-sm text-gray-900 bg-blue-500/20 px-3 py-1 rounded-full border border-gray-200">
                    Based on {data.topicSuggestions.generatedFromKeywords?.length || 0} selected keywords
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {data.topicSuggestions.aiGenerated.map((topic, index) => {
                    const isSelected = isTopicSelected(topic);
                    const canSelect = !isSelected && selectedTopics.length < maxTopics;
                    const isDisabled = !isSelected && selectedTopics.length >= maxTopics;

                    return (
                      <button
                        key={`ai-${index}`}
                        onClick={() => toggleTopicSelection(topic)}
                        disabled={isDisabled}
                        className={`p-4 rounded-lg border text-left transition-all hover:shadow-md hover:shadow-blue-500/20 relative ${
                          isSelected
                            ? 'border-blue-500 bg-blue-500/20 shadow-md shadow-blue-500/20'
                            : isDisabled
                            ? 'border-gray-200 bg-gray-100 opacity-50 cursor-not-allowed'
                            : 'border-gray-300 bg-gray-200/50 hover:border-blue-300'
                        }`}
                      >
                        <div className="flex items-start space-x-3">
                          <div className="flex-shrink-0 mt-1">
                            <div className={`w-6 h-6 rounded flex items-center justify-center ${
                              isSelected
                                ? 'bg-blue-500 text-white'
                                : 'bg-blue-500/20 border border-gray-200'
                            }`}>
                              <span className={`font-bold text-sm ${
                                isSelected ? 'text-white' : 'text-gray-600'
                              }`}>
                                {isSelected ? '✓' : '✨'}
                              </span>
                            </div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className={`font-medium text-sm leading-tight ${
                              isSelected ? 'text-gray-900' : isDisabled ? 'text-gray-600' : 'text-gray-600'
                            }`}>
                              {topic}
                            </h4>
                          </div>
                          {isDisabled && (
                            <div className="absolute top-2 right-2 text-xs text-blue-600 bg-gray-100 px-2 py-1 rounded border border-gray-300">
                              Max {maxTopics}
                            </div>
                          )}
                        </div>
                      </button>
                    );
                  })}
                </div>
              </div>
            )
          )}

          {/* Original Topic Ideas */}
          {(data.topicSuggestions.trendingAngles || data.topicSuggestions.problemFocused || data.topicSuggestions.comparisons) && (
            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-6 ">💡 Additional Topic Ideas</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Show trending angles, problem-focused, and comparisons */}
                {[
                  ...(data.topicSuggestions.trendingAngles || []),
                  ...(data.topicSuggestions.problemFocused || []),
                  ...(data.topicSuggestions.comparisons || [])
                ].slice(0, 6).map((topic, index) => {
                  const isSelected = isTopicSelected(topic);
                  const isDisabled = !isSelected && selectedTopics.length >= maxTopics;

                  return (
                    <button
                      key={index}
                      onClick={() => toggleTopicSelection(topic)}
                      disabled={isDisabled}
                      className={`p-4 rounded-lg border text-left transition-all hover:shadow-md hover:shadow-blue-500/20 relative ${
                        isSelected
                          ? 'border-blue-500 bg-blue-500/20 shadow-md shadow-blue-500/20'
                          : isDisabled
                          ? 'border-gray-200 bg-gray-100 opacity-50 cursor-not-allowed'
                          : 'border-gray-300 bg-gray-200/50 hover:border-blue-300'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 mt-1">
                          <div className={`w-6 h-6 rounded flex items-center justify-center ${
                            isSelected
                              ? 'bg-blue-500 text-white'
                              : 'bg-blue-500/20 border border-gray-200'
                          }`}>
                            <span className={`font-bold text-sm ${
                              isSelected ? 'text-white' : 'text-gray-600'
                            }`}>
                              {isSelected ? '✓' : '#'}
                            </span>
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className={`font-medium text-sm leading-tight ${
                            isSelected ? 'text-gray-900' : isDisabled ? 'text-gray-600' : 'text-gray-600'
                          }`}>
                            {topic}
                          </h4>
                        </div>
                        {isDisabled && (
                          <div className="absolute top-2 right-2 text-xs text-blue-600 bg-gray-100 px-2 py-1 rounded border border-gray-300">
                            Max {maxTopics}
                          </div>
                        )}
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
          )}

          {/* Questions Section */}
          {data.topicSuggestions.highIntentQuestions && data.topicSuggestions.highIntentQuestions.length > 0 && (
            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-6 ">❓ Question-Based Topics</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Show only high-intent questions */}
                {data.topicSuggestions.highIntentQuestions.slice(0, 6).map((question, index) => {
                  const isSelected = isTopicSelected(question);
                  const isDisabled = !isSelected && selectedTopics.length >= maxTopics;

                  return (
                    <button
                      key={`q-${index}`}
                      onClick={() => toggleTopicSelection(question)}
                      disabled={isDisabled}
                      className={`p-4 rounded-lg border text-left transition-all hover:shadow-md hover:shadow-blue-500/20 relative ${
                        isSelected
                          ? 'border-blue-500 bg-blue-500/20 shadow-md shadow-blue-500/20'
                          : isDisabled
                          ? 'border-gray-200 bg-gray-100 opacity-50 cursor-not-allowed'
                          : 'border-gray-300 bg-gray-200/50 hover:border-blue-300'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 mt-1">
                          <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                            isSelected
                              ? 'bg-blue-500 text-white'
                              : 'bg-blue-500/20 border border-gray-200'
                          }`}>
                            <span className={`font-bold text-sm ${
                              isSelected ? 'text-white' : 'text-gray-600'
                            }`}>
                              {isSelected ? '✓' : '?'}
                            </span>
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className={`text-sm leading-tight ${
                            isSelected ? 'text-gray-900' : isDisabled ? 'text-gray-600' : 'text-gray-600'
                          }`}>
                            {question}
                          </p>
                        </div>
                        {isDisabled && (
                          <div className="absolute top-2 right-2 text-xs text-blue-600 bg-gray-100 px-2 py-1 rounded border border-gray-300">
                            Max {maxTopics}
                          </div>
                        )}
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      )}

      {/* No Topics Available */}
      {(!data.topicSuggestions || (Object.keys(data.topicSuggestions).length === 0) || (Object.keys(data.topicSuggestions).length === 1 && data.topicSuggestions.error)) && !data.topicSuggestions?.error && (
        <div className="bg-white border border-gray-200 shadow-sm rounded-xl p-8 border border-gray-300 text-center">
          <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4 border border-gray-200">
            <span className="text-2xl">🔍</span>
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-3 ">No Topics Available</h3>
          <p className="text-gray-900 mb-4 text-lg">
            Please go back to Step 1 to research keywords and generate article topics.
          </p>
        </div>
      )}

      {/* Selected Topics Section - Displayed at bottom */}
      {selectedTopics.length > 0 && (
        <div className="bg-white border border-gray-200 shadow-sm rounded-xl p-6 border border-gray-300">
          {/* Topic Selection Message */}
          {(() => {
            const message = getTopicSelectionMessage(selectedTopics.length);
            return message ? (
              <div className="mb-6 p-4 bg-blue-500/20 rounded-lg border border-gray-200">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-1">
                    <div className="w-8 h-8 bg-blue-500/30 rounded-full flex items-center justify-center border border-gray-200">
                      <span className="text-gray-700 font-bold text-sm">💡</span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <p className="text-gray-900 font-medium mb-2">{message.title}</p>
                    {message.suggestions.length > 0 && (
                      <ul className="text-gray-600 text-sm space-y-1">
                        {message.suggestions.map((suggestion, index) => (
                          <li key={index}>• {suggestion}</li>
                        ))}
                      </ul>
                    )}
                  </div>
                </div>
              </div>
            ) : null;
          })()}

          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-bold text-gray-900 ">✅ Selected Topics ({selectedTopics.length}/{maxTopics})</h3>
          </div>
          <div className="space-y-3">
            {selectedTopics.map((topicObj, index) => (
              <div key={index} className="bg-gray-200/50 rounded-lg p-4 border border-gray-300">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-1">
                    <div className="w-6 h-6 bg-blue-500/30 rounded flex items-center justify-center border border-gray-200">
                      <span className="text-gray-700 font-bold text-sm">{index + 1}</span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <textarea
                      value={topicObj.edited}
                      onChange={(e) => updateTopicText(index, e.target.value)}
                      className="w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 resize-none placeholder-gray-500"
                      rows={2}
                      placeholder="Edit your topic..."
                    />
                  </div>
                  <button
                    onClick={() => removeSelectedTopic(index)}
                    className="flex-shrink-0 p-1 text-red-400 hover:text-red-300 hover:bg-red-900/20 rounded"
                    title="Remove topic"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Custom Topic Input Section */}
      <div className="bg-white border border-gray-200 shadow-sm rounded-xl p-6 border border-gray-300">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-bold text-gray-900 ">✏️ Add Your Own Topic</h3>
          <div className="text-sm text-gray-900 bg-blue-500/20 px-3 py-1 rounded-full border border-gray-200">
            {selectedTopics.length}/{maxTopics} topics selected
          </div>
        </div>
        <p className="text-sm text-gray-600 mb-4">
          Have a specific topic in mind? Add your own custom topic to the selection.
        </p>

        <div className="flex space-x-3">
          <div className="flex-1">
            <input
              type="text"
              value={customTopic}
              onChange={(e) => setCustomTopic(e.target.value)}
              onKeyPress={handleCustomTopicKeyPress}
              placeholder="Enter your custom article topic..."
              className="w-full px-4 py-3 bg-gray-200 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-500"
              disabled={selectedTopics.length >= maxTopics}
            />
          </div>
          <button
            onClick={addCustomTopic}
            disabled={!customTopic.trim() || selectedTopics.length >= maxTopics}
            className="bg-blue-500 hover:bg-blue-600 px-6 py-3 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed font-medium"
          >
            Add Topic
          </button>
        </div>

        {selectedTopics.length >= maxTopics && (
          <p className="text-sm text-gray-700 mt-2">
            Maximum {maxTopics} topics reached. Remove a topic to add a new one.
          </p>
        )}
      </div>
        </>
      )}
    </div>
  );
};

export default Step1TopicSelection;
