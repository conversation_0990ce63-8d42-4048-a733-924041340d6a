import React, { useState } from 'react';
import { ChevronRightIcon, ChevronLeftIcon, SparklesIcon } from '@heroicons/react/24/outline';
import StepIndicator from './StepIndicator';
import Step0KeywordResearch from './Step0KeywordResearch';
import Step1TopicSelection from './Step1TopicSelection';
import Step3Sources from './Step3Sources';
import Step3Product from './Step3Product';
import Step4EEAT from './Step4EEAT';
import Step4Parameters from './Step4Parameters';
import Step5Generation from './Step5Generation';
import AuthenticatedLayout from './layout/AuthenticatedLayout';

const STEPS = [
  { id: 1, title: 'Keyword Research', subtitle: 'Discover content opportunities', component: Step0KeywordResearch, icon: '🔍' },
  { id: 2, title: 'Choose Topics', subtitle: 'Define your article focus', component: Step1TopicSelection, icon: '🎯' },
  { id: 3, title: 'Gather Sources', subtitle: 'Add supporting references', component: Step3Sources, icon: '📚' },
  { id: 4, title: 'Product Integration', subtitle: 'Optional product mentions', component: Step3Product, icon: '🏷️' },
  { id: 5, title: 'Authority Profile', subtitle: 'Establish your expertise', component: Step4EEAT, icon: '👤' },
  { id: 6, title: 'Style & Format', subtitle: 'Customize writing style', component: Step4Parameters, icon: '⚙️' },
  { id: 7, title: 'Generate Article', subtitle: 'Create your content', component: Step5Generation, icon: '✨' },
];

function ArticleGenerator() {
  const [currentStep, setCurrentStep] = useState(0); // Index 0 = Step 1
  const [isGeneratingTopics, setIsGeneratingTopics] = useState(false);
  const [articleData, setArticleData] = useState({
    keywords: [],
    selectedTopic: '',
    selectedTopics: [], // New: array of selected topics with original and edited text
    topicSuggestions: [],
    keywordResearchData: null, // Store keyword research results temporarily
    keywordResearchSelections: [], // Store selections from keyword research
    sources: [], // Legacy: keep for backward compatibility
    topicSources: {}, // New: sources organized by topic { topicId: [sources] }
    productInfo: {
      name: '',
      link: '',
      description: '',
      features: []
    },
    eeatProfile: {
      authorName: '',
      authorBio: '',
      authorCredentials: '',
      experienceYears: '',
      relevantExperience: ''
    },
    outputParameters: {
      contentType: 'generic',
      tonality: 'informative',
      length: 'medium_article',
      format: 'markdown',
      includeOutline: false,
      includeSources: true,
      includeCallToAction: false,
      callToActionText: '',
      targetAudience: '',
      writingStyle: 'Informative',
      seoFocus: true
    },
    generatedArticle: ''
  });

  const updateArticleData = (updates) => {
    setArticleData(prev => ({
      ...prev,
      ...updates
    }));
  };

  const nextStep = () => {
    if (currentStep < STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (stepIndex) => {
    setCurrentStep(stepIndex);
  };

  const CurrentStepComponent = STEPS[currentStep].component;

  const canProceedToNext = () => {
    switch (currentStep) {
      case 0:
        return true; // Keyword research is optional, can proceed anytime
      case 1:
        return articleData.selectedTopics && articleData.selectedTopics.length > 0;
      case 2:
        return true; // Sources are optional
      case 3:
        return true; // Product info is optional
      case 4:
        return true; // Parameters have defaults
      case 5:
        return true; // Final step
      default:
        return false;
    }
  };

  return (
    <AuthenticatedLayout>
      <div className="min-h-screen" style={{backgroundColor: '#3B4A6B'}}>
        {/* Status indicator */}
        <div className="w-full px-8 py-4">
          <div className="flex items-center justify-end">
            <div className="flex items-center space-x-2 px-4 py-2 bg-slate-700 rounded-full border border-slate-500">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-white">AI Ready</span>
            </div>
          </div>
        </div>

        {/* Main Content - Full Width */}
        <main className="w-full py-4">
        <div className="flex min-h-screen">
          {/* Left Sidebar - Step Indicator */}
          <div className="w-64 flex-shrink-0 px-4">
            <div className="sticky top-32">
              <StepIndicator
                steps={STEPS}
                currentStep={currentStep}
                onStepClick={goToStep}
              />
            </div>
          </div>

          {/* Main Content Area - Full Width */}
          <div className="flex-1 px-6">
            {/* Clean Content Container */}
            <div className="rounded-lg shadow-lg border border-slate-600 overflow-hidden" style={{backgroundColor: '#3B4A6B'}}>
              {/* Clean Header */}
              <div className="px-8 py-6 border-b border-slate-600" style={{backgroundColor: '#3B4A6B'}}>
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold text-white mb-1">
                      {STEPS[currentStep].title}
                    </h2>
                    <p className="text-white font-medium">
                      {STEPS[currentStep].subtitle}
                    </p>
                  </div>
                  <div className="text-4xl text-white">
                    {STEPS[currentStep].icon}
                  </div>
                </div>
              </div>

              {/* Step Content */}
              <div className="relative p-8" style={{backgroundColor: '#3B4A6B'}}>
                <CurrentStepComponent
                  data={articleData}
                  updateData={updateArticleData}
                  onNext={nextStep}
                  onPrev={prevStep}
                  isFirstStep={currentStep === 0}
                  isLastStep={currentStep === STEPS.length - 1}
                  isGeneratingTopics={isGeneratingTopics}
                  setIsGeneratingTopics={setIsGeneratingTopics}
                />
              </div>

              {/* Clean Navigation */}
              <div className="px-8 py-6 border-t border-slate-600" style={{backgroundColor: '#3B4A6B'}}>
                <div className="flex justify-between items-center">
                  <button
                    onClick={prevStep}
                    disabled={currentStep === 0}
                    className="flex items-center space-x-2 px-6 py-3 bg-slate-600 text-white rounded-lg font-medium hover:bg-slate-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 border border-slate-500"
                  >
                    <ChevronLeftIcon className="w-4 h-4" />
                    <span>Previous</span>
                  </button>

                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-white font-medium">
                      Step {currentStep + 1} of {STEPS.length}
                    </span>
                    <div className="w-32 bg-slate-200 rounded-full h-2 border border-slate-300">
                      <div
                        className="bg-orange-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${((currentStep + 1) / STEPS.length) * 100}%` }}
                      ></div>
                    </div>
                  </div>

                  <button
                    onClick={nextStep}
                    disabled={currentStep === STEPS.length - 1 || !canProceedToNext()}
                    className="flex items-center space-x-2 px-6 py-3 bg-orange-500 text-white font-medium rounded-lg hover:bg-orange-400 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    <span>Next</span>
                    <ChevronRightIcon className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      </div>
    </AuthenticatedLayout>
  );
}

export default ArticleGenerator;
