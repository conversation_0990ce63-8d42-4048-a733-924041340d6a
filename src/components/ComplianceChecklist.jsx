import React, { useState, useEffect } from 'react';
import { CheckIcon, ExclamationTriangleIcon, ShieldCheckIcon, DocumentCheckIcon } from '@heroicons/react/24/outline';

const COMPLIANCE_CHECKLIST_ITEMS = {
  factual_accuracy: {
    id: 'factual_accuracy',
    title: 'Factual Accuracy Verified Against Sources',
    description: 'All claims, statistics, and information have been verified against reliable sources',
    category: 'accuracy',
    mandatory: true,
    icon: DocumentCheckIcon
  },
  jurisdictional_compliance: {
    id: 'jurisdictional_compliance',
    title: 'Jurisdictional Compliance Rules Met',
    description: 'Content meets all regulatory requirements for the target jurisdiction',
    category: 'compliance',
    mandatory: true,
    icon: ShieldCheckIcon
  },
  responsible_gaming: {
    id: 'responsible_gaming',
    title: 'Responsible Gaming Disclaimers Included and Correct',
    description: 'Appropriate responsible gaming messaging and disclaimers are present',
    category: 'compliance',
    mandatory: true,
    icon: ShieldCheckIcon
  },
  tone_language: {
    id: 'tone_language',
    title: 'Tone and Language are Responsible and Non-Predatory',
    description: 'Language avoids predatory terms and promotes responsible engagement',
    category: 'content',
    mandatory: true,
    icon: CheckIcon
  },
  seo_polish: {
    id: 'seo_polish',
    title: 'Final SEO and Style Polish Complete',
    description: 'Content is optimized for search engines and follows style guidelines',
    category: 'quality',
    mandatory: false,
    icon: CheckIcon
  },
  legal_review: {
    id: 'legal_review',
    title: 'Legal Review Completed (if required)',
    description: 'Content has been reviewed by legal professionals familiar with gambling regulations',
    category: 'compliance',
    mandatory: false,
    icon: ShieldCheckIcon
  },
  age_verification: {
    id: 'age_verification',
    title: 'Age Verification Requirements Included',
    description: 'Content includes appropriate age verification messaging (18+)',
    category: 'compliance',
    mandatory: true,
    icon: ShieldCheckIcon
  },
  source_citations: {
    id: 'source_citations',
    title: 'Sources Properly Cited and Linked',
    description: 'All referenced sources are properly attributed and linked where appropriate',
    category: 'accuracy',
    mandatory: true,
    icon: DocumentCheckIcon
  },
  no_guarantees: {
    id: 'no_guarantees',
    title: 'No Guaranteed Outcome Claims',
    description: 'Content avoids promising guaranteed wins or outcomes',
    category: 'compliance',
    mandatory: true,
    icon: ExclamationTriangleIcon
  },
  support_resources: {
    id: 'support_resources',
    title: 'Support Resources and Helplines Included',
    description: 'Appropriate gambling support resources and helplines are mentioned',
    category: 'compliance',
    mandatory: true,
    icon: ShieldCheckIcon
  }
};

const ComplianceChecklist = ({ 
  data, 
  updateData, 
  onComplianceComplete, 
  isVisible = true,
  contentType = '',
  jurisdiction = 'international' 
}) => {
  const [checkedItems, setCheckedItems] = useState(data?.complianceChecklist || {});
  const [showDetails, setShowDetails] = useState(false);
  const [lastReviewDate, setLastReviewDate] = useState(data?.lastComplianceReview || null);

  // Filter checklist items based on content type and jurisdiction
  const getRelevantChecklistItems = () => {
    let items = { ...COMPLIANCE_CHECKLIST_ITEMS };
    
    // Content type specific adjustments
    if (contentType === 'brand_copy') {
      // Brand copy doesn't need source citations as much
      items.source_citations.mandatory = false;
    } else if (contentType === 'game_guide') {
      // Game guides should emphasize educational aspects
      items.no_guarantees.description = 'Content focuses on education rather than promising guaranteed success';
    } else if (contentType === 'industry_news') {
      // News articles need stronger source verification
      items.source_citations.mandatory = true;
      items.factual_accuracy.description = 'All news claims and quotes have been verified against primary sources';
    }

    // Jurisdiction specific adjustments
    if (jurisdiction !== 'international') {
      items.jurisdictional_compliance.description = `Content meets all regulatory requirements for ${jurisdiction}`;
    }

    return items;
  };

  const relevantItems = getRelevantChecklistItems();
  const mandatoryItems = Object.values(relevantItems).filter(item => item.mandatory);
  const optionalItems = Object.values(relevantItems).filter(item => !item.mandatory);

  const handleItemCheck = (itemId, checked) => {
    const newCheckedItems = {
      ...checkedItems,
      [itemId]: {
        checked,
        timestamp: new Date().toISOString(),
        reviewer: 'current_user' // In a real app, this would be the actual user
      }
    };
    
    setCheckedItems(newCheckedItems);
    
    // Update parent data
    updateData({
      complianceChecklist: newCheckedItems,
      lastComplianceReview: new Date().toISOString()
    });

    // Check if all mandatory items are completed
    const allMandatoryCompleted = mandatoryItems.every(item => 
      newCheckedItems[item.id]?.checked === true
    );

    if (allMandatoryCompleted && onComplianceComplete) {
      onComplianceComplete(true);
    }
  };

  const getMandatoryCompletionStatus = () => {
    const completedMandatory = mandatoryItems.filter(item => 
      checkedItems[item.id]?.checked === true
    ).length;
    
    return {
      completed: completedMandatory,
      total: mandatoryItems.length,
      percentage: Math.round((completedMandatory / mandatoryItems.length) * 100)
    };
  };

  const getOptionalCompletionStatus = () => {
    const completedOptional = optionalItems.filter(item => 
      checkedItems[item.id]?.checked === true
    ).length;
    
    return {
      completed: completedOptional,
      total: optionalItems.length,
      percentage: optionalItems.length > 0 ? Math.round((completedOptional / optionalItems.length) * 100) : 100
    };
  };

  const mandatoryStatus = getMandatoryCompletionStatus();
  const optionalStatus = getOptionalCompletionStatus();
  const allMandatoryCompleted = mandatoryStatus.completed === mandatoryStatus.total;

  if (!isVisible) return null;

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <ShieldCheckIcon className="w-6 h-6 text-blue-600" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Compliance & Quality Checklist
              </h3>
              <p className="text-sm text-gray-600">
                Complete all mandatory items before publishing
              </p>
            </div>
          </div>
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="text-sm text-blue-600 hover:text-blue-700 font-medium"
          >
            {showDetails ? 'Hide Details' : 'Show Details'}
          </button>
        </div>

        {/* Progress Summary */}
        <div className="mt-4 space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">
              Mandatory Items ({mandatoryStatus.completed}/{mandatoryStatus.total})
            </span>
            <span className={`text-sm font-semibold ${
              allMandatoryCompleted ? 'text-green-600' : 'text-orange-600'
            }`}>
              {mandatoryStatus.percentage}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                allMandatoryCompleted ? 'bg-green-500' : 'bg-orange-500'
              }`}
              style={{ width: `${mandatoryStatus.percentage}%` }}
            />
          </div>
          
          {optionalItems.length > 0 && (
            <>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">
                  Optional Items ({optionalStatus.completed}/{optionalStatus.total})
                </span>
                <span className="text-sm font-semibold text-blue-600">
                  {optionalStatus.percentage}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${optionalStatus.percentage}%` }}
                />
              </div>
            </>
          )}
        </div>

        {/* Completion Status */}
        {allMandatoryCompleted ? (
          <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <CheckIcon className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium text-green-800">
                All mandatory compliance checks completed!
              </span>
            </div>
          </div>
        ) : (
          <div className="mt-3 p-3 bg-orange-50 border border-orange-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <ExclamationTriangleIcon className="w-5 h-5 text-orange-600" />
              <span className="text-sm font-medium text-orange-800">
                {mandatoryStatus.total - mandatoryStatus.completed} mandatory items remaining
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Detailed Checklist */}
      {showDetails && (
        <div className="px-6 py-4 space-y-6">
          {/* Mandatory Items */}
          <div>
            <h4 className="text-md font-semibold text-gray-900 mb-3 flex items-center">
              <ExclamationTriangleIcon className="w-4 h-4 text-red-500 mr-2" />
              Mandatory Items
            </h4>
            <div className="space-y-3">
              {mandatoryItems.map(item => (
                <ChecklistItem
                  key={item.id}
                  item={item}
                  checked={checkedItems[item.id]?.checked || false}
                  timestamp={checkedItems[item.id]?.timestamp}
                  onCheck={(checked) => handleItemCheck(item.id, checked)}
                  mandatory={true}
                />
              ))}
            </div>
          </div>

          {/* Optional Items */}
          {optionalItems.length > 0 && (
            <div>
              <h4 className="text-md font-semibold text-gray-900 mb-3 flex items-center">
                <CheckIcon className="w-4 h-4 text-blue-500 mr-2" />
                Optional Items
              </h4>
              <div className="space-y-3">
                {optionalItems.map(item => (
                  <ChecklistItem
                    key={item.id}
                    item={item}
                    checked={checkedItems[item.id]?.checked || false}
                    timestamp={checkedItems[item.id]?.timestamp}
                    onCheck={(checked) => handleItemCheck(item.id, checked)}
                    mandatory={false}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Last Review Info */}
          {lastReviewDate && (
            <div className="pt-4 border-t border-gray-200">
              <p className="text-xs text-gray-500">
                Last reviewed: {new Date(lastReviewDate).toLocaleString()}
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Individual checklist item component
const ChecklistItem = ({ item, checked, timestamp, onCheck, mandatory }) => {
  const IconComponent = item.icon;
  
  return (
    <div className={`flex items-start space-x-3 p-3 rounded-lg border ${
      checked 
        ? 'bg-green-50 border-green-200' 
        : mandatory 
          ? 'bg-red-50 border-red-200' 
          : 'bg-gray-50 border-gray-200'
    }`}>
      <div className="flex-shrink-0 mt-1">
        <input
          type="checkbox"
          checked={checked}
          onChange={(e) => onCheck(e.target.checked)}
          className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
        />
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-start space-x-2">
          <IconComponent className={`w-4 h-4 mt-0.5 flex-shrink-0 ${
            checked ? 'text-green-600' : mandatory ? 'text-red-500' : 'text-gray-400'
          }`} />
          <div className="flex-1">
            <h5 className={`text-sm font-medium ${
              checked ? 'text-green-800' : 'text-gray-900'
            }`}>
              {item.title}
              {mandatory && (
                <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                  Required
                </span>
              )}
            </h5>
            <p className={`text-xs mt-1 ${
              checked ? 'text-green-700' : 'text-gray-600'
            }`}>
              {item.description}
            </p>
            {checked && timestamp && (
              <p className="text-xs text-green-600 mt-1">
                ✓ Completed {new Date(timestamp).toLocaleString()}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComplianceChecklist;
