import React, { useState, useEffect } from 'react';
import { Link, useSearchParams, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  MagnifyingGlassIcon,
  ClockIcon,
  UserIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import { API_CONFIG } from '../../config/api';
import { formatDate, calculateReadingTime } from '../../utils/articleUtils';
import {
  enrichArticlesWithContentType,
  getContentTypeCardStyle,
  getContentTypeFeature
} from '../../utils/contentTypeUtils';
import MainNavigation from '../layout/MainNavigation';
import { getContentTypeImage } from '../../utils/imageUtils';
import { useCurrentLanguage } from '../../utils/languageUtils';

const PublicArticles = () => {
  const { t } = useTranslation();
  const currentLanguage = useCurrentLanguage();
  const [searchParams, setSearchParams] = useSearchParams();
  const { contentType: urlContentType } = useParams();
  const [articles, setArticles] = useState([]);
  const [contentTypes, setContentTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({});
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '');
  // Use URL param contentType if available, otherwise use search param type
  const [selectedContentType, setSelectedContentType] = useState(
    urlContentType || searchParams.get('type') || ''
  );
  const [currentPage, setCurrentPage] = useState(parseInt(searchParams.get('page')) || 1);

  // Update selectedContentType when URL param changes
  useEffect(() => {
    if (urlContentType && urlContentType !== selectedContentType) {
      setSelectedContentType(urlContentType);
      setCurrentPage(1);
    }
  }, [urlContentType]);

  useEffect(() => {
    fetchArticles();
    fetchContentTypes();
  }, [currentPage, selectedContentType, searchTerm, currentLanguage]);

  const fetchArticles = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '12',
        language: currentLanguage
      });

      if (selectedContentType) {
        params.append('contentType', selectedContentType);
      }

      if (searchTerm) {
        params.append('search', searchTerm);
      }

      const response = await fetch(`${API_CONFIG.BASE_URL}/api/public/articles?${params}`);
      
      if (response.ok) {
        const data = await response.json();
        // Enrich articles with content type information
        const enrichedArticles = enrichArticlesWithContentType(data.articles);
        setArticles(enrichedArticles);
        setPagination(data.pagination);
      } else {
        console.error('Failed to fetch articles');
      }
    } catch (error) {
      console.error('Error fetching articles:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchContentTypes = async () => {
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}/api/public/content-types?language=${currentLanguage}`);
      
      if (response.ok) {
        const data = await response.json();
        setContentTypes(data.contentTypes);
      }
    } catch (error) {
      console.error('Error fetching content types:', error);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    updateSearchParams({ search: searchTerm, page: 1 });
  };

  const handleContentTypeFilter = (contentType) => {
    setSelectedContentType(contentType);
    setCurrentPage(1);
    updateSearchParams({ type: contentType, page: 1 });
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    updateSearchParams({ page });
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const updateSearchParams = (updates) => {
    const newParams = new URLSearchParams(searchParams);
    
    Object.entries(updates).forEach(([key, value]) => {
      if (value) {
        newParams.set(key, value);
      } else {
        newParams.delete(key);
      }
    });

    setSearchParams(newParams);
  };



  if (loading && articles.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-indigo-900">
        <MainNavigation />
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-400 mx-auto"></div>
            <p className="mt-4 text-slate-300">{t('articles.loadingArticles')}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-indigo-900">
      <MainNavigation />
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-800/20 to-cyan-800/20 backdrop-blur-xl border-b border-slate-500/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="inline-flex items-center space-x-2 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 text-slate-400 px-6 py-3 rounded-full text-sm font-medium mb-6 border border-slate-500/30">
              <SparklesIcon className="w-5 h-5" />
              <span>{t('articles.expertContent')}</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-4 bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
              {selectedContentType 
                ? t(`contentTypes.${selectedContentType}`)
                : t('articles.title')}
            </h1>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto">
              {selectedContentType 
                ? t('articles.categorySubtitle', { category: t(`contentTypes.${selectedContentType}`) })
                : t('articles.subtitle')}
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          {/* Search Bar */}
          <form onSubmit={handleSearch} className="flex gap-4">
            <div className="flex-1 relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder={t('articles.searchPlaceholder')}
                className="w-full pl-10 pr-4 py-3 bg-white/5 border border-slate-500/30 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-slate-600 backdrop-blur-xl"
              />
            </div>
            <button
              type="submit"
              className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 px-6 py-3 text-white rounded-lg font-medium transition-all duration-300"
            >
              {t('articles.searchButton')}
            </button>
          </form>

          {/* Content Type Filters */}
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => handleContentTypeFilter('')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                !selectedContentType
                  ? 'bg-cyan-600 text-white'
                  : 'bg-white/5 text-slate-300 hover:bg-cyan-600/50 backdrop-blur-xl'
              }`}
            >
              {t('articles.allTypes')}
            </button>
            {contentTypes.map((type) => (
              <button
                key={type.content_type}
                onClick={() => handleContentTypeFilter(type.content_type)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedContentType === type.content_type
                    ? 'bg-cyan-600 text-white'
                    : 'bg-white/5 text-slate-300 hover:bg-cyan-600/50 backdrop-blur-xl'
                }`}
              >
                {type.icon} {t(`contentTypes.${type.content_type}`)} ({type.article_count})
              </button>
            ))}
          </div>
        </div>

        {/* Articles Grid */}
        {articles.length === 0 ? (
          <div className="bg-white/5 backdrop-blur-xl rounded-xl p-12 text-center border border-slate-500/20">
            <SparklesIcon className="w-16 h-16 text-slate-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">{t('articles.noArticlesFound')}</h3>
            <p className="text-slate-300">{t('articles.tryAdjusting')}</p>
          </div>
        ) : (
          <>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
              {articles.map((article) => {
                const specialFeature = getContentTypeFeature(article.content_type);
                const articleImage = article.featured_image || getContentTypeImage(article.content_type, 'thumbnail');

                return (
                  <Link
                    key={article.id}
                    to={`/articles/${article.slug}`}
                    className="group bg-white/5 backdrop-blur-xl rounded-xl overflow-hidden border border-slate-500/20 hover:border-slate-600/50 hover:scale-[1.02] transition-all duration-300 hover:bg-white/10 shadow-lg hover:shadow-cyan-500/25"
                  >
                    {/* Article Image */}
                    <div className="relative h-48 overflow-hidden">
                      <img
                        src={articleImage}
                        alt={article.title}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                        onError={(e) => {
                          e.target.src = getContentTypeImage(article.content_type, 'thumbnail');
                        }}
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                      
                      {/* Content Type Badge */}
                      <div className="absolute top-4 left-4 z-10">
                        <div className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r ${article.contentTypeInfo.color} text-white shadow-lg backdrop-blur-sm`}>
                          <span>{article.contentTypeInfo.icon}</span>
                          <span>{t(`contentTypes.${article.content_type}`)}</span>
                        </div>
                      </div>

                      {/* Special Feature Badge */}
                      {specialFeature && (
                        <div className="absolute top-4 right-4 z-10">
                          <div className={`flex items-center space-x-1 bg-black/50 backdrop-blur-sm px-2 py-1 rounded-full text-xs ${specialFeature.color}`}>
                            <span>{specialFeature.icon}</span>
                            <span>{specialFeature.text}</span>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="p-6">
                      {/* Enhanced Title */}
                      <h3 className="text-xl font-bold text-white mb-3 line-clamp-2 group-hover:text-cyan-500 transition-colors leading-tight">
                        {article.title}
                      </h3>

                      {/* Enhanced Excerpt */}
                      <p className="text-slate-300 text-base mb-6 line-clamp-3 leading-relaxed">
                        {article.excerpt}
                      </p>

                      {/* Enhanced Meta Information */}
                      <div className="flex items-center justify-between text-sm text-slate-400 border-t border-slate-500/20 pt-4">
                        <div className="flex items-center space-x-2">
                          <UserIcon className="w-4 h-4" />
                          <span className="font-medium">{article.author}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <ClockIcon className="w-4 h-4" />
                          <span>{formatDate(article.published_at)}</span>
                        </div>
                      </div>

                      {/* Reading Time Estimate */}
                      <div className="mt-3 text-xs text-slate-400">
                        <span>📖 {calculateReadingTime(article.excerpt || '')} {t('articles.minRead')}</span>
                      </div>
                    </div>
                  </Link>
                );
              })}
            </div>

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="flex items-center justify-center space-x-2">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="p-2 rounded-lg bg-white/5 text-slate-300 hover:bg-cyan-600/50 disabled:opacity-50 disabled:cursor-not-allowed backdrop-blur-xl border border-slate-500/20"
                >
                  <ChevronLeftIcon className="w-5 h-5" />
                </button>

                {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((page) => (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium border transition-colors ${
                      currentPage === page
                        ? 'bg-cyan-600 text-white border-cyan-600'
                        : 'bg-white/5 text-slate-300 hover:bg-cyan-600/50 border-slate-500/20 backdrop-blur-xl'
                    }`}
                  >
                    {page}
                  </button>
                ))}

                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === pagination.totalPages}
                  className="p-2 rounded-lg bg-white/5 text-slate-300 hover:bg-cyan-600/50 disabled:opacity-50 disabled:cursor-not-allowed backdrop-blur-xl border border-slate-500/20"
                >
                  <ChevronRightIcon className="w-5 h-5" />
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default PublicArticles;
