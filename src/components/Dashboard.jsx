import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import {
  PlusIcon,
  DocumentTextIcon,
  ClockIcon,
  CheckCircleIcon,
  SparklesIcon,
  FolderIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import { API_CONFIG } from '../config/api';
import AuthenticatedLayout from './layout/AuthenticatedLayout';

const Dashboard = () => {
  const { t, i18n } = useTranslation();
  const { user, authenticatedFetch } = useAuth();
  const [tasks, setTasks] = useState([]);
  const [stats, setStats] = useState({ totalTasks: 0, statusBreakdown: [] });
  const [categorizedContent, setCategorizedContent] = useState({});
  const [contentTypes, setContentTypes] = useState([]);
  const [loading, setLoading] = useState(true);

  // Force English language on Dashboard load if not already set
  useEffect(() => {
    if (i18n.language !== 'en') {
      i18n.changeLanguage('en');
    }
  }, [i18n]);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Fetch recent tasks, stats, and categorized content
        const [tasksResponse, statsResponse, categoriesResponse] = await Promise.all([
          authenticatedFetch(`${API_CONFIG.ENDPOINTS.TASKS.LIST}?limit=5`),
          authenticatedFetch(API_CONFIG.ENDPOINTS.TASKS.STATS),
          authenticatedFetch(`/api/public/content/categories?limit=10`)
        ]);

        if (tasksResponse.ok) {
          const tasksData = await tasksResponse.json();
          setTasks(tasksData.tasks);
        }

        if (statsResponse.ok) {
          const statsData = await statsResponse.json();
          setStats(statsData);
        }

        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json();
          setCategorizedContent(categoriesData.categories);
        }

      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [authenticatedFetch]);

  const getStatusIcon = (status) => {
    if (status.includes('Completed')) {
      return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
    } else if (status.includes('Generating')) {
      return <ClockIcon className="w-5 h-5 text-yellow-500 animate-spin" />;
    } else {
      return <DocumentTextIcon className="w-5 h-5 text-blue-500" />;
    }
  };

  const getStatusColor = (status) => {
    if (status.includes('Completed')) return 'bg-green-100 text-green-800';
    if (status.includes('Generating')) return 'bg-yellow-100 text-yellow-800';
    if (status.includes('Review')) return 'bg-purple-100 text-purple-800';
    return 'bg-blue-100 text-blue-800';
  };

  const getContentTypeInfo = (contentType) => {
    const typeMap = {
      'casino_review': { icon: '🎰', label: t('contentTypes.casino_review'), color: 'from-purple-500 to-purple-600' },
      'game_guide': { icon: '🎮', label: t('contentTypes.game_guide'), color: 'from-blue-500 to-blue-600' },
      'strategy_article': { icon: '📊', label: t('contentTypes.strategy_article', 'Strategy Articles'), color: 'from-green-500 to-green-600' },
      'brand_copy': { icon: '✨', label: t('contentTypes.brand_copy', 'Brand Copy'), color: 'from-yellow-500 to-yellow-600' },
      'industry_news': { icon: '📰', label: t('contentTypes.industry_news'), color: 'from-red-500 to-red-600' },
      'sports_betting': { icon: '⚽', label: t('contentTypes.sports_betting'), color: 'from-indigo-500 to-indigo-600' },
      'bonus_analysis': { icon: '🎁', label: t('contentTypes.bonus_analysis'), color: 'from-yellow-500 to-yellow-600' },
      'regulatory_update': { icon: '⚖️', label: t('contentTypes.regulatory_update'), color: 'from-red-500 to-red-600' },
      'generic': { icon: '📝', label: t('contentTypes.general'), color: 'from-gray-500 to-gray-600' },
      'uncategorized': { icon: '📄', label: 'Uncategorized', color: 'from-gray-500 to-gray-600' }
    };

    return typeMap[contentType] || typeMap['uncategorized'];
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <AuthenticatedLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8 text-center">
          <h2 className="text-4xl font-bold text-casino-gold-400 mb-2 neon-glow font-casino">
            {t('dashboard.welcome', { name: user?.fullName || 'Writer' })}
          </h2>
          <p className="text-casino-gold-200 text-lg">
            {t('dashboard.subtitle')}
          </p>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Link
            to="/tasks/new"
            className="casino-card rounded-2xl p-6 text-white hover:scale-105 transition-all duration-300 chip-shadow animate-pulse-gold"
          >
            <div className="flex items-center space-x-3">
              <PlusIcon className="w-8 h-8 text-casino-gold-400" />
              <div>
                <h3 className="text-lg font-semibold text-casino-gold-400">{t('dashboard.quickActions.startNewArticle')}</h3>
                <p className="text-casino-gold-200">{t('dashboard.quickActions.startNewDesc')}</p>
              </div>
            </div>
          </Link>

          <Link
            to="/content/categories"
            className="casino-card rounded-2xl p-6 hover:scale-105 transition-all duration-300 chip-shadow"
          >
            <div className="flex items-center space-x-3">
              <FolderIcon className="w-8 h-8 text-casino-gold-400" />
              <div>
                <h3 className="text-lg font-semibold text-casino-gold-400">{t('dashboard.quickActions.contentCategories')}</h3>
                <p className="text-casino-gold-200">{t('dashboard.quickActions.categoriesDesc')}</p>
              </div>
            </div>
          </Link>

          <Link
            to="/tasks"
            className="casino-card rounded-2xl p-6 hover:scale-105 transition-all duration-300 chip-shadow"
          >
            <div className="flex items-center space-x-3">
              <DocumentTextIcon className="w-8 h-8 text-casino-gold-400" />
              <div>
                <h3 className="text-lg font-semibold text-casino-gold-400">{t('dashboard.quickActions.allTasks')}</h3>
                <p className="text-casino-gold-200">{t('dashboard.quickActions.allTasksDesc')}</p>
              </div>
            </div>
          </Link>

          <Link
            to="/generator"
            className="casino-card rounded-2xl p-6 hover:scale-105 transition-all duration-300 chip-shadow"
          >
            <div className="flex items-center space-x-3">
              <SparklesIcon className="w-8 h-8 text-casino-red-400" />
              <div>
                <h3 className="text-lg font-semibold text-casino-gold-400">{t('dashboard.quickActions.quickGenerator')}</h3>
                <p className="text-casino-gold-200">{t('dashboard.quickActions.quickGeneratorDesc')}</p>
              </div>
            </div>
          </Link>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="casino-card rounded-xl p-6 chip-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-casino-gold-300">{t('dashboard.stats.totalTasks')}</p>
                <p className="text-2xl font-bold text-casino-gold-400 neon-glow">{stats.totalTasks}</p>
              </div>
              <DocumentTextIcon className="w-8 h-8 text-casino-gold-500" />
            </div>
          </div>

          {stats.statusBreakdown.slice(0, 3).map((stat, index) => (
            <div key={stat.status} className="casino-card rounded-xl p-6 chip-shadow">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-casino-gold-300">{stat.status}</p>
                  <p className="text-2xl font-bold text-casino-gold-400">{stat.count}</p>
                </div>
                {getStatusIcon(stat.status)}
              </div>
            </div>
          ))}
        </div>

        {/* Content Categories Overview */}
        <div className="casino-card rounded-2xl overflow-hidden chip-shadow mb-8">
          <div className="px-6 py-4 border-b border-casino-gold-500/30">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-casino-gold-400">{t('dashboardPage.contentByCategory')}</h3>
              <Link
                to="/content/categories"
                className="text-casino-gold-500 hover:text-casino-gold-400 text-sm font-medium transition-colors flex items-center space-x-1"
              >
                <span>{t('dashboardPage.viewAll')}</span>
                <ChevronRightIcon className="w-4 h-4" />
              </Link>
            </div>
          </div>

          {Object.keys(categorizedContent).length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-6">
              {Object.entries(categorizedContent).slice(0, 6).map(([contentType, tasks]) => {
                const typeInfo = getContentTypeInfo(contentType);
                const completedCount = tasks.filter(task => task.status.includes('Completed')).length;

                return (
                  <Link
                    key={contentType}
                    to={`/content/category/${contentType}`}
                    className="bg-gradient-to-br from-casino-dark-700/50 to-casino-dark-800/50 rounded-xl p-4 hover:from-casino-dark-600/50 hover:to-casino-dark-700/50 transition-all duration-200 border border-casino-gold-500/20 hover:border-casino-gold-500/40"
                  >
                    <div className="flex items-center space-x-3 mb-3">
                      <span className="text-2xl">{typeInfo.icon}</span>
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-casino-gold-200 truncate">{typeInfo.label}</h4>
                        <p className="text-xs text-casino-gold-300">{tasks.length} articles</p>
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-casino-gold-300">{completedCount} completed</span>
                      <ChevronRightIcon className="w-4 h-4 text-casino-gold-400" />
                    </div>
                  </Link>
                );
              })}
            </div>
          ) : (
            <div className="p-8 text-center">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6">
                {[
                  { type: 'casino_review', icon: '🎰', label: t('contentTypes.casino_review') },
                  { type: 'game_guide', icon: '🎮', label: t('contentTypes.game_guide') },
                  { type: 'strategy_article', icon: '📊', label: t('contentTypes.strategy_article', 'Strategy Articles') },
                  { type: 'brand_copy', icon: '✨', label: t('contentTypes.brand_copy', 'Brand Copy') },
                  { type: 'industry_news', icon: '📰', label: t('contentTypes.industry_news') },
                  { type: 'sports_betting', icon: '⚽', label: t('contentTypes.sports_betting') },
                ].map((contentType) => (
                  <div
                    key={contentType.type}
                    className="bg-gradient-to-br from-casino-dark-700/30 to-casino-dark-800/30 rounded-xl p-4 border border-casino-gold-500/20"
                  >
                    <div className="text-center">
                      <span className="text-3xl mb-2 block">{contentType.icon}</span>
                      <h4 className="text-xs font-medium text-casino-gold-200">{contentType.label}</h4>
                      <p className="text-xs text-casino-gold-300">0 articles</p>
                    </div>
                  </div>
                ))}
              </div>
              <h4 className="text-lg font-medium text-casino-gold-400 mb-2">{t('dashboardPage.readyForContent')}</h4>
              <p className="text-casino-gold-200 mb-4">
                {t('dashboardPage.createDifferentContent')}
              </p>
              <Link
                to="/tasks/new"
                className="casino-button inline-flex items-center px-6 py-3 text-white rounded-lg chip-shadow animate-pulse-gold"
              >
                <PlusIcon className="w-5 h-5 mr-2" />
                {t('dashboardPage.createFirstArticle')}
              </Link>
            </div>
          )}
        </div>

        {/* Recent Tasks */}
        <div className="casino-card rounded-2xl overflow-hidden chip-shadow">
          <div className="px-6 py-4 border-b border-casino-gold-500/30">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-casino-gold-400">{t('dashboardPage.recentTasks')}</h3>
              <Link
                to="/tasks"
                className="text-casino-gold-500 hover:text-casino-gold-400 text-sm font-medium transition-colors"
              >
                {t('dashboardPage.viewAll')}
              </Link>
            </div>
          </div>

          {tasks.length === 0 ? (
            <div className="px-6 py-12 text-center">
              <DocumentTextIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">{t('dashboardPage.noArticlesYet')}</h4>
              <p className="text-gray-600 mb-4">{t('dashboardPage.startFirstArticle')}</p>
              <Link
                to="/tasks/new"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <PlusIcon className="w-4 h-4 mr-2" />
                {t('dashboardPage.startArticle')}
              </Link>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {tasks.map((task) => (
                <Link
                  key={task.id}
                  to={`/tasks/${task.id}`}
                  className="block px-6 py-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(task.status)}
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">{task.name}</h4>
                        <p className="text-xs text-gray-500">
                          Updated {new Date(task.updated_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                      {task.status}
                    </span>
                  </div>
                </Link>
              ))}
            </div>
          )}
        </div>
      </div>
    </AuthenticatedLayout>
  );
};

export default Dashboard;
