import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import {
  Bars3Icon,
  XMarkIcon,
  HomeIcon,
  DocumentTextIcon,
  Cog6ToothIcon,
  UserIcon,
  ArrowRightOnRectangleIcon,
  PlusIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import WriterJLogo from '../WriterJLogo';

const AuthenticatedNavigation = () => {
  const { t, i18n } = useTranslation();
  const { user, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);

  const currentLang = i18n.language;

  // Check if user is admin
  const isAdmin = user?.role === 'admin' || user?.role === 'super_admin';

  // Navigation items
  const navigationItems = [
    {
      name: t('nav.dashboard'),
      href: `/${currentLang}/dashboard`,
      icon: HomeIcon,
      current: location.pathname.includes('/dashboard')
    },
    {
      name: t('nav.tasks'),
      href: `/${currentLang}/tasks`,
      icon: DocumentTextIcon,
      current: location.pathname.includes('/tasks')
    }
  ];

  // Add admin panel for admin users
  if (isAdmin) {
    navigationItems.push({
      name: t('nav.adminPanel'),
      href: `/${currentLang}/admin`,
      icon: ShieldCheckIcon,
      current: location.pathname.includes('/admin')
    });
  }

  // User menu items
  const userMenuItems = [
    {
      name: t('nav.profile'),
      href: `/${currentLang}/profile`,
      icon: UserIcon
    },
    {
      name: t('nav.settings'),
      href: `/${currentLang}/settings`,
      icon: Cog6ToothIcon
    }
  ];

  const handleLogout = async () => {
    logout();
    navigate(`/${currentLang}/login`);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const toggleUserMenu = () => {
    setIsUserMenuOpen(!isUserMenuOpen);
  };

  // Close mobile menu when clicking on a link
  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const classNames = (...classes) => {
    return classes.filter(Boolean).join(' ');
  };

  return (
    <nav className="bg-gradient-to-r from-casino-dark-800 via-casino-dark-700 to-casino-dark-800 backdrop-blur-xl border-b border-casino-gold-500/30 sticky top-0 z-50 shadow-lg shadow-casino-gold-500/10">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo and brand */}
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Link to={`/${currentLang}/dashboard`} className="flex items-center space-x-3">
                <WriterJLogo className="w-10 h-10" showText={false} />
                <div className="hidden sm:block">
                  <h1 className="text-lg font-bold text-casino-gold-400 neon-glow">Writer 777</h1>
                  <p className="text-xs text-casino-gold-200">AI Content Hub</p>
                </div>
              </Link>
            </div>
          </div>

          {/* Desktop navigation */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-4">
              {navigationItems.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className={classNames(
                    item.current
                      ? 'bg-casino-gold-500/20 text-casino-gold-100 border-casino-gold-400'
                      : 'text-casino-gold-200 hover:bg-casino-gold-500/10 hover:text-casino-gold-100 border-transparent',
                    'px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-2 border'
                  )}
                >
                  <item.icon className="w-4 h-4" />
                  <span>{item.name}</span>
                </Link>
              ))}
            </div>
          </div>

          {/* Right side - Quick actions and user menu */}
          <div className="hidden md:block">
            <div className="ml-4 flex items-center md:ml-6 space-x-4">
              {/* Quick action - New article */}
              <Link
                to={`/${currentLang}/tasks/new`}
                className="bg-gradient-to-r from-casino-gold-500 to-casino-gold-600 hover:from-casino-gold-600 hover:to-casino-gold-700 text-casino-dark-900 px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-200 flex items-center space-x-2 shadow-lg hover:shadow-xl"
              >
                <PlusIcon className="w-4 h-4" />
                <span>{t('nav.newArticle')}</span>
              </Link>

              {/* User menu */}
              <div className="relative">
                <button
                  type="button"
                  className="flex max-w-xs items-center rounded-lg bg-casino-dark-700/50 p-2 text-casino-gold-200 hover:bg-casino-gold-500/20 hover:text-casino-gold-100 transition-all duration-200 border border-casino-gold-500/30"
                  onClick={toggleUserMenu}
                >
                  <span className="sr-only">Open user menu</span>
                  <div className="flex items-center space-x-2">
                    <UserIcon className="w-5 h-5" />
                    <span className="text-sm font-medium">
                      {user?.fullName || user?.email || 'User'}
                    </span>
                  </div>
                </button>

                {/* User dropdown menu */}
                {isUserMenuOpen && (
                  <div className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-lg bg-casino-dark-800 py-1 shadow-lg ring-1 ring-casino-gold-500/30 border border-casino-gold-500/20">
                    <div className="px-4 py-2 border-b border-casino-gold-500/20">
                      <p className="text-sm text-casino-gold-200">Signed in as</p>
                      <p className="text-sm font-medium text-casino-gold-100 truncate">
                        {user?.email}
                      </p>
                    </div>
                    {userMenuItems.map((item) => (
                      <Link
                        key={item.name}
                        to={item.href}
                        className="flex items-center space-x-2 px-4 py-2 text-sm text-casino-gold-200 hover:bg-casino-gold-500/20 hover:text-casino-gold-100 transition-all duration-200"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <item.icon className="w-4 h-4" />
                        <span>{item.name}</span>
                      </Link>
                    ))}
                    <button
                      onClick={handleLogout}
                      className="flex w-full items-center space-x-2 px-4 py-2 text-sm text-casino-gold-200 hover:bg-casino-red-500/20 hover:text-casino-red-300 transition-all duration-200"
                    >
                      <ArrowRightOnRectangleIcon className="w-4 h-4" />
                      <span>{t('auth.logout')}</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              type="button"
              className="inline-flex items-center justify-center rounded-lg p-2 text-casino-gold-200 hover:bg-casino-gold-500/20 hover:text-casino-gold-100 transition-all duration-200"
              onClick={toggleMobileMenu}
            >
              <span className="sr-only">Open main menu</span>
              {isMobileMenuOpen ? (
                <XMarkIcon className="block h-6 w-6" />
              ) : (
                <Bars3Icon className="block h-6 w-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden">
          <div className="space-y-1 px-2 pb-3 pt-2 sm:px-3 bg-casino-dark-800/95 backdrop-blur-xl border-t border-casino-gold-500/20">
            {navigationItems.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={classNames(
                  item.current
                    ? 'bg-casino-gold-500/20 text-casino-gold-100'
                    : 'text-casino-gold-200 hover:bg-casino-gold-500/10 hover:text-casino-gold-100',
                  'block px-3 py-2 rounded-lg text-base font-medium transition-all duration-200 flex items-center space-x-3'
                )}
                onClick={closeMobileMenu}
              >
                <item.icon className="w-5 h-5" />
                <span>{item.name}</span>
              </Link>
            ))}
            
            {/* Mobile quick action */}
            <Link
              to={`/${currentLang}/tasks/new`}
              className="bg-gradient-to-r from-casino-gold-500 to-casino-gold-600 text-casino-dark-900 block px-3 py-2 rounded-lg text-base font-semibold transition-all duration-200 flex items-center space-x-3 mt-4"
              onClick={closeMobileMenu}
            >
              <PlusIcon className="w-5 h-5" />
              <span>{t('nav.newArticle')}</span>
            </Link>
          </div>

          {/* Mobile user menu */}
          <div className="border-t border-casino-gold-500/20 bg-casino-dark-800/95 backdrop-blur-xl pb-3 pt-4">
            <div className="flex items-center px-5">
              <div className="flex-shrink-0">
                <UserIcon className="h-8 w-8 text-casino-gold-400" />
              </div>
              <div className="ml-3">
                <div className="text-sm font-medium text-casino-gold-100">
                  {user?.fullName || 'User'}
                </div>
                <div className="text-sm text-casino-gold-200">{user?.email}</div>
              </div>
            </div>
            <div className="mt-3 space-y-1 px-2">
              {userMenuItems.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className="flex items-center space-x-3 px-3 py-2 rounded-lg text-base font-medium text-casino-gold-200 hover:bg-casino-gold-500/10 hover:text-casino-gold-100 transition-all duration-200"
                  onClick={closeMobileMenu}
                >
                  <item.icon className="w-5 h-5" />
                  <span>{item.name}</span>
                </Link>
              ))}
              <button
                onClick={() => {
                  handleLogout();
                  closeMobileMenu();
                }}
                className="flex w-full items-center space-x-3 px-3 py-2 rounded-lg text-base font-medium text-casino-gold-200 hover:bg-casino-red-500/20 hover:text-casino-red-300 transition-all duration-200"
              >
                <ArrowRightOnRectangleIcon className="w-5 h-5" />
                <span>{t('auth.logout')}</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default AuthenticatedNavigation;