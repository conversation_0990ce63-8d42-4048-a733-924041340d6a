/**
 * Utility functions for extracting structured data from article content
 */

/**
 * Decode HTML entities in text
 * @param {string} text - Text with HTML entities
 * @returns {string} Decoded text
 */
const decodeHtmlEntities = (text) => {
  if (!text) return '';

  const entityMap = {
    '&lt;': '<',
    '&gt;': '>',
    '&amp;': '&',
    '&quot;': '"',
    '&#39;': "'",
    '&nbsp;': ' '
  };

  return text.replace(/&[a-zA-Z0-9#]+;/g, (entity) => {
    return entityMap[entity] || entity;
  });
};

/**
 * Extract SEO metadata from article content
 * @param {string} content - Article content
 * @returns {Object} SEO metadata object
 */
export const extractSEOData = (content) => {
  if (!content) return { title: '', description: '', keywords: [], tags: [] };

  const seoData = {
    title: '',
    description: '',
    keywords: [],
    tags: []
  };

  // Extract SEO Title - various formats (HTML and markdown)
  const seoTitleMatch = content.match(/<p><strong>SEO Title:<\/strong><br>(.*?)<\/p>/i) ||
                       content.match(/\*\*SEO Title:\*\*\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                       content.match(/SEO Title:\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i);

  if (seoTitleMatch && seoTitleMatch[1]) {
    seoData.title = decodeHtmlEntities(seoTitleMatch[1])
      .trim()
      .replace(/[#*_`\[\]]/g, '') // Remove markdown
      .replace(/[.]*$/, '') // Remove trailing periods
      .split(/meta\s*description/i)[0] // Stop at meta description
      .trim();
  }

  // Extract Meta Description
  const metaDescMatch = content.match(/<p><strong>Meta Description:<\/strong><br>(.*?)<\/p>/i) ||
                       content.match(/\*\*Meta Description:\*\*\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                       content.match(/Meta Description:\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i);

  if (metaDescMatch && metaDescMatch[1]) {
    seoData.description = decodeHtmlEntities(metaDescMatch[1])
      .trim()
      .replace(/[#*_`\[\]]/g, '')
      .trim();
  }

  // Extract Keywords
  const keywordsMatch = content.match(/<p><strong>Focus Keywords:<\/strong><br>(.*?)<\/p>/i) ||
                       content.match(/\*\*Focus Keywords:\*\*\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                       content.match(/Focus Keywords:\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                       content.match(/\*\*Keywords:\*\*\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i);

  if (keywordsMatch && keywordsMatch[1]) {
    seoData.keywords = decodeHtmlEntities(keywordsMatch[1])
      .split(',')
      .map(k => k.trim())
      .filter(k => k.length > 0);
  }

  // Extract Tags
  const tagsMatch = content.match(/<p><strong>Tags:<\/strong><br>(.*?)<\/p>/i) ||
                   content.match(/\*\*Tags:\*\*\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                   content.match(/Tags:\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i);

  if (tagsMatch && tagsMatch[1]) {
    seoData.tags = decodeHtmlEntities(tagsMatch[1])
      .split(',')
      .map(t => t.trim())
      .filter(t => t.length > 0);
  }

  return seoData;
};

/**
 * Clean article content by removing SEO metadata section
 * @param {string} content - Original article content
 * @returns {string} Cleaned content without SEO metadata
 */
export const cleanArticleContent = (content) => {
  if (!content) return '';

  // Remove the SEO metadata section
  // This typically includes SEO Title, Meta Description, Focus Keywords, Tags
  let cleanedContent = content;

  // Remove HTML-formatted SEO sections (new format)
  cleanedContent = cleanedContent.replace(/<p><strong>SEO Title:<\/strong><br>.*?<\/p>/gi, '');
  cleanedContent = cleanedContent.replace(/<p><strong>Meta Description:<\/strong><br>.*?<\/p>/gi, '');
  cleanedContent = cleanedContent.replace(/<p><strong>Focus Keywords:<\/strong><br>.*?<\/p>/gi, '');
  cleanedContent = cleanedContent.replace(/<p><strong>Tags:<\/strong><br>.*?<\/p>/gi, '');

  // Remove separator line
  cleanedContent = cleanedContent.replace(/<p><strong>-+<\/strong><\/p>/gi, '');

  // Remove markdown-formatted SEO sections (old format)
  cleanedContent = cleanedContent.replace(/\*?\*?SEO Title:\*?\*?\s*\[?[^\]\n]+?\]?(?:\s*\n)?/gi, '');
  cleanedContent = cleanedContent.replace(/\*?\*?Meta Description:\*?\*?\s*\[?[^\]\n]+?\]?(?:\s*\n)?/gi, '');
  cleanedContent = cleanedContent.replace(/\*?\*?(?:Focus )?Keywords:\*?\*?\s*\[?[^\]\n]+?\]?(?:\s*\n)?/gi, '');
  cleanedContent = cleanedContent.replace(/\*?\*?Tags:\*?\*?\s*\[?[^\]\n]+?\]?(?:\s*\n)?/gi, '');

  // Remove casino info JSON blocks
  cleanedContent = cleanedContent.replace(/<!-- CASINO_INFO_START -->[\s\S]*?<!-- CASINO_INFO_END -->/gi, '');

  // Remove any visible JSON blocks that might appear in content
  cleanedContent = cleanedContent.replace(/\{[\s\S]*?"pros"[\s\S]*?"cons"[\s\S]*?\}/gi, '');

  // Remove Visual Aid Suggestions section
  cleanedContent = cleanedContent.replace(/\*?\*?Visual Aid Suggestions:\*?\*?[\s\S]*?(?=\n\n|\n#|$)/gi, '');

  // Remove any empty lines at the beginning and multiple consecutive empty lines
  cleanedContent = cleanedContent.replace(/^\s*\n+/, '');
  cleanedContent = cleanedContent.replace(/\n\s*\n\s*\n/g, '\n\n');

  return cleanedContent;
};

/**
 * Extract Visual Aid Suggestions from content (to be removed per requirements)
 * @param {string} content - Article content
 * @returns {Array} Empty array (feature being removed)
 */
export const extractVisualAidSuggestions = (content) => {
  // Feature removed per requirements
  return [];
};