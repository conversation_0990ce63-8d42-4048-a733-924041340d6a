/**
 * URL Localization utilities for handling multilingual routes
 */

const SUPPORTED_LANGUAGES = ['en', 'zh', 'pt', 'es', 'de', 'fr', 'it', 'ja'];
const DEFAULT_LANGUAGE = 'en';

/**
 * Generate localized URL for given path and language
 */
export const getLocalizedUrl = (path, language = DEFAULT_LANGUAGE) => {
  // Remove leading slash if present
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  
  // Don't add language prefix for default language
  if (language === DEFAULT_LANGUAGE) {
    return `/${cleanPath}`;
  }
  
  return `/${language}/${cleanPath}`;
};

/**
 * Get current language from pathname
 */
export const getCurrentLanguageFromPath = (pathname) => {
  const segments = pathname.split('/').filter(Boolean);
  const firstSegment = segments[0];
  
  if (SUPPORTED_LANGUAGES.includes(firstSegment)) {
    return firstSegment;
  }
  
  return DEFAULT_LANGUAGE;
};

/**
 * Remove language prefix from pathname
 */
export const removeLanguageFromPath = (pathname) => {
  const segments = pathname.split('/').filter(Boolean);
  
  if (segments.length > 0 && SUPPORTED_LANGUAGES.includes(segments[0])) {
    return '/' + segments.slice(1).join('/');
  }
  
  return pathname;
};

/**
 * Generate alternate URLs for all supported languages
 */
export const generateAlternateUrls = (pathname, baseUrl = window.location.origin) => {
  const pathWithoutLanguage = removeLanguageFromPath(pathname);
  
  return SUPPORTED_LANGUAGES.map(lang => ({
    language: lang,
    url: `${baseUrl}${getLocalizedUrl(pathWithoutLanguage, lang)}`,
    hreflang: lang
  }));
};

/**
 * Generate language-aware navigation link
 */
export const createLocalizedLink = (to, currentLanguage = DEFAULT_LANGUAGE) => {
  return getLocalizedUrl(to, currentLanguage);
};

/**
 * Detect if URL has language prefix
 */
export const hasLanguagePrefix = (pathname) => {
  const segments = pathname.split('/').filter(Boolean);
  return segments.length > 0 && SUPPORTED_LANGUAGES.includes(segments[0]);
};

/**
 * Language-aware router utilities
 */
export const routerUtils = {
  /**
   * Navigate to path with current language
   */
  navigateWithLanguage: (navigate, path, language = DEFAULT_LANGUAGE) => {
    const localizedPath = getLocalizedUrl(path, language);
    navigate(localizedPath);
  },

  /**
   * Replace current path with language version
   */
  replaceWithLanguage: (navigate, currentPath, newLanguage) => {
    const pathWithoutLanguage = removeLanguageFromPath(currentPath);
    const newPath = getLocalizedUrl(pathWithoutLanguage, newLanguage);
    navigate(newPath, { replace: true });
  },

  /**
   * Check if current route matches pattern
   */
  matchesRoute: (pathname, pattern) => {
    const pathWithoutLanguage = removeLanguageFromPath(pathname);
    return pathWithoutLanguage === pattern || pathWithoutLanguage.startsWith(pattern + '/');
  }
};

/**
 * API utilities for language-aware requests
 */
export const apiUtils = {
  /**
   * Add language parameter to API requests
   */
  addLanguageParam: (url, language) => {
    const urlObj = new URL(url, window.location.origin);
    urlObj.searchParams.set('lang', language);
    return urlObj.toString();
  },

  /**
   * Get language-aware API endpoint
   */
  getLocalizedEndpoint: (endpoint, language = DEFAULT_LANGUAGE) => {
    return apiUtils.addLanguageParam(endpoint, language);
  }
};

/**
 * SEO utilities for multilingual content
 */
export const seoUtils = {
  /**
   * Generate hreflang links
   */
  generateHrefLangLinks: (pathname, baseUrl = window.location.origin) => {
    const alternateUrls = generateAlternateUrls(pathname, baseUrl);
    const pathWithoutLanguage = removeLanguageFromPath(pathname);
    
    return [
      ...alternateUrls.map(({ language, url }) => ({
        rel: 'alternate',
        hreflang: language,
        href: url
      })),
      // Add x-default pointing to English version
      {
        rel: 'alternate',
        hreflang: 'x-default',
        href: `${baseUrl}${getLocalizedUrl(pathWithoutLanguage, DEFAULT_LANGUAGE)}`
      }
    ];
  },

  /**
   * Get canonical URL for current language
   */
  getCanonicalUrl: (pathname, language, baseUrl = window.location.origin) => {
    const pathWithoutLanguage = removeLanguageFromPath(pathname);
    return `${baseUrl}${getLocalizedUrl(pathWithoutLanguage, language)}`;
  }
};

export {
  SUPPORTED_LANGUAGES,
  DEFAULT_LANGUAGE
};