/**
 * Content Type Utilities - Mapping and configuration for different article types
 */

import { getContentTypeImage, getContentTypeCardBackground } from './imageUtils';

/**
 * Content type configuration with colors, icons, labels, and images
 */
export const CONTENT_TYPE_CONFIG = {
  'bonus_analysis': {
    label: 'Bonus Analysis',
    icon: '🎁',
    color: 'from-emerald-600 to-teal-600',
    description: 'Comprehensive bonus reviews and comparisons',
    backgroundImage: 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=800&h=400&fit=crop&crop=center',
    thumbnailImage: 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=400&h=250&fit=crop&crop=center'
  },
  'brand_copy': {
    label: 'Brand Copy',
    icon: '✨',
    color: 'from-yellow-600 to-orange-600',
    description: 'Premium marketing and brand content',
    backgroundImage: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=400&fit=crop&crop=center',
    thumbnailImage: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=250&fit=crop&crop=center'
  },
  'casino_review': {
    label: 'Casino Review',
    icon: '🎰',
    color: 'from-purple-600 to-pink-600',
    description: 'In-depth casino reviews and ratings',
    backgroundImage: 'https://images.unsplash.com/photo-1596838132731-3301c3fd4317?w=800&h=400&fit=crop&crop=center',
    thumbnailImage: 'https://images.unsplash.com/photo-1596838132731-3301c3fd4317?w=400&h=250&fit=crop&crop=center'
  },
  'game_guide': {
    label: 'Game Guide',
    icon: '🎮',
    color: 'from-blue-600 to-cyan-600',
    description: 'Step-by-step game tutorials and guides',
    backgroundImage: 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=800&h=400&fit=crop&crop=center',
    thumbnailImage: 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=400&h=250&fit=crop&crop=center'
  },
  'industry_news': {
    label: 'Industry News',
    icon: '📰',
    color: 'from-red-600 to-pink-600',
    description: 'Latest industry news and updates',
    backgroundImage: 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=800&h=400&fit=crop&crop=center',
    thumbnailImage: 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=400&h=250&fit=crop&crop=center'
  },
  'regulatory_update': {
    label: 'Regulatory Update',
    icon: '⚖️',
    color: 'from-slate-600 to-gray-600',
    description: 'Legal and regulatory developments',
    backgroundImage: 'https://images.unsplash.com/photo-1589391886645-d51941baf7fb?w=800&h=400&fit=crop&crop=center',
    thumbnailImage: 'https://images.unsplash.com/photo-1589391886645-d51941baf7fb?w=400&h=250&fit=crop&crop=center'
  },
  'sports_betting': {
    label: 'Sports Betting',
    icon: '⚽',
    color: 'from-indigo-600 to-purple-600',
    description: 'Sports betting analysis and predictions',
    backgroundImage: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=400&fit=crop&crop=center',
    thumbnailImage: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=250&fit=crop&crop=center'
  },
  'strategy_article': {
    label: 'Strategy Article',
    icon: '📊',
    color: 'from-green-600 to-emerald-600',
    description: 'Data-driven strategy and analysis',
    backgroundImage: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop&crop=center',
    thumbnailImage: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=250&fit=crop&crop=center'
  }
};

/**
 * Get content type information for a given content type
 * @param {string} contentType - The content type identifier
 * @returns {Object} Content type configuration object
 */
export const getContentTypeInfo = (contentType) => {
  return CONTENT_TYPE_CONFIG[contentType] || {
    label: 'Article',
    icon: '📄',
    color: 'from-gray-600 to-slate-600',
    description: 'General article content'
  };
};

/**
 * Add content type information to an article object
 * @param {Object} article - Article object
 * @returns {Object} Article object with contentTypeInfo added
 */
export const enrichArticleWithContentType = (article) => {
  if (!article) return article;

  // Handle different possible field names for content type
  const contentType = article.content_type || article.contentType || article.type || 'casino_review';

  console.log('Enriching article:', article.title, 'with content_type:', contentType);
  console.log('Original article fields:', Object.keys(article));

  const contentTypeInfo = getContentTypeInfo(contentType);
  console.log('Content type info:', contentTypeInfo);

  return {
    ...article,
    content_type: contentType, // Ensure consistent field name
    contentTypeInfo
  };
};

/**
 * Add content type information to an array of articles
 * @param {Array} articles - Array of article objects
 * @returns {Array} Array of articles with contentTypeInfo added
 */
export const enrichArticlesWithContentType = (articles) => {
  if (!Array.isArray(articles)) return articles;
  
  return articles.map(enrichArticleWithContentType);
};

/**
 * Get all available content types
 * @returns {Array} Array of content type configurations
 */
export const getAllContentTypes = () => {
  return Object.entries(CONTENT_TYPE_CONFIG).map(([key, config]) => ({
    content_type: key,
    ...config
  }));
};

/**
 * Get content type by key
 * @param {string} key - Content type key
 * @returns {Object|null} Content type configuration or null if not found
 */
export const getContentTypeByKey = (key) => {
  return CONTENT_TYPE_CONFIG[key] || null;
};

/**
 * Check if a content type exists
 * @param {string} contentType - Content type to check
 * @returns {boolean} True if content type exists
 */
export const isValidContentType = (contentType) => {
  return contentType in CONTENT_TYPE_CONFIG;
};

/**
 * Get content type color classes for styling
 * @param {string} contentType - Content type identifier
 * @returns {string} CSS gradient classes
 */
export const getContentTypeColor = (contentType) => {
  const config = getContentTypeInfo(contentType);
  return config.color;
};

/**
 * Get content type icon
 * @param {string} contentType - Content type identifier
 * @returns {string} Emoji icon
 */
export const getContentTypeIcon = (contentType) => {
  const config = getContentTypeInfo(contentType);
  return config.icon;
};

/**
 * Get content type label
 * @param {string} contentType - Content type identifier
 * @returns {string} Human-readable label
 */
export const getContentTypeLabel = (contentType) => {
  const config = getContentTypeInfo(contentType);
  return config.label;
};

/**
 * Get card styling for different content types
 * @param {string} contentType - Content type identifier
 * @returns {string} CSS classes for card styling
 */
export const getContentTypeCardStyle = (contentType) => {
  const styles = {
    'bonus_analysis': 'bg-gradient-to-br from-emerald-900/10 to-teal-900/10 border-emerald-500/30',
    'brand_copy': 'bg-gradient-to-br from-yellow-900/10 to-orange-900/10 border-yellow-500/30',
    'casino_review': 'bg-gradient-to-br from-purple-900/10 to-pink-900/10 border-purple-500/30',
    'game_guide': 'bg-gradient-to-br from-blue-900/10 to-cyan-900/10 border-blue-500/30',
    'industry_news': 'bg-gradient-to-br from-red-900/10 to-pink-900/10 border-red-500/30',
    'regulatory_update': 'bg-gradient-to-br from-slate-900/10 to-gray-900/10 border-slate-500/30',
    'sports_betting': 'bg-gradient-to-br from-indigo-900/10 to-purple-900/10 border-indigo-500/30',
    'strategy_article': 'bg-gradient-to-br from-green-900/10 to-emerald-900/10 border-green-500/30'
  };
  return styles[contentType] || 'bg-gradient-to-br from-gray-900/10 to-slate-900/10 border-gray-500/30';
};

/**
 * Get special feature badge for content types
 * @param {string} contentType - Content type identifier
 * @returns {Object|null} Feature badge configuration or null
 */
export const getContentTypeFeature = (contentType) => {
  const features = {
    'bonus_analysis': {
      icon: '🏅',
      text: 'Verified',
      color: 'text-emerald-400'
    },
    'brand_copy': {
      icon: '✨',
      text: 'Premium',
      color: 'text-yellow-400'
    },
    'casino_review': {
      icon: '⭐',
      text: 'Rated',
      color: 'text-yellow-400'
    },
    'game_guide': {
      icon: '🎯',
      text: 'Tutorial',
      color: 'text-blue-400'
    },
    'industry_news': {
      icon: '🔥',
      text: 'Breaking',
      color: 'text-red-400'
    },
    'regulatory_update': {
      icon: '⚖️',
      text: 'Official',
      color: 'text-slate-400'
    },
    'sports_betting': {
      icon: '🏆',
      text: 'Analysis',
      color: 'text-purple-400'
    },
    'strategy_article': {
      icon: '📊',
      text: 'Data-driven',
      color: 'text-green-400'
    }
  };
  return features[contentType] || null;
};
