/**
 * 游戏信息解析工具
 * 从文章内容中提取JSON格式的游戏信息
 */

/**
 * 从文章内容中提取游戏信息JSON
 * @param {string} content - 文章HTML内容
 * @returns {Object|null} - 解析后的游戏信息对象，如果未找到则返回null
 */
export function extractGameInfo(content) {
  if (!content || typeof content !== 'string') {
    return null;
  }

  try {
    // Method 1: Try to extract from HTML comments (preferred format)
    const startMarker = '<!-- GAME_INFO_START -->';
    const endMarker = '<!-- GAME_INFO_END -->';
    
    const startIndex = content.indexOf(startMarker);
    const endIndex = content.indexOf(endMarker);
    
    if (startIndex !== -1 && endIndex !== -1 && startIndex < endIndex) {
      const jsonStart = startIndex + startMarker.length;
      const jsonContent = content.substring(jsonStart, endIndex).trim();
      
      const gameInfo = JSON.parse(jsonContent);
      console.log('✓ Frontend extracted game info from HTML comments:', gameInfo);
      return gameInfo;
    }

    // Method 2: Try to extract standalone JSON block (fallback)
    console.log('HTML comments not found, trying to extract standalone JSON...');
    
    // Look for JSON block that starts with { and contains typical game info fields
    const jsonRegex = /\{[\s\S]*?"provider"[\s\S]*?"rtp"[\s\S]*?\}/;
    const jsonMatch = content.match(jsonRegex);
    
    if (jsonMatch) {
      const jsonContent = jsonMatch[0];
      const gameInfo = JSON.parse(jsonContent);
      
      // Basic validation for game info fields
      const basicFields = ['provider', 'rtp'];
      const hasBasicFields = basicFields.some(field => gameInfo.hasOwnProperty(field));
      
      if (hasBasicFields) {
        console.log('✓ Frontend extracted game info from standalone JSON:', gameInfo);
        return gameInfo;
      }
    }

    console.log('No valid game info JSON found in content');
    return null;
    
  } catch (error) {
    console.error('Error parsing game info:', error);
    return null;
  }
}

/**
 * 从文章内容中移除游戏信息JSON块
 * @param {string} content - 文章HTML内容 
 * @returns {string} - 移除游戏信息后的内容
 */
export function removeGameInfoFromContent(content) {
  if (!content || typeof content !== 'string') {
    return content;
  }
  
  // Method 1: Remove HTML comment wrapped JSON (preferred format)
  const startMarker = '<!-- GAME_INFO_START -->';
  const endMarker = '<!-- GAME_INFO_END -->';
  
  const startIndex = content.indexOf(startMarker);
  const endIndex = content.indexOf(endMarker);
  
  if (startIndex !== -1 && endIndex !== -1) {
    const beforeJson = content.substring(0, startIndex);
    const afterJson = content.substring(endIndex + endMarker.length);
    console.log('✓ Frontend removed game info from HTML comments');
    return (beforeJson + afterJson).trim();
  }
  
  // Method 2: Remove standalone JSON block (fallback)
  console.log('HTML comments not found, trying to remove standalone JSON...');
  
  // Look for JSON block that starts with { and contains typical game info fields
  const jsonRegex = /\{[\s\S]*?"provider"[\s\S]*?"rtp"[\s\S]*?\}/;
  const cleanContent = content.replace(jsonRegex, '').trim();
  
  if (cleanContent !== content) {
    console.log('✓ Frontend removed standalone JSON block');
    return cleanContent;
  }
  
  console.log('No game info JSON found to remove');
  return content;
}

/**
 * 获取默认的游戏信息
 * @param {string} gameTitle - 游戏标题，用于生成合理的默认值
 * @returns {Object} - 默认游戏信息对象
 */
export function getDefaultGameInfo(gameTitle = '') {
  return {
    provider: 'Unknown Provider',
    rtp: '96.00%',
    volatility: 'Medium',
    minBet: '$0.20',
    maxBet: '$100',
    maxWin: '1000x',
    reels: 5,
    rows: 3,
    paylines: '25 paylines',
    bonusFeatures: ['Free Spins', 'Wild Symbols'],
    theme: 'Classic',
    gameType: 'Video Slot',
    rating: 4.0,
    mobileOptimized: true,
    demoAvailable: true,
    recommendedCasinos: [
      {
        name: 'BetVIP Casino',
        bonus: '100% up to $500 + 200 Free Spins',
        rating: 4.8,
        playUrl: '#'
      },
      {
        name: 'Lucky Spin Casino', 
        bonus: '150% up to $300 + 100 Free Spins',
        rating: 4.5,
        playUrl: '#'
      },
      {
        name: 'Golden Gates Casino',
        bonus: '200% up to $1000 + 50 Free Spins', 
        rating: 4.6,
        playUrl: '#'
      }
    ]
  };
}

/**
 * 验证游戏信息的完整性
 * @param {Object} gameInfo - 游戏信息对象
 * @returns {Object} - { isValid: boolean, errors: string[] }
 */
export function validateGameInfo(gameInfo) {
  const errors = [];
  
  if (!gameInfo || typeof gameInfo !== 'object') {
    return { isValid: false, errors: ['Game info must be an object'] };
  }
  
  // 检查必填字段
  const requiredFields = ['provider', 'rtp', 'volatility', 'minBet', 'maxBet', 'maxWin'];
  requiredFields.forEach(field => {
    if (!gameInfo[field]) {
      errors.push(`${field} is required`);
    }
  });
  
  // 检查RTP格式
  if (gameInfo.rtp && !/^\d{1,3}\.\d{1,2}%$/.test(gameInfo.rtp)) {
    errors.push('RTP format should be like "96.50%"');
  }
  
  // 检查波动性枚举值
  const validVolatility = ['Low', 'Medium', 'High', 'Very High'];
  if (gameInfo.volatility && !validVolatility.includes(gameInfo.volatility)) {
    errors.push(`Volatility must be one of: ${validVolatility.join(', ')}`);
  }
  
  // 检查评分范围
  if (gameInfo.rating && (gameInfo.rating < 1 || gameInfo.rating > 5)) {
    errors.push('Rating must be between 1 and 5');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}