/**
 * Utility functions for article processing and display
 */

/**
 * Calculate estimated reading time based on word count
 * @param {string} content - Article content (HTML or plain text)
 * @param {number} wordsPerMinute - Average reading speed (default: 200)
 * @returns {number} Estimated reading time in minutes
 */
export const calculateReadingTime = (content, wordsPerMinute = 200) => {
  if (!content) return 0;
  
  // Remove HTML tags and get plain text
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = content;
  const text = tempDiv.textContent || tempDiv.innerText || '';
  
  // Count words
  const wordCount = text.trim().split(/\s+/).filter(word => word.length > 0).length;
  
  // Calculate reading time
  const readTime = Math.ceil(wordCount / wordsPerMinute);
  
  return Math.max(1, readTime); // Minimum 1 minute
};

/**
 * Generate table of contents from HTML content
 * @param {string} htmlContent - HTML content to parse
 * @returns {Array} Array of heading objects with id, text, and level
 */
export const generateTableOfContents = (htmlContent) => {
  if (!htmlContent) return [];
  
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlContent;
  const headings = tempDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');
  
  return Array.from(headings).map((heading, index) => ({
    id: `heading-${index}`,
    text: heading.textContent?.trim() || '',
    level: parseInt(heading.tagName.charAt(1)),
    element: heading
  })).filter(item => item.text.length > 0);
};

/**
 * Add IDs to headings in HTML content for table of contents navigation
 * @param {string} htmlContent - Original HTML content
 * @param {Array} tableOfContents - TOC array from generateTableOfContents
 * @returns {string} HTML content with heading IDs added
 */
export const addHeadingIds = (htmlContent, tableOfContents) => {
  if (!htmlContent || !tableOfContents.length) return htmlContent;
  
  let modifiedContent = htmlContent;
  
  tableOfContents.forEach((item, index) => {
    const headingRegex = new RegExp(
      `<h${item.level}([^>]*)>\\s*${escapeRegExp(item.text)}\\s*</h${item.level}>`,
      'gi'
    );
    
    modifiedContent = modifiedContent.replace(
      headingRegex,
      `<h${item.level}$1 id="${item.id}">${item.text}</h${item.level}>`
    );
  });
  
  return modifiedContent;
};

/**
 * Extract excerpt from content with smart truncation
 * @param {string} content - Article content (HTML or plain text)
 * @param {number} maxLength - Maximum excerpt length (default: 200)
 * @returns {string} Extracted excerpt
 */
export const extractExcerpt = (content, maxLength = 200) => {
  if (!content) return '';
  
  // Remove HTML tags and get plain text
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = content;
  const plainText = tempDiv.textContent || tempDiv.innerText || '';
  
  if (plainText.length <= maxLength) {
    return plainText;
  }
  
  // Find the last complete sentence within the limit
  const truncated = plainText.substring(0, maxLength);
  const lastSentence = truncated.lastIndexOf('.');
  
  if (lastSentence > maxLength * 0.7) {
    return truncated.substring(0, lastSentence + 1);
  }
  
  // Find the last complete word
  const lastSpace = truncated.lastIndexOf(' ');
  if (lastSpace > maxLength * 0.8) {
    return truncated.substring(0, lastSpace) + '...';
  }
  
  return truncated + '...';
};

/**
 * Format date for display
 * @param {string|Date} dateString - Date to format
 * @param {string} locale - Locale for formatting (default: 'en-US')
 * @returns {string} Formatted date string
 */
export const formatDate = (dateString, locale = 'en-US') => {
  if (!dateString) return '';
  
  try {
    return new Date(dateString).toLocaleDateString(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
};

/**
 * Format relative time (e.g., "2 days ago")
 * @param {string|Date} dateString - Date to format
 * @returns {string} Relative time string
 */
export const formatRelativeTime = (dateString) => {
  if (!dateString) return '';
  
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    const intervals = [
      { label: 'year', seconds: 31536000 },
      { label: 'month', seconds: 2592000 },
      { label: 'week', seconds: 604800 },
      { label: 'day', seconds: 86400 },
      { label: 'hour', seconds: 3600 },
      { label: 'minute', seconds: 60 }
    ];
    
    for (const interval of intervals) {
      const count = Math.floor(diffInSeconds / interval.seconds);
      if (count >= 1) {
        return `${count} ${interval.label}${count > 1 ? 's' : ''} ago`;
      }
    }
    
    return 'Just now';
  } catch (error) {
    console.error('Error formatting relative time:', error);
    return formatDate(dateString);
  }
};

/**
 * Escape special characters for regex
 * @param {string} string - String to escape
 * @returns {string} Escaped string
 */
const escapeRegExp = (string) => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

/**
 * Smooth scroll to element with offset for fixed headers
 * @param {string} elementId - ID of element to scroll to
 * @param {number} offset - Offset from top (default: 100)
 */
export const scrollToElement = (elementId, offset = 100) => {
  const element = document.getElementById(elementId);
  if (!element) return;
  
  const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
  const offsetPosition = elementPosition - offset;
  
  window.scrollTo({
    top: offsetPosition,
    behavior: 'smooth'
  });
};

/**
 * Copy text to clipboard with fallback
 * @param {string} text - Text to copy
 * @returns {Promise<boolean>} Success status
 */
export const copyToClipboard = async (text) => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      const success = document.execCommand('copy');
      document.body.removeChild(textArea);
      return success;
    }
  } catch (error) {
    console.error('Error copying to clipboard:', error);
    return false;
  }
};

/**
 * Share content using Web Share API with fallback
 * @param {Object} shareData - Data to share (title, text, url)
 * @returns {Promise<boolean>} Success status
 */
export const shareContent = async (shareData) => {
  try {
    if (navigator.share && navigator.canShare && navigator.canShare(shareData)) {
      await navigator.share(shareData);
      return true;
    } else {
      // Fallback: copy URL to clipboard
      const success = await copyToClipboard(shareData.url || window.location.href);
      return success;
    }
  } catch (error) {
    console.error('Error sharing content:', error);
    return false;
  }
};
