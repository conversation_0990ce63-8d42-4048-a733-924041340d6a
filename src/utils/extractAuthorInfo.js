/**
 * Extract real author information from article content or game_info
 */

/**
 * Extract author from E-E-A-T configuration in game_info
 * @param {Object} gameInfo - The game_info object
 * @returns {string|null} - Extracted author name or null
 */
function extractAuthorFromGameInfo(gameInfo) {
  if (!gameInfo) return null;
  
  try {
    // Check E-E-A-T configuration
    if (gameInfo.eeat && gameInfo.eeat.author) {
      const author = gameInfo.eeat.author;
      
      // If author is an object with name field
      if (typeof author === 'object' && author.name) {
        return author.name;
      }
      
      // If author is a string
      if (typeof author === 'string') {
        return author;
      }
    }
    
    // Check for author in seo configuration
    if (gameInfo.seo && gameInfo.seo.author) {
      return gameInfo.seo.author;
    }
    
  } catch (error) {
    console.log('Error extracting author from game_info:', error);
  }
  
  return null;
}

/**
 * Extract author from article content (look for "About the Author" sections)
 * @param {string} content - Article HTML content
 * @returns {string|null} - Extracted author name or null
 */
function extractAuthorFromContent(content) {
  if (!content) return null;
  
  try {
    // Common patterns for author information
    const patterns = [
      // About the Author sections
      /(?:About the Author|Sobre o Autor|Author Profile)[:\s]*([^<\n]+)/i,
      // Author: John Smith
      /Author[:\s]+([^<\n,]+)/i,
      // By: John Smith  
      /By[:\s]+([^<\n,]+)/i,
      // Written by John Smith
      /Written by[:\s]+([^<\n,]+)/i,
      // Escrito por João Silva (Portuguese)
      /Escrito por[:\s]+([^<\n,]+)/i,
    ];
    
    for (const pattern of patterns) {
      const match = content.match(pattern);
      if (match && match[1]) {
        const authorName = match[1].trim();
        // Skip generic names
        if (!authorName.toLowerCase().includes('writer') && 
            !authorName.toLowerCase().includes('ai') &&
            authorName.length > 2) {
          return authorName;
        }
      }
    }
    
  } catch (error) {
    console.log('Error extracting author from content:', error);
  }
  
  return null;
}

/**
 * Get proper author name with fallback logic
 * @param {Object} article - Article object with author, content, game_info
 * @returns {string} - Best available author name
 */
export function getProperAuthor(article) {
  // Check if current author is already good (not generic)
  if (article.author && 
      !article.author.toLowerCase().includes('writer') &&
      !article.author.toLowerCase().includes('ai') &&
      article.author.trim() !== '') {
    return article.author;
  }
  
  // Try to extract from game_info
  if (article.game_info) {
    const gameInfo = typeof article.game_info === 'string' ? 
      JSON.parse(article.game_info) : article.game_info;
    
    const gameInfoAuthor = extractAuthorFromGameInfo(gameInfo);
    if (gameInfoAuthor) {
      return gameInfoAuthor;
    }
  }
  
  // Try to extract from content
  const contentAuthor = extractAuthorFromContent(article.content);
  if (contentAuthor) {
    return contentAuthor;
  }
  
  // Fallback to current author or default
  return article.author || 'Writer 777';
}