import { useLocation, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

export const SUPPORTED_LANGUAGES = ['pt', 'es', 'en', 'de', 'fr', 'it', 'ja', 'zh'];
export const DEFAULT_LANGUAGE = 'pt';

/**
 * Map language codes to locale strings for date formatting
 */
export const LANGUAGE_LOCALE_MAP = {
  'pt': 'pt-BR',
  'es': 'es-ES',
  'en': 'en-US',
  'de': 'de-DE',
  'fr': 'fr-FR',
  'it': 'it-IT',
  'ja': 'ja-JP',
  'zh': 'zh-CN'
};

/**
 * Get locale string for current language
 * @param {string} language - Language code
 * @returns {string} - Locale string for date formatting
 */
export const getLocaleForLanguage = (language) => {
  return LANGUAGE_LOCALE_MAP[language] || LANGUAGE_LOCALE_MAP[DEFAULT_LANGUAGE];
};

/**
 * Hook to get current language from URL
 */
export const useCurrentLanguage = () => {
  const { lang } = useParams();
  return lang && SUPPORTED_LANGUAGES.includes(lang) ? lang : DEFAULT_LANGUAGE;
};

/**
 * Generate a path with current language prefix
 * @param {string} path - The path without language prefix
 * @returns {string} - Path with language prefix
 */
export const useLanguagePath = () => {
  const currentLang = useCurrentLanguage();
  
  return (path) => {
    // Remove leading slash if present
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;
    return `/${currentLang}${cleanPath ? '/' + cleanPath : ''}`;
  };
};

/**
 * Generate path for specific language
 * @param {string} path - The path without language prefix
 * @param {string} language - Target language code
 * @returns {string} - Path with language prefix
 */
export const generateLanguagePath = (path, language = DEFAULT_LANGUAGE) => {
  if (!SUPPORTED_LANGUAGES.includes(language)) {
    language = DEFAULT_LANGUAGE;
  }
  
  // Remove leading slash if present
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  return `/${language}${cleanPath ? '/' + cleanPath : ''}`;
};

/**
 * Get path without language prefix
 * @param {string} fullPath - Full path with language prefix
 * @returns {string} - Path without language prefix
 */
export const getPathWithoutLanguage = (fullPath) => {
  const segments = fullPath.split('/').filter(Boolean);
  const firstSegment = segments[0];
  
  if (SUPPORTED_LANGUAGES.includes(firstSegment)) {
    return '/' + segments.slice(1).join('/');
  }
  
  return fullPath;
};

/**
 * Extract language from URL path
 * @param {string} path - URL path
 * @returns {string} - Language code or default language
 */
export const extractLanguageFromPath = (path) => {
  const segments = path.split('/').filter(Boolean);
  const firstSegment = segments[0];
  
  return SUPPORTED_LANGUAGES.includes(firstSegment) ? firstSegment : DEFAULT_LANGUAGE;
};