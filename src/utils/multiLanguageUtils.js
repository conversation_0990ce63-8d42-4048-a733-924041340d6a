/**
 * Multi-language utility functions for content handling
 */

/**
 * Get content type information with localization support
 */
export const getLocalizedContentTypeInfo = (contentType, t) => {
  const typeMap = {
    'casino_review': { 
      icon: '🎰', 
      color: 'from-purple-500 to-pink-500',
      description: 'In-depth casino reviews and ratings'
    },
    'game_guide': { 
      icon: '🎮', 
      color: 'from-blue-500 to-cyan-500',
      description: 'Step-by-step game tutorials and guides'
    },
    'strategy_article': { 
      icon: '📊', 
      color: 'from-green-500 to-emerald-500',
      description: 'Data-driven strategy and analysis'
    },
    'brand_copy': { 
      icon: '✨', 
      color: 'from-yellow-500 to-orange-500',
      description: 'Premium marketing and brand content'
    },
    'industry_news': { 
      icon: '📰', 
      color: 'from-red-500 to-pink-500',
      description: 'Latest industry news and updates'
    },
    'regulatory_update': { 
      icon: '⚖️', 
      color: 'from-slate-500 to-gray-500',
      description: 'Legal and regulatory developments'
    },
    'sports_betting': { 
      icon: '⚽', 
      color: 'from-indigo-500 to-purple-500',
      description: 'Sports betting analysis and predictions'
    },
    'bonus_analysis': { 
      icon: '🎁', 
      color: 'from-emerald-500 to-teal-500',
      description: 'Comprehensive bonus reviews and comparisons'
    }
  };
  
  const typeInfo = typeMap[contentType] || { 
    icon: '📄', 
    color: 'from-gray-500 to-slate-500',
    description: 'Quality content'
  };

  return {
    ...typeInfo,
    label: t(`contentTypes.${contentType}`, contentType),
    description: typeInfo.description // TODO: Add description translations
  };
};

/**
 * Format article date according to current language
 */
export const formatArticleDate = (dateString, language) => {
  if (!dateString) return '';
  
  const localeMap = {
    'en': 'en-US',
    'zh': 'zh-CN',
    'pt': 'pt-BR', 
    'es': 'es-ES',
    'de': 'de-DE',
    'fr': 'fr-FR',
    'it': 'it-IT',
    'ja': 'ja-JP'
  };
  
  const locale = localeMap[language] || 'en-US';
  
  try {
    return new Date(dateString).toLocaleDateString(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
};

/**
 * Get localized reading time text
 */
export const getLocalizedReadingTime = (content, t, wordsPerMinute = 200) => {
  if (!content) return t('article.readingTime', { time: 0 });
  
  // Remove HTML tags and get plain text
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = content;
  const text = tempDiv.textContent || tempDiv.innerText || '';
  
  // Count words (adjust for different languages)
  const wordCount = text.trim().split(/\s+/).filter(word => word.length > 0).length;
  
  // Adjust reading speed for different languages
  const languageMultipliers = {
    'en': 1.0,    // English baseline
    'zh': 0.7,    // Chinese characters read faster
    'pt': 0.9,    // Portuguese slightly slower
    'es': 0.95,   // Spanish slightly slower
    'de': 0.85,   // German slightly slower
    'fr': 0.9,    // French slightly slower
    'it': 0.95,   // Italian slightly slower
    'ja': 0.6     // Japanese characters read faster
  };
  
  const multiplier = languageMultipliers[t('language')] || 1.0;
  const adjustedWPM = wordsPerMinute * multiplier;
  
  // Calculate reading time
  const readTime = Math.ceil(wordCount / adjustedWPM);
  const finalTime = Math.max(1, readTime); // Minimum 1 minute
  
  return t('article.readingTime', { time: finalTime });
};

/**
 * Get language-specific URL path
 */
export const getLanguagePath = (path, language) => {
  // Remove leading slash if present
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  
  // Don't add language prefix for English (default)
  if (language === 'en') {
    return `/${cleanPath}`;
  }
  
  return `/${language}/${cleanPath}`;
};

/**
 * Extract language from URL path
 */
export const extractLanguageFromPath = (pathname) => {
  const segments = pathname.split('/').filter(Boolean);
  const potentialLang = segments[0];
  const supportedLanguages = ['en', 'zh', 'pt', 'es', 'de', 'fr', 'it', 'ja'];
  
  if (supportedLanguages.includes(potentialLang)) {
    return {
      language: potentialLang,
      pathWithoutLanguage: '/' + segments.slice(1).join('/')
    };
  }
  
  return {
    language: 'en', // Default language
    pathWithoutLanguage: pathname
  };
};

/**
 * Content language detection utilities
 */
export const detectContentLanguage = (content) => {
  if (!content) return 'en';
  
  // Simple language detection based on common words
  const text = content.toLowerCase();
  
  // Portuguese indicators
  const portugueseWords = ['que', 'uma', 'para', 'com', 'não', 'mais', 'seu', 'ele', 'tem', 'foram'];
  const portugueseCount = portugueseWords.filter(word => text.includes(` ${word} `)).length;
  
  // Spanish indicators  
  const spanishWords = ['que', 'una', 'para', 'con', 'por', 'más', 'como', 'pero', 'sus', 'fue'];
  const spanishCount = spanishWords.filter(word => text.includes(` ${word} `)).length;
  
  // English indicators
  const englishWords = ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'her'];
  const englishCount = englishWords.filter(word => text.includes(` ${word} `)).length;
  
  // Return language with highest match count
  if (portugueseCount > spanishCount && portugueseCount > englishCount) return 'pt';
  if (spanishCount > englishCount) return 'es';
  return 'en';
};