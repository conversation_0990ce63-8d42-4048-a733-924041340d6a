// Image utilities for content types and articles
import { imageService } from '../services/imageService';

// Legacy Unsplash collections for different content types (kept for backward compatibility)
export const contentTypeImages = {
  'bonus_analysis': {
    background: 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=800&h=400&fit=crop&crop=center',
    thumbnail: 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=400&h=250&fit=crop&crop=center',
    collection: 'gift-bonus-rewards',
    keywords: ['gift', 'bonus', 'rewards', 'money', 'discount']
  },
  'brand_copy': {
    background: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=400&fit=crop&crop=center',
    thumbnail: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=250&fit=crop&crop=center',
    collection: 'marketing-branding',
    keywords: ['marketing', 'branding', 'creative', 'design', 'content']
  },
  'casino_review': {
    background: 'https://images.unsplash.com/photo-1596838132731-3301c3fd4317?w=800&h=400&fit=crop&crop=center',
    thumbnail: 'https://images.unsplash.com/photo-1596838132731-3301c3fd4317?w=400&h=250&fit=crop&crop=center',
    collection: 'casino-poker-chips-cards',
    keywords: ['casino', 'poker', 'chips', 'cards', 'gambling']
  },
  'game_guide': {
    background: 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=800&h=400&fit=crop&crop=center',
    thumbnail: 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=400&h=250&fit=crop&crop=center',
    collection: 'gaming-controller',
    keywords: ['gaming', 'controller', 'video games', 'esports']
  },
  'industry_news': {
    background: 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=800&h=400&fit=crop&crop=center',
    thumbnail: 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=400&h=250&fit=crop&crop=center',
    collection: 'news-newspaper',
    keywords: ['news', 'newspaper', 'journalism', 'media', 'information']
  },
  'regulatory_update': {
    background: 'https://images.unsplash.com/photo-1589391886645-d51941baf7fb?w=800&h=400&fit=crop&crop=center',
    thumbnail: 'https://images.unsplash.com/photo-1589391886645-d51941baf7fb?w=400&h=250&fit=crop&crop=center',
    collection: 'law-legal-justice',
    keywords: ['law', 'legal', 'justice', 'courthouse', 'regulation']
  },
  'sports_betting': {
    background: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=400&fit=crop&crop=center',
    thumbnail: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=250&fit=crop&crop=center',
    collection: 'sports-stadium',
    keywords: ['sports', 'stadium', 'football', 'betting', 'competition']
  },
  'strategy_article': {
    background: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop&crop=center',
    thumbnail: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=250&fit=crop&crop=center',
    collection: 'charts-analytics',
    keywords: ['charts', 'analytics', 'strategy', 'data', 'graphs']
  }
};

// Default fallback images
export const defaultImages = {
  article: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=250&fit=crop&crop=center',
  hero: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=1200&h=600&fit=crop&crop=center',
  placeholder: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=250&fit=crop&crop=center'
};

// Function to get image for specific content type (enhanced with dynamic loading)
export const getContentTypeImage = async (contentType, imageType = 'thumbnail') => {
  try {
    // Try to get custom image from imageService first
    const customImage = await imageService.getContentTypeImage(contentType);
    if (customImage) {
      return customImage;
    }
  } catch (error) {
    console.warn('Failed to load custom image, using fallback:', error.message);
  }
  
  // Fallback to legacy configuration
  const typeConfig = contentTypeImages[contentType];
  if (!typeConfig) {
    return defaultImages.article;
  }
  
  return typeConfig[imageType] || typeConfig.thumbnail || defaultImages.article;
};

// Function to get background image with overlay for content type cards (enhanced)
export const getContentTypeCardBackground = async (contentType) => {
  try {
    // Try to get custom background from imageService first
    return await imageService.getContentTypeCardBackground(contentType);
  } catch (error) {
    console.warn('Failed to load custom background, using fallback:', error.message);
    
    // Fallback to legacy implementation
    const image = await getContentTypeImage(contentType, 'background');
    return {
      backgroundImage: `linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.4)), url(${image})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat'
    };
  }
};

// Function to generate random article image based on content type
export const getRandomArticleImage = (contentType) => {
  const typeConfig = contentTypeImages[contentType];
  if (!typeConfig) {
    return defaultImages.article;
  }
  
  // Generate variations using different Unsplash parameters
  const variations = [
    `https://source.unsplash.com/400x250/?${typeConfig.keywords.join(',')}&sig=${Math.floor(Math.random() * 1000)}`,
    typeConfig.thumbnail,
    `https://picsum.photos/400/250?random=${Math.floor(Math.random() * 1000)}`
  ];
  
  return variations[Math.floor(Math.random() * variations.length)];
};

// Function to optimize image loading
export const getOptimizedImageUrl = (url, width = 400, height = 250, quality = 80) => {
  if (!url) return defaultImages.placeholder;
  
  // If it's already an Unsplash URL, add optimization parameters
  if (url.includes('unsplash.com')) {
    const baseUrl = url.split('?')[0];
    return `${baseUrl}?w=${width}&h=${height}&fit=crop&crop=center&q=${quality}`;
  }
  
  return url;
};

// Legacy hero section images for different themes (kept for backward compatibility)
export const heroImages = {
  main: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=1200&h=600&fit=crop&crop=center',
  tech: 'https://images.unsplash.com/photo-1518186285589-2f7649de83e0?w=1200&h=600&fit=crop&crop=center',
  business: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=1200&h=600&fit=crop&crop=center',
  gaming: 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=1200&h=600&fit=crop&crop=center'
};

// Function to get themed images for special occasions (enhanced with dynamic loading)
export const getThemedImage = async (theme = 'main') => {
  try {
    // Try to get custom hero image from imageService first
    const customImage = await imageService.getHeroImage(theme);
    if (customImage) {
      return customImage;
    }
  } catch (error) {
    console.warn('Failed to load custom hero image, using fallback:', error.message);
  }
  
  // Fallback to legacy configuration
  return heroImages[theme] || heroImages.main;
};