-- Writer777 本地开发数据库设置
-- 使用方法: sudo -u postgres psql -f create-local-db.sql

-- 创建数据库用户
CREATE USER writer777 WITH PASSWORD 'writer777_dev';

-- 创建数据库
CREATE DATABASE writer777_dev OWNER writer777;

-- 给用户必要的权限
ALTER USER writer777 CREATEDB;
GRANT ALL PRIVILEGES ON DATABASE writer777_dev TO writer777;

-- 显示创建结果
\echo '=== 数据库设置完成 ==='
\echo '数据库名称: writer777_dev'
\echo '用户名称: writer777'
\echo '密码: writer777_dev'
\echo '连接字符串: postgresql://writer777:writer777_dev@localhost:5432/writer777_dev'

-- 列出数据库
\l

-- 列出用户
\du