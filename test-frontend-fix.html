<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test JSON Parsing Fix</title>
</head>
<body>
    <h1>Test FAQ JSON Parsing</h1>
    <div id="results"></div>

    <script>
        // 模拟前端修复逻辑
        function repairCasinoJSON(jsonString) {
            console.log('Starting JSON repair...');
            let repairedJson = jsonString;
            
            // Handle HTML encoded content
            if (jsonString.includes('&quot;') || jsonString.includes('<br>') || jsonString.includes('<p>')) {
                // Decode HTML entities and remove HTML tags
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = jsonString;
                repairedJson = tempDiv.textContent || tempDiv.innerText || '';
                
                // Additional cleanup for problematic HTML elements
                repairedJson = repairedJson
                    // Fix HTML links that break JSON structure
                    .replace(/<a\s+href="([^"]*)"[^>]*>([^<]*)<\/a>/g, '"$2"')
                    // Remove any remaining HTML tags
                    .replace(/<[^>]*>/g, '')
                    // Fix common JSON issues
                    .replace(/"\s*:\s*"([^"]*)"([^",}\]]*)"([^",}\]]*)/g, '": "$1$2$3"')
                    // Handle broken quotes in URLs
                    .replace(/https:\/\/([^"]*)"([^"]*)"([^"]*)/g, 'https://$1$2$3')
                    // Clean up extra whitespace
                    .replace(/\s+/g, ' ')
                    .trim();
            }
            
            try {
                const parsed = JSON.parse(repairedJson);
                console.log('✅ Basic parsing succeeded');
                return parsed;
            } catch (error) {
                console.log('❌ Basic parsing failed:', error.message);
                
                // Try aggressive JSON repair
                try {
                    // Fix screenshot URLs with broken quotes
                    repairedJson = repairedJson.replace(
                        /"url":\s*"<a href="([^"]*)"[^>]*>([^<]*)<\/a>"/g,
                        '"url": "$1"'
                    );
                    
                    // Fix any remaining broken links
                    repairedJson = repairedJson.replace(
                        /<a\s+href="([^"]*)"[^>]*>([^<]*)<\/a>/g,
                        '$1'
                    );
                    
                    // Remove HTML tags that might still be present
                    repairedJson = repairedJson.replace(/<[^>]*>/g, '');
                    
                    // Fix broken URLs missing closing quotes
                    repairedJson = repairedJson.replace(
                        /"url":\s*"([^"]*\.jpg),/g,
                        '"url": "$1",'
                    );
                    
                    // Fix URLs without quotes at all
                    repairedJson = repairedJson.replace(
                        /"url":\s*([^",\s]+\.(jpg|png|gif)),/g,
                        '"url": "$1",'
                    );
                    
                    // Fix common quote escaping issues
                    repairedJson = repairedJson.replace(/\\"/g, '"');
                    
                    // Try to balance quotes
                    const openQuotes = (repairedJson.match(/"/g) || []).length;
                    if (openQuotes % 2 !== 0) {
                        console.warn('Unbalanced quotes detected, attempting to fix...');
                        if (!repairedJson.trim().endsWith('}')) {
                            repairedJson = repairedJson.trim() + '"}]}';
                        }
                    }
                    
                    const parsed = JSON.parse(repairedJson);
                    console.log('✅ Aggressive repair succeeded');
                    return parsed;
                } catch (repairError) {
                    console.error('❌ Aggressive repair also failed:', repairError);
                    return null;
                }
            }
        }

        // 测试用的破损JSON（模拟数据库中的实际内容）
        const brokenJson = `<p>{<br>  &quot;established&quot;: &quot;2022&quot;,<br>  &quot;faq&quot;: [<br>    {<br>      &quot;question&quot;: &quot;A plataforma 735 Bet é legítima e segura?&quot;,<br>      &quot;answer&quot;: &quot;Devido à falta de informações transparentes sobre sua licença...&quot;<br>    },<br>    {<br>      &quot;question&quot;: &quot;Como posso me cadastrar na 735 Bet?&quot;,<br>      &quot;answer&quot;: &quot;O processo de cadastro é tipicamente rápido...&quot;<br>    }<br>  ],<br>  &quot;screenshots&quot;: [<br>    {<br>      &quot;url&quot;: &quot;<a href="https://example.com/casino-homepage.jpg">https://example.com/casino-homepage.jpg</a>&quot;,<br>      &quot;caption&quot;: &quot;Página Inicial da 735 Bet&quot;<br>    }<br>  ]<br>}</p>`;

        const result = repairCasinoJSON(brokenJson);
        
        const resultsDiv = document.getElementById('results');
        if (result) {
            resultsDiv.innerHTML = `
                <h2>✅ JSON Parsing Success!</h2>
                <p><strong>FAQ Count:</strong> ${result.faq ? result.faq.length : 0}</p>
                <h3>FAQ Content:</h3>
                <ul>
                    ${result.faq ? result.faq.map((item, index) => 
                        `<li><strong>Q${index + 1}:</strong> ${item.question}<br>
                         <em>A:</em> ${item.answer.substring(0, 100)}...</li>`
                    ).join('') : 'No FAQ found'}
                </ul>
                <h3>Other Data:</h3>
                <p>Screenshots: ${result.screenshots ? result.screenshots.length : 0}</p>
            `;
        } else {
            resultsDiv.innerHTML = '<h2>❌ JSON Parsing Failed</h2>';
        }
    </script>
</body>
</html>