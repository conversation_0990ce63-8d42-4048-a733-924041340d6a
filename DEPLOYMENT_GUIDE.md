# Writer 777 - Complete Deployment Guide

## Overview
This guide covers deploying both the frontend (Vercel) and backend (Railway) for Writer 777.

## Backend Deployment (Railway)

### 1. Railway Setup
- **Service URL:** `https://writer777-production.up.railway.app`
- **Repository:** Connect your GitHub repo
- **Root Directory:** `backend`

### 2. Required Environment Variables in Railway
Set these in Railway Dashboard → Your Backend Service → Variables:

```bash
# Database (PostgreSQL)
DATABASE_URL=${{ Postgres.DATABASE_URL }}  # Auto-set when you add PostgreSQL

# API Keys
GEMINI_API_KEY=AIzaSyDoKMqGr_gaamAD9D4qz_Q-KgbsSbf0LsI
SERPER_API_KEY=9326837be69f9dbaa18dd2cd193d57df4dd2febc
API_KEY=aag_a0f9a0342d98977ce9ddd48f0a38b789072c3c79109251556ad125a0d11e67fd

# Security
JWT_SECRET=your_jwt_secret_minimum_32_characters_long

# Environment
NODE_ENV=production
PORT=8080  # Railway auto-sets this

# CORS (Optional - for additional frontend domains)
ALLOWED_ORIGINS=https://www.writer777.com,https://writer777.com,https://writer777.vercel.app
```

### 3. Add PostgreSQL Database
1. In Railway Dashboard → Click "New" → "Database" → "PostgreSQL"
2. Railway automatically sets `DATABASE_URL` environment variable
3. Backend will auto-create all necessary tables on startup

## Frontend Deployment (Vercel)

### 1. Vercel Setup
- **Repository:** Connect your GitHub repo
- **Framework:** Vite (auto-detected)
- **Root Directory:** `.` (project root)
- **Build Command:** `npm run build`
- **Output Directory:** `dist`

### 2. Environment Variables in Vercel
Set these in Vercel Dashboard → Your Project → Settings → Environment Variables:

```bash
# Backend API URL
VITE_API_BASE_URL=https://writer777-production.up.railway.app

# API Key (matches backend API_KEY)
VITE_API_KEY=aag_a0f9a0342d98977ce9ddd48f0a38b789072c3c79109251556ad125a0d11e67fd
```

## Verification Steps

### 1. Backend Health Check
Visit: `https://writer777-production.up.railway.app/health`
Expected response:
```json
{
  "status": "OK",
  "message": "AI Article Generator API is running"
}
```

### 2. Frontend Connection Test
1. Open your Vercel-deployed frontend
2. Try to register a new account
3. Check browser Network tab for API calls to Railway backend

### 3. Database Connection
Check Railway logs for:
```
DATABASE_URL exists: true
DATABASE_URL type: PostgreSQL
Connected to PostgreSQL database
Database tables initialized successfully
```

## Troubleshooting

### Backend Issues
- **CORS errors:** Add your Vercel domain to `ALLOWED_ORIGINS` in Railway
- **Database errors:** Ensure PostgreSQL service is running and `DATABASE_URL` is set
- **API key errors:** Verify `GEMINI_API_KEY` and `SERPER_API_KEY` are correct

### Frontend Issues
- **API connection failed:** Check `VITE_API_BASE_URL` points to Railway backend
- **Authentication errors:** Verify `VITE_API_KEY` matches backend `API_KEY`
- **Build errors:** Ensure all dependencies are in `package.json`

## Local Development

### Backend
```bash
cd backend
cp .env.example .env
# Edit .env with your API keys
npm install
npm run dev
```

### Frontend
```bash
# In project root
cp .env.frontend.example .env.local
# Edit .env.local to point to local backend
npm install
npm run dev
```

## Production URLs
- **Frontend:** Your Vercel deployment URL
- **Backend:** `https://writer777-production.up.railway.app`
- **Database:** PostgreSQL on Railway (internal)
