#!/bin/bash

# Writer777 本地开发环境一键设置脚本
# 这个脚本会设置PostgreSQL数据库和用户

set -e  # 遇到错误时停止

echo "🚀 Writer777 本地开发环境设置"
echo "=================================="

# 检查是否在正确的目录
if [ ! -f "CLAUDE.md" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 检查PostgreSQL服务
echo "🔍 检查PostgreSQL服务..."
if ! pgrep -x "postgres" > /dev/null; then
    echo "❌ PostgreSQL服务未运行"
    echo "请先启动PostgreSQL服务:"
    echo "sudo service postgresql start"
    exit 1
fi
echo "✅ PostgreSQL服务正在运行"

# 数据库配置
DB_NAME="writer777_dev"
DB_USER="writer777"
DB_PASSWORD="writer777_dev"

echo ""
echo "📊 创建数据库和用户..."
echo "数据库名: $DB_NAME"
echo "用户名: $DB_USER"

# 尝试创建数据库和用户
echo ""
echo "🔐 需要输入PostgreSQL超级用户(postgres)的密码来创建数据库"
echo "如果没有设置密码，请尝试: sudo -u postgres psql"
echo ""

# 创建临时SQL文件
cat > /tmp/writer777_setup.sql << EOF
-- 删除已存在的用户和数据库（如果存在）
DROP DATABASE IF EXISTS $DB_NAME;
DROP USER IF EXISTS $DB_USER;

-- 创建新用户
CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';

-- 给用户创建数据库权限
ALTER USER $DB_USER CREATEDB;

-- 创建数据库
CREATE DATABASE $DB_NAME OWNER $DB_USER;

-- 连接到新数据库并给用户所有权限
\c $DB_NAME;
GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;
GRANT ALL PRIVILEGES ON SCHEMA public TO $DB_USER;

-- 显示结果
\echo '✅ 数据库设置完成！'
\echo '数据库名称: $DB_NAME'
\echo '用户名称: $DB_USER'
\echo '连接字符串: postgresql://$DB_USER:$DB_PASSWORD@localhost:5432/$DB_NAME'

EOF

# 执行SQL脚本
sudo -u postgres psql -f /tmp/writer777_setup.sql

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 数据库创建成功！"
    
    # 测试连接
    echo ""
    echo "🧪 测试数据库连接..."
    PGPASSWORD=$DB_PASSWORD psql -U $DB_USER -h localhost -d $DB_NAME -c "SELECT 'Connection successful!' as status;"
    
    if [ $? -eq 0 ]; then
        echo "✅ 数据库连接测试成功！"
    else
        echo "❌ 数据库连接测试失败"
    fi
    
    # 清理临时文件
    rm -f /tmp/writer777_setup.sql
    
    echo ""
    echo "📦 安装依赖包..."
    cd backend
    npm install
    
    echo ""
    echo "🗄️ 初始化数据库表结构..."
    npm run init-db
    
    if [ $? -eq 0 ]; then
        echo "✅ 数据库表结构初始化成功！"
        
        echo ""
        echo "👤 创建超级管理员账户..."
        node scripts/create-super-admin.js
        
        echo ""
        echo "🎉 本地开发环境设置完成！"
        echo ""
        echo "📝 下一步:"
        echo "1. 设置你的API密钥在 backend/.env 文件中:"
        echo "   GEMINI_API_KEY=your_actual_api_key"
        echo "   SERPER_API_KEY=your_actual_api_key"
        echo ""
        echo "2. 启动开发服务器:"
        echo "   # 后端 (在 backend 目录):"
        echo "   npm run dev"
        echo ""
        echo "   # 前端 (在根目录):"
        echo "   npm run dev"
        echo ""
        echo "🌐 访问地址:"
        echo "   前端: http://localhost:5173"
        echo "   后端: http://localhost:3001"
        echo ""
        echo "🔑 默认管理员账户:"
        echo "   邮箱: <EMAIL>"
        echo "   密码: admin123"
    else
        echo "❌ 数据库表结构初始化失败"
        exit 1
    fi
else
    echo "❌ 数据库设置失败"
    rm -f /tmp/writer777_setup.sql
    exit 1
fi