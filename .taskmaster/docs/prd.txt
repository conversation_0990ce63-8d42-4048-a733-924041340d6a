Enhancement Specification: Adapting the AI Content System for the Casino & Betting Niche

1. Project Overview & Goal
This document outlines the required modifications to adapt the existing AI article generation platform into a specialized, compliant system for the casino and betting industry.
The core generation logic and 7-step workflow will be preserved and augmented with critical safety, compliance, and content-specific features. The primary objective is to enable the creation of responsible, accurate, and SEO-effective content for this highly regulated YMYL (Your Money Your Life) niche.

2. Core Architectural Change: Content-Type Driven Prompting
The most significant architectural change is the evolution from the current single-prompt system to a multi-prompt architecture.
Backend Requirement: The backend must be modified to support storing, managing, and selecting from multiple, distinct AI prompt templates.
Logic: The system will select the appropriate prompt template based on a new "Content Type" field that the user will select at the beginning of the workflow.

3. Modifications to the Existing 7-Step Workflow & UI/UX
The existing workflow will be enhanced with a new initial step and modifications to existing steps.
Step 1: Content Type Selection (New Pre-Step)
Requirement: A new screen or selection modal must be added at the very beginning of the "Create a Task" workflow.
UI Element: A dropdown or card selection menu where the user must choose the specific Content Type they intend to create.
Content Types to include:
Website Brand Copy
Game Guide
Strategy Article
Casino/Game Review
Industry News/Analysis
Sports Betting Article

Step 2: Keyword Research (Modification of Existing Step 1)
Enhancement: Based on the selected Content Type and Target Region (see next step), the UI could optionally flag or provide warnings for potentially non-compliant keywords (e.g., "guaranteed win"). This requires a configurable list of high-risk terms.

Step 3: Topic Generation (Modification of Existing Step 2)
Backend Logic: The AI prompt used for this step must now be dynamically selected based on the Content Type.
Example: For a "Game Guide," the prompt will ask for "step-by-step instructional topics." For "Industry News," it will ask for "analytical or reporting-focused angles."

Step 4: Jurisdictional & Compliance Settings (New Step)
Requirement: A new, mandatory step must be inserted into the workflow after Topic Generation.
UI Fields Required:
Target Language & Target Region: These selections are critical and will be passed to the final prompt.
Mandatory Disclaimers: A text area, pre-populated with localized templates based on the selected region, but editable by the user.
Content Restrictions: A field for the user to input specific compliance rules for the AI to follow.

Step 5-8: Subsequent Steps (Modifications of Existing Steps 3-7)
Source Provision, Product Info, Author Info, and Article Parameters: These steps in the workflow remain functionally the same, but the UI should emphasize their importance in the context of a YMYL niche.
Generation & Review: The final step is now Generation followed by a mandatory, enhanced review process.

4. AI Prompt Engineering Requirements (New & Modified Templates)
The backend will need to store and manage a new library of prompt templates. Each template will share the core safety principles but be fine-tuned for its specific content type.
Master Template Elements (Shared across all prompts):
The RESPONSIBLE GAMING FIRST, JURISDICTIONAL COMPLIANCE, and ACCURACY & SOURCE RELIANCE rules.
The logic to dynamically insert the user-provided Disclaimers and Restrictions.
The instruction to generate only in the Target Language.
Specialized Instructions per Template (Examples):
Game Guide Prompt: Will have a "Clear and Patient Game Instructor" persona and prioritize step-by-step clarity.
Review Prompt: Will have an "Objective Industry Analyst" persona and be instructed to structure the article around the user-provided Review Criteria.
Brand Copy Prompt: Will have an "iGaming Brand Copywriter" persona and be instructed to focus on persuasive, benefit-oriented language.
...and so on for each content type.

5. Post-Generation Workflow Enhancement
The existing editor/output screen where the generated article is displayed must be enhanced to facilitate the mandatory human review process.
UI Requirement: Add a "Compliance & Quality Checklist" feature to the editor view.
Functionality: Before the user can mark the article as "complete" or export it, they must manually tick off checkboxes confirming a human review has been place.
Checklist Items (Example):
[ ] Factual Accuracy Verified Against Sources
[ ] Jurisdictional Compliance Rules Met
[ ] Responsible Gaming Disclaimers Included and Correct
[ ] Tone and Language are Responsible and Non-Predatory
[ ] Final SEO and Style Polish Complete

By framing the project this way, you are leveraging your existing, powerful system as a base and strategically adding the necessary layers of control, specificity, and safety required to operate effectively in the casino and betting niche.